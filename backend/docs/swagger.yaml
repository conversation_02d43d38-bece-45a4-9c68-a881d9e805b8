basePath: /api/v1
definitions:
  ErrorResponse:
    properties:
      error:
        example: Action failed
        type: string
    type: object
  LogDTO:
    properties:
      admin:
        example: admin
        type: string
      created_at:
        example: "2020-01-01 00:00:00"
        type: string
      entity:
        example: order
        type: string
      entity_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      ip:
        example: 127.0.0.1
        type: string
      message:
        example: Log Message
        type: string
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      proto:
        description: http, gRPC
        example: http
        type: string
      title:
        example: Log Title
        type: string
      type:
        description: info,  error
        example: info
        type: string
      user:
        example: user
        type: string
    type: object
  ResponseSimulateForSwagger:
    properties:
      performance:
        example: 0.1s
        type: string
      result:
        type: string
      trace:
        type: string
    type: object
  SimulateResponseForSwagger:
    properties:
      access_token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
        type: string
      context:
        $ref: '#/definitions/entities.Attrs'
      created_at:
        example: "2021-01-01T00:00:00Z"
        type: string
      edges:
        items:
          $ref: '#/definitions/dtos.Edge'
        type: array
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      name:
        example: my_simulation
        type: string
      nodes:
        items:
          $ref: '#/definitions/dtos.Node'
        type: array
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      simulate_output:
        $ref: '#/definitions/dtos.SimulateOutputForSwaggger'
      version_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      version_name:
        example: v1.0.0
        type: string
    type: object
  SuccessResponse:
    properties:
      message:
        example: Action completed successfully
        type: string
    type: object
  dtos._Simulate:
    properties:
      ID:
        example: 123e4567-e89b-12d3-a456-426614174000
        type: string
      created_at:
        example: "2024-11-26T15:04:05Z"
        type: string
      name:
        example: Simulation Name
        type: string
    type: object
  dtos.AddUserToOrganization:
    properties:
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      user_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    required:
    - organization_id
    - user_id
    type: object
  dtos.CreateOrganization:
    properties:
      name:
        example: mono payments
        type: string
      parent_organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    required:
    - name
    type: object
  dtos.DefaultRequestForSwagger:
    properties:
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      simulate_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      type:
        example: 1
        type: integer
      value:
        example: '{"key":"value"}'
        type: string
    type: object
  dtos.Edge:
    properties:
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      sourceHandle:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      sourceId:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      targetId:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      type:
        example: edge
        type: string
    type: object
  dtos.FavoriteNode:
    properties:
      label:
        example: Node Label
        type: string
      name:
        example: Node Name
        type: string
      type:
        example: Node Type
        type: string
    type: object
  dtos.Login:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        example: "123456"
        type: string
    required:
    - email
    - password
    type: object
  dtos.Node:
    properties:
      content: {}
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      label:
        example: request
        type: string
      name:
        example: my_request
        type: string
      position:
        additionalProperties:
          type: integer
        type: object
      type:
        example: inputNode
        type: string
    type: object
  dtos.PaginatedData:
    properties:
      page:
        example: 1
        type: integer
      per_page:
        example: 10
        type: integer
      rows:
        items:
          type: object
        type: array
      total:
        example: 100
        type: integer
      total_pages:
        example: 10
        type: integer
    type: object
  dtos.Register:
    properties:
      email:
        example: <EMAIL>
        type: string
      name:
        example: mono payments
        type: string
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      password:
        example: "123456"
        type: string
    required:
    - email
    - name
    - organization_id
    - password
    type: object
  dtos.RequestForAddDSN:
    properties:
      db_engine:
        description: '1: mysql, 2: postgres ...'
        example: 2
        type: integer
      dsn:
        example: postgres://user:password@localhost:5432/dbname
        type: string
      key:
        example: my-psql-dsn
        type: string
    type: object
  dtos.RequestForAddRole:
    properties:
      name:
        example: admin
        type: string
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    type: object
  dtos.RequestForChangeAdminInfo:
    properties:
      emailForAlert:
        example: <EMAIL>
        type: string
      smsForAlert:
        example: "905555555555"
        type: string
    type: object
  dtos.RequestForCreateUser:
    properties:
      email:
        example: <EMAIL>
        type: string
      name:
        example: mono
        type: string
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      password:
        type: string
      password_confirm:
        type: string
      role_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
    type: object
  dtos.RequestForDefaultRequest:
    properties:
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      simulate_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      type:
        example: 1
        type: integer
      value:
        example: '{"key":"value"}'
        type: string
    type: object
  dtos.RequestForForgotPassword:
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  dtos.RequestForGenerateAppToken:
    properties:
      expire_at:
        type: string
      name:
        type: string
    type: object
  dtos.RequestForGetDefaultRequest:
    properties:
      simulate_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      type:
        example: 1
        type: integer
    type: object
  dtos.RequestForResetPassword:
    properties:
      code:
        example: "123456"
        type: string
      password:
        type: string
      password_confirm:
        type: string
    required:
    - code
    - password
    - password_confirm
    type: object
  dtos.RequestForSimByID:
    properties:
      context:
        additionalProperties: {}
        type: object
      domain:
        type: string
      token:
        type: string
    type: object
  dtos.RequestForUpdateOrganization:
    properties:
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      name:
        example: mono payments edit
        type: string
    required:
    - id
    - name
    type: object
  dtos.RequestForUpdateUser:
    properties:
      email:
        example: <EMAIL>
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      name:
        example: mono
        type: string
    type: object
  dtos.ResponseForDSN:
    properties:
      created_at:
        example: "2021-01-01T00:00:00Z"
        type: string
      db_engine:
        example: 2
        type: integer
      dsn:
        example: postgres://user:password@localhost:5432/dbname
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      key:
        example: my-psql-dsn
        type: string
      last_dsn_check_at:
        example: "2021-01-01T00:00:00Z"
        type: string
      last_dsn_check_status:
        example: 1
        type: integer
    type: object
  dtos.ResponseForDashboard:
    properties:
      last_favorite_node:
        $ref: '#/definitions/dtos.FavoriteNode'
      last_simulate:
        $ref: '#/definitions/dtos._Simulate'
      total_evaluation_count:
        example: 12
        type: integer
      total_favorite_nodes_count:
        example: 5
        type: integer
      total_simulate_count:
        example: 8
        type: integer
    type: object
  dtos.ResponseForScheduledWork:
    properties:
      admin_id:
        type: string
      created_at:
        type: string
      each_x_time:
        type: string
      end_date:
        type: string
      how_many_time_to_try:
        type: integer
      id:
        type: string
      name_of_work:
        type: string
      organization_id:
        type: string
      request:
        type: string
      simulate_id:
        type: string
      start_date:
        type: string
      total_error:
        type: integer
      total_success:
        type: integer
      total_work:
        type: integer
    type: object
  dtos.Simulate:
    type: object
  dtos.SimulateLogForSwagger:
    properties:
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      message:
        example: error message
        type: string
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      proto:
        example: grpc
        type: string
      request:
        example: '{"key":"value"}'
        type: string
      response:
        example: '{"key":"value"}'
        type: string
      simulate_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      type:
        example: error
        type: string
    type: object
  dtos.SimulateOutputForSwaggger:
    properties:
      ID:
        type: string
      performance:
        type: string
      result:
        $ref: '#/definitions/entities.Attrs'
      trace:
        $ref: '#/definitions/entities.Attrs'
    type: object
  dtos.User:
    properties:
      allow_organization_change:
        example: true
        type: boolean
      email:
        example: <EMAIL>
        type: string
      emailForAlert:
        example: <EMAIL>
        type: string
      id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      name:
        example: mono payments
        type: string
      organization_id:
        example: f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212
        type: string
      organization_name:
        example: mono payments
        type: string
      smsForAlert:
        example: "905555555555"
        type: string
    type: object
  entities.Attrs:
    additionalProperties: true
    type: object
host: localhost:4000
info:
  contact:
    name: API Support
    url: https://github.com/monopayments/mono-grc/issues
  description: Pars .
  title: Pars
  version: "1.0"
paths:
  /application/token:
    get:
      consumes:
      - application/json
      description: Get All Application Token
      produces:
      - application/json
      responses:
        "200":
          description: Tokens fetched successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get All Application Token
      tags:
      - Application Token
    post:
      consumes:
      - application/json
      description: Generate a new token with the provided details.
      parameters:
      - description: Token generation details
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForGenerateAppToken'
      produces:
      - application/json
      responses:
        "200":
          description: Organization created successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Generate Token
      tags:
      - Application Token
  /application/token/{id}:
    delete:
      consumes:
      - application/json
      description: Delete token by ID
      parameters:
      - description: Token ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Token deleted successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete Token
      tags:
      - Application Token
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticates a user and returns a JWT token upon successful login.
      parameters:
      - description: User login credentials
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.Login'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful, returns JWT token
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Invalid login payload
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: User Login
      tags:
      - Authentication
  /auth/register:
    post:
      consumes:
      - application/json
      description: Registers a new user and returns a JWT token upon successful registration.
      parameters:
      - description: User registration information
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.Register'
      produces:
      - application/json
      responses:
        "200":
          description: Registration successful, returns JWT token
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Invalid registration payload
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: User Registration
      tags:
      - Authentication
  /auth/token-refresh:
    get:
      consumes:
      - application/json
      description: Refreshes token and returns a new JWT token.
      produces:
      - application/json
      responses:
        "200":
          description: Token refreshed successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Token Refresh
      tags:
      - Authentication
  /dash:
    get:
      consumes:
      - application/json
      description: Get dashboard data for current organization.
      produces:
      - application/json
      responses:
        "200":
          description: Success dashboard data
          schema:
            $ref: '#/definitions/dtos.ResponseForDashboard'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get Dashboard Data
      tags:
      - Dashboard
  /logs/:
    get:
      consumes:
      - application/json
      description: List logs with pagination
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Number of logs per page
        in: query
        name: per_page
        type: integer
      - description: Entity name
        in: query
        name: entity
        type: string
      - description: Entity ID
        in: query
        name: entity_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of logs
          schema:
            items:
              $ref: '#/definitions/dtos.PaginatedData'
            type: array
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: List logs with pagination
      tags:
      - Logs
  /logs/{id}:
    get:
      consumes:
      - application/json
      description: Get a log by ID
      parameters:
      - description: Log ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Log details
          schema:
            $ref: '#/definitions/LogDTO'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get a log by ID
      tags:
      - Logs
  /logs/simulate/{simulate_id}:
    get:
      consumes:
      - application/json
      description: Get simulate logs with pagination, use simulate ID
      parameters:
      - description: Simulate ID
        in: path
        name: simulate_id
        required: true
        type: string
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Number of logs per page
        in: query
        name: per_page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of simulate logs
          schema:
            items:
              $ref: '#/definitions/dtos.PaginatedData'
            type: array
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get simulate logs with pagination
      tags:
      - Logs
  /logs/simulate/single/{log_id}:
    get:
      consumes:
      - application/json
      description: Get a single simulate log by simulate ID and log ID
      parameters:
      - description: Simulate ID
        in: path
        name: simulate_id
        required: true
        type: string
      - description: Log ID
        in: path
        name: log_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Simulate log details
          schema:
            $ref: '#/definitions/dtos.SimulateLogForSwagger'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get a single simulate log by simulate ID and log ID
      tags:
      - Logs
  /node/favorites:
    get:
      consumes:
      - application/json
      description: Get All Favorite Nodes with optional pagination parameters.
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Number of logs per page
        in: query
        name: per_page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of favorite nodes
          schema:
            items:
              $ref: '#/definitions/dtos.PaginatedData'
            type: array
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get All Favorite Nodes
      tags:
      - Favorite Node
    post:
      consumes:
      - application/json
      description: Create or update a favorite node
      parameters:
      - description: payload for the favorite node
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.Node'
      produces:
      - application/json
      responses:
        "200":
          description: Successfully created or updated the favorite node
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Create or update a favorite node
      tags:
      - Favorite Node
  /node/favorites/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a favorite node by ID
      parameters:
      - description: Favorite Node ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully deleted the favorite node
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Delete a favorite node by ID
      tags:
      - Favorite Node
  /organization:
    get:
      consumes:
      - application/json
      description: Get All Organization
      produces:
      - application/json
      responses:
        "200":
          description: Organization details fetched successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get All Organization
      tags:
      - Organization
    put:
      consumes:
      - application/json
      description: Update organization by ID
      parameters:
      - description: Organization details
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdateOrganization'
      produces:
      - application/json
      responses:
        "200":
          description: User successfully added
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update Organization by ID
      tags:
      - Organization
  /organization/:
    get:
      consumes:
      - application/json
      description: Get Current Organization Details
      produces:
      - application/json
      responses:
        "200":
          description: Organization details fetched successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get Current Organization Details
      tags:
      - Organization
  /organization/{id}:
    delete:
      consumes:
      - application/json
      description: Delete organization by ID
      parameters:
      - description: User addition details
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.AddUserToOrganization'
      produces:
      - application/json
      responses:
        "200":
          description: User successfully added
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete Organization
      tags:
      - Organization
    get:
      consumes:
      - application/json
      description: Get Organization Detail by ID
      produces:
      - application/json
      responses:
        "200":
          description: Organization details fetched successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get Organization Detail by ID
      tags:
      - Organization
  /organizations/:
    post:
      consumes:
      - application/json
      description: Creates a new organization with the provided details.
      parameters:
      - description: Organization creation details
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.CreateOrganization'
      produces:
      - application/json
      responses:
        "200":
          description: Organization created successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create Organization
      tags:
      - Organization
  /pars/gpt:
    get:
      consumes:
      - application/json
      description: Get all simulates
      produces:
      - application/json
      responses:
        "200":
          description: Successfully created
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get all simulates
    post:
      consumes:
      - application/json
      description: Create a simulate with PARS gpt
      produces:
      - application/json
      responses:
        "200":
          description: Successfully created
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Create a simulate with PARS gpt
      tags:
      - PARS gpt
  /pars/gpt/:id:
    get:
      consumes:
      - application/json
      description: Get simulate
      produces:
      - application/json
      responses:
        "200":
          description: Successfully created
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get simulate
      tags:
      - PARS gpt
  /pars/gpt/rate:
    post:
      consumes:
      - application/json
      description: Rate simulate
      produces:
      - application/json
      responses:
        "200":
          description: Successfully rated
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Rate simulate
      tags:
      - PARS gpt
  /preferences/dsn:
    get:
      consumes:
      - application/json
      description: Get DSN
      produces:
      - application/json
      responses:
        "200":
          description: The DSN configuration
          schema:
            items:
              $ref: '#/definitions/dtos.ResponseForDSN'
            type: array
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get DSN
      tags:
      - Preferences
    post:
      consumes:
      - application/json
      description: Adds a new DSN configuration for db node.
      parameters:
      - description: Request body to add DSN
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForAddDSN'
      produces:
      - application/json
      responses:
        "201":
          description: success" "DSN successfully added
          schema:
            type: string
        "400":
          description: Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Add DSN
      tags:
      - Preferences
  /preferences/dsn/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a DSN by ID
      parameters:
      - description: The ID of the DSN to delete
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: success"    "DSN successfully deleted
          schema:
            type: string
        "400":
          description: Bad request - invalid DSN ID
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete DSN
      tags:
      - Preferences
  /retry/{id}:
    post:
      consumes:
      - application/json
      description: Retry a simulate by ID
      produces:
      - application/json
      responses:
        "200":
          description: Successfully created
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Retry a simulate by ID
      tags:
      - Retry
  /role:
    get:
      consumes:
      - application/json
      description: Get All Roles
      produces:
      - application/json
      responses:
        "200":
          description: Roles fetched successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get All Roles
      tags:
      - role
    post:
      consumes:
      - application/json
      description: Creates a new role with the provided details.
      parameters:
      - description: Role creation details
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForAddRole'
      produces:
      - application/json
      responses:
        "200":
          description: role created successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create Role
      tags:
      - Role
  /scheduled:
    get:
      consumes:
      - application/json
      description: Get scheduled works
      produces:
      - application/json
      responses:
        "200":
          description: Successfully created
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get scheduled works
      tags:
      - Scheduled
    post:
      consumes:
      - application/json
      description: Create a scheduled work
      produces:
      - application/json
      responses:
        "200":
          description: Successfully created
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Create a scheduled work
      tags:
      - Scheduled
  /scheduled/{id}:
    get:
      consumes:
      - application/json
      description: Get a scheduled work
      parameters:
      - description: Scheduled Work ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Successfully created
          schema:
            $ref: '#/definitions/dtos.ResponseForScheduledWork'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get a scheduled work
      tags:
      - Scheduled
  /scheduled/{id}/logs:
    get:
      consumes:
      - application/json
      description: Get scheduled work logs
      parameters:
      - description: Scheduled Work ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get scheduled work logs
      tags:
      - Scheduled
  /simulate/:
    get:
      consumes:
      - application/json
      description: Fetches a list of simulations, with pagination and optional filtering
        by version_id.
      parameters:
      - default: 1
        description: Page number for pagination
        in: query
        name: page
        type: integer
      - default: 10
        description: Number of simulations per page
        in: query
        name: per_page
        type: integer
      - description: Filter simulations by version ID
        in: query
        name: version_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of simulations
          schema:
            $ref: '#/definitions/dtos.PaginatedData'
        "400":
          description: Bad request - Invalid query parameters
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get Simulations List
      tags:
      - Simulate
    post:
      consumes:
      - application/json
      description: Executes the simulation based on the provided payload data.
      parameters:
      - description: Data to simulate
        in: body
        name: simulate
        required: true
        schema:
          $ref: '#/definitions/dtos.Simulate'
      produces:
      - application/json
      responses:
        "200":
          description: Simulation result data
          schema:
            $ref: '#/definitions/ResponseSimulateForSwagger'
        "400":
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Simulate Data
      tags:
      - Simulate
    put:
      consumes:
      - application/json
      description: Updates an existing simulation or creates a new one based on the
        provided payload.
      parameters:
      - description: Simulate data to be updated or created
        in: body
        name: simulate
        required: true
        schema:
          $ref: '#/definitions/dtos.Simulate'
      produces:
      - application/json
      responses:
        "200":
          description: ID of the updated or created simulation
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update or Create Simulate Data
      tags:
      - Simulate
  /simulate/{simulate_id}:
    delete:
      consumes:
      - application/json
      description: Delete Simulate Data by Simulate ID
      parameters:
      - description: The ID of the simulate data to delete
        in: path
        name: simulate_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 'Success: Simulate data deleted'
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Bad request - invalid simulate ID
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete Simulate Data
      tags:
      - Simulate
    get:
      consumes:
      - application/json
      description: Simulate Rule by Simulate ID
      parameters:
      - description: Simulate ID
        in: path
        name: simulate_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Simulation result data
          schema:
            $ref: '#/definitions/SimulateResponseForSwagger'
        "400":
          description: Bad request - Invalid simulate ID
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Simulate Rule
      tags:
      - Simulate
  /simulate/by-id:
    post:
      consumes:
      - application/json
      description: Simulate by id
      produces:
      - application/json
      responses:
        "200":
          description: sim by id request
          schema:
            $ref: '#/definitions/dtos.RequestForSimByID'
        "400":
          description: Bad request - Invalid parameters
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Simulate by id
      tags:
      - Simulate
  /simulate/default-request/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Default Request by ID
      parameters:
      - description: ID of the default request to be deleted
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: successfully deleted
          schema:
            type: string
        "400":
          description: Bad request - Invalid request ID
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Delete Default Request
      tags:
      - Default Request
  /simulate/default-request/add:
    post:
      consumes:
      - application/json
      description: This endpoint is used to add a default request for simulation.
      parameters:
      - description: Default Request Payload
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForDefaultRequest'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
        "400":
          description: Bad request - Invalid simulate ID
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Add a Default Request
      tags:
      - Default Request
  /simulate/default-request/get:
    post:
      consumes:
      - application/json
      description: Get Default Request
      parameters:
      - description: Default Request Criteria
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForGetDefaultRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Successfully retrieved the default request data
          schema:
            $ref: '#/definitions/dtos.DefaultRequestForSwagger'
        "400":
          description: Bad request - Invalid parameters
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get Default Request
      tags:
      - Default Request
  /simulate/detail/{simulate_id}:
    get:
      consumes:
      - application/json
      description: Simulate Detail by ID
      parameters:
      - description: Simulate ID
        in: path
        name: simulate_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Simulation detail data
          schema:
            $ref: '#/definitions/SimulateResponseForSwagger'
        "400":
          description: Bad request - Invalid simulate ID
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Simulate Detail by ID
      tags:
      - Simulate
  /simulate/name/{id}:
    put:
      consumes:
      - application/json
      description: Update Simulate Nme
      parameters:
      - description: Simulate data to be updated or created
        in: body
        name: simulate
        required: true
        schema:
          $ref: '#/definitions/dtos.Simulate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/SuccessResponse'
        "400":
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update Simulate Nme
      tags:
      - Simulate
  /simulate/rules:
    get:
      consumes:
      - application/json
      description: Get Simulation Rules List with pagination.
      parameters:
      - default: 1
        description: Page number for pagination
        in: query
        name: page
        type: integer
      - default: 10
        description: Number of simulation rules per page
        in: query
        name: per_page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of simulation rules
          schema:
            $ref: '#/definitions/dtos.PaginatedData'
        "400":
          description: Bad request - Invalid query parameters
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get Simulation Rules List
      tags:
      - Simulate
  /user:
    get:
      consumes:
      - application/json
      description: Get All Users
      produces:
      - application/json
      responses:
        "200":
          description: Users fetched successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get All Users
      tags:
      - User
    put:
      consumes:
      - application/json
      description: Update User
      parameters:
      - description: User Payload For Update
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForUpdateUser'
      produces:
      - application/json
      responses:
        "200":
          description: ok" "User added successfully
          schema:
            type: string
        "400":
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Update User
      tags:
      - User
  /user-new/{id}:
    get:
      consumes:
      - application/json
      description: Get User Detail by ID
      produces:
      - application/json
      responses:
        "200":
          description: Organization details fetched successfully
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get User Detail by ID
      tags:
      - User
  /user/:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves the details of the currently authenticated
        user.
      produces:
      - application/json
      responses:
        "200":
          description: User details retrieved successfully
          schema:
            $ref: '#/definitions/dtos.User'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Get User Information
      tags:
      - User
  /user/{id}:
    delete:
      consumes:
      - application/json
      description: Delete user by ID
      produces:
      - application/json
      responses:
        "200":
          description: User successfully added
          schema:
            $ref: '#/definitions/SuccessResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete User
      tags:
      - User
  /user/add:
    post:
      consumes:
      - application/json
      description: Create User
      parameters:
      - description: User Payload
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForCreateUser'
      produces:
      - application/json
      responses:
        "200":
          description: ok" "User added successfully
          schema:
            type: string
        "400":
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Create User
      tags:
      - User
  /user/change-admin-info:
    post:
      consumes:
      - application/json
      description: Change Admin Information
      parameters:
      - description: Admin Info Change Payload
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForChangeAdminInfo'
      produces:
      - application/json
      responses:
        "200":
          description: ok" "Admin information successfully updated
          schema:
            type: string
        "400":
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Change Admin Information
      tags:
      - User
  /user/forgot-password:
    post:
      consumes:
      - application/json
      description: Forgot Password
      parameters:
      - description: Payload For Forgot Password
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForForgotPassword'
      produces:
      - application/json
      responses:
        "200":
          description: ok" "Password reset code sent successfully
          schema:
            type: string
        "400":
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Forgot Password
      tags:
      - User
  /user/reset-password:
    post:
      consumes:
      - application/json
      description: Reset Password
      parameters:
      - description: Payload For Reset Password
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dtos.RequestForResetPassword'
      produces:
      - application/json
      responses:
        "200":
          description: ok" "Password reset successfully
          schema:
            type: string
        "400":
          description: Bad request - Invalid input data
          schema:
            $ref: '#/definitions/ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/ErrorResponse'
      summary: Reset Password
      tags:
      - User
schemes:
- http
securityDefinitions:
  BearerAuth:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
