// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "API Support",
            "url": "https://github.com/monopayments/mono-grc/issues"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/application/token": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get All Application Token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Application Token"
                ],
                "summary": "Get All Application Token",
                "responses": {
                    "200": {
                        "description": "Tokens fetched successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Generate a new token with the provided details.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Application Token"
                ],
                "summary": "Generate Token",
                "parameters": [
                    {
                        "description": "Token generation details",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForGenerateAppToken"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Organization created successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/application/token/{id}": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete token by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Application Token"
                ],
                "summary": "Delete Token",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Token ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Token deleted successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/auth/login": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Authenticates a user and returns a JWT token upon successful login.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "User Login",
                "parameters": [
                    {
                        "description": "User login credentials",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.Login"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Login successful, returns JWT token",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid login payload",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/auth/register": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Registers a new user and returns a JWT token upon successful registration.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "User Registration",
                "parameters": [
                    {
                        "description": "User registration information",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.Register"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Registration successful, returns JWT token",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid registration payload",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/auth/token-refresh": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Refreshes token and returns a new JWT token.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "Token Refresh",
                "responses": {
                    "200": {
                        "description": "Token refreshed successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/dash": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get dashboard data for current organization.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Dashboard"
                ],
                "summary": "Get Dashboard Data",
                "responses": {
                    "200": {
                        "description": "Success dashboard data",
                        "schema": {
                            "$ref": "#/definitions/dtos.ResponseForDashboard"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/logs/": {
            "get": {
                "description": "List logs with pagination",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Logs"
                ],
                "summary": "List logs with pagination",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of logs per page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Entity name",
                        "name": "entity",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Entity ID",
                        "name": "entity_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of logs",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/dtos.PaginatedData"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/logs/simulate/single/{log_id}": {
            "get": {
                "description": "Get a single simulate log by simulate ID and log ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Logs"
                ],
                "summary": "Get a single simulate log by simulate ID and log ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Simulate ID",
                        "name": "simulate_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Log ID",
                        "name": "log_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Simulate log details",
                        "schema": {
                            "$ref": "#/definitions/dtos.SimulateLogForSwagger"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/logs/simulate/{simulate_id}": {
            "get": {
                "description": "Get simulate logs with pagination, use simulate ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Logs"
                ],
                "summary": "Get simulate logs with pagination",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Simulate ID",
                        "name": "simulate_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of logs per page",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of simulate logs",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/dtos.PaginatedData"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/logs/{id}": {
            "get": {
                "description": "Get a log by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Logs"
                ],
                "summary": "Get a log by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Log ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Log details",
                        "schema": {
                            "$ref": "#/definitions/LogDTO"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/node/favorites": {
            "get": {
                "description": "Get All Favorite Nodes with optional pagination parameters.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Favorite Node"
                ],
                "summary": "Get All Favorite Nodes",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of logs per page",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of favorite nodes",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/dtos.PaginatedData"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Create or update a favorite node",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Favorite Node"
                ],
                "summary": "Create or update a favorite node",
                "parameters": [
                    {
                        "description": "payload for the favorite node",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.Node"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully created or updated the favorite node",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Bad request",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/node/favorites/{id}": {
            "delete": {
                "description": "Delete a favorite node by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Favorite Node"
                ],
                "summary": "Delete a favorite node by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Favorite Node ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully deleted the favorite node",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organization": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get All Organization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Get All Organization",
                "responses": {
                    "200": {
                        "description": "Organization details fetched successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update organization by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Update Organization by ID",
                "parameters": [
                    {
                        "description": "Organization details",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdateOrganization"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User successfully added",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organization/": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Current Organization Details",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Get Current Organization Details",
                "responses": {
                    "200": {
                        "description": "Organization details fetched successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organization/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Organization Detail by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Get Organization Detail by ID",
                "responses": {
                    "200": {
                        "description": "Organization details fetched successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete organization by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Delete Organization",
                "parameters": [
                    {
                        "description": "User addition details",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.AddUserToOrganization"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "User successfully added",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Creates a new organization with the provided details.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization"
                ],
                "summary": "Create Organization",
                "parameters": [
                    {
                        "description": "Organization creation details",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.CreateOrganization"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Organization created successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/pars/gpt": {
            "get": {
                "description": "Get all simulates",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "summary": "Get all simulates",
                "responses": {
                    "200": {
                        "description": "Successfully created",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Create a simulate with PARS gpt",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "PARS gpt"
                ],
                "summary": "Create a simulate with PARS gpt",
                "responses": {
                    "200": {
                        "description": "Successfully created",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/pars/gpt/:id": {
            "get": {
                "description": "Get simulate",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "PARS gpt"
                ],
                "summary": "Get simulate",
                "responses": {
                    "200": {
                        "description": "Successfully created",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/pars/gpt/rate": {
            "post": {
                "description": "Rate simulate",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "PARS gpt"
                ],
                "summary": "Rate simulate",
                "responses": {
                    "200": {
                        "description": "Successfully rated",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/preferences/dsn": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get DSN",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Preferences"
                ],
                "summary": "Get DSN",
                "responses": {
                    "200": {
                        "description": "The DSN configuration",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/dtos.ResponseForDSN"
                            }
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Adds a new DSN configuration for db node.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Preferences"
                ],
                "summary": "Add DSN",
                "parameters": [
                    {
                        "description": "Request body to add DSN",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForAddDSN"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success\" \"DSN successfully added",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/preferences/dsn/{id}": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete a DSN by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Preferences"
                ],
                "summary": "Delete DSN",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The ID of the DSN to delete",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success\"    \"DSN successfully deleted",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad request - invalid DSN ID",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/retry/{id}": {
            "post": {
                "description": "Retry a simulate by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Retry"
                ],
                "summary": "Retry a simulate by ID",
                "responses": {
                    "200": {
                        "description": "Successfully created",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/role": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get All Roles",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "role"
                ],
                "summary": "Get All Roles",
                "responses": {
                    "200": {
                        "description": "Roles fetched successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Creates a new role with the provided details.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Role"
                ],
                "summary": "Create Role",
                "parameters": [
                    {
                        "description": "Role creation details",
                        "name": "payload",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForAddRole"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "role created successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/scheduled": {
            "get": {
                "description": "Get scheduled works",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Scheduled"
                ],
                "summary": "Get scheduled works",
                "responses": {
                    "200": {
                        "description": "Successfully created",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "description": "Create a scheduled work",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Scheduled"
                ],
                "summary": "Create a scheduled work",
                "responses": {
                    "200": {
                        "description": "Successfully created",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/scheduled/{id}": {
            "get": {
                "description": "Get a scheduled work",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Scheduled"
                ],
                "summary": "Get a scheduled work",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Scheduled Work ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully created",
                        "schema": {
                            "$ref": "#/definitions/dtos.ResponseForScheduledWork"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/scheduled/{id}/logs": {
            "get": {
                "description": "Get scheduled work logs",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Scheduled"
                ],
                "summary": "Get scheduled work logs",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Scheduled Work ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/simulate/": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Fetches a list of simulations, with pagination and optional filtering by version_id.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Simulate"
                ],
                "summary": "Get Simulations List",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number for pagination",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of simulations per page",
                        "name": "per_page",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter simulations by version ID",
                        "name": "version_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of simulations",
                        "schema": {
                            "$ref": "#/definitions/dtos.PaginatedData"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid query parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Updates an existing simulation or creates a new one based on the provided payload.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Simulate"
                ],
                "summary": "Update or Create Simulate Data",
                "parameters": [
                    {
                        "description": "Simulate data to be updated or created",
                        "name": "simulate",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.Simulate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "ID of the updated or created simulation",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Executes the simulation based on the provided payload data.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Simulate"
                ],
                "summary": "Simulate Data",
                "parameters": [
                    {
                        "description": "Data to simulate",
                        "name": "simulate",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.Simulate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Simulation result data",
                        "schema": {
                            "$ref": "#/definitions/ResponseSimulateForSwagger"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/simulate/by-id": {
            "post": {
                "description": "Simulate by id",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Simulate"
                ],
                "summary": "Simulate by id",
                "responses": {
                    "200": {
                        "description": "sim by id request",
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForSimByID"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/simulate/default-request/add": {
            "post": {
                "description": "This endpoint is used to add a default request for simulation.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Default Request"
                ],
                "summary": "Add a Default Request",
                "parameters": [
                    {
                        "description": "Default Request Payload",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForDefaultRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "success",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid simulate ID",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/simulate/default-request/get": {
            "post": {
                "description": "Get Default Request",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Default Request"
                ],
                "summary": "Get Default Request",
                "parameters": [
                    {
                        "description": "Default Request Criteria",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForGetDefaultRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successfully retrieved the default request data",
                        "schema": {
                            "$ref": "#/definitions/dtos.DefaultRequestForSwagger"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/simulate/default-request/{id}": {
            "delete": {
                "description": "Delete Default Request by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Default Request"
                ],
                "summary": "Delete Default Request",
                "parameters": [
                    {
                        "type": "string",
                        "description": "ID of the default request to be deleted",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "successfully deleted",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid request ID",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/simulate/detail/{simulate_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Simulate Detail by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Simulate"
                ],
                "summary": "Simulate Detail by ID",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Simulate ID",
                        "name": "simulate_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Simulation detail data",
                        "schema": {
                            "$ref": "#/definitions/SimulateResponseForSwagger"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid simulate ID",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/simulate/name/{id}": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update Simulate Nme",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Simulate"
                ],
                "summary": "Update Simulate Nme",
                "parameters": [
                    {
                        "description": "Simulate data to be updated or created",
                        "name": "simulate",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.Simulate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/simulate/rules": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get Simulation Rules List with pagination.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Simulate"
                ],
                "summary": "Get Simulation Rules List",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number for pagination",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Number of simulation rules per page",
                        "name": "per_page",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of simulation rules",
                        "schema": {
                            "$ref": "#/definitions/dtos.PaginatedData"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid query parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/simulate/{simulate_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Simulate Rule by Simulate ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Simulate"
                ],
                "summary": "Simulate Rule",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Simulate ID",
                        "name": "simulate_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Simulation result data",
                        "schema": {
                            "$ref": "#/definitions/SimulateResponseForSwagger"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid simulate ID",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete Simulate Data by Simulate ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Simulate"
                ],
                "summary": "Delete Simulate Data",
                "parameters": [
                    {
                        "type": "string",
                        "description": "The ID of the simulate data to delete",
                        "name": "simulate_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Success: Simulate data deleted",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "400": {
                        "description": "Bad request - invalid simulate ID",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get All Users",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get All Users",
                "responses": {
                    "200": {
                        "description": "Users fetched successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "description": "Update User",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Update User",
                "parameters": [
                    {
                        "description": "User Payload For Update",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForUpdateUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "ok\" \"User added successfully",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user-new/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get User Detail by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get User Detail by ID",
                "responses": {
                    "200": {
                        "description": "Organization details fetched successfully",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user/": {
            "get": {
                "description": "This endpoint retrieves the details of the currently authenticated user.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Get User Information",
                "responses": {
                    "200": {
                        "description": "User details retrieved successfully",
                        "schema": {
                            "$ref": "#/definitions/dtos.User"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user/add": {
            "post": {
                "description": "Create User",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Create User",
                "parameters": [
                    {
                        "description": "User Payload",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForCreateUser"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "ok\" \"User added successfully",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user/change-admin-info": {
            "post": {
                "description": "Change Admin Information",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Change Admin Information",
                "parameters": [
                    {
                        "description": "Admin Info Change Payload",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForChangeAdminInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "ok\" \"Admin information successfully updated",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user/forgot-password": {
            "post": {
                "description": "Forgot Password",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Forgot Password",
                "parameters": [
                    {
                        "description": "Payload For Forgot Password",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForForgotPassword"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "ok\" \"Password reset code sent successfully",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user/reset-password": {
            "post": {
                "description": "Reset Password",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Reset Password",
                "parameters": [
                    {
                        "description": "Payload For Reset Password",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dtos.RequestForResetPassword"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "ok\" \"Password reset successfully",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad request - Invalid input data",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user/{id}": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete user by ID",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "summary": "Delete User",
                "responses": {
                    "200": {
                        "description": "User successfully added",
                        "schema": {
                            "$ref": "#/definitions/SuccessResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string",
                    "example": "Action failed"
                }
            }
        },
        "LogDTO": {
            "type": "object",
            "properties": {
                "admin": {
                    "type": "string",
                    "example": "admin"
                },
                "created_at": {
                    "type": "string",
                    "example": "2020-01-01 00:00:00"
                },
                "entity": {
                    "type": "string",
                    "example": "order"
                },
                "entity_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "ip": {
                    "type": "string",
                    "example": "127.0.0.1"
                },
                "message": {
                    "type": "string",
                    "example": "Log Message"
                },
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "proto": {
                    "description": "http, gRPC",
                    "type": "string",
                    "example": "http"
                },
                "title": {
                    "type": "string",
                    "example": "Log Title"
                },
                "type": {
                    "description": "info,  error",
                    "type": "string",
                    "example": "info"
                },
                "user": {
                    "type": "string",
                    "example": "user"
                }
            }
        },
        "ResponseSimulateForSwagger": {
            "type": "object",
            "properties": {
                "performance": {
                    "type": "string",
                    "example": "0.1s"
                },
                "result": {
                    "type": "string"
                },
                "trace": {
                    "type": "string"
                }
            }
        },
        "SimulateResponseForSwagger": {
            "type": "object",
            "properties": {
                "access_token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
                },
                "context": {
                    "$ref": "#/definitions/entities.Attrs"
                },
                "created_at": {
                    "type": "string",
                    "example": "2021-01-01T00:00:00Z"
                },
                "edges": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dtos.Edge"
                    }
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "name": {
                    "type": "string",
                    "example": "my_simulation"
                },
                "nodes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/dtos.Node"
                    }
                },
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "simulate_output": {
                    "$ref": "#/definitions/dtos.SimulateOutputForSwaggger"
                },
                "version_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "version_name": {
                    "type": "string",
                    "example": "v1.0.0"
                }
            }
        },
        "SuccessResponse": {
            "type": "object",
            "properties": {
                "message": {
                    "type": "string",
                    "example": "Action completed successfully"
                }
            }
        },
        "dtos.AddUserToOrganization": {
            "type": "object",
            "required": [
                "organization_id",
                "user_id"
            ],
            "properties": {
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "user_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                }
            }
        },
        "dtos.CreateOrganization": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "name": {
                    "type": "string",
                    "example": "mono payments"
                },
                "parent_organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                }
            }
        },
        "dtos.DefaultRequestForSwagger": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "simulate_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "type": {
                    "type": "integer",
                    "example": 1
                },
                "value": {
                    "type": "string",
                    "example": "{\"key\":\"value\"}"
                }
            }
        },
        "dtos.Edge": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "sourceHandle": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "sourceId": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "targetId": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "type": {
                    "type": "string",
                    "example": "edge"
                }
            }
        },
        "dtos.FavoriteNode": {
            "type": "object",
            "properties": {
                "label": {
                    "type": "string",
                    "example": "Node Label"
                },
                "name": {
                    "type": "string",
                    "example": "Node Name"
                },
                "type": {
                    "type": "string",
                    "example": "Node Type"
                }
            }
        },
        "dtos.Login": {
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "password": {
                    "type": "string",
                    "example": "123456"
                }
            }
        },
        "dtos.Node": {
            "type": "object",
            "properties": {
                "content": {},
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "label": {
                    "type": "string",
                    "example": "request"
                },
                "name": {
                    "type": "string",
                    "example": "my_request"
                },
                "position": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "integer"
                    }
                },
                "type": {
                    "type": "string",
                    "example": "inputNode"
                }
            }
        },
        "dtos.PaginatedData": {
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer",
                    "example": 1
                },
                "per_page": {
                    "type": "integer",
                    "example": 10
                },
                "rows": {
                    "type": "array",
                    "items": {
                        "type": "object"
                    }
                },
                "total": {
                    "type": "integer",
                    "example": 100
                },
                "total_pages": {
                    "type": "integer",
                    "example": 10
                }
            }
        },
        "dtos.Register": {
            "type": "object",
            "required": [
                "email",
                "name",
                "organization_id",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "name": {
                    "type": "string",
                    "example": "mono payments"
                },
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "password": {
                    "type": "string",
                    "example": "123456"
                }
            }
        },
        "dtos.RequestForAddDSN": {
            "type": "object",
            "properties": {
                "db_engine": {
                    "description": "1: mysql, 2: postgres ...",
                    "type": "integer",
                    "example": 2
                },
                "dsn": {
                    "type": "string",
                    "example": "postgres://user:password@localhost:5432/dbname"
                },
                "key": {
                    "type": "string",
                    "example": "my-psql-dsn"
                }
            }
        },
        "dtos.RequestForAddRole": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "example": "admin"
                },
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                }
            }
        },
        "dtos.RequestForChangeAdminInfo": {
            "type": "object",
            "properties": {
                "emailForAlert": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "smsForAlert": {
                    "type": "string",
                    "example": "905555555555"
                }
            }
        },
        "dtos.RequestForCreateUser": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "name": {
                    "type": "string",
                    "example": "mono"
                },
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "password": {
                    "type": "string"
                },
                "password_confirm": {
                    "type": "string"
                },
                "role_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                }
            }
        },
        "dtos.RequestForDefaultRequest": {
            "type": "object",
            "properties": {
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "simulate_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "type": {
                    "type": "integer",
                    "example": 1
                },
                "value": {
                    "type": "string",
                    "example": "{\"key\":\"value\"}"
                }
            }
        },
        "dtos.RequestForForgotPassword": {
            "type": "object",
            "required": [
                "email"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                }
            }
        },
        "dtos.RequestForGenerateAppToken": {
            "type": "object",
            "properties": {
                "expire_at": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "dtos.RequestForGetDefaultRequest": {
            "type": "object",
            "properties": {
                "simulate_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "type": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "dtos.RequestForResetPassword": {
            "type": "object",
            "required": [
                "code",
                "password",
                "password_confirm"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "123456"
                },
                "password": {
                    "type": "string"
                },
                "password_confirm": {
                    "type": "string"
                }
            }
        },
        "dtos.RequestForSimByID": {
            "type": "object",
            "properties": {
                "context": {
                    "type": "object",
                    "additionalProperties": {}
                },
                "domain": {
                    "type": "string"
                },
                "token": {
                    "type": "string"
                }
            }
        },
        "dtos.RequestForUpdateOrganization": {
            "type": "object",
            "required": [
                "id",
                "name"
            ],
            "properties": {
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "name": {
                    "type": "string",
                    "example": "mono payments edit"
                }
            }
        },
        "dtos.RequestForUpdateUser": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "name": {
                    "type": "string",
                    "example": "mono"
                }
            }
        },
        "dtos.ResponseForDSN": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2021-01-01T00:00:00Z"
                },
                "db_engine": {
                    "type": "integer",
                    "example": 2
                },
                "dsn": {
                    "type": "string",
                    "example": "postgres://user:password@localhost:5432/dbname"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "key": {
                    "type": "string",
                    "example": "my-psql-dsn"
                },
                "last_dsn_check_at": {
                    "type": "string",
                    "example": "2021-01-01T00:00:00Z"
                },
                "last_dsn_check_status": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "dtos.ResponseForDashboard": {
            "type": "object",
            "properties": {
                "last_favorite_node": {
                    "$ref": "#/definitions/dtos.FavoriteNode"
                },
                "last_simulate": {
                    "$ref": "#/definitions/dtos._Simulate"
                },
                "total_evaluation_count": {
                    "type": "integer",
                    "example": 12
                },
                "total_favorite_nodes_count": {
                    "type": "integer",
                    "example": 5
                },
                "total_simulate_count": {
                    "type": "integer",
                    "example": 8
                }
            }
        },
        "dtos.ResponseForScheduledWork": {
            "type": "object",
            "properties": {
                "admin_id": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "each_x_time": {
                    "type": "string"
                },
                "end_date": {
                    "type": "string"
                },
                "how_many_time_to_try": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "name_of_work": {
                    "type": "string"
                },
                "organization_id": {
                    "type": "string"
                },
                "request": {
                    "type": "string"
                },
                "simulate_id": {
                    "type": "string"
                },
                "start_date": {
                    "type": "string"
                },
                "total_error": {
                    "type": "integer"
                },
                "total_success": {
                    "type": "integer"
                },
                "total_work": {
                    "type": "integer"
                }
            }
        },
        "dtos.Simulate": {
            "type": "object"
        },
        "dtos.SimulateLogForSwagger": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "message": {
                    "type": "string",
                    "example": "error message"
                },
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "proto": {
                    "type": "string",
                    "example": "grpc"
                },
                "request": {
                    "type": "string",
                    "example": "{\"key\":\"value\"}"
                },
                "response": {
                    "type": "string",
                    "example": "{\"key\":\"value\"}"
                },
                "simulate_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "type": {
                    "type": "string",
                    "example": "error"
                }
            }
        },
        "dtos.SimulateOutputForSwaggger": {
            "type": "object",
            "properties": {
                "ID": {
                    "type": "string"
                },
                "performance": {
                    "type": "string"
                },
                "result": {
                    "$ref": "#/definitions/entities.Attrs"
                },
                "trace": {
                    "$ref": "#/definitions/entities.Attrs"
                }
            }
        },
        "dtos.User": {
            "type": "object",
            "properties": {
                "allow_organization_change": {
                    "type": "boolean",
                    "example": true
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "emailForAlert": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "name": {
                    "type": "string",
                    "example": "mono payments"
                },
                "organization_id": {
                    "type": "string",
                    "example": "f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"
                },
                "organization_name": {
                    "type": "string",
                    "example": "mono payments"
                },
                "smsForAlert": {
                    "type": "string",
                    "example": "905555555555"
                }
            }
        },
        "dtos._Simulate": {
            "type": "object",
            "properties": {
                "ID": {
                    "type": "string",
                    "example": "123e4567-e89b-12d3-a456-426614174000"
                },
                "created_at": {
                    "type": "string",
                    "example": "2024-11-26T15:04:05Z"
                },
                "name": {
                    "type": "string",
                    "example": "Simulation Name"
                }
            }
        },
        "entities.Attrs": {
            "type": "object",
            "additionalProperties": true
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:4000",
	BasePath:         "/api/v1",
	Schemes:          []string{"http"},
	Title:            "Mono GRC",
	Description:      "Mono GRC .",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
