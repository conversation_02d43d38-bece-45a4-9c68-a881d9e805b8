version: "3"
services:
  pars-vue-db:
    image: "postgres:14.6"
    container_name: pars-vue-db
    volumes:
      - vue_flow_ex_data:/var/lib/postgresql/data
    networks:
      - mono
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=pars-vue
      - POSTGRES_PASSWORD=pars-vue
      - POSTGRES_DB=pars-vue

  pars-vue-redis:
    image: "redis:latest"
    container_name: pars-vue-redis
    networks:
      - mono

  pars-vue-nats:
    image: nats:latest
    container_name: pars-vue-nats
    networks:
      - mono
  
  pars-vue:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: pars-vue
    container_name: pars-vue
    restart: always
    environment:
      - MIGRATE=true
      - SEED=true
    networks:
      - mono
    volumes:
      - ./:/app
      - ./config-hot.yaml:/app/config.yaml
    ports:
      - 4000:4000
      - 50055:50055
    depends_on:
      - pars-vue-db

  pars-vue-web:
    container_name: pars-vue-web
    build:
      context: ../frontend
      dockerfile: Dockerfile.dev
    restart: always
    volumes:
      - ../frontend/src:/app/src
      - /app/node_modules
    ports:
      - "7000:7000"
    networks:
      - mono
    depends_on:
      - pars-vue-db

networks:
  mono:
    name: mono_network
    driver: bridge

volumes:
  vue_flow_ex_data:
    external: false