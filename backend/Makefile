prepare:
	yarn --cwd ../frontend/ install && yarn --cwd ../frontend/ build && rsync -a ../frontend/dist/ pkg/embed/dist

build:
	go build -o pars-vue main.go

run:
	./pars-vue

db:
	docker run -it -d --restart always --name grcdb -e POSTGRES_PASSWORD=pars-vue -e POSTGRES_DB=pars-vue -e POSTGRES_USER=pars-vue -p 5432:5432 postgres:14.6

nats:
	docker run -it -d -ti --restart always --name nats -p 8222:8222 -p 4222:4222 nats:latest

redis:
	docker run -it -d --name redis --restart always -p 6379:6379 redis

jaeger:
	docker run -it -d --restart always --name jaeger -p 16686:16686 -p 14268:14268  jaegertracing/all-in-one

swagger:
	swag init

proto:
	protoc --go_out=. --go-grpc_out=.  protos/*.proto

docker_build:
	docker build -t registry.mono.cash/monopayments/vue-flow-ex .

hot:
	docker compose -p pars-vue -f docker-compose-hot.yml up
	
compose:
	docker compose -p pars-vue -f docker-compose.yml up

dev:
	MIGRATE=true SEED=true air --build.cmd "go build -o /tmp/pars-vue ./main.go" --build.bin "/tmp/pars-vue"