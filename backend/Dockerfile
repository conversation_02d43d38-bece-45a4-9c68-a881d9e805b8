FROM golang:1.24 as builder

RUN apk add --no-cache upx 

WORKDIR /src/pars-vue

COPY . .

RUN go get .

RUN CGO_ENABLED=0 GOOS=linux go build -a -ldflags="-s -w" -installsuffix cgo -o pars-vue main.go
RUN upx pars-vue

FROM scratch

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

WORKDIR /bin/pars-vue

COPY --from=builder /src/pars-vue/config.example.yaml /bin/pars-vue/config.yaml
COPY --from=builder /src/pars-vue/pars-vue .

CMD [ "./pars-vue" ]
