package query

var Query = map[string]string{
	"id":                           "id = ?",
	"id_in":                        "id IN (?)",
	"provider":                     "provider = ?",
	"descriptor":                   "descriptor = ?",
	"hash_id":                      "hash_id = ?",
	"organization_id":              "organization_id = ?",
	"reference_id":                 "reference_id = ?",
	"is_main":                      "is_main = ?",
	"is_user_object":               "is_user_object = ?",
	"is_user_object_true":          "is_user_object = true",
	"is_user_object_false":         "is_user_object = false",
	"status_in":                    "status IN (?)",
	"status":                       "status = ?",
	"labels_in_array":              "? <@ labels",
	"user_id":                      "user_id = ?",
	"name":                         "name = ?",
	"email":                        "email = ?",
	"order_id":                     "order_id = ?",
	"term_id":                      "term_id = ?",
	"vpos_id":                      "vpos_id = ?",
	"installment_vpos_id":          "installment_vpos_id = ?",
	"three_d_vpos_id":              "three_d_vpos_id = ?",
	"three_d_installment_vpos_id":  "three_d_installment_vpos_id = ?",
	"wallet_id":                    "wallet_id = ?",
	"admin_id":                     "admin_id = ?",
	"department_id":                "department_id = ?",
	"role_id":                      "role_id = ?",
	"bin_group_id":                 "bin_group_id = ?",
	"rule_id":                      "rule_id = ?",
	"parent_id":                    "parent_id = ?",
	"entity_id":                    "entity_id = ?",
	"entity_id_in":                 "entity_id IN (?)",
	"entity":                       "entity = ?",
	"entity_type":                  "entity_type = ?",
	"type":                         "type = ?",
	"is_api_user":                  "is_api_user = ?",
	"is_api_user_true":             "is_api_user = true",
	"is_api_user_false":            "is_api_user = false",
	"order_reference_id":           "order_reference_id = ?",
	"sub_merchant_id":              "sub_merchant_id = ?",
	"sub_merchant_key":             "sub_merchant_key = ?",
	"sub_merchant_external_id":     "sub_merchant_external_id = ?",
	"archived":                     "archived = ?",
	"archived_true":                "archived = true",
	"archived_false":               "archived = false",
	"invoice_number":               "invoice_number = ?",
	"acquirer_commission_group_id": "acquirer_commission_group_id = ?",
	"identity_number":              "identity_number = ?",
	"response_not_null":            "response IS NOT NULL",
	"created_at_between":           "created_at BETWEEN ? AND ?",
	"wallet_payable":               "payable = ?",
	"account_id":                   "account_id  = ?",
	"masked_number":                "masked_number  = ?",
	"slug":                         "slug = ?",
	"iban":                         "iban = ?",
	"device_id":                    "device_id = ?",
	"ip":                           "ip = ?",
	"consent_reference_id":         "consent_reference_id = ?",
	"activation_code":              "activation_code = ?",
	"pin_code":                     "pin_code = ?",
	"reset_password_code":          "reset_password_code = ?",
	"conversation_id":              "conversation_id = ?",
	"currency_id":                  "currency_id = ?",
	"is_organization_bank_account": "is_organization_bank_account = ?",
	"external_reference_id":        "external_reference_id = ?",
	"sender_wallet_id":             "sender_wallet_id = ?",
	"receiver_wallet_id":           "receiver_wallet_id = ?",
	"borderless_id":                "borderless_id = ?",
	"organization_id_in":           "organization_id IN (?)",
	"domain_address":               "domain_address = ?",
	"checkout_domain":              "checkout_domain = ?",
	"action":                       "action = ?",
	"maker":                        "maked_by = ?",
	"checker":                      "checked_by = ?",
	"code":                         "code = ?",
	"disabled":                     "disabled = ?",
	"phone":                        "phone = ?",
	"dispute_id":                   "dispute_id = ?",
	"is_default":                   "is_default = ?",
	"invoice_status":               "invoice_status = ?",
	"name_like":                    "name LIKE ?",
	"name_ilike":                   "name ILIKE ?",
	"is_active":                    "is_active = ?",
	"credit_code":                  "credit_code = ?",
	"tokenization_service_id":      "tokenization_service_id = ?",
	"tenant_id":                    "tenant_id = ?",
	"main":                         "main = ?",
	"related_reference_id":         "related_reference_id = ?",
	"payment_term_reference_id":    "payment_term_reference_id = ?",
	"currency_code":                "currency_code = ?",
	"created_at":                   "created_at = ?",
	"created_at_big_or_equal":      "created_at >= ?",
	"created_at_less_or_equal":     "created_at <= ?",
	"tx_date_big_or_equal":         "transaction_date >= ?",
	"tx_date_less_or_equal":        "transaction_date <= ?",
	"owner_id":                     "owner_id = ?",
	"currency":                     "currency = ?",
	"paid_date_between":            "paid_date BETWEEN ? AND ?",
	"updated_at_between":           "updated_at BETWEEN ? AND ?",
	"id_or_parent_id":              "id = ? OR parent_id = ?",
	"action_type":                  "action_type = ?",
	"main_trn_id":                  "main_trn_id = ?",
	"event_worker_id":              "event_worker_id = ?",
	"bin":                          "bin = ?",
	"receiver_wallet_id_not_null":  "receiver_wallet_id IS NOT NULL",
	"sender_wallet_id_not_null":    "sender_wallet_id IS NOT NULL",
	"is_rule_specific":             "is_rule_specific = ?",
	"screen_pin":                   "screen_pin = ?",
	"payment_type":                 "payment_type = ?",
	"is_regex":                     "is_regex = ?",
	"is_allowed":                   "is_allowed = ?",
	"target_field":                 "target_field = ?",
	"target_field_value":           "target_field_value = ?",
	"waas_provider":                "waas_provider = ?",
	"waas_provider_id":             "waas_provider_id = ?",
	"operation_report_id":          "operation_report_id = ?",
	"data_or":                      "data = ? OR data = ?",
	"title":                        "title = ?",
	"client":                       "client = ?",
	"proto":                        "proto = ?",

	"order_buyer_firstname":  "buyer->>'name' ILIKE ?",
	"order_buyer_lastname":   "buyer->>'surname' ILIKE ?",
	"order_buyer_email":      "buyer->>'email' = ?",
	"order_buyer_phone":      "buyer->>'gsm_number' = ?",
	"amount_decimal":         "amount_decimal = ?",
	"amount_decimal_between": "amount_decimal BETWEEN ? AND ?",
	"first_name":             "first_name = ?",
	"last_name":              "last_name = ?",
	"provider_type":          "provider_type = ?",

	"waas_provider_external_id": "waas_provider_external_id = ?",

	"withdrawable":              "withdrawable = ?",
	"transferable":              "transferable = ?",
	"payable":                   "payable = ?",
	"depositable":               "depositable = ?",
	"wallet_to_wallet_transfer": "wallet_scopes_wallet_to_wallet_transfer = ?",
	"person_to_person_transfer": "wallet_scopes_person_to_person_transfer = ?",
	"balance_between":           "balance BETWEEN ? AND ?",
	"type_or_triple":            "(type = ? OR type = ? OR type = ?)",
	"order_payment_id":          "order_payment_id = ?",

	"avoid_auth_install":    "avoid_auth_install = ?",
	"avoid_preauth_install": "avoid_preauth_install = ?",
	"three_d":               "three_d = ?",

	"id_or_descriptor": "id = ? OR descriptor = ?",

	"order_reference_id_in": "order_reference_id IN (?)",
	"bin_in":                "bin IN (?)",
	"enabled":               "enabled = ?",
	"transaction_type":      "transaction_type = ?",
	"language":              "language = ?",
	"paid":                  "paid = ?",
	"item_id":               "item_id = ?",
}

const (
	WhereID                        string = "id"
	WhereIDIN                      string = "id_in"
	WhereCurrencyID                string = "currency_id"
	WhereHashID                    string = "hash_id"
	WhereDescriptor                string = "descriptor"
	WhereProvider                  string = "provider"
	WhereOrganizationID            string = "organization_id"
	WhereOrganizationIDIN          string = "organization_id_in"
	WhereDomainAddress             string = "domain_address"
	WhereCheckoutDomain            string = "checkout_domain"
	WhereCreatedAtBetween          string = "created_at_between"
	WherePaidDateBetween           string = "paid_date_between"
	WhereUpdatedAtBetween          string = "updated_at_between"
	WhereCreatedAt                 string = "created_at"
	WhereCreatedAtBigOrEqual       string = "created_at_big_or_equal"
	WhereCreatedAtLessOrEqual      string = "created_at_less_or_equal"
	WhereTxDateBigOrEqual          string = "tx_date_big_or_equal"
	WhereTxDateLessOrEqual         string = "tx_date_less_or_equal"
	WhereUserID                    string = "user_id"
	WhereAdminID                   string = "admin_id"
	WhereOrderID                   string = "order_id"
	WhereTermID                    string = "term_id"
	WhereWalletID                  string = "wallet_id"
	WhereIsMain                    string = "is_main"
	WhereIsUserObject              string = "is_user_object"
	WhereIsUserObjectTrue          string = "is_user_object_true"
	WhereIsUserObjectFalse         string = "is_user_object_false"
	WhereIsApiUser                 string = "is_api_user"
	WhereIsApiUserTrue             string = "is_api_user_true"
	WhereIsApiUserFalse            string = "is_api_user_false"
	WhereStatus                    string = "status"
	WhereVposID                    string = "vpos_id"
	WhereInstallmentVposID         string = "installment_vpos_id"
	WhereThreeDVposID              string = "three_d_vpos_id"
	WhereThreeDInstallmentVposID   string = "three_d_installment_vpos_id"
	WhereStatusIn                  string = "status_in"
	WhereName                      string = "name"
	WhereEmail                     string = "email"
	WhereFirstName                 string = "first_name"
	WhereLastName                  string = "last_name"
	WhereReferenceID               string = "reference_id"
	WhereExternalReferenceID       string = "external_reference_id"
	WhereSenderWalletID            string = "sender_wallet_id"
	WhereReceiverWalletID          string = "receiver_wallet_id"
	WhereEntityID                  string = "entity_id"
	WhereEntityIDIN                string = "entity_id_in"
	WhereEntity                    string = "entity"
	WhereEntityType                string = "entity_type"
	WhereType                      string = "type"
	WhereDepartmentID              string = "department_id"
	WhereRoleID                    string = "role_id"
	WhereBinGroupID                string = "bin_group_id"
	WhereBIN                       string = "bin"
	WhereBININ                     string = "bin_in"
	WhereRuleID                    string = "rule_id"
	WhereParentID                  string = "parent_id"
	WhereOrderReferenceID          string = "order_reference_id"
	WhereInvoiceNumber             string = "invoice_number"
	WhereSubMerchantID             string = "sub_merchant_id"
	WhereSubMerchantKey            string = "sub_merchant_key"
	WhereSubMerchantExternalID     string = "sub_merchant_external_id"
	WhereAcquirerCommissionGroupID string = "acquirer_commission_group_id"
	WhereIdentityNumber            string = "identity_number"
	WhereArchived                  string = "archived"
	WhereArchivedTrue              string = "archived_true"
	WhereArchivedFalse             string = "archived_false"
	WhereLabelsInArray             string = "labels_in_array"
	OrderByCreatedAtDesc           string = "created_at DESC"
	OrderByNameAsc                 string = "name ASC"
	OrderByNameDesc                string = "name DESC"
	OrderBySortAsc                 string = "sort ASC"
	WhereResponseISNOTNULL         string = "response_not_null"
	AccountId                      string = "account_id"
	WhereWalletPayable             string = "wallet_payable"
	WhereMaskedNumber              string = "masked_number"
	WhereSlug                      string = "slug"
	WhereIBAN                      string = "iban"
	WhereDeviceId                  string = "device_id"
	WhereIp                        string = "ip"
	WhereConsentReferenceID        string = "consent_reference_id"
	WhereActivationCode            string = "activation_code"
	WherePinCode                   string = "pin_code"
	WhereResetPasswordCode         string = "reset_password_code"
	WhereConversationID            string = "conversation_id"
	WhereIsOrganizationBankAccount string = "is_organization_bank_account"
	BorderlessID                   string = "borderless_id"
	WhereAction                    string = "action"
	WhereActionType                string = "action_type"
	WhereMaker                     string = "maker"
	WhereChecker                   string = "checker"
	WhereCode                      string = "code"
	WhereDisabled                  string = "disabled"
	WherePhone                     string = "phone"
	WhereDisputeID                 string = "dispute_id"
	WhereIsDefault                 string = "is_default"
	WhereInvoiceStatus             string = "invoice_status"
	WhereNameLike                  string = "name_like"
	WhereNameILike                 string = "name_ilike"
	WhereIsActive                  string = "is_active"
	WhereCreditCode                string = "credit_code"
	WhereTokenizationServiceID     string = "tokenization_service_id"
	WhereTenantID                  string = "tenant_id"
	WhereMain                      string = "main"
	WhereRelatedReferenceID        string = "related_reference_id"
	WherePaymentTermReferenceID    string = "payment_term_reference_id"
	WhereCurrencyCode              string = "currency_code"
	WhereOwnerID                   string = "owner_id"
	WhereCurrency                  string = "currency"
	WhereIDOrParentID              string = "id_or_parent_id"
	WhereMainTrnID                 string = "main_trn_id"
	WhereEventWorkerID             string = "event_worker_id"
	WhereReceiverWalletIDIsNotNull string = "receiver_wallet_id_not_null"
	WhereSenderWalletIDIsNotNull   string = "sender_wallet_id_not_null"
	WhereIsRuleSpecific            string = "is_rule_specific"
	WhereScreenPin                 string = "screen_pin"
	WherePaymentType               string = "payment_type"
	WhereIsRegex                   string = "is_regex"
	WhereIsAllowed                 string = "is_allowed"
	WhereTargetField               string = "target_field"
	WhereTargetFieldValue          string = "target_field_value"
	WhereWaasProvider              string = "waas_provider"
	WhereWaasProviderID            string = "waas_provider_id"
	WhereWalletType                string = "waas_provider"
	WhereOperationReportID         string = "operation_report_id"
	WhereDATAOr                    string = "data_or"
	WhereClient                    string = "client"
	WhereProto                     string = "proto"

	WhereTitle                string = "title"
	WhereOrderBuyerName       string = "order_buyer_firstname"
	WhereOrderBuyerSurname    string = "order_buyer_lastname"
	WhereOrderBuyerEmail      string = "order_buyer_email"
	WhereOrderBuyerPhone      string = "order_buyer_phone"
	WhereAmountDecimal        string = "amount_decimal"
	WhereAmountDecimalBetween string = "amount_decimal_between"
	WhereProviderType         string = "provider_type"

	WhereWaasProviderExternalID string = "waas_provider_external_id"

	WhereWithdrawable           string = "withdrawable"
	WhereTransferable           string = "transferable"
	WherePayable                string = "payable"
	WhereDepositable            string = "depositable"
	WhereWalletToWalletTransfer string = "wallet_to_wallet_transfer"
	WherePersonToPersonTransfer string = "person_to_person_transfer"
	WhereBalanceBetween         string = "balance_between"
	WhereTypeORTriple           string = "type_or_triple"

	WhereOrderPaymentID string = "order_payment_id"

	WhereAvoidAuthInstall    string = "avoid_auth_install"
	WhereAvoidPreauthInstall string = "avoid_preauth_install"
	WhereThreeD              string = "three_d"

	WhereIDOrDescriptor string = "id_or_descriptor"

	WhereOrderReferenceIDIN string = "order_reference_id_in"

	WhereEnabled         string = "enabled"
	WhereTransactionType string = "transaction_type"
	WhereLanguage        string = "language"

	WherePaid   string = "paid"
	WhereItemID string = "item_id"
)

func BuildQuery(keys ...string) string {
	if len(keys) == 0 {
		return ""
	}
	if len(keys) == 1 {
		return Query[keys[0]]
	}

	var query string
	for _, key := range keys {
		query += Query[key] + " AND "
	}

	return query[:len(query)-5] // remove last " AND "
}

type QueryBuilder struct {
	Keys          []QueryKey `json:"keys"`
	JoinTableName string     `json:"join_table_name"`
}

type QueryKey struct {
	Key    string
	Values []interface{}
	Skip   bool
}

//	condition := query.QueryBuilder{
//		Keys: []query.QueryKey{
//			{
//				Key:    query.WhereUserID,
//				Values: []any{state.CurrentUser(ctx)},
//			},
//			{
//				Key:    query.WhereIsUserObject,
//				Values: []any{true},
//			},
//			{
//				Key: query.WhereCreatedAtBetween,
//				Values: []any{
//					req.GetStartDate(),
//					req.GetEndDate(),
//				},
//			},
//			{
//				Key:    query.WhereWalletPayable,
//				Values: []any{req.GetPayable()},
//			},
//		},
//	}
//
// sql, data := condition.GetQueriesWithValues()
func (qb *QueryBuilder) GetQueriesWithValues() (string, []interface{}) {
	// check keys with index in values
	// if key value is empty, remove from query
	// if key value is not empty, add to query
	if len(qb.Keys) == 0 {
		return "", nil
	}

	if qb.JoinTableName != "" {
		qb.JoinTableName += "."
	} else {
		qb.JoinTableName = ""
	}

	var query string
	var values []interface{}
	for _, key := range qb.Keys {
		if key.Skip {
			continue
		}
		if len(key.Values) > 0 && key.Values[0] != "" {
			switch key.Key {
			case WhereAmountDecimalBetween:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values[0], key.Values[1])
			case WhereCreatedAtBetween:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values[0], key.Values[1])
			case WhereUpdatedAtBetween:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values[0], key.Values[1])
			case WherePaidDateBetween:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values[0], key.Values[1])
			case WhereStatusIn:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values...)
			case WhereOrderReferenceIDIN:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values...)
			case WhereOrganizationIDIN:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values...)
			case WhereIDOrParentID:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values[0], key.Values[1])
			case WhereBalanceBetween:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values[0], key.Values[1])
			case WhereTypeORTriple:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values[0], key.Values[1], key.Values[2])
			case WhereBININ:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values...)
			default:
				query += qb.JoinTableName + Query[key.Key] + " AND "
				values = append(values, key.Values[0])
			}
		}
	}

	if len(query) == 0 {
		return "", nil
	}

	return query[:len(query)-5], values // remove last " AND "
}
