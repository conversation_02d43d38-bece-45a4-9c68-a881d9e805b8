package constants

type Integrity struct {
	Name        string            `json:"name"`
	Code        string            `json:"code"`
	Description string            `json:"description"`
	Methods     []IntegrityMethod `json:"methods"`
}

type IntegrityMethod struct {
	Name string `json:"name"`
	Code string `json:"code"`
}

var IntegrityList = []Integrity{
	{
		Name:        "Submerchant",
		Code:        "submerchant",
		Description: "Submerchant integrity for submerchant organizations and users",
		Methods: []IntegrityMethod{
			{
				Name: "Check",
				Code: "check",
			},
			{
				Name: "Make",
				Code: "make",
			},
		},
	},
}
