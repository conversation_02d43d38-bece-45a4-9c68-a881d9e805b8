package syslog

import (
	"log"
	"log/syslog"
	"net"

	"github.com/parsguru/pars-vue/pkg/config"
)

const (
	LOG_INFO = 6
	LOG_ERR  = 3
)

var sysLogWriter *syslog.Writer

func InitSyslogClient() {

	protocol := config.ReadValue().Syslog.Protocol
	if protocol != "udp" && protocol != "tcp" {
		log.Fatalf("Invalid syslog protocol: %s. Supported protocols are 'udp' and 'tcp'.", protocol)
	}

	writer, err := syslog.Dial(protocol, net.JoinHostPort(config.ReadValue().Syslog.Host, config.ReadValue().Syslog.Port), syslog.LOG_INFO, config.ReadValue().AppName)
	if err != nil {
		log.Fatalf("Failed to connect to syslog server: %v", err)
	}

	sysLogWriter = writer

	writer.Info("Syslog client initialized")
}

func GetSyslogWriter() *syslog.Writer {
	return sysLogWriter
}
