package config

import (
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

type Config struct {
	AppName       string   `yaml:"app_name"`
	Host          string   `yaml:"host"`
	Port          string   `yaml:"port"`
	GrpcPort      string   `yaml:"grpc_port"`
	Env           string   `yaml:"env"`
	BaseUrl       string   `yaml:"base_url"`
	FrontBaseUrl  string   `yaml:"front_base_url"`
	JwtSecret     string   `yaml:"jwt_secret"`
	JwtExpire     int      `yaml:"jwt_expire"`
	JwtIssuer     string   `yaml:"jwt_issuer"`
	TotpIssuer    string   `yaml:"totp_issuer"`
	AdminEmail    string   `yaml:"admin_email"`
	AdminName     string   `yaml:"admin_name"`
	AdminPassword string   `yaml:"admin_password"`
	LicenseKey    string   `yaml:"license_key"`
	HashSalt      string   `yaml:"hash_salt"`
	AllowMethods  []string `yaml:"allow_methods"`
	AllowHeaders  []string `yaml:"allow_headers"`
	Jaeger        struct {
		ServiceName string `yaml:"service_name"`
		Url         string `yaml:"url"`
	}
	AllowOrigins []string `yaml:"allow_origins"`
	Smtp         struct {
		Host     string `yaml:"host"`
		Port     int    `yaml:"port"`
		From     string `yaml:"from"`
		Sender   string `yaml:"sender"`
		Reply    string `yaml:"reply"`
		Password string `yaml:"password"`
	} `yaml:"smtp"`
	Database struct {
		Host     string `yaml:"host"`
		Port     string `yaml:"port"`
		User     string `yaml:"user"`
		Password string `yaml:"password"`
		Name     string `yaml:"name"`
		SslMode  string `yaml:"sslmode"`
	} `yaml:"database"`
	Redis struct {
		Host     string `yaml:"host"`
		Port     string `yaml:"port"`
		Password string `yaml:"password"`
		DB       int    `yaml:"db"`
	} `yaml:"redis"`
	GrpcAllowedIPs []string `yaml:"grpc_allowed_ips"`
	Netgsm         struct {
		Username string `yaml:"username"`
		Password string `yaml:"password"`
	}
	OpenAI struct {
		ApiURL       string `yaml:"openai_api_url"`
		OpenAIApiKey string `yaml:"openai_api_key"`
		Model        string `yaml:"openai_model"`
	} `yaml:"openai"`
	Nats struct {
		Url string `yaml:"url"`
	} `yaml:"nats"`
	Stamp struct {
		Host string `yaml:"host"`
		Port string `yaml:"port"`
	}
	Syslog struct {
		Host     string `yaml:"host"`
		Port     string `yaml:"port"`
		Protocol string `yaml:"protocol"` // "udp" or "tcp"
	} `yaml:"syslog"`
	Checkout struct {
		Url  string `yaml:"url"`
		Logo string `yaml:"logo"`
	}
}

var configs *Config

func ReadValue() *Config {
	if configs != nil {
		return configs
	}
	filename, _ := filepath.Abs("./config.yaml")
	yamlFile, _ := os.ReadFile(filename)
	err := yaml.Unmarshal(yamlFile, &configs)
	if err != nil {
		log.Fatal("error loading config.yaml ", err)
	}
	if len(configs.HashSalt) < 24 || len(configs.HashSalt) > 32 {
		log.Fatal("config.hashSalt length min 24 max 32")
	}
	return configs
}
