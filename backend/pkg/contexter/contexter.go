package contexter

type contextKey string

func (c contextKey) String() string {
	return string(c)
}

var (
	ContextKeyInternal                      = contextKey("InternalAuth")
	ContextKeyCurrentUserId                 = contextKey("CurrentUserId")
	ContextKeyCurrentUserEmail              = contextKey("CurrentUserEmail")
	ContextKeyCurrentUserOrgId              = contextKey("CurrentUserOrgId")
	ContextKeyCurrentUserOrg                = contextKey("CurrentUserOrg")
	ContextKeyCurrentUserIsOrganizationUser = contextKey("CurrentUserIsOrganizationUser")
	ContextKeyCurrentIP                     = contextKey("CurrentIP")
	ContextKeyReferenceID                   = contextKey("ReferenceID")
	ContextKeyMonoKey                       = contextKey("MonoKey")
	ContextKeyMonoSecret                    = contextKey("MonoSecret")
	ContextKeyUserAgent                     = contextKey("CurrentUserAgent")
	ContextKeyUserScope                     = contextKey("CurrentUserScope")
	ContextKeyCurrentUserOrgTimeZone        = contextKey("CurrentUserOrgTimeZone")
	ContextKeyIsApi                         = contextKey("IsApi")
	ContextKeyRequestHost                   = contextKey("RequestHost")
)
