package mail

import (
	"log"

	"github.com/parsguru/pars-vue/pkg/config"
	mail "github.com/xhit/go-simple-mail"
)

var (
	password = config.ReadValue().Smtp.Password
	host     = config.ReadValue().Smtp.Host
	port     = config.ReadValue().Smtp.Port
	sender   = config.ReadValue().Smtp.Sender
)

func MailClient() *mail.SMTPClient {
	server := mail.NewSMTPClient()
	server.Host = host
	server.Port = port
	server.Username = sender
	server.Password = password
	server.Encryption = mail.EncryptionTLS
	smtpClient, err := server.Connect()
	if err != nil {
		log.Fatal(err)
	}
	return smtpClient
}
