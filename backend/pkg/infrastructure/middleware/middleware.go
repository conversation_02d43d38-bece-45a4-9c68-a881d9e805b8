package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"github.com/parsguru/pars-vue/pkg/ptr"
	"github.com/parsguru/pars-vue/pkg/redis"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
)

func Authorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		jwt := utils.JwtWrapper{
			Issuer:    config.ReadValue().JwtIssuer,
			SecretKey: config.ReadValue().JwtSecret,
		}
		monoAuth := c.Request.Header.Get("x-mono-auth")

		if monoAuth != "" {
			if jwt.ValidateToken(monoAuth) {
				claims, _ := jwt.ParseToken(monoAuth)
				// state.SetCurrentApiOrgId(claims.OrganizationID)
				c.Set("CurrentApiORG", claims.OrganizationID)
				c.Next()
				return
			} else {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid Authorization Token or Token Expired"})
				return
			}
		}

		db := database.SilentDB()

		bearer := c.Request.Header.Get("Authorization")

		if bearer == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization Token is required"})
			return
		}

		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Incorrect Format of Authorization Token"})
			return
		}

		token := strings.Split(bearer, "Bearer ")[1]

		var expiredTokenCount int64
		db.Model(&entities.ExpiredToken{}).Where("token = ?", token).Count(&expiredTokenCount)

		if expiredTokenCount > 0 {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid Authorization Token or Token Expired"})
			return
		}

		if jwt.ValidateToken(token) {
			claims, err := jwt.ParseToken(token)
			if err != nil {
				expiredToken := entities.ExpiredToken{
					Token: token,
				}
				db.Model(&entities.ExpiredToken{}).Create(&expiredToken)
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid Authorization Token or Token Expired"})
				return
			}

			if ok := redis.AdminUserSessionCheck(claims.ID, token); !ok {
				expiredToken := entities.ExpiredToken{
					Token: token,
				}
				db.Model(&entities.ExpiredToken{}).Create(&expiredToken)
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization Not Allowed"})
				return
			}

			var admin entities.Admin
			db.Model(&entities.Admin{}).Where("id = ?", claims.ID).First(&admin)

			if ptr.BoolValue(admin.Suspended) {
				expiredToken := entities.ExpiredToken{
					Token: token,
				}
				db.Model(&entities.ExpiredToken{}).Create(&expiredToken)
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Authorization Not Allowed"})
				return
			}

			c.Set(state.CurrentAdminId, claims.ID)

			c.Set(state.CurrentOrgId, claims.OrganizationID)
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.MainOrg, claims.Main)

			c.Next()
		} else {
			expiredToken := entities.ExpiredToken{
				Token: token,
			}
			db.Model(&entities.ExpiredToken{}).Create(&expiredToken)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid Authorization Token or Token Expired"})
			return
		}
	}
}

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("CurrentIP", c.ClientIP())
		//c.Set(state.CurrentAdminIP, c.ClientIP())
		c.Set(state.CurrentAdminAgentCTX, c.GetHeader("User-Agent"))

		c.Next()
	}
}
