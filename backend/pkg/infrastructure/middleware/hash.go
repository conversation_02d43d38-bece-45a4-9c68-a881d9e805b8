package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/utils"
)

func HashCheck() gin.HandlerFunc {
	return func(c *gin.Context) {
		hash := c.<PERSON>("X-HASH")
		if hash == "" || hash == "null" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "X-HASH header is required"})
			return
		}
		if !utils.HashidCheck(hash, c.Param("id")) {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "HashID does not match id"})
			return
		}
		c.Next()
	}
}

func HashCheckCustom(param string) gin.HandlerFunc {
	return func(c *gin.Context) {
		hash := c.GetHeader("X-HASH")
		if hash == "" || hash == "null" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "X-HASH header is required"})
			return
		}
		if !utils.HashidCheck(hash, c.Param(param)) {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "HashID does not match id"})
			return
		}
		c.Next()
	}
}
