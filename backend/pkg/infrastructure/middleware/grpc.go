package middleware

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	grpc_auth "github.com/grpc-ecosystem/go-grpc-middleware/auth"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/contexter"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"github.com/parsguru/pars-vue/pkg/monocodes"
	"github.com/parsguru/pars-vue/pkg/utils"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

func GrpcAuthorize(ctx context.Context) (context.Context, error) {
	var (
		internalAuth string
		monoSecret   string
		monoKey      string
		ipaddr       string
	)

	md, _ := metadata.FromIncomingContext(ctx)
	db := database.SilentDB()

	if len(md.Get("x-mono-internal-auth")) > 0 {
		internalAuth = md.Get("x-mono-internal-auth")[0]
		ctx = context.WithValue(ctx, contexter.ContextKeyInternal, true)
	}

	if len(md.Get("x-mono-reference-id")) > 0 {
		reference_id := md.Get("x-mono-reference-id")[0]
		ctx = context.WithValue(ctx, contexter.ContextKeyReferenceID, reference_id)
	}

	if len(md.Get("x-forwarded-for")) > 0 {
		ipaddr = md.Get("x-forwarded-for")[0]
		ctx = context.WithValue(ctx, contexter.ContextKeyCurrentIP, ipaddr)
	}

	if len(md["x-real-ip"]) > 0 {
		ipaddr = md["x-real-ip"][0]
		if ipaddr != "" {
			ctx = context.WithValue(ctx, contexter.ContextKeyCurrentIP, ipaddr)
		}
	}

	if len(md.Get("request-host")) > 0 {
		ctx = context.WithValue(ctx, contexter.ContextKeyRequestHost, md.Get("request-host")[0])
		//log.Println("request-host: ", md.Get("request-host")[0])
		//log.Println("DomainOrganizationID: ", DomainOrganizationID)
	}

	// log 2023/02/21 10:46:23 GRPC IP:  ********:37970
	// grpc ip not working as expected
	// we need to check x-forwarded-for or x-real-ip
	// if there is no x-forwarded-for or x-real-ip
	// first we need to check ip address is working or not
	// if !utils.IsContain(ipaddr, config.ReadValue().GrpcAllowedIPs) {
	// 	return nil, status.Error(codes.Unauthenticated, "unauthorized")
	// }

	if len(md.Get("x-mono-key")) > 0 && len(md.Get("x-mono-secret")) > 0 {
		monoKey = md.Get("x-mono-key")[0]
		monoSecret = md.Get("x-mono-secret")[0]

		var user entities.User
		db.Model(&entities.User{}).Where("api_key = ? AND api_secret = ?", monoKey, monoSecret).First(&user)

		ctx = context.WithValue(ctx, contexter.ContextKeyCurrentUserId, user.ID.String())
		ctx = context.WithValue(ctx, contexter.ContextKeyMonoKey, monoKey)
		ctx = context.WithValue(ctx, contexter.ContextKeyMonoSecret, monoSecret)
	}

	if len(md.Get("web-user-agent")) > 0 {
		ctx = context.WithValue(ctx, contexter.ContextKeyUserAgent, md.Get("web-user-agent")[0])
	}

	if internalAuth != "" || (monoKey != "" && monoSecret != "") {
		return ctx, nil
	}

	token, err := grpc_auth.AuthFromMD(ctx, "bearer")
	if err != nil {
		return nil, err
	}
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().JwtSecret,
		Issuer:    config.ReadValue().JwtIssuer,
		Expire:    config.ReadValue().JwtExpire,
	}

	var jwttoken entities.Token

	db.Unscoped().Model(&entities.Token{}).Where("token = ?", token).First(&jwttoken)

	// TODO: refactor
	// var expiredTokenCount int64
	// db.Model(&entities.Token{}).Where("token = ?", token).Count(&expiredTokenCount)

	// log.Println("expiredTokenCount: ", expiredTokenCount)

	// if expiredTokenCount > 0 {
	// 	return nil, status.Error(codes.Unauthenticated, monocodes.GrpcError(monocodes.TokenExpired))
	// }

	if !jwt.ValidateUserToken(token) {
		return nil, status.Error(codes.Unauthenticated, monocodes.GrpcError(monocodes.TokenExpired))
	}

	claims, err := jwt.ParseUserToken(token)
	if err != nil {
		return nil, status.Errorf(codes.Unauthenticated, monocodes.GrpcError(monocodes.Unauthenticated))
	}

	ctx = context.WithValue(ctx, contexter.ContextKeyCurrentUserId, claims.ID)
	ctx = context.WithValue(ctx, contexter.ContextKeyCurrentUserEmail, claims.Email)
	ctx = context.WithValue(ctx, contexter.ContextKeyCurrentUserOrgId, claims.OrganizationID)
	// ctx = context.WithValue(ctx, contexter.ContextKeyUserScope, claims.Scopes)
	// ctx = context.WithValue(ctx, contexter.ContextKeyCurrentUserOrgTimeZone, claims.Timezeone)

	// ctx = context.WithValue(ctx, contexter.ContextKeyIsApi, claims.IsApiUser)
	return ctx, nil
}

func GrpcAuthorizeApplicationToken(ctx context.Context) (context.Context, error) {
	md, _ := metadata.FromIncomingContext(ctx)
	db := database.SilentDB()

	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().JwtSecret,
		Issuer:    config.ReadValue().JwtIssuer,
		Expire:    config.ReadValue().JwtExpire,
	}

	applicationAuthHeader := md.Get("application_authorization")
	if len(applicationAuthHeader) == 0 {
		return nil, errors.New("application_authorization header is missing")
	}

	token := applicationAuthHeader[0]
	if !jwt.ValidateApplicationToken(token) {
		return nil, status.Error(codes.Unauthenticated, monocodes.GrpcError(monocodes.TokenExpired))
	}

	var application_token entities.TokenForApplication
	db.Model(&entities.TokenForApplication{}).
		Where("token = ?", token).
		First(&application_token)

	if application_token.ID == uuid.Nil {
		return nil, status.Error(codes.Unauthenticated, monocodes.GrpcError(monocodes.Unauthenticated))
	}

	if application_token.ExpireAt.Before(time.Now()) {
		return nil, status.Error(codes.Unauthenticated, monocodes.GrpcError(monocodes.TokenExpired))
	}

	claims, err := jwt.ParseApplicationToken(token)
	if err != nil {
		return nil, status.Errorf(codes.Unauthenticated, monocodes.GrpcError(monocodes.Unauthenticated))
	}

	// if claims.ApplicationID != application_token.Application {
	// 	return nil, status.Errorf(codes.Unauthenticated, monocodes.GrpcError(monocodes.Unauthenticated))
	// }

	ctx = context.WithValue(ctx, contexter.ContextKeyCurrentUserOrgId, claims.OrganizationID)

	return ctx, nil
}

/*
	md := metadata.Pairs("application_authorization", "token")
	ctx := metadata.NewOutgoingContext(context.Background(), md)

	client.SomeMethod(ctx, request)

*/
