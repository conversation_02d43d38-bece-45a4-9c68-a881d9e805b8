package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"github.com/parsguru/pars-vue/pkg/state"
)

// role permission middleware
func RoleCheck(entity string, permission bool, actionType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		db := database.ClientDB()
		var admin entities.Admin
		db.Where(query.BuildQuery(query.WhereID), state.GetContextValueAsUUID(c, "CurrentAdminId")).First(&admin)
		var userRole entities.Role
		db.Model(userRole).Where(query.BuildQuery(query.WhereID), admin.RoleID).First(&userRole)
		var permissions []entities.Permission
		var userPermission entities.Permission
		db.Model(permissions).Where("role_id = ?", userRole.ID).Find(&permissions)
		for _, permission := range permissions {
			if permission.Entity == entity {
				userPermission = permission
			}
		}

		switch actionType {
		case "create":
			if !userPermission.HasPermissionCreate(entity, permission) {
				c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "You don't have permission do this action"})
				return
			}
		case "read":
			if !userPermission.HasPermissionRead(entity, permission) {
				c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "You don't have permission do this action"})
				return
			}
		case "update":
			if !userPermission.HasPermissionUpdate(entity, permission) {
				c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "You don't have permission do this action"})
				return
			}
		case "delete":
			if !userPermission.HasPermissionDelete(entity, permission) {
				c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "You don't have permission do this action"})
				return
			}
		}

		c.Next()
	}
}
