package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/uptrace/opentelemetry-go-extra/otelgorm"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	db       *gorm.DB
	silentDB *gorm.DB
	err      error
)

func InitDB(host, port, user, password, dbname string) {
	initSilentDB(host, port, user, password, dbname)
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s", host, port, user, password, dbname, config.ReadValue().Database.SslMode)
	db, err = gorm.Open(
		postgres.New(
			postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true,
			},
		),
	)
	if err != nil {
		panic(err)
	}

	sqldb, _ := db.DB()
	sqldb.SetMaxIdleConns(20)
	sqldb.SetMaxOpenConns(99)
	sqldb.SetConnMaxLifetime(time.Hour)
	sqldb.SetConnMaxIdleTime(time.Minute * 5)

	if err := db.Use(otelgorm.NewPlugin(otelgorm.WithDBName(dbname), otelgorm.WithoutQueryVariables())); err != nil {
		panic(err)
	}

	if os.Getenv("MIGRATE") == "true" {
		db.AutoMigrate(
			&entities.Permission{},
			&entities.Role{},
			&entities.Organization{},
			&entities.User{},
			&entities.Admin{},
			&entities.Log{},
			&entities.Simulate{},
			&entities.Activity{},
			&entities.Node{},
			&entities.Edge{},
			&entities.SimulateOutput{},
			&entities.Token{},
			&entities.Evaluations{},
			&entities.DBNodeSetting{},
			&entities.DefaultRequest{},
			&entities.Alert{},
			&entities.SimulateLog{},
			&entities.TokenForApplication{},
			&entities.ApplicationTokenAuthorization{},
			&entities.ParsGptResult{},
			&entities.Retry{},
			&entities.ScheduledWork{},
			&entities.ScheduledWorkLog{},
			&entities.AuthenticationAttempt{},
			&entities.ExpiredToken{},
			&entities.EntityBlob{},
		)

		fmt.Println("Migrated")
	}

	if os.Getenv("SEED") == "true" {
		go func() {
			InitSeed()
		}()
	}
}

func ClientDB() *gorm.DB {
	return db
}

func initSilentDB(host, port, user, password, dbname string) {
	logsettings := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			LogLevel:                  logger.Silent, // Log level
			IgnoreRecordNotFoundError: true,          // Ignore ErrRecordNotFound error for logger
			ParameterizedQueries:      true,          // Allow use of parameterized queries
		},
	)
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s", host, port, user, password, dbname, config.ReadValue().Database.SslMode)
	db, err = gorm.Open(
		postgres.New(
			postgres.Config{
				DSN:                  dsn,
				PreferSimpleProtocol: true,
			},
		),
	)

	sqldb, _ := db.DB()
	sqldb.SetMaxIdleConns(20)
	sqldb.SetMaxOpenConns(99)
	sqldb.SetConnMaxLifetime(time.Hour)
	sqldb.SetConnMaxIdleTime(time.Minute * 5)
	if err != nil {
		panic(err)
	}

	db.Logger = logsettings

	silentDB = db
}

func SilentDB() *gorm.DB {
	return silentDB
}
