package server

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path"
	"syscall"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/app/api/routes"
	docs "github.com/parsguru/pars-vue/docs"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/domains/admin"
	"github.com/parsguru/pars-vue/pkg/domains/auth"
	"github.com/parsguru/pars-vue/pkg/domains/dash"
	logs "github.com/parsguru/pars-vue/pkg/domains/log"
	"github.com/parsguru/pars-vue/pkg/domains/maintenance"
	"github.com/parsguru/pars-vue/pkg/domains/organization"
	"github.com/parsguru/pars-vue/pkg/domains/parsgpt"
	"github.com/parsguru/pars-vue/pkg/domains/preferences"
	"github.com/parsguru/pars-vue/pkg/domains/retry"
	"github.com/parsguru/pars-vue/pkg/domains/role"
	"github.com/parsguru/pars-vue/pkg/domains/scheduled"
	"github.com/parsguru/pars-vue/pkg/domains/simulate"
	"github.com/parsguru/pars-vue/pkg/domains/statistics"
	"github.com/parsguru/pars-vue/pkg/domains/token"
	"github.com/parsguru/pars-vue/pkg/embed"
	"github.com/parsguru/pars-vue/pkg/global"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
	"go.opentelemetry.io/otel"
)

var (
	swaggerUser string
	swaggerPass string
)

func LaunchHttpServer(host, port, app_name string, AllowMethods, AllowOrigins, AllowHeaders []string) {
	log.Println("Starting HTTP server on port " + port)
	gin.SetMode(gin.ReleaseMode)

	app := gin.New()
	app.Use(gin.LoggerWithFormatter(func(ginlog gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] - %s \"%s %s %s %d %s\"\n",
			ginlog.TimeStamp.Format("2006-01-02 15:04:05"),
			ginlog.ClientIP,
			ginlog.Method,
			ginlog.Path,
			ginlog.Request.Proto,
			ginlog.StatusCode,
			ginlog.Latency,
		)
	}))

	app.Use(gin.Recovery())
	app.Use(otelgin.Middleware(app_name))
	app.Use(middleware.ClaimIp())

	app.Use(middleware.Secure())
	app.Use(cors.New(cors.Config{
		AllowMethods:     AllowMethods,
		AllowHeaders:     AllowHeaders,
		AllowOrigins:     AllowOrigins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(
		ginprom.Engine(app),
		ginprom.Subsystem("gin"),
		ginprom.Path("/metrics"),
		ginprom.Ignore("/swagger/*any", "/assets/*any"),
	)
	app.Use(p.Instrument())
	tracer := otel.Tracer(config.ReadValue().AppName)

	api := app.Group("/api/v1")

	/* routes */

	simulateRepo := simulate.NewRepo(database.ClientDB())
	simulateService := simulate.NewService(simulateRepo)
	routes.SimulateRoutes(api, simulateService, tracer)

	authRepo := auth.NewRepo(database.ClientDB())
	authService := auth.NewService(authRepo)
	routes.AuthRoutes(api, authService, tracer)

	logRepo := logs.NewRepo(database.ClientDB())
	logService := logs.NewService(logRepo)
	routes.LogRoutes(api, logService, tracer)

	organizationRepo := organization.NewRepo(database.ClientDB())
	organizationService := organization.NewService(organizationRepo)
	routes.OrganizationRoutes(api, organizationService, tracer)

	adminRepo := admin.NewRepo(database.ClientDB())
	adminService := admin.NewService(adminRepo)
	routes.AdminRoutes(api, adminService, tracer)

	dashRepository := dash.NewRepo(database.ClientDB())
	dashService := dash.NewService(dashRepository)
	routes.DashRoutes(api, dashService, tracer)

	preferencesRepo := preferences.NewRepo(database.ClientDB())
	preferencesService := preferences.NewService(preferencesRepo)
	routes.PreferencesRoutes(api, preferencesService, tracer)

	roleRepo := role.NewRepo(database.ClientDB())
	roleService := role.NewService(roleRepo)
	routes.RoleRoutes(api, roleService, tracer)

	tokenRepo := token.NewRepo(database.ClientDB())
	tokenService := token.NewService(tokenRepo)
	routes.TokenRoutes(api, tokenService, tracer)

	parsGptRepo := parsgpt.NewRepo(database.ClientDB())
	parsGptService := parsgpt.NewService(parsGptRepo)
	routes.ParsGptRoutes(api, parsGptService, tracer)

	retryRepo := retry.NewRepo(database.ClientDB())
	retryService := retry.NewService(retryRepo)
	routes.RetryRoutes(api, retryService, tracer)

	scheduledRepo := scheduled.NewRepo(database.ClientDB())
	scheduledService := scheduled.NewService(scheduledRepo)
	routes.ScheduledRoutes(api, scheduledService, tracer)

	maintenanceRepo := maintenance.NewRepo()
	maintenanceService := maintenance.NewService(maintenanceRepo)
	routes.MaintenanceRoutes(api, maintenanceService, tracer)

	statisticsRepo := statistics.NewRepo(database.ClientDB())
	statisticsService := statistics.NewService(statisticsRepo)
	routes.StatisticsRoutes(api, statisticsService, tracer)

	routes.ExampleRoutes(api)

	/* routes */

	app.GET("/swagger", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "swagger/index.html")
	})

	if os.Getenv("SWAGGER_USER") != "" {
		swaggerUser = os.Getenv("SWAGGER_USER")
	} else {
		swaggerUser = "brms"
	}
	if os.Getenv("SWAGGER_PASS") != "" {
		swaggerPass = os.Getenv("SWAGGER_PASS")
	} else {
		swaggerPass = "tapsilat-brms"
	}

	docs.SwaggerInfo.Host = config.ReadValue().BaseUrl
	docs.SwaggerInfo.Version = os.Getenv("APP_VERSION")
	app.GET("/swagger/*any",
		gin.BasicAuth(gin.Accounts{
			swaggerUser: swaggerPass,
		}),
		ginSwagger.WrapHandler(swaggerFiles.Handler),
	)

	app.GET("/assets/*filepath", func(c *gin.Context) {
		c.FileFromFS(path.Join("/dist/", c.Request.URL.Path), http.FS(embed.StaticsFS()))
	})
	app.Any("/", func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})
	app.GET("/robots.txt", func(c *gin.Context) {
		c.FileFromFS("dist/robots.txt", http.FS(embed.StaticsFS()))
	})
	app.GET("/favicon.ico", func(c *gin.Context) {
		c.FileFromFS("dist/favicon.ico", http.FS(embed.StaticsFS()))
	})
	app.GET("/version", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"version": global.GetVersion(),
		})
	})
	app.NoRoute(func(c *gin.Context) {
		c.FileFromFS("dist/", http.FS(embed.StaticsFS()))
	})

	srv := &http.Server{
		Addr:    ":" + port,
		Handler: app,
	}

	if config.ReadValue().Env == "development" {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("listen: %s\n", err)
		}
	} else {
		go func() {
			if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				log.Fatalf("listen: %s\n", err)
			}
		}()

		quit := make(chan os.Signal, 1)

		signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
		<-quit
		log.Println("Shutdown Server ...")

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := srv.Shutdown(ctx); err != nil {
			log.Fatal("Server Shutdown:", err)
		}
		<-ctx.Done()
		log.Println("timeout of 5 seconds.")

		log.Println("Server exiting")
	}
}
