package server

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"time"

	//grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	//grpc_auth "github.com/grpc-ecosystem/go-grpc-middleware/auth"
	//grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	//grpc_ctxtags "github.com/grpc-ecosystem/go-grpc-middleware/tags"
	//grpc_opentracing "github.com/grpc-ecosystem/go-grpc-middleware/tracing/opentracing"
	//"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors"
	//"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/selector"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpc_auth "github.com/grpc-ecosystem/go-grpc-middleware/auth"
	grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	grpc_ctxtags "github.com/grpc-ecosystem/go-grpc-middleware/tags"
	"github.com/parsguru/pars-vue/app/api/grpcroutes"

	pb "github.com/parsguru/pars-vue/grpc"

	parsAuthv1Server "github.com/parsguru/pars-vue/grpc/pars_auth/v1"
	parsControlv1Server "github.com/parsguru/pars-vue/grpc/pars_control/v1"
	parsSimulatev1Server "github.com/parsguru/pars-vue/grpc/pars_simulate/v1"

	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"

	//"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	tracesdk "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.4.0"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

func UnarylogMiddleware() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Extract request metadata
		startTime := time.Now()
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			md = metadata.MD{}
		}
		var ip string
		if len(md["x-forwarded-for"]) > 0 {
			ip = md.Get("x-forwarded-for")[0]
		}
		method := info.FullMethod
		var org string
		if state.CurrentUserOrganizationName(ctx) != "" {
			org = state.CurrentUserOrganizationName(ctx)
		} else {
			org = "-"
		}

		resp, err := handler(ctx, req)
		code, _ := status.FromError(err)
		endtime := time.Now()

		fmt.Printf("[ GRPC LOG ] - %s %s %s %s %s [%s] \n",
			time.Now().Format("2006-01-02 15:04:05"),
			ip,
			org,
			method,
			endtime.Sub(startTime).String(),
			code.Code().String(),
		)

		return resp, err
	}
}

func UnarySkipperMiddleware() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {

		return handler(ctx, req)
	}
}

func LaunchGRPCServer(port string) {
	lis, err := net.Listen("tcp", fmt.Sprintf(":%s", port))
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	process := os.Getpid()
	log.Default().Printf("GRPC Listening on port %s", port)
	exp, _ := jaeger.New(
		jaeger.WithCollectorEndpoint(
			jaeger.WithEndpoint(config.ReadValue().Jaeger.Url),
		),
	)

	tp := tracesdk.NewTracerProvider(
		tracesdk.WithBatcher(exp),
		tracesdk.WithSampler(tracesdk.AlwaysSample()),
		tracesdk.WithResource(resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceNameKey.String(config.ReadValue().Jaeger.ServiceName+"-grpc"),
			attribute.String("environment", config.ReadValue().Env),
			attribute.Int64("ID", int64(process)),
		)),
	)

	otel.SetTracerProvider(tp)
	otel.SetTextMapPropagator(
		propagation.NewCompositeTextMapPropagator(
			propagation.TraceContext{},
			propagation.Baggage{},
		),
	)

	tracer := otel.Tracer(
		config.ReadValue().Jaeger.ServiceName + "-grpc",
	)

	/* selectorFunc := func(ctx context.Context, callMeta interceptors.CallMeta) bool {

		if state.InternalAuth(ctx) {
			return false
		} else {
			switch callMeta.Service {
			case "Login":
				return false
			case "Register":
				return false
			default:
				return true
			}
		}
	} */
	grpcserver := grpc.NewServer(
		grpc.StatsHandler(otelgrpc.NewServerHandler()),
		grpc.UnaryInterceptor(
			grpc_middleware.ChainUnaryServer(
				// grpc_recovery.UnaryServerInterceptor(),
				grpc_auth.UnaryServerInterceptor(middleware.GrpcAuthorizeApplicationToken),
				// selector.UnaryServerInterceptor(UnarySkipperMiddleware(), selector.MatchFunc(selectorFunc)),
				grpc_ctxtags.UnaryServerInterceptor(),
				grpc_recovery.UnaryServerInterceptor(),
				// grpc_opentracing.UnaryServerInterceptor(),
				UnarylogMiddleware(),
			),
		),
		grpc.StreamInterceptor(
			grpc_middleware.ChainStreamServer(
				grpc_recovery.StreamServerInterceptor(),
				grpc_auth.StreamServerInterceptor(middleware.GrpcAuthorizeApplicationToken),
				grpc_ctxtags.StreamServerInterceptor(),
				grpc_recovery.StreamServerInterceptor(),
				// grpc_opentracing.StreamServerInterceptor(),
			),
		),
	)

	db := database.ClientDB()

	parsAuthv1Server.RegisterParsAuthServiceServer(grpcserver, &grpcroutes.ParsAuthServer{
		Tracer: tracer,
		DB:     db,
	})

	pb.RegisterLogServiceServer(grpcserver, &grpcroutes.ParsLogServer{
		Tracer: tracer,
		DB:     db,
	})

	parsSimulatev1Server.RegisterParsSimulateServiceServer(grpcserver, &grpcroutes.ParsSimulateServer{
		Tracer: tracer,
		DB:     db,
	})

	pb.RegisterUserServiceServer(grpcserver, &grpcroutes.UserServer{
		Tracer: tracer,
		DB:     db,
	})

	pb.RegisterOrganizationServiceServer(grpcserver, &grpcroutes.OrganizationServer{
		Tracer: tracer,
		DB:     db,
	})

	pb.RegisterPreferencesServiceServer(grpcserver, &grpcroutes.PreferencesServer{
		Tracer: tracer,
		DB:     db,
	})

	parsControlv1Server.RegisterParsControlServiceServer(grpcserver, &grpcroutes.ParsControlServer{
		Tracer: tracer,
		DB:     db,
	})

	if err := grpcserver.Serve(lis); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
