package nat

import (
	"log"
	"os"
	"os/signal"

	"github.com/nats-io/nats.go"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/global"
	amqp "github.com/rabbitmq/amqp091-go"
)

var (
	nc  *nats.Conn
	err error
)

func InitNats() {
	nc, err = nats.Connect(config.ReadValue().Nats.Url, nats.Name("pars-vue-nats"+"_"+global.GetAppID()))
	if err != nil {
		log.Fatalf("Can't connect to nats: %s", err)
	}

	if config.ReadValue().Env == "production" {
		c := make(chan os.Signal, 1)
		signal.Notify(c, os.Interrupt)
		go func() {
			<-c
			log.Println("Closing connection to nats")
			nc.Close()
		}()
	}

}

func NatConn() *nats.Conn {
	return nc
}

var (
	nc_eventer  *nats.Conn
	err_eventer error
	rb_eventer  *amqp.Channel
)

func NatEventerConn() *nats.Conn {
	return nc_eventer
}

func RabbitEventerConn() *amqp.Channel {
	return rb_eventer
}

func InitEventerNats() {
	nc_eventer, err_eventer = nats.Connect(config.ReadValue().Nats.Url, nats.Name("pars-event"+"_"+global.GetAppID()))
	if err_eventer != nil {
		log.Fatalf("Can't connect to nats: %s", err_eventer)
	}

	if config.ReadValue().Env == "production" {
		c := make(chan os.Signal, 1)
		signal.Notify(c, os.Interrupt)
		go func() {
			<-c
			log.Println("Closing connection to nats")
			nc_eventer.Close()
		}()
	}

}
