package eventer

import (
	"encoding/json"
	"log"

	"github.com/parsguru/pars-vue/pkg/nat"
)

type EventSourceListenerData struct {
	Entity         string
	Action         string
	Id             string
	OrganizationID string
	Data           string
}

func PushEventSourceListener(eventData EventSourceListenerData) {
	ncli := nat.NatEventerConn()

	jsonData, err := json.Marshal(eventData)
	if err != nil {
		log.Println("Error PushEventSourceListener marshalling event data: ", err)
	}

	if err := ncli.Publish("eventer", jsonData); err != nil {
		log.Println("Error PushEventSourceListener publishing event: ", err)
	}

}
