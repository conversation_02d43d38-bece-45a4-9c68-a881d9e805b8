package eventer

import (
	"encoding/json"
	"fmt"
	"log"
	"strings"

	"github.com/parsguru/pars-vue/pkg/nat"
)

type EventData struct {
	Entity string      `json:"entity"`
	Action string      `json:"action"`
	Id     string      `json:"id,omitempty"`
	Data   interface{} `json:"data,omitempty"`
}

func publishData(channel, eventType string, data interface{}, withData bool) {
	ncli := nat.NatConn()

	sliced := strings.Split(eventType, ".")
	eventData := EventData{
		Entity: sliced[0],
		Action: sliced[1],
	}

	if withData {
		eventData.Data = data
	} else {
		eventData.Id = fmt.Sprintf("%v", data)
	}

	jsonData, err := json.Marshal(eventData)
	if err != nil {
		log.Fatalln("Error marshalling event data: ", err)
	}

	if err := ncli.Publish(channel, jsonData); err != nil {
		log.Fatalln("Error publishing event: ", err)
	}
}

func PushEvent(eventType string, data interface{}) {
	publishData("entity", eventType, data, false)
}

func PushEventWithData(eventType string, data string) {
	publishData("entity", eventType, data, true)
}

func PushNotificationWithData(notificationType string, data interface{}) {
	publishData("notification", notificationType, data, true)
}
