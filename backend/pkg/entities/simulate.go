package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Simulate struct {
	gorm.Model

	ID             uuid.UUID       `json:"ID" gorm:"primary_key"`
	Name           string          `json:"name"`
	Nodes          []Node          `json:"nodes"`
	Edges          []Edge          `json:"edges"`
	Context        Attrs           `json:"context"`
	SimulateOutput *SimulateOutput `json:"output,omitempty"`
	AdminID        uuid.UUID       `json:"admin_id"`
	OrganizationID uuid.UUID       `json:"organization_id"`
	VersionName    string          `json:"version_name"`
	VersionNumber  int             `json:"version_number"`
	VersionID      uuid.UUID       `json:"version_id"`
	AccessToken    string          `json:"access_token"`
}

func (s *Simulate) BeforeCreate(tx *gorm.DB) (err error) {
	s.ID = uuid.New()
	return
}
