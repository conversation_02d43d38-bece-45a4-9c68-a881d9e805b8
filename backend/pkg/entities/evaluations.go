package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Evaluations struct {
	gorm.Model

	ID             uuid.UUID `json:"ID" gorm:"primary_key"`
	Content        string    `json:"content"`
	Context        string    `json:"context"`
	SimulateID     uuid.UUID `json:"-"`
	OrganizationID uuid.UUID `json:"organization_id"`
	AdminID        uuid.UUID `json:"admin_id"`
	IsThereError   bool      `json:"is_there_error"`
	ProtoType      string    `json:"proto_type"`
	SimulateResult Attrs     `json:"simulate_result"`
}

func (e *Evaluations) BeforeCreate(tx *gorm.DB) (err error) {
	e.ID = uuid.New()
	return
}
