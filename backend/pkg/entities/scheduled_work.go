package entities

import (
	"time"

	"github.com/google/uuid"
)

type ScheduledWork struct {
	Base

	OrganizationID   uuid.UUID `json:"organization_id"`
	AdminID          uuid.UUID `json:"admin_id"`
	SimulateID       uuid.UUID `json:"simulate_id"`
	NameOfWork       string    `json:"name_of_work"`
	Request          string    `json:"request"`
	EachXTime        time.Time `json:"each_x_time" `
	StartDate        time.Time `json:"start_date"`
	EndDate          time.Time `json:"end_date"`
	HowManyTimeToTry int       `json:"how_many_time_to_try"`
	LastRunDate      time.Time `json:"last_run_date"`
}

type ScheduledWorkLog struct {
	Base

	ScheduledWorkID uuid.UUID `json:"scheduled_work_id"`
	TotalWork       int       `json:"total_work" default:"0"`
	TotalSuccess    int       `json:"total_success" default:"0"`
	TotalError      int       `json:"total_error" default:"0"`
}
