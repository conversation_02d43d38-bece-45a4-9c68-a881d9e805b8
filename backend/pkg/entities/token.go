package entities

import (
	"time"

	"github.com/google/uuid"
)

type Token struct {
	Base
	IsActive       *bool     `json:"is_active" gorm:"default:true" example:"true"`
	Name           string    `json:"name" example:"example token"`
	Token          string    `json:"token" gorm:"index" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"`
	Expire         time.Time `json:"expire" example:"2021-01-01T00:00:00Z"`
	UserID         uuid.UUID `json:"user_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	PublicKey      string    `json:"public_key" example:"public key"`
	OrganizationID uuid.UUID `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	ChannelID      uuid.UUID `json:"channel_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212" gorm:"default:null"`
}
