package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Edge struct {
	gorm.Model
	ID             string    `json:"id" gorm:"primary_key"`
	SourceID       string    `json:"sourceId"`
	Type           string    `json:"type"`
	TargetID       string    `json:"targetId"`
	SourceHandle   string    `json:"sourceHandle,omitempty"`
	SimulateID     uuid.UUID `json:"-"`
	VersionID      uuid.UUID `json:"version_id"`
	OrganizationID uuid.UUID `json:"organization_id"`
	AdminID        uuid.UUID `json:"admin_id"`
	VersionName    string    `json:"version_name"`
	Simulate       Simulate  `json:"-"`
}
