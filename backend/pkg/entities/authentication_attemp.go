package entities

import (
	"context"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
)

const (
	AuthenticationAttemptMethodPassword uint64 = iota + 1
	AuthenticationAttemptMethodMagicLink
	AuthenticationAttemptMethodTwoFactor
	AuthenticationAttemptMethodPin
	AuthenticationAttemptMethodForgotPassword
	AuthenticationAttemptMethodResetPassword
	AuthenticationAttemptMethodSms
)

const (
	AuthenticationAttemptStatusSuccess uint64 = iota + 1
	AuthenticationAttemptStatusFailed
)

type AuthenticationAttempt struct {
	Base
	Method         uint64    `json:"method" example:"1"`
	Status         uint64    `json:"status" example:"1"`
	IP             string    `json:"ip" gorm:"not null" example:"127.0.0.1"`
	Agent          string    `json:"agent" example:"Mozilla/5.0"`
	UserID         uuid.UUID `json:"user_id" gorm:"not null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"not null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

var AuthenticationAttemptMethods = map[uint64]string{
	AuthenticationAttemptMethodPassword:       "password",
	AuthenticationAttemptMethodMagicLink:      "magic_link",
	AuthenticationAttemptMethodTwoFactor:      "two_factor",
	AuthenticationAttemptMethodPin:            "pin",
	AuthenticationAttemptMethodForgotPassword: "forgot_password",
	AuthenticationAttemptMethodResetPassword:  "reset_password",
	AuthenticationAttemptMethodSms:            "sms",
}

func (a AuthenticationAttempt) GetMethod() string {
	return AuthenticationAttemptMethods[a.Method]
}

var AuthenticationAttemptStatuses = map[uint64]string{
	AuthenticationAttemptStatusSuccess: "success",
	AuthenticationAttemptStatusFailed:  "failed",
}

func (a AuthenticationAttempt) GetStatus() string {
	return AuthenticationAttemptStatuses[a.Status]
}

func (a AuthenticationAttempt) Create(ctx context.Context, db *gorm.DB) {

	if a.UserID == uuid.Nil {
		a.UserID = state.CurrentUser(ctx)
	}

	if a.OrganizationID == uuid.Nil {
		a.OrganizationID = state.CurrentUserOrganization(ctx)
	}

	if a.IP == "" {
		a.IP = state.CurrentGRPCIP(ctx)
	}

	if a.Agent == "" {
		a.Agent = state.CurrentUserAgent(ctx)
	}

	db.WithContext(ctx).Model(&AuthenticationAttempt{}).Create(&a)
}
