package entities

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
	"github.com/parsguru/pars-vue/pkg/utils"
	"gorm.io/gorm"
)

type Base struct {
	ID           uuid.UUID      `gorm:"type:uuid;primary_key" json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	HashID       string         `gorm:"type:varchar(255);unique_index" json:"hash_id" example:"kKE4ZvqVgPl9d8eA8RDAxyGJz2onEC"`
	CreatedAt    time.Time      `json:"created_at" example:"2021-01-01T00:00:00Z"`
	UpdatedAt    time.Time      `json:"updated_at" example:"2021-01-01T00:00:00Z"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index" swaggertype:"string" format:"date-time"`
	Labels       pq.StringArray `json:"labels" gorm:"type:text[]"`
	IsUserObject *bool          `json:"is_user_object" gorm:"default:false"`
	IsDummy      *bool          `json:"is_dummy" gorm:"default:false"`
}

type PositionAttrs map[string]float64

func (a PositionAttrs) Value() (driver.Value, error) {
	return json.Marshal(a)
}

func (a *PositionAttrs) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(b, &a)
}

type Attrs map[string]interface{}

func (a Attrs) Value() (driver.Value, error) {
	return json.Marshal(a)
}

func (a *Attrs) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(b, &a)
}

func (g *Base) BeforeCreate(tx *gorm.DB) (err error) {
	g.ID = uuid.New()
	g.HashID = utils.GenerateHashid(g.ID.String())
	return nil
}

func (g *Base) ToSearch(ctx context.Context, db *gorm.DB) (map[string]interface{}, error) {
	indexDto := map[string]interface{}{
		"id":             g.ID,
		"hash_id":        g.HashID,
		"created_at":     g.CreatedAt.Unix(),
		"updated_at":     g.UpdatedAt.Unix(),
		"deleted_at":     g.DeletedAt.Time.Unix(),
		"labels":         g.Labels,
		"is_user_object": g.IsUserObject,
	}
	return indexDto, nil
}
