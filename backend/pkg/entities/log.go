package entities

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"github.com/google/uuid"
)

type LogLevel uint

const (
	LogLevel0 LogLevel = iota
	LogLevel1
	LogLevel2
	LogLevel3
	LogLevel4
	LogLevel5
)

var LogLevelList = map[LogLevel]string{
	LogLevel0: "LEVEL0",
	LogLevel1: "LEVEL1",
	LogLevel2: "LEVEL2",
	LogLevel3: "LEVEL3",
	LogLevel4: "LEVEL4",
	LogLevel5: "LEVEL5",
}

func (level LogLevel) String() string {
	if val, ok := LogLevelList[level]; ok {
		return val
	}
	return "UNKNOWN"
}

// Implement MarshalJSON to encode LogLevel as an integer
func (level LogLevel) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("%d", level)), nil
}

// Implement UnmarshalJSON to decode LogLevel from an integer
func (level *LogLevel) UnmarshalJSON(data []byte) error {
	var intLevel int
	if err := json.Unmarshal(data, &intLevel); err != nil {
		return err
	}
	*level = LogLevel(intLevel)
	return nil
}

// Implement custom GORM data type handling
func (level LogLevel) Value() (driver.Value, error) {
	return int64(level), nil
}

func (level *LogLevel) Scan(value interface{}) error {
	intVal, ok := value.(int64)
	if !ok {
		return fmt.Errorf("failed to scan LogLevel")
	}
	*level = LogLevel(intVal)
	return nil
}

type Log struct {
	Base
	Location       string    `json:"location" example:"/path/to/file.go:123" gorm:"default:null"`
	Title          string    `json:"title" example:"example title"`
	Message        string    `json:"message" example:"order created"`
	Entity         string    `json:"entity" example:"order"`
	Type           string    `json:"type" example:"info"`
	Ip             string    `json:"ip" example:"127.0.0.1"`
	Proto          string    `json:"proto" example:"http"` // http, grpc
	IsApi          *bool     `json:"is_api" gorm:"default:false"`
	RequestID      string    `json:"request_id" example:"123e4567-e89b-12d3-a456-************"`
	EntityID       uuid.UUID `json:"entity_id" gorm:"default:null;type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	UserID         uuid.UUID `json:"user_id" gorm:"default:null;type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AdminID        uuid.UUID `json:"admin_id" gorm:"default:null;type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"default:null;type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Level          LogLevel  `json:"level" gorm:"type:int;default:0"`
}
