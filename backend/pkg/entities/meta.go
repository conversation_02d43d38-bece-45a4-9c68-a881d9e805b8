package entities

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/google/uuid"
)

type Meta struct {
	Base
	MKey           string    `json:"mkey" example:"example key"`
	MValue         Mkvs      `json:"mvalue" gorm:"type:jsonb"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"default:null;type:uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type Mkvs []Mkv

type Mkv struct {
	K string `json:"k"`
	V string `json:"v"`
}

func (mkvs *Mkvs) Scan(value interface{}) error {
	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, mkvs)
	case string:
		if v != "" {
			return mkvs.Scan([]byte(v))
		}
	default:
		return errors.New("not supported")
	}
	return nil
}

func (mkvs Mkvs) Value() (driver.Value, error) {
	_data, err := json.Marshal(mkvs)
	if err != nil {
		return nil, err
	}
	return string(_data), nil
}
