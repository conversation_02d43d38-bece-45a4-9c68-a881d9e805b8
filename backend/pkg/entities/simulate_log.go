package entities

import "github.com/google/uuid"

type SimulateLog struct {
	Base
	SimulateID     uuid.UUID `json:"simulate_id"`
	OrganizationID uuid.UUID `json:"organization_id"`
	AdminID        uuid.UUID `json:"admin_id"`
	Type           string    `json:"type" example:"error"`
	Proto          string    `json:"proto" example:"grpc"`
	Source         string    `json:"source" example:"editor"`
	Domain         string    `json:"domain"`
	Message        string    `json:"message"`
	Request        string    `json:"request"`
	Response       string    `json:"response"`
	IsRetried      bool      `json:"is_retried" default:"false"`
	IsScheduled    bool      `json:"is_scheduled" default:"false"`
	ScheduledID    uuid.UUID `json:"scheduled_id"`
	XTry           int       `json:"x_try" default:"0"`
}
