package entities

import "github.com/google/uuid"

type Store struct {
	Base
	SimulateID     uuid.UUID `json:"simulate_id"`
	OrganizationID uuid.UUID `json:"organization_id"`
	AdminID        uuid.UUID `json:"admin_id"`
	Type           string    `json:"type" example:"error"`
	Proto          string    `json:"proto" example:"grpc"`
	Application    string    `json:"application" example:"pars"`
	Message        string    `json:"message"`
	Request        string    `json:"request"`
	Response       string    `json:"response"`
}
