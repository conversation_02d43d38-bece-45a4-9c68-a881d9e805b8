package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type SimulateOutput struct {
	gorm.Model
	ID             uuid.UUID `json:"ID" gorm:"primary_key"`
	Performance    string    `json:"performance"`
	Result         Attrs     `json:"result"`
	Trace          Attrs     `json:"trace"`
	SimulateID     uuid.UUID `json:"-"`
	OrganizationID uuid.UUID `json:"organization_id"`
	AdminID        uuid.UUID `json:"admin_id"`
}

func (s *SimulateOutput) BeforeCreate(tx *gorm.DB) (err error) {
	s.ID = uuid.New()
	return
}
