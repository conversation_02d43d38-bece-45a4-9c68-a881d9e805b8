package entities

import (
	"github.com/google/uuid"
)

type Activity struct {
	Base
	Action         string    `json:"action" example:"create"`
	Entity         string    `json:"entity" example:"bank"`
	IpAddress      string    `json:"ip_address" example:"127.0.0.1"`
	AdminID        uuid.UUID `json:"admin_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID uuid.UUID `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}
