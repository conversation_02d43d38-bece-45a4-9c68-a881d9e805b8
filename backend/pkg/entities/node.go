package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Node struct {
	gorm.Model
	ID              uuid.UUID     `json:"id" gorm:"primary_key"`
	NodeID          string        `json:"node_id"`
	Name            string        `json:"name"`
	Position        PositionAttrs `json:"position"`
	Type            string        `json:"type"`
	Content         Attrs         `json:"content"`
	FunctionContent string        `json:"function_content"`
	SimulateID      uuid.UUID     `json:"-"`
	VersionID       uuid.UUID     `json:"version_id"`
	OrganizationID  uuid.UUID     `json:"organization_id"`
	AdminID         uuid.UUID     `json:"admin_id"`
	VersionName     string        `json:"version_name"`
	Simulate        Simulate      `json:"-"`
}

func (n *Node) BeforeCreate(tx *gorm.DB) (err error) {
	n.ID = uuid.New()
	return
}
