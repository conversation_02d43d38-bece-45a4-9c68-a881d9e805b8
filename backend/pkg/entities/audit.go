package entities

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Audit struct {
	UpdatedBy   uuid.UUID  `json:"updated_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	UpdatedDate *time.Time `json:"updated_date" example:"2021-01-01T00:00:00Z"`
	DeletedBy   uuid.UUID  `json:"deleted_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	DeletedDate *time.Time `json:"deleted_date" example:"2021-01-01T00:00:00Z"`
	MakedBy     uuid.UUID  `json:"maked_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	MakedDate   *time.Time `json:"maked_date" example:"2021-01-01T00:00:00Z"`
	CheckedBy   uuid.UUID  `json:"checked_by" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CheckedDate *time.Time `json:"checked_date" example:"2021-01-01T00:00:00Z"`
}

type AuiditByDto struct {
	ID   uuid.UUID `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name string    `json:"name" example:"test"`
}
type AuditActionDto struct {
	By AuiditByDto `json:"by"`
	At int64       `json:"at" example:"1612128000"`
}

type AuiditDto struct {
	Updated AuditActionDto `json:"updated"`
	Deleted AuditActionDto `json:"deleted"`
	Maked   AuditActionDto `json:"maked"`
	Checked AuditActionDto `json:"checked"`
}

func (a *Audit) ToSearch(ctx context.Context, db *gorm.DB) (*AuiditDto, error) {

	indexDto := AuiditDto{}

	if a.UpdatedBy != uuid.Nil {
		var admin User
		if err := db.WithContext(ctx).Where("id = ?", a.UpdatedBy).First(&admin).Error; err != nil {
			return nil, err
		}
		indexDto.Updated.By.ID = admin.ID
		indexDto.Updated.By.Name = admin.Name
		if a.UpdatedDate != nil {
			indexDto.Updated.At = a.UpdatedDate.Unix()
		}
	}

	if a.DeletedBy != uuid.Nil {
		var admin User
		if err := db.WithContext(ctx).Where("id = ?", a.DeletedBy).First(&admin).Error; err != nil {
			return nil, err
		}
		indexDto.Deleted.By.ID = admin.ID
		indexDto.Deleted.By.Name = admin.Name
		if a.DeletedDate != nil {
			indexDto.Deleted.At = a.DeletedDate.Unix()
		}
	}

	if a.MakedBy != uuid.Nil {
		var admin User
		if err := db.WithContext(ctx).Where("id = ?", a.MakedBy).First(&admin).Error; err != nil {
			return nil, err
		}
		indexDto.Maked.By.ID = admin.ID
		indexDto.Maked.By.Name = admin.Name
		if a.MakedDate != nil {
			indexDto.Maked.At = a.MakedDate.Unix()
		}
	}

	if a.CheckedBy != uuid.Nil {
		var admin User
		if err := db.WithContext(ctx).Where("id = ?", a.CheckedBy).First(&admin).Error; err != nil {
			return nil, err
		}
		indexDto.Checked.By.ID = admin.ID
		indexDto.Checked.By.Name = admin.Name
		if a.CheckedDate != nil {
			indexDto.Checked.At = a.CheckedDate.Unix()
		}
	}

	return &indexDto, nil
}
