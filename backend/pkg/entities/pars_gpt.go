package entities

import "github.com/google/uuid"

type ParsGptResult struct {
	Base

	Prompt         string    `json:"prompt"`
	// Nodes          []_Node   `json:"nodes"`
	// Edges          []_Edge   `json:"edges"`
	JSONResult     string    `json:"json_result"`
	Temperature    float64   `json:"temperature"`
	OrganizationID uuid.UUID `json:"organization_id"`
	AdminID        uuid.UUID `json:"admin_id"`
	RateOfResult   int       `json:"rate_of_result"`
}

type ParsGptResultJSON struct {
	Nodes []_Node `json:"nodes"`
	Edges []_Edge `json:"edges"`
}

type _Node struct {
	ID       string        `json:"id"`
	Name     string        `json:"name"`
	Position PositionAttrs `json:"position"`
	Type     string        `json:"type"`
	Content  Attrs         `json:"content"`
}

type _Edge struct {
	ID       string `json:"id"`
	SourceID string `json:"sourceId"`
	Type     string `json:"type"`
	TargetID string `json:"targetId"`
}
