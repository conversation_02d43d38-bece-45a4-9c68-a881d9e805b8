package entities

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type User struct {
	Base
	Name                    string    `json:"name" example:"John"`
	Email                   string    `json:"email" gorm:"not null; unique"`
	Status                  bool      `json:"status"` // active, inactive, deleted
	Locale                  string    `json:"locale" gorm:"default:'en'"`
	Tfa                     *bool     `json:"tfa" gorm:"default:false"`
	TfaKey                  string    `json:"tfa_key"`
	RoleID                  uuid.UUID `json:"role_id"`
	OrganizationID          uuid.UUID `json:"organization_id"`
	Token                   Token     `json:"token"`
	ResetPasswordCode       string    `json:"reset_password_code" example:"12345678"`
	ResetPasswordCodeExpire time.Time `json:"reset_password_code_expire" example:"2021-01-01T00:00:00Z"`
	ForcePasswordChange     *bool     `json:"force_password_change" gorm:"default:false"`
	LastPasswordChange      time.Time `json:"last_password_change" example:"2021-01-01T00:00:00Z"`
	SMSForAlert             string    `json:"sms_for_alert"`
	EmailForAlert           string    `json:"email_for_alert"`
}

func (u *User) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = uuid.New()
	return
}
