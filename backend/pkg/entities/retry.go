package entities

import "github.com/google/uuid"

type Retry struct {
	Base

	OrganizationID       uuid.UUID `json:"organization_id"`
	AdminID              uuid.UUID `json:"admin_id"`
	RetriedSimulateLogID uuid.UUID `json:"retried_simulate_log_id"`
	RetriedSimulateID    uuid.UUID `json:"retried_simulate_id"`
	Status               string    `json:"status"` // "pending", "success", "failed"
	XTry                 int       `json:"x_try"`
	Message              string    `json:"message"`
	Request              string    `json:"request"`
	Response             string    `json:"response"`
}
