package entities

import (
	"github.com/google/uuid"
)

type Role struct {
	Base
	Audit
	Name           string    `json:"name" example:"example role"`
	OrganizationID uuid.UUID `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Locked         *bool     `json:"locked" gorm:"default:false" example:"false"`
}

type Permission struct {
	Base
	RoleID uuid.UUID `json:"role_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Entity string    `json:"entity" example:"product"`
	Create bool      `json:"create" example:"true"`
	Read   bool      `json:"read" example:"true"`
	Update bool      `json:"update" example:"true"`
	Delete bool      `json:"delete" example:"true"`
}

func (permission *Permission) HasPermissionCreate(entity string, perm bool) bool {
	if permission.Entity == entity && permission.Create == perm {
		return true
	}
	return false
}

func (permission *Permission) HasPermissionRead(entity string, perm bool) bool {
	if permission.Entity == entity && permission.Read == perm {
		return true
	}
	return false
}

func (permission *Permission) HasPermissionUpdate(entity string, perm bool) bool {
	if permission.Entity == entity && permission.Update == perm {
		return true
	}
	return false
}

func (permission *Permission) HasPermissionDelete(entity string, perm bool) bool {
	if permission.Entity == entity && permission.Delete == perm {
		return true
	}
	return false
}