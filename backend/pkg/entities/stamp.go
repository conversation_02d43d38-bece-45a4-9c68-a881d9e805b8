package entities

import "github.com/google/uuid"

const (
	StampProviderTypeSha512 = 1
)

var StampProviderType = map[uint64]string{
	StampProviderTypeSha512: "sha-512",
}

type StampProvider struct {
	Base
	Audit
	Enabled        *bool     `json:"enabled"`
	MaxStampCount  uint64    `json:"max_stamp"`
	StampCount     uint64    `json:"stamp_count" gorm:"default:0"`
	ProviderType   uint64    `json:"provider_type"`
	ProviderHost   string    `json:"provider_host"`
	ProviderPort   string    `json:"provider_port"`
	ProviderUser   string    `json:"provider_user"`
	ProviderPass   string    `json:"provider_pass"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
}

func (s *StampProvider) GetTypeName() string {
	return StampProviderType[s.ProviderType]
}
