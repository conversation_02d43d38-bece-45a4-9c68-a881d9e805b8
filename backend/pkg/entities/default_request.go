package entities

import (
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type DefaultRequest struct {
	gorm.Model
	ID             uuid.UUID `json:"id" gorm:"primary_key"`
	Value          string    `json:"value"`
	SimulateID     uuid.UUID `json:"simulate_id"`
	OrganizationID uuid.UUID `json:"organization_id"`
	AdminID        uuid.UUID `json:"admin_id"`
	Type           uint      `json:"type"`
}

func (n *DefaultRequest) BeforeCreate(tx *gorm.DB) (err error) {
	n.ID = uuid.New()
	return
}
