package entities

import (
	"time"

	"github.com/google/uuid"
)

type Admin struct {
	Base
	Audit
	Suspended               *bool     `json:"suspended" gorm:"default:false" example:"false"`
	SuspendReason           string    `json:"suspend_reason" example:"reason"`
	Name                    string    `json:"name" example:"john doe"`
	FirstName               string    `json:"first_name" example:"john"`
	LastName                string    `json:"last_name" example:"doe"`
	Email                   string    `json:"email" gorm:"unique" example:"<EMAIL>"`
	Phone                   string    `json:"phone" example:"+1234567890"`
	Password                string    `json:"password" example:"**********"`
	Status                  string    `json:"status" gorm:"default:'active'" example:"active"` // active, inactive, deleted
	Locale                  string    `json:"locale" gorm:"default:'en'" example:"en"`         // en, tr
	Tfa                     *bool     `json:"tfa" gorm:"default:false" example:"false"`
	PinLogin                *bool     `json:"pin_login" gorm:"default:false" example:"false"`
	TfaKey                  string    `json:"tfa_key" example:"123456"`
	LastLoginTime           time.Time `json:"last_login_time" example:"2021-01-01T00:00:00Z"`
	LastLoginIP             string    `json:"last_login_ip" example:"127.0.0.1"`
	Agent                   string    `json:"agent" example:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"`
	IPWhitelist             string    `json:"ip_whitelist" example:"127.0.0.1"`
	AuthCode                string    `json:"auth_code" example:"12345678"`
	AuthCodeExpire          time.Time `json:"auth_code_expire" example:"2021-01-01T00:00:00Z"`
	ResetPasswordCode       string    `json:"reset_password_code" example:"12345678"`
	ResetPasswordCodeExpire time.Time `json:"reset_password_code_expire" example:"2021-01-01T00:00:00Z"`
	RoleID                  uuid.UUID `json:"role_id" gorm:"not null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID          uuid.UUID `json:"organization_id" gorm:"not null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}
