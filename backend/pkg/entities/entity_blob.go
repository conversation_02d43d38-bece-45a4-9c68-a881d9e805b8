package entities

import "github.com/google/uuid"

const (
	StampTypeTimestamp = 1
	StampTypeDekont    = 2
)

var StampTypeList = map[uint64]string{
	StampTypeTimestamp: "timestamp",
	StampTypeDekont:    "dekont",
}

const (
	StampStatusSuccess = 1
	StampStatusQueue   = 2
	StampStatusError   = 3
)

var StampStatusList = map[uint64]string{
	StampStatusSuccess: "success",
	StampStatusQueue:   "queue",
	StampStatusError:   "error",
}

type EntityBlob struct {
	Base
	Url             string    `json:"url"`    // s3 etc.
	Type            uint64    `json:"type"`   // timestamp, dekont
	Entity          string    `json:"entity"` // user, company
	Action          string    `json:"action"` // create, update, delete
	ContentSent     string    `json:"content_sent"`
	ContentReceived string    `json:"content_received"`
	Status          uint64    `json:"status" gorm:"default:1"` // 1: success, 2: queue, 3: error
	ReferenceID     string    `json:"reference_id"`
	IpAddress       string    `json:"ip_address"`
	UserAgent       string    `json:"user_agent"`
	ClientID        string    `json:"client_id"`
	OrganizationID  uuid.UUID `json:"organization_id" gorm:"type:uuid;"`
}
