package entities

import (
	"github.com/google/uuid"
)

type Organization struct {
	Base
	Audit

	TenantID       uint64           `json:"tenant_id"`
	Name           string           `json:"name" example:"monopayment"`
	Token          string           `json:"token" example:"monopayment"`
	Logo           string           `json:"logo" example:"https://monopayment.com/logo.png"`
	Favicon        string           `json:"favicon" example:"https://monopayment.com/favicon.ico"`
	ParentID       uuid.UUID        `json:"parent_id,omitempty" gorm:"default:null" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Main           *bool            `json:"main" gorm:"default:false" example:"false"`
	PublicStatus   int              `json:"public_status" gorm:"default:1" example:"1"`
	DomainAddress  string           `json:"domain_address" gorm:"default:null" example:"monopayments.com"`
	CheckoutDomain string           `json:"checkout_domain" gorm:"default:null" example:"https://monopayment.com"`
	Timezone       string           `json:"timezone" gorm:"default:Etc/UTC" example:"UTC"`
	Simulate       []*Simulate      `json:"simulates"`
}
