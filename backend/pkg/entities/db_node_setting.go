package entities

import (
	"time"

	"github.com/google/uuid"
)

type DBNodeSetting struct {
	Base
	Key                string    `json:"key"`
	DSN                string    `json:"dsn"`
	DBEngine           uint      `json:"db_engine"`             // 1: mysql, 2: postgres ...
	LastDSNCheckStatus uint      `json:"last_dsn_check_status"` // 0: success, 1: fail 2: unknown
	LastDSNCheckAt     time.Time `json:"last_dsn_check_at"`
	OrganizationID     uuid.UUID `json:"organization_id"`
	AdminID            uuid.UUID `json:"admin_id"`
}
