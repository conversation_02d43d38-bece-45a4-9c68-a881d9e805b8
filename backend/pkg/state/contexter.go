package state

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func GetContextValueAsUUID(c *gin.Context, key string) uuid.UUID {
	value := c.GetString(key)
	return uuid.MustParse(value)
}

func GetContextValueAsString(c *gin.Context, key string) string {
	return c.GetString(key)
}

func GetContextValueAsBool(c *gin.Context, key string) bool {
	value := c.GetBool(key)
	return value
}
