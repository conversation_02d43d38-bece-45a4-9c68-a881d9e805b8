package state

import (
	"context"

	"github.com/google/uuid"
)

const (
	CurrentAdminId       = "CurrentAdminId"
	CurrentAdminToken    = "CurrentAdminToken"
	CurrentOrgId         = "CurrentOrgId"
	CurrentUserIP        = "CurrentIP"
	CurrentAdminAgentCTX = "CurrentAdminAgent"
	MainOrg              = "MainOrg"
)

func CurrentAdminUser(ctx context.Context) uuid.UUID {
	value := ctx.Value(CurrentAdminId)
	if value == nil {
		return uuid.Nil
	}
	return uuid.MustParse(value.(string))
}

func CurrentAdminOrganization(ctx context.Context) uuid.UUID {
	value := ctx.Value(CurrentOrgId)
	if value == nil {
		return uuid.Nil
	}
	return uuid.MustParse(value.(string))
}

func AmIAuthorized(ctx context.Context) bool {
	value := ctx.Value(MainOrg)
	if value == nil {
		return false
	}
	return value.(bool)
}

func CurrentIP(ctx context.Context) string {
	value := ctx.Value(CurrentUserIP)
	if value == nil {
		return ""
	}
	return value.(string)
}

func CurrentAdminAgent(ctx context.Context) string {
	value := ctx.Value(CurrentAdminAgentCTX)
	if value == nil {
		return ""
	}
	return value.(string)
}

func GetCurrentAdminToken(ctx context.Context) string {
	value := ctx.Value(CurrentAdminToken)
	if value == nil {
		return ""
	}
	return value.(string)
}
