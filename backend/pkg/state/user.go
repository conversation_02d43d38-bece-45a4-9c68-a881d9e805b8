package state

import (
	"context"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/contexter"
)

func CurrentUser(c context.Context) uuid.UUID {
	value := c.Value(contexter.ContextKeyCurrentUserId)
	if value != nil {
		return uuid.MustParse(value.(string))
	}
	return uuid.Nil
}

func CurrentUserEmail(c context.Context) string {
	value := c.Value(contexter.ContextKeyCurrentUserEmail)
	if value != nil {
		return value.(string)
	}
	return ""
}

func CurrentUserOrganization(c context.Context) uuid.UUID {
	value := c.Value(contexter.ContextKeyCurrentUserOrgId)
	if value != nil {
		return uuid.MustParse(value.(string))
	}
	return uuid.Nil
}

func CurrentUserOrganizationName(c context.Context) string {
	value := c.Value(contexter.ContextKeyCurrentUserOrg)
	if value != nil {
		return value.(string)
	}
	return ""
}

func CurrentGRPCIP(c context.Context) string {
	value := c.Value(contexter.ContextKeyCurrentIP)
	if value != nil {
		return value.(string)
	}
	return ""
}

func CurrentUserIsOrganizationUser(c context.Context) bool {
	IsOrganizationUser := c.Value(contexter.ContextKeyCurrentUserIsOrganizationUser)
	if IsOrganizationUser != nil {
		return IsOrganizationUser.(bool)
	}
	return false
}

func CurrentUserAgent(c context.Context) string {
	value := c.Value(contexter.ContextKeyUserAgent)
	if value != nil {
		return value.(string)
	}
	return ""
}

func CurrentUserScope(c context.Context) string {
	value := c.Value(contexter.ContextKeyUserScope)
	if value != nil {
		return value.(string)
	}
	return ""
}

func CurrentUserOrgTimeZone(c context.Context) string {
	value := c.Value(contexter.ContextKeyCurrentUserOrgTimeZone)
	if value != nil {
		return value.(string)
	}
	return ""
}

func CurrentUserIsApiUser(c context.Context) bool {
	IsApi := c.Value(contexter.ContextKeyIsApi)
	if IsApi != nil {
		return IsApi.(bool)
	}
	return false
}

func CurrentUserReferenceID(c context.Context) string {
	value := c.Value(contexter.ContextKeyReferenceID)
	if value != nil {
		return value.(string)
	}
	return ""
}
