package state

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/contexter"
)

// func CurrentApiOrg(c context.Context) uuid.UUID {
// 	value := c.Value("CurrentApiORG").(string)
// 	return uuid.MustParse(value)
// }

func CurrentApiKey(c context.Context) string {
	value := c.Value(contexter.ContextKeyMonoKey).(string)
	return value
}

func CurrentApiSecret(c context.Context) string {
	value := c.Value(contexter.ContextKeyMonoSecret).(string)
	return value
}
