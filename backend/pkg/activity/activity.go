package activity

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/domains/activity"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
)

func NewActivity(activity_type, entity, ipaddres string, ctx context.Context, t trace.Tracer) {
	db := database.ClientDB()
	activityService := activity.NewService(activity.NewRepo(db))
	activity := dtos.ActivityCreateDTO{
		AdminID:        state.CurrentAdminUser(ctx),
		IpAddress:      ipaddres,
		Action:         activity_type,
		Entity:         entity,
		OrganizationID: state.CurrentUserOrganization(ctx),
	}
	activityService.Create(&activity, ctx, t)
}
