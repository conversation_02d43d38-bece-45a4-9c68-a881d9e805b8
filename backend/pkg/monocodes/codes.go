package monocodes

import (
	"errors"
	"fmt"
)

const (
	Unauthenticated                                                uint64 = 16
	TokenNotActive                                                 uint64 = 17
	TokenExpired                                                   uint64 = 18
	EmailNotVerified                                               uint64 = 100
	MobileNotVerified                                              uint64 = 120
	KycNotVerified                                                 uint64 = 130
	UserSuspended                                                  uint64 = 140
	AuthCheckTokenKycProviderNotFound                              uint64 = 1000
	AuthCheckTokenKycProviderFindFailed                            uint64 = 1001
	AuthCheckTokenKycProviderSecretNotFound                        uint64 = 1002
	AuthCheckTokenKycCheckFailed                                   uint64 = 1003
	BankAccountCreatePermissionDenied                              uint64 = 100000
	BankAccountCreateFailed                                        uint64 = 100010
	BankAccountCreateIbanRequired                                  uint64 = 100020
	BankAccountCreateNameRequired                                  uint64 = 100030
	BankAccountCreateCurrencyRequired                              uint64 = 100040
	BankAccountCreateTitleRequired                                 uint64 = 100045
	BankAccountCreateBankNotFound                                  uint64 = 100050
	BankAccountCreateBankFindFailed                                uint64 = 100060
	BankAccountCreateCurrencyNotFound                              uint64 = 100070
	BankAccountCreateCurrencyFindFailed                            uint64 = 100080
	BankAccountCreateIbanInvalid                                   uint64 = 100081
	BankAccountCreateFindDuplicateFromNameFailed                   uint64 = 100090
	BankAccountCreateNameNotUnique                                 uint64 = 100100
	BankAccountCreateFindDuplicateFromIbanFailed                   uint64 = 100110
	BankAccountCreateIbanNotUnique                                 uint64 = 100120
	BankAccountCreateSuccess                                       uint64 = 100130
	BankAccountListPermissionDenied                                uint64 = 100200
	BankAccountListFailed                                          uint64 = 100210
	BankAccountBankNotFound                                        uint64 = 100220
	BankAccountUpdatePermissionDenied                              uint64 = 100300
	BankAccountUpdateFailed                                        uint64 = 100310
	BankAccountUpdateSuccess                                       uint64 = 100320
	BankAccountUpdateFindDuplicateFromNameFailed                   uint64 = 100330
	BankAccountUpdateNameNotUnique                                 uint64 = 100340
	BankAccountGetPermissionDenied                                 uint64 = 100400
	BankAccountGetNotFound                                         uint64 = 100410
	BankAccountGetFailed                                           uint64 = 100420
	BankAccountDeletePermissionDenied                              uint64 = 100500
	BankAccountDeleteNotFound                                      uint64 = 100510
	BankAccountDeleteFailed                                        uint64 = 100520
	BankAccountDeleteSuccess                                       uint64 = 100530
	OrganizationBankAccountsGetPermissionDenied                    uint64 = 100600
	OrganizationBankAccountsGetWalletIdRequired                    uint64 = 100610
	OrganizationBankAccountsGetWalletNotFound                      uint64 = 100620
	OrganizationBankAccountsGetWalletFailed                        uint64 = 100630
	OrganizationBankAccountsGetNotFound                            uint64 = 100640
	OrganizationBankAccountsGetFailed                              uint64 = 100650
	OrderCreatePermissionDenied                                    uint64 = 100700
	OrderCreateBasketItemAmountNotEqualToBasketItemAmount          uint64 = 100701
	OrderCreateOrganizationNotFound                                uint64 = 100710
	OrderCreateOrganizationFailed                                  uint64 = 100720
	OrderCreateOrderPaymentTermCreateFailed                        uint64 = 100730
	OrderCreateOrderPaymentTermAmountNotEqualToOrderAmount         uint64 = 100740
	OrderCreateRelationCreateFailed                                uint64 = 100750
	OrderCreateGetOrganizationForCheckoutCustomizationFailed       uint64 = 100760
	OrderCreatePaymentPageCreateFailed                             uint64 = 100770
	OrderCreateOrderPaymentPageUpdateFailed                        uint64 = 100780
	OrderCreateFailed                                              uint64 = 100790
	OrderCreateSuccess                                             uint64 = 100800
	OrderGetNotFound                                               uint64 = 100810
	OrderGetFailed                                                 uint64 = 100820
	OrderStatusStreamGetOrderFailed                                uint64 = 100830
	OrderStatusStreamGetOrderRelationFailed                        uint64 = 100840
	OrderStatusStreamGetOrganizationFailed                         uint64 = 100850
	OrderStatusStreamGetOrderNotFound                              uint64 = 100860
	OrderStatusStreamSendFailed                                    uint64 = 100870
	OrderRetryOrderNotFound                                        uint64 = 100880
	OrderRetryOrderAlreadyPaid                                     uint64 = 100890
	OrderRetryOrderRelationNotFound                                uint64 = 100900
	OrderRetryOrderFailed                                          uint64 = 100910
	OrderRetryOrderRelationFailed                                  uint64 = 100920
	OrderRetryOrderGetOrganizationFailed                           uint64 = 100930
	OrderRetryOrderUpdateOrderFailed                               uint64 = 100940
	OrderRetryOrderUpdateOrderRelationFailed                       uint64 = 100950
	OrderPayOrderCardNotFound                                      uint64 = 100960
	OrderPayOrderCardFailed                                        uint64 = 100970
	OrderPayOrderOrganizationNotAllowPayment                       uint64 = 100980
	OrderPayOrderStatusNotWaitingForPayment                        uint64 = 100990
	OrderPayOrderVposCurrencyNotFound                              uint64 = 101000
	OrderPayOrderVposCurrencyGetFailed                             uint64 = 101010
	OrderPayOrderVposGetFailed                                     uint64 = 101020
	OrderPayOrderCardSchemeGetFailed                               uint64 = 101030
	OrderPayOrderCardSchemeNotAllowed                              uint64 = 101040
	OrderPayOrderUserWalletCreateFailed                            uint64 = 101050
	OrderPayOrderUpdateOrderRelationFailed                         uint64 = 101060
	OrderPayOrderPaymentTermGetFailed                              uint64 = 101070
	OrderPayOrderVposMarketplaceNotActive                          uint64 = 101080
	OrderPayOrderCreateOrderDetailFailed                           uint64 = 101090
	OrderPayOrderCreateOrderAcquirerTryFailed                      uint64 = 101100
	OrderPayOrderCommitFailed                                      uint64 = 101110
	OrderGetOrderInfoOrderNotFound                                 uint64 = 101120
	OrderGetOrderInfoFailed                                        uint64 = 101130
	OrderGetOrderInfoOrganizationNotFound                          uint64 = 101140
	OrderGetOrderInfoOrganizationFailed                            uint64 = 101150
	OrderOrderDetailOrderNotFound                                  uint64 = 101160
	OrderOrderDetailFailed                                         uint64 = 101170
	OrderOrderDetailOrganizationNotFound                           uint64 = 101180
	OrderOrderDetailOrganizationFailed                             uint64 = 101190
	OrderOrderDetailPaymentPageNotFound                            uint64 = 101200
	OrderOrderDetailPaymentPageFailed                              uint64 = 101210
	OrderOrderDetailPaymentTermsFailed                             uint64 = 101220
	OrderOrderPaymentDetailOrderDetailNotFound                     uint64 = 101230
	OrderOrderPaymentDetailFailed                                  uint64 = 101240
	OrderGetOrderListFailed                                        uint64 = 101250
	OrderGetOrderTXsGetOrderNotFound                               uint64 = 101260
	OrderGetOrderTXsGetOrderFailed                                 uint64 = 101270
	OrderGetOrderTXsFailed                                         uint64 = 101280
	OrderGetOrderTXsSenderWalletNotFound                           uint64 = 101290
	OrderGetOrderTXsSenderWalletFailed                             uint64 = 101300
	OrderGetOrderTXsReceiverWalletNotFound                         uint64 = 101310
	OrderGetOrderTXsReceiverWalletFailed                           uint64 = 101320
	OrderRefundOrderPermissionDenied                               uint64 = 101330
	OrderRefundGetOrderNotFound                                    uint64 = 101340
	OrderRefundGetOrderFailed                                      uint64 = 101350
	OrderRefundOrderTypeDeposit                                    uint64 = 101355
	OrderRefundOrderStatusIsNotPaid                                uint64 = 101360
	OrderRefundAmountIsCannotBeGreaterThanOrderAmount              uint64 = 101370
	OrderRefundOrderRelationNotFound                               uint64 = 101380
	OrderRefundOrderRelationFailed                                 uint64 = 101390
	OrderRefundVPOSNotFound                                        uint64 = 101400
	OrderRefundVPOSFailed                                          uint64 = 101410
	OrderRefundOrderDetailNotFound                                 uint64 = 101415
	OrderRefundOrderDetailFailed                                   uint64 = 101416
	OrderRefundOrderFailed                                         uint64 = 101420
	OrderRefundOrganizationWalletNotFound                          uint64 = 101430
	OrderRefundOrganizationWalletFailed                            uint64 = 101440
	OrderRefundPosWalletNotFound                                   uint64 = 101450
	OrderRefundPosWalletFailed                                     uint64 = 101460
	OrderRefundUserCardWalletNotFound                              uint64 = 101470
	OrderRefundUserCardWalletFailed                                uint64 = 101480
	OrderRefundOrderUpdateOrganizationWalletFailed                 uint64 = 101490
	OrderRefundOrderUpdatePosWalletFailed                          uint64 = 101500
	OrderRefundOrderCreateTxPosToUserFailed                        uint64 = 101510
	OrderRefundOrderUpdateUserCardWalletFailed                     uint64 = 101520
	OrderRefundOrderUpdateOrderFailed                              uint64 = 101530
	OrderCancelOrderPermissionDenied                               uint64 = 101540
	OrderCancelOrderGetOrderNotFound                               uint64 = 101550
	OrderCancelOrderGetOrderFailed                                 uint64 = 101560
	OrderCancelCannotCancelDepositOrder                            uint64 = 101565
	OrderCancelOrderGetUserNotFound                                uint64 = 101570
	OrderCancelOrderGetUserFailed                                  uint64 = 101580
	OrderCancelOrderIsNotPaid                                      uint64 = 101590
	OrderCancelOrderGetOrderRelationNotFound                       uint64 = 101600
	OrderCancelOrderGetOrderRelationFailed                         uint64 = 101610
	OrderCancelOrderGetVPOSNotFound                                uint64 = 101620
	OrderCancelOrderGetVPOSFailed                                  uint64 = 101630
	OrderCancelOrderFailed                                         uint64 = 101640
	OrderCancelOrderGetOrganizationWalletNotFound                  uint64 = 101650
	OrderCancelOrderGetOrganizationWalletFailed                    uint64 = 101660
	OrderCancelOrderGetPosWalletNotFound                           uint64 = 101670
	OrderCancelOrderGetPosWalletFailed                             uint64 = 101680
	OrderCancelOrderGetUserCardWalletNotFound                      uint64 = 101690
	OrderCancelOrderGetUserCardWalletFailed                        uint64 = 101700
	OrderCancelOrderCreateTxOrgToPosFailed                         uint64 = 101710
	OrderCancelOrderUpdateOrganizationWalletFailed                 uint64 = 101720
	OrderCancelOrderUpdatePosWalletFailed                          uint64 = 101730
	OrderCancelOrderCreateTxPosToUserFailed                        uint64 = 101740
	OrderCancelOrderUpdateUserCardWalletFailed                     uint64 = 101750
	OrderCancelOrderUpdateOrderFailed                              uint64 = 101760
	OrderPaymentDetailRequestInvalidArgument                       uint64 = 101770
	OrderPaymentDetailRequestNotFound                              uint64 = 101780
	OrderPaymentDetailRequestFailed                                uint64 = 101790
	CardListPermissionDenied                                       uint64 = 101800
	CardListNotFound                                               uint64 = 101810
	CardListFailed                                                 uint64 = 101820
	CardListTokenizationServiceListFailed                          uint64 = 101830
	CardListTokenizationServiceListNotFound                        uint64 = 101840
	CardGetPermissionDenied                                        uint64 = 101850
	CardGetNotFound                                                uint64 = 101860
	CardGetFailed                                                  uint64 = 101870
	CardGetIdRequired                                              uint64 = 101880
	CardCreatePermissionDenied                                     uint64 = 101890
	CardCreateConversationIdAlreadyExists                          uint64 = 101895
	CardCreateFailed                                               uint64 = 101900
	CardCreateUserNotFound                                         uint64 = 101910
	CardCreateSuccess                                              uint64 = 101920
	CardUpdatePermissionDenied                                     uint64 = 101930
	CardUpdateFailed                                               uint64 = 101940
	CardUpdateCardNotFound                                         uint64 = 101950
	CardUpdateSuccess                                              uint64 = 101960
	CardDeleteCardNotFound                                         uint64 = 101970
	CardDeleteFailed                                               uint64 = 101980
	CardDeleteSuccess                                              uint64 = 101990
	TokenizationServiceListPermissionDenied                        uint64 = 102000
	TokenizationServiceListNotFound                                uint64 = 102010
	TokenizationServiceListFailed                                  uint64 = 102020
	TokenizationServiceCardListFailed                              uint64 = 102030
	TokenCreateFailed                                              uint64 = 110000
	TokenCreatePermissionDenied                                    uint64 = 110010
	TokenCreateExpireDateBetweenZeroTo9999Days                     uint64 = 110020
	TokenCreateApiKeyNotFound                                      uint64 = 110030
	TokenCreateApiKeyUserNotFound                                  uint64 = 110040
	TokenCreateApiKeyUserOrganizationNotFound                      uint64 = 110050
	TokenCreateInvalidIpAddress                                    uint64 = 110051
	TokenCreateApiUserCreateFailed                                 uint64 = 110060
	TokenCreateApiUserGetFailed                                    uint64 = 110070
	TokenCreateFailedJwtGeneration                                 uint64 = 110080
	TokenCreateSuccess                                             uint64 = 110090
	TokenListPermissionDenied                                      uint64 = 110100
	TokenListFailed                                                uint64 = 110110
	TokenEnablePermissionDenied                                    uint64 = 110120
	TokenEnableTokenNotFound                                       uint64 = 110130
	TokenEnableFindTokenFailed                                     uint64 = 110140
	TokenEnableFailed                                              uint64 = 110150
	TokenEnableSuccess                                             uint64 = 110160
	TokenDisablePermissionDenied                                   uint64 = 110200
	TokenDisableTokenNotFound                                      uint64 = 110210
	TokenDisableFindTokenFailed                                    uint64 = 110220
	TokenDisableFailed                                             uint64 = 110230
	TokenDisableSuccess                                            uint64 = 110240
	TokenDeletePermissionDenied                                    uint64 = 110300
	TokenDeleteTokenNotFound                                       uint64 = 110310
	TokenDeleteFindTokenFailed                                     uint64 = 110320
	TokenDeleteFailed                                              uint64 = 110330
	TokenDeleteSuccess                                             uint64 = 110340
	ApiKeyGeneratePermissionDenied                                 uint64 = 110400
	ApiKeyGenerateFailed                                           uint64 = 110410
	TransactionListPermissionDenied                                uint64 = 120000
	TransactionListFailed                                          uint64 = 120010
	TransactionListSenderWalletNotFound                            uint64 = 120020
	TransactionListSenderWalletFindFailed                          uint64 = 120030
	TransactionListReceiverWalletNotFound                          uint64 = 120040
	TransactionListReceiverWalletFindFailed                        uint64 = 120050
	TransactionShortcutNotFound                                    uint64 = 120060
	TransactionShortcutListFailed                                  uint64 = 120070
	TransactionShortcutListSenderWalletNotFound                    uint64 = 120080
	TransactionShortcutListSenderWalletFindFailed                  uint64 = 120090
	TransactionShortcutListReceiverWalletNotFound                  uint64 = 120100
	TransactionShortcutListReceiverWalletFindFailed                uint64 = 120110
	TransactionBankWithdrawWalletNotFound                          uint64 = 120120
	TransactionBankWithdrawWalletFindFailed                        uint64 = 120130
	TransactionBankWithdrawPermissionDenied                        uint64 = 120135
	TransactionBankWithdrawUserNotFound                            uint64 = 120140
	TransactionBankWithdrawUserFindFailed                          uint64 = 120150
	TransactionBankWithdrawOrganizationNotFound                    uint64 = 120160
	TransactionBankWithdrawOrganizationFindFailed                  uint64 = 120170
	TransactionBankWithdrawBankAccountNotFound                     uint64 = 120180
	TransactionBankWithdrawBankAccountFindFailed                   uint64 = 120190
	TransactionBankWithdrawBankAccountCreateFailed                 uint64 = 120195
	TransactionBankWithdrawWalletCreateFailed                      uint64 = 120196
	TransactionBankWithdrawAmountLessThanZero                      uint64 = 120200
	TransactionBankWithdrawAmountLessThanWalletBalance             uint64 = 120210
	TransactionBankWithdrawTxCreateFailed                          uint64 = 120220
	TransactionBankWithdrawShortcutCreateFailed                    uint64 = 120225
	TransactionBankWithdrawSuccess                                 uint64 = 120230
	TransactionNotFound                                            uint64 = 120240
	TransactionGetFailed                                           uint64 = 120250
	TransactionGetTXCurrencyNotFound                               uint64 = 120260
	TransactionGetTXCurrencyFindFailed                             uint64 = 120270
	TransactionW2WSendSenderWalletNotFound                         uint64 = 120280
	TransactionW2WSendUserLimitFindFailed                          uint64 = 120281
	TransactionW2WSendConversationIDAlreadyExists                  uint64 = 120282
	TransactionW2WSendOtpCodeUsed                                  uint64 = 120283
	TransactionW2WSendOtpCodeInvalid                               uint64 = 120284
	TransactionW2WCantSameSenderAndReceiver                        uint64 = 120285
	TransactionW2WSendSenderWalletBalanceZero                      uint64 = 120286
	TransactionW2WSendSenderWalletFindFailed                       uint64 = 120290
	TransactionW2WSendSenderWalletNotBelongToUser                  uint64 = 120295
	TransactionW2WSendSenderWalletNotBelongToOrganization          uint64 = 120296
	TransactionW2WSendReceiverWalletNotFound                       uint64 = 120300
	TransactionW2WSendReceiverWalletFindFailed                     uint64 = 120310
	TransactionW2WSendLimitNotFound                                uint64 = 120315
	TransactionW2WSendLimitFindFailed                              uint64 = 120316
	TransactionW2WSendDifferentCurrency                            uint64 = 120320
	TransactionW2WSendAmountNeedGreaterThanZero                    uint64 = 120330
	TransactionW2WSendAmountGreaterThanWalletBalance               uint64 = 120340
	TransactionW2WSendMade                                         uint64 = 120345
	TransactionW2WSendOtpCodeRequired                              uint64 = 120346
	TransactionShortcutGetFailed                                   uint64 = 120350
	TransactionShortcutUpdateFailed                                uint64 = 120360
	TransactionShortcutDeleteFailed                                uint64 = 120370
	TransactionShortcutDeleteSuccess                               uint64 = 120380
	TransactionShortcutCloneFailed                                 uint64 = 120390
	WalletGetPermissionDenied                                      uint64 = 120900
	WalletGetWalletNotFound                                        uint64 = 120910
	WalletGetFailed                                                uint64 = 120920
	WalletCreatePermissionDenied                                   uint64 = 130000
	WalletConversationIDAlreadyExists                              uint64 = 130001
	WalletCreateFailed                                             uint64 = 130010
	WalletAlreadyExists                                            uint64 = 130020
	WalletCreateSuccess                                            uint64 = 130030
	WalletUpdatePermissionDenied                                   uint64 = 130040
	WalletUpdateWalletNotFound                                     uint64 = 130050
	WalletUpdateFailed                                             uint64 = 130060
	WalletUpdateWaasProviderNotFound                               uint64 = 130070
	WalletUpdateGetWaasWalletFailed                                uint64 = 130080
	WalletUpdateWaasPersonalAccountCreateFailed                    uint64 = 130090
	WalletUpdateSuccess                                            uint64 = 130100
	WalletListPermissionDenied                                     uint64 = 130110
	WalletListWalletNotFound                                       uint64 = 130120
	WalletListFindFailed                                           uint64 = 130130
	CurrencyListFailed                                             uint64 = 130140
	OrganizationUserCreatePermissionDenied                         uint64 = 140000
	OrganizationCreateUserConversationIdAlreadyExists              uint64 = 140001
	OrganizationUserCreateOrganizationNotFound                     uint64 = 140010
	OrganizationUserCreateOrganizationFindFailed                   uint64 = 140020
	OrganizationUserCreateUserAlreadyExists                        uint64 = 140030
	OrganizationUserCreateUserCreateFailed                         uint64 = 140040
	OrganizationUserCreateLimitUserCreateFailed                    uint64 = 140045
	OrganizationUserCreateSuccess                                  uint64 = 140050
	OrganizationUserTokenCreatePermissionDenied                    uint64 = 140060
	OrganizationUserTokenCreateOrganizationNotFound                uint64 = 140070
	OrganizationUserTokenCreateOrganizationFindFailed              uint64 = 140080
	OrganizationUserTokenCreateUserNotFound                        uint64 = 140090
	OrganizationUserTokenCreateUserFindFailed                      uint64 = 140100
	OrganizationUserTokenCreateTokenGenerateFailed                 uint64 = 140110
	OrganizationUserTokenCreateTokenCreateFailed                   uint64 = 140120
	OrganizationUserTokenCreateSuccess                             uint64 = 140130
	TermSequenceMustBeOnlyInteger                                  uint64 = 140140
	OrganizationBusinessCreateAccountCreateFailed                  uint64 = 140150
	OrganizationBusinessCreateAccountMetaCreateFailed              uint64 = 140160
	OrganizationBusinessCreateAccountCreateSuccess                 uint64 = 140170
	CardIssueSuccess                                               uint64 = 150000
	CardIssuePermissionDenied                                      uint64 = 150010
	CardIssueFailed                                                uint64 = 150020
	CardIssueUserNotFound                                          uint64 = 150030
	UserUpdateSucces                                               uint64 = 150040
	UserValidationNameFail                                         uint64 = 150050
	UserValidationSurNameFail                                      uint64 = 150060
	UserValidationFirstNameFail                                    uint64 = 150070
	UserValidationLastNameFail                                     uint64 = 150080
	CompanyCreateSuccess                                           uint64 = 150090
	CompanyCreateFail                                              uint64 = 160000
	CompanyCreatePermissionDenied                                  uint64 = 160010
	CardUpdateInvalidPIN                                           uint64 = 160020
	CardUpdateInvalidLimit                                         uint64 = 150100
	OrderCreateInvalidAmount                                       uint64 = 150110
	OrderCreateBasketItemInvalidPrice                              uint64 = 150120
	OrderCreateBasketItemInvalidQuantity                           uint64 = 150130
	OrderCreateInvalidTaxAmount                                    uint64 = 150140
	OrderCreateInvalidEmail                                        uint64 = 150150
	OrganizationCreateUserInvalidEmail                             uint64 = 150160
	OrderCreateBuyerInvalidEmail                                   uint64 = 150170
	OrderCreateSubOrganizationInvalidEmail                         uint64 = 150180
	OrganizationCreateUserEmailIsRequired                          uint64 = 150190
	MagicLoginInvalidEmail                                         uint64 = 150200
	MagicTestLoginInvalidEmail                                     uint64 = 150210
	TFAuthInvalidEmail                                             uint64 = 150220
	RegisterInvalidEmail                                           uint64 = 150230
	CompanyUpdateSuccess                                           uint64 = 150450
	CompanyUpdateFail                                              uint64 = 150460
	CompanyUpdatePermissionDenied                                  uint64 = 150470
	OfficierBirthDayError                                          uint64 = 150480
	OfficierGetAppoitedAtError                                     uint64 = 150400
	OfficierGetResignedAtError                                     uint64 = 150410
	ShareholderPeopleBirthDayError                                 uint64 = 150420
	ShareholderPeopleGetAppoitedAtError                            uint64 = 150430
	ShareholderPeopleGetResignedAtError                            uint64 = 150440
	TransactionDepositUserNotFound                                 uint64 = 150240
	TransactionDepositUserFindFailed                               uint64 = 150250
	TransactionDepositOrderCreateFailed                            uint64 = 150260
	TransactionDepositStarted                                      uint64 = 150270
	TransactionDepositWalletNotFound                               uint64 = 150280
	TransactionDepositWalletFindFailed                             uint64 = 150290
	TransactionDepositWalletCurrencyNotFound                       uint64 = 150300
	TransactionDepositWalletCurrencyFindFailed                     uint64 = 150310
	CurrencyNotFound                                               uint64 = 150320
	CurrencyDisabled                                               uint64 = 150321
	MetaNameNotFound                                               uint64 = 150330
	MetaDataNotFound                                               uint64 = 150340
	CardAlreadyExist                                               uint64 = 150350
	TwoDOrderCallbackFailed                                        uint64 = 150360
	FeeListFailed                                                  uint64 = 150500
	FeeNotFound                                                    uint64 = 150510
	FeeCreateSuccess                                               uint64 = 150520
	FeeCreateFailed                                                uint64 = 150530
	FeeCreatePermissionDenied                                      uint64 = 150540
	FeeUpdateSuccess                                               uint64 = 150550
	FeeUpdateFailed                                                uint64 = 150560
	FeeUpdatePermissionDenied                                      uint64 = 150570
	FeeDeleteSuccess                                               uint64 = 150580
	FeeDeleteFailed                                                uint64 = 150590
	FeeDeletePermissionDenied                                      uint64 = 150600
	FeeCalculateFailed                                             uint64 = 150610
	FeeUserIsNotOrganizationUser                                   uint64 = 150620
	FeeCreateInvalidName                                           uint64 = 150630
	FeeCreateInvalidAction                                         uint64 = 150640
	FeeCreateInvalidAmount                                         uint64 = 150650
	FeeCreateInvalidPercentage                                     uint64 = 150660
	FeeUpdateInvalidName                                           uint64 = 150670
	FeeUpdateInvalidAction                                         uint64 = 150680
	FeeUpdateInvalidAmount                                         uint64 = 150690
	FeeUpdateInvalidPercentage                                     uint64 = 150700
	AddDeviceFailed                                                uint64 = 150710
	AddIpFailed                                                    uint64 = 150720
	AddTrustedDeviceSuccess                                        uint64 = 150730
	UpdateDeviceFailed                                             uint64 = 150740
	AddTrustedIpSuccess                                            uint64 = 150750
	DeleteTrustedDeviceSuccess                                     uint64 = 150760
	DeleteTrustedIpSuccess                                         uint64 = 150770
	ProfileCreatePermissionDenied                                  uint64 = 150780
	ProfileGetPermissionDenied                                     uint64 = 150790
	ProfileDeletePermissionDenied                                  uint64 = 150800
	ProfileUpdatePermissionDenied                                  uint64 = 150812
	DeviceAlreadyExist                                             uint64 = 150810
	IpAlreadyExist                                                 uint64 = 150820
	TransactionShortcutSameReceiverAndSender                       uint64 = 150830
	BankAccountTakeConsentFailed                                   uint64 = 150840
	BankAccountTakeConsentPermissionDenied                         uint64 = 150850
	BankAccountTakeConsentSuccess                                  uint64 = 150860
	BankAccountTakeConsentBankAccountNotFound                      uint64 = 150870
	BankAccountCancelConsentBankAccountNotFound                    uint64 = 150880
	BankAccountCancelConsentFailed                                 uint64 = 150890
	BankAccountCancelConsentConsentNotFound                        uint64 = 150900
	BankAccountCancelConsentSuccess                                uint64 = 150910
	BankAccountGetConsentStatusConsentNotFound                     uint64 = 150920
	BankAccountGetConsentStatusFailed                              uint64 = 150930
	TransactionBankWithdrawIbanCannotBeEmpty                       uint64 = 150940
	TransactionBankWithdrawIbanIsNotValid                          uint64 = 150950
	TransactionBankWithdrawBankAccountTitleCannotBeEmpty           uint64 = 150960
	TransactionBankWithdrawBankAccountTitleIsNotValid              uint64 = 150970
	TransactionBankWithdrawBankAccountIdOrIbanCannotBeEmpty        uint64 = 150980
	TransactionBankWithdrawConversationIdAlreadyExists             uint64 = 150985
	TransactionBankWithdrawBankAccountIdAndIbanCannotBeBothFilled  uint64 = 150990
	AuthSignUpInvalidEmail                                         uint64 = 152000
	AuthSignUpPasswordNotMatch                                     uint64 = 152010
	AuthSignUpPasswordNotStrong                                    uint64 = 152015
	AuthSignUpInvalidFirstName                                     uint64 = 152016
	AuthSignUpInvalidLastName                                      uint64 = 152017
	AuthSignUpUserAlreadyExists                                    uint64 = 152020
	AuthSignUpFailed                                               uint64 = 152030
	AuthSignUpSuccess                                              uint64 = 152040
	AuthSignUpSuccessLoginFailed                                   uint64 = 152050
	AuthActivateInvalidCode                                        uint64 = 152100
	AuthActivateUserNotFound                                       uint64 = 152110
	AuthActivateFailed                                             uint64 = 152120
	AuthActivateUserAlreadyActive                                  uint64 = 152125
	AuthActivateSuccess                                            uint64 = 152130
	AuthIdentifyInvalidEmail                                       uint64 = 152200
	AuthIdentifyUserNotFound                                       uint64 = 152210
	AuthIdentifyFailed                                             uint64 = 152220
	AuthIdentifyUserNotActive                                      uint64 = 152230
	AuthIdentifyUserIPNotAllowed                                   uint64 = 152240
	AuthIdentifySuccess                                            uint64 = 152250
	AuthAuthenticateEmailRequired                                  uint64 = 152300
	AuthAuthenticateInvalidEmail                                   uint64 = 152310
	AuthAuthenticatePasswordRequired                               uint64 = 152320
	AuthAuthenticateUserNotFound                                   uint64 = 152330
	AuthAuthenticateFailed                                         uint64 = 152340
	AuthAuthenticateUserIPNotAllowed                               uint64 = 152350
	AuthAuthenticateUserNotActive                                  uint64 = 152360
	AuthAuthenticateInvalidPassword                                uint64 = 152370
	AuthAuthenticateOrganizationNotFound                           uint64 = 152380
	AuthAuthenticateSuccess                                        uint64 = 152390
	AuthCreatePinEmailRequired                                     uint64 = 152400
	AuthCreatePinInvalidEmail                                      uint64 = 152410
	AuthCreatePinUserNotFound                                      uint64 = 152420
	AuthCreatePinFailed                                            uint64 = 152430
	AuthCreatePinUserNotActive                                     uint64 = 152440
	AuthCreatePinUserPinAuthNotActive                              uint64 = 152445
	AuthCreatePinUserIPNotAllowed                                  uint64 = 152450
	AuthCreatePinSuccess                                           uint64 = 152460
	AuthPinAuthEmailRequired                                       uint64 = 152500
	AuthPinAuthInvalidEmail                                        uint64 = 152510
	AuthPinAuthPinRequired                                         uint64 = 152520
	AuthPinAuthUserNotFound                                        uint64 = 152530
	AuthPinAuthUserNotActive                                       uint64 = 152540
	AuthPinAuthUserIPNotAllowed                                    uint64 = 152550
	AuthPinAuthPinExpired                                          uint64 = 152560
	AuthPinAuthFailed                                              uint64 = 152570
	AuthPinAuthSuccess                                             uint64 = 152580
	AuthTwoFactorAuthEmailRequired                                 uint64 = 152600
	AuthTwoFactorAuthInvalidEmail                                  uint64 = 152610
	AuthTwoFactorAuthCodeRequired                                  uint64 = 152620
	AuthTwoFactorAuthUserNotFound                                  uint64 = 152630
	AuthTwoFactorAuthFailed                                        uint64 = 152640
	AuthTwoFactorAuthUserNotActive                                 uint64 = 152650
	AuthTwoFactorAuthUserTwoFactorAuthNotActive                    uint64 = 152655
	AuthTwoFactorAuthUserIPNotAllowed                              uint64 = 152660
	AuthTwoFactorAuthInvalidCode                                   uint64 = 152670
	AuthTwoFactorAuthSuccess                                       uint64 = 152680
	AuthMagicLoginEmailRequired                                    uint64 = 152700
	AuthMagicLoginInvalidEmail                                     uint64 = 152710
	AuthMagicLoginUserNotFound                                     uint64 = 152720
	AuthMagicLoginFailed                                           uint64 = 152730
	AuthMagicLoginUserNotActive                                    uint64 = 152740
	AuthMagicLoginUserIPNotAllowed                                 uint64 = 152750
	AuthMagicLoginSuccess                                          uint64 = 152760
	AuthVerifyTokenTokenRequired                                   uint64 = 152800
	AuthVerifyTokenTokenExpired                                    uint64 = 152810
	AuthVerifyTokenInvalid                                         uint64 = 152820
	AuthVerifyTokenUserNotFound                                    uint64 = 152830
	AuthVerifyTokenUserIPNotAllowed                                uint64 = 152840
	AuthVerifyTokenUserDifferentAgent                              uint64 = 152850
	AuthVerifyTokenFailed                                          uint64 = 152860
	AuthForgotPasswordEmailRequired                                uint64 = 152900
	AuthForgotPasswordInvalidEmail                                 uint64 = 152910
	AuthForgotPasswordUserNotFound                                 uint64 = 152920
	AuthForgotPasswordUserNotActive                                uint64 = 152930
	AuthForgotPasswordUserAlreadySent                              uint64 = 152935
	AuthForgotPasswordUserIPNotAllowed                             uint64 = 152940
	AuthForgotPasswordFailed                                       uint64 = 152950
	AuthForgotPasswordSuccess                                      uint64 = 152960
	AuthResetPasswordEmailRequired                                 uint64 = 153000
	AuthResetPasswordInvalidEmail                                  uint64 = 153010
	AuthResetPasswordCodeRequired                                  uint64 = 153020
	AuthResetPasswordPasswordRequired                              uint64 = 153030
	AuthResetPasswordPasswordConfirmRequired                       uint64 = 153040
	AuthResetPasswordPasswordNotMatch                              uint64 = 153050
	AuthResetPasswordPasswordNotStrong                             uint64 = 153055
	AuthResetPasswordOtpRequired                                   uint64 = 153056
	AuthResetPasswordOtpInvalid                                    uint64 = 153057
	AuthResetPasswordUserNotFound                                  uint64 = 153060
	AuthResetPasswordFailed                                        uint64 = 153070
	AuthResetPasswordUserNotActive                                 uint64 = 153080
	AuthResetPasswordUserIPNotAllowed                              uint64 = 153090
	AuthResetPasswordCodeExpired                                   uint64 = 153100
	AuthResetPasswordSuccess                                       uint64 = 153110
	AuthLogoutTokenRequired                                        uint64 = 153120
	AuthLogoutSuccess                                              uint64 = 153130
	TransactionShortcutUpdateReceiverDescriptorCannotBeEmpty       uint64 = 153140
	TransactionShortcutUpdateReceiverIbanCannotBeEmpty             uint64 = 153150
	TransactionShortcutUpdateReceiverIbanIsNotValid                uint64 = 153160
	TransactionShortcutUpdateReceiverBankAccountTitleCannotBeEmpty uint64 = 153170
	OrderConversationIdAlreadyExists                               uint64 = 153180
	LimitMaxMonthlyTxnCountReached                                 uint64 = 154000
	LimitMaxDailyTxnCountReached                                   uint64 = 154010
	LimitMaxMonthlyTxnAmtReached                                   uint64 = 154020
	LimitMaxDailyTxnAmtReached                                     uint64 = 154030
	LimitMaxIncomeAmtReached                                       uint64 = 154040
	LimitMaxExpenseAmtReached                                      uint64 = 154050
	LimitMaxWithdrawalAmtReached                                   uint64 = 154060
	LimitMaxTopupAmtReached                                        uint64 = 154070
	LimitMaxWalletBalanceReached                                   uint64 = 154080
	LimitOneTimeMaxTopupAmtReached                                 uint64 = 154085
	LimitOneTimeMaxTxnAmtReached                                   uint64 = 154090
	LimitOneTimeMaxTxnCountReached                                 uint64 = 154100
	LimitOneTimeMaxWithdrawalAmtReached                            uint64 = 154101
	LimitTransactionAmountGreaterThanMaxTopupAmt                   uint64 = 154102
	LimitTransactionAmountGreaterThanMaxDailyTxnAmt                uint64 = 154110
	LimitTransactionAmountGreaterThanMaxMonthlyTxnAmt              uint64 = 154111
	LimitTransactionAmountGreaterThanMaxExpenseAmt                 uint64 = 154120
	LimitTransactionAmountGreaterThanOneTimeMaxTxnAmt              uint64 = 154121
	LimitTransactionAmountGreaterThanMaxIncomeAmt                  uint64 = 154122
	LimitMaxWalletCountReached                                     uint64 = 154130
	TransactionW2WSendWaasProviderNotFound                         uint64 = 155000
	TransactionW2WSendWaasProviderFindFailed                       uint64 = 155010
	TransactionW2WSendWaasUserNotFound                             uint64 = 155020
	TransactionW2WSendWaasUserFindFailed                           uint64 = 155030
	TransactionW2WSendWaasWalletNotFound                           uint64 = 155040
	TransactionW2WSendWaasWalletFindFailed                         uint64 = 155050
	OrderAddPaymentTermPermissionDenied                            uint64 = 156000
	OrderAddPaymentTermNotFound                                    uint64 = 156010
	OrderAddPaymentTermFailed                                      uint64 = 156020
	OrderAddPaymentTermCurrencyNotFound                            uint64 = 156030
	OrderAddPaymentTermCurrencyFailed                              uint64 = 156040
	OrderAddPaymentTermSuccess                                     uint64 = 156050
	OrderRemovePaymentTermPermissionDenied                         uint64 = 156060
	OrderRemovePaymentTermNotFound                                 uint64 = 156070
	OrderRemovePaymentTermFailed                                   uint64 = 156080
	OrderRemovePaymentTermIsPaid                                   uint64 = 156085
	OrderRemovePaymentTermSuccess                                  uint64 = 156090
	OrderUpdatePaymentTermPermissionDenied                         uint64 = 156100
	OrderUpdatePaymentTermNotFound                                 uint64 = 156110
	OrderUpdatePaymentTermFailed                                   uint64 = 156120
	OrderUpdatePaymentTermSuccess                                  uint64 = 156130
	OrderUpdatePermissionDenied                                    uint64 = 156140
	OrderUpdateOrderNotFound                                       uint64 = 156150
	OrderUpdateOrderFindFailed                                     uint64 = 156160
	OrderUpdateOrderSuccess                                        uint64 = 156170
	TokenApiUserDeleteFailed                                       uint64 = 157000
	TransferSendMoneyWalletNotFound                                uint64 = 170000
	TransferSendMoneyWalletFindFailed                              uint64 = 170010
	TransferSendMoneyQuotationFailed                               uint64 = 170020
	TransferSendMoneyInsufficientBalance                           uint64 = 170030
	TransferSendMoneyFailed                                        uint64 = 170040
	TransferSendMoneySuccess                                       uint64 = 170050
	TransferSendMoneyTransactionCreateFailed                       uint64 = 170060
	TransferSendMoneyLimitFailed                                   uint64 = 170070
	TransactionIssueMoneyConversationIDAlreadyExists               uint64 = 180000
	TransactionIssueMoneyWalletNotFound                            uint64 = 180010
	TransactionIssueMoneyWalletGetFailed                           uint64 = 180020
	TransactionIssueMoneyWalletCurrencyNotMatch                    uint64 = 180030
	TransactionIssueMoneyAmountMustBePositive                      uint64 = 180040
	TransactionIssueMoneyWalletUpdateFailed                        uint64 = 180050
	TransactionIssueMoneyReferenceIdNotValid                       uint64 = 180060
	TransactionIssueMoneyTransactionCreateFailed                   uint64 = 180070
	TransactionIssueMoneyTransactionCommitFailed                   uint64 = 180080
	TransactionIssueMoneySuccess                                   uint64 = 180090
	TransactionIssueMoneyPermissionDenied                          uint64 = 180100
	TransactionIssueMoneyOrganizationWalletGetFailed               uint64 = 180110
	TransactionBurnMoneyPermissionDenied                           uint64 = 181000
	TransactionBurnMoneyConversationIDAlreadyExists                uint64 = 181010
	TransactionBurnMoneyWalletNotFound                             uint64 = 181020
	TransactionBurnMoneyWalletGetFailed                            uint64 = 181030
	TransactionBurnMoneyWalletCurrencyNotMatch                     uint64 = 181040
	TransactionBurnMoneyAmountMustBePositive                       uint64 = 181050
	TransactionBurnMoneyWalletBalanceLessThanZero                  uint64 = 181060
	TransactionBurnMoneyWalletUpdateFailed                         uint64 = 181070
	TransactionBurnMoneyReferenceIdNotValid                        uint64 = 181080
	TransactionBurnMoneyTransactionCreateFailed                    uint64 = 181090
	TransactionBurnMoneyTransactionCommitFailed                    uint64 = 181100
	TransactionBurnMoneySuccess                                    uint64 = 181110
	OrderPayWithWalletOrderNotFound                                uint64 = 190000
	OrderPayWithWalletOrderFindFailed                              uint64 = 190010
	OrderPayWithWalletOrderAlreadyPaid                             uint64 = 190050
	OrderPayWithWalletOrderAlreadyCancelled                        uint64 = 190060
	OrderPayWithWalletOrderAlreadyRefunded                         uint64 = 190070
	OrderPayWithWalletOrderAlreadyPartiallyRefunded                uint64 = 190080
	OrderPayWithWalletUserWalletNotFound                           uint64 = 190200
	OrderPayWithWalletUserWalletFindFailed                         uint64 = 190210
	OrderPayWithWalletInsufficientBalance                          uint64 = 190220
	OrderPayWithWalletOrganizationWalletNotFound                   uint64 = 190230
	OrderPayWithWalletOrganizationWalletFindFailed                 uint64 = 190240
	OrderPayWithWalletSuccess                                      uint64 = 190250
	OrderPayWithWalletFailed                                       uint64 = 190260
	OrderPayWithWalletPaymentTermsNotFound                         uint64 = 190270
	OrderPayWithWalletPaymentTermsFindFailed                       uint64 = 190280
	OrderPayWithWalletUserWalletUpdateFailed                       uint64 = 190290
	OrderPayWithWalletOrganizationWalletUpdateFailed               uint64 = 190300
	OrganizationSetLimitUserPermissionDenied                       uint64 = 200000
	OrganizationSetLimitUserUserNotFound                           uint64 = 200010
	OrganizationSetLimitUserUserFindFailed                         uint64 = 200020
	OrganizationSetLimitUserLimitNotFound                          uint64 = 200030
	OrganizationSetLimitUserLimitFindFailed                        uint64 = 200040
	OrganizationSetLimitFailed                                     uint64 = 200050
	OrganizationSetLimitSuccess                                    uint64 = 200060
	OrganizationGetUserLimitPermissionDenied                       uint64 = 200070
	OrganizationGetUserLimitUserNotFound                           uint64 = 200080
	OrganizationGetUserLimitUserFindFailed                         uint64 = 200090
	LimitNotFound                                                  uint64 = 200100
	LimitGetFailed                                                 uint64 = 200110
	CardBinNotFound                                                uint64 = 202000
	CardBinFindFailed                                              uint64 = 202010
	TransferWithdrawalRequestListFailed                            uint64 = 203000
	TransferWithdrawalCreateWalletNotFound                         uint64 = 203200
	TransferWithdrawalCreateWalletFindFailed                       uint64 = 203210
	TransferWithdrawalCreateAmountMustBeGreaterThanZero            uint64 = 203220
	TransferWithdrawalCreateInsufficientBalance                    uint64 = 203230
	TransferWithdrawalCreateTransferWalletNotFound                 uint64 = 203240
	TransferWithdrawalCreateTransferWalletFindFailed               uint64 = 203250
	TransferWithdrawalCreateMoneyTransferCreateFailed              uint64 = 203260
	TransferWithdrawalCreateWalletUpdateFailed                     uint64 = 203270
	TransferWithdrawalCreateTransactionCreateFailed                uint64 = 203280
	TransferWithdrawalStatusMoneyTransferNotFound                  uint64 = 203290
	TransferWithdrawalStatusMoneyTransferFindFailed                uint64 = 203300
	TransferWithdrawalStatusUserNotFound                           uint64 = 203310
	TransferWithdrawalStatusUserFindFailed                         uint64 = 203320
	TransferWithdrawalStatusCurrencyNotFound                       uint64 = 203330
	TransferWithdrawalStatusCurrencyFindFailed                     uint64 = 203340
	TransferWithdrawalStatusSuccess                                uint64 = 203350
	TransferWithdrawalCreateSuccess                                uint64 = 203360
	TransferWithdrawalCreateReceiverOrganizationNotFound           uint64 = 203370
	TransferWithdrawalCreateReceiverOrganizationFindFailed         uint64 = 203380
	TransferWithdrawalCreateTransactionCommitFailed                uint64 = 203390
	TransferWithdrawalApplyTransactionNotFound                     uint64 = 203400
	TransferWithdrawalApplyTransactionFindFailed                   uint64 = 203410
	TransferWithdrawalApplyTransactionUpdateFailed                 uint64 = 203420
	TransferWithdrawalApplyMoneyTransferUpdateFailed               uint64 = 203430
	TransferWithdrawalApplyTransactionCommitFailed                 uint64 = 203440
	TransferWithdrawalApplySuccess                                 uint64 = 203450
	TransferWithdrawalApplyTransferWalletNotFound                  uint64 = 203460
	TransferWithdrawalApplyTransferWalletFindFailed                uint64 = 203470
	TransferWithdrawalApplyInsufficientBalance                     uint64 = 203480
	TransferWithdrawalApplyTransferWalletUpdateFailed              uint64 = 203490
	TransferWithdrawalCancelMoneyTransferNotFound                  uint64 = 203500
	TransferWithdrawalCancelMoneyTransferFindFailed                uint64 = 203510
	TransferWithdrawalCancelTransactionNotFound                    uint64 = 203520
	TransferWithdrawalCancelTransactionFindFailed                  uint64 = 203530
	TransferWithdrawalCancelTransactionUpdateFailed                uint64 = 203540
	TransferWithdrawalCancelMoneyTransferUpdateFailed              uint64 = 203550
	TransferWithdrawalCancelTransferWalletNotFound                 uint64 = 203560
	TransferWithdrawalCancelTransferWalletFindFailed               uint64 = 203570
	TransferWithdrawalCancelInsufficientBalance                    uint64 = 203580
	TransferWithdrawalCancelOrganizationFindFailed                 uint64 = 203590
	TransferWithdrawalCancelTransactionCreateFailed                uint64 = 203600
	TransferWithdrawalCancelTransactionCommitFailed                uint64 = 203610
	TransferWithdrawalCancelSuccess                                uint64 = 203620
	TransferWithdrawalCancelTransferWalletUpdateFailed             uint64 = 203630
	TransferWithdrawalCancelUserWalletNotFound                     uint64 = 203640
	TransferWithdrawalCancelUserWalletFindFailed                   uint64 = 203650
	TransferWithdrawalCancelUserWalletUpdateFailed                 uint64 = 203660
	TransferWithdrawalStatusWalletNotFound                         uint64 = 203670
	TransferWithdrawalStatusWalletFindFailed                       uint64 = 203680
	TransferTopupMoneyWalletNotFound                               uint64 = 204000
	TransferTopupMoneyWalletFindFailed                             uint64 = 204010
	TransferTopupMoneyCurrencyNotFound                             uint64 = 204020
	TransferTopupMoneyCurrencyFindFailed                           uint64 = 204030
	TransferTopupMoneyCurrencyMismatch                             uint64 = 204040
	TransferTopupMoneyQuotationExpired                             uint64 = 204050
	TransactionGetTxDocumentFailed                                 uint64 = 205000
	TransactionGetTxDocumentSuccess                                uint64 = 205010
	OrganizationDesignOrganizationNotFound                         uint64 = 206000
	OrganizationDesignNotFound                                     uint64 = 206010
	OrganizationDesignFindFailed                                   uint64 = 206020
	OrganizationDesignInvalidType                                  uint64 = 206030
	AuthMagicLoginUserSuspended                                    uint64 = 207000
	TransferSendMoneyUserSuspended                                 uint64 = 207010
	TransactionW2WSendReceiverUserNotFound                         uint64 = 207020
	TransactionW2WSendReceiverUserSuspended                        uint64 = 207030
	TransactionW2WSendReceiverUserFindFailed                       uint64 = 207040
	TransactionBankWithdrawUserSuspended                           uint64 = 207050
	GenerateOTPUserNotFound                                        uint64 = 210000
	GenerateOTPUserFindError                                       uint64 = 210010
	GenerateOTPSuspendedUser                                       uint64 = 210020
	GenerateOTPInactiveUser                                        uint64 = 210030
	GenerateOTPOrganizationNotFound                                uint64 = 210040
	GenerateOTPOrganizationFindError                               uint64 = 210050
	GenerateOTPCommunicationProviderNotFound                       uint64 = 210060
	GenerateOTPCommunicationProviderFindError                      uint64 = 210070
	GenerateOTPCreateError                                         uint64 = 210080
	GenerateOTPSuccess                                             uint64 = 210090
	GenerateOTPOrganizationOTPActionNotFound                       uint64 = 210100
	GenerateOTPOrganizationOTPActionFindError                      uint64 = 210110
	GenerateOTPOtpNotActive                                        uint64 = 210120
	MobileVerificationPhoneUpdateError                             uint64 = 220000
	MobileVerificationPhoneExists                                  uint64 = 220010
	MobileVerificationPhoneUnusable                                uint64 = 220020
	VerifyOTPCodeRequired                                          uint64 = 220110
	VerifyOTPReferenceIdRequired                                   uint64 = 220120
	VerifyOTPNotFound                                              uint64 = 220130
	VerifyOTPFindError                                             uint64 = 220140
	VerifyOTPAlreadyUsed                                           uint64 = 220150
	VerifyOTPCodeNotValid                                          uint64 = 220160
	VerifyOTPUpdateError                                           uint64 = 220170
	VerifyOTPSuccess                                               uint64 = 220180
	AuthSmsOTPIdentificationPhoneRequired                          uint64 = 230000
	AuthSmsOTPIdentificationUserNotFound                           uint64 = 230010
	AuthSmsOTPIdentificationFailed                                 uint64 = 230020
	AuthSmsOTPIdentificationUserNotActive                          uint64 = 230030
	AuthSmsOTPIdentificationUserIPNotAllowed                       uint64 = 230040
	AuthSmsOTPIdentificationUserMobileVerified                     uint64 = 230050
	AuthSmsOTPIdentificationOTPNotActive                           uint64 = 230060
	AuthSmsOTPIdentificationSuccess                                uint64 = 230070
	AuthSmsOTPAuthenticateCodeRequired                             uint64 = 230080
	AuthSmsOTPAuthenticateReferenceIdRequired                      uint64 = 230090
	AuthSmsOTPAuthenticateUserNotActive                            uint64 = 230100
	AuthSmsOTPAuthenticateUserIPNotAllowed                         uint64 = 230110
	AuthSmsOTPAuthenticateCodeNotValid                             uint64 = 230120
	AuthSmsOTPAuthenticateFailed                                   uint64 = 230130
	AuthSmsOTPAuthenticateSuccess                                  uint64 = 230140
	SecurityImageUpdateError                                       uint64 = 240000
	SecurityImageUpdateSuccess                                     uint64 = 240010
	PayInvoiceInvoiceNotFound                                      uint64 = 251000
	PayInvoiceInvoiceFindFailed                                    uint64 = 251010
	PayInvoiceOrderCreateFailed                                    uint64 = 251020
	PayInvoiceInvoiceUpdateFailed                                  uint64 = 251030
	PayInvoiceOrderCreateSuccess                                   uint64 = 251040
	CommunicationSendMessageSuccess                                uint64 = 260000
	CommunicationSendMessageFailed                                 uint64 = 260010
	PayInvoiceOrderFindFailed                                      uint64 = 251050
	PayInvoiceWalletBalanceInsufficient                            uint64 = 251060
	PayInvoiceOrganizationFindFailed                               uint64 = 251070
	PayInvoiceWalletNotFound                                       uint64 = 251080
	PayInvoiceWalletFindFailed                                     uint64 = 251090
	PayInvoiceWalletUpdateFailed                                   uint64 = 251100
	PayInvoiceTransactionCreateFailed                              uint64 = 251110
	PayInvoiceSuccess                                              uint64 = 251120
	PayInvoiceBillCompanyFindFailed                                uint64 = 251130

	BillTypeNotFound    uint64 = 252000
	BillCompanyNotFound uint64 = 252010

	TransactionDepositOrganizationFindFailed          uint64 = 253000
	TransactionDepositUserLimitFindFailed             uint64 = 253010
	TransactionDepositWalletBalanceLimitReached       uint64 = 253020
	TransactionDepositWalletOneTimeTopupLimitExceeded uint64 = 253030
	TransactionDepositWalletTopupLimitExceeded        uint64 = 253040

	OrderPayOrderWalletNotFound            uint64 = 254000
	OrderPayOrderWalletGetFailed           uint64 = 254010
	OrderPayOrderWalletCurrencyNotFound    uint64 = 254020
	OrderPayOrderWalletCurrencyGetFailed   uint64 = 254030
	OrderPayUserLimitGetFailed             uint64 = 254040
	OrderPayOrderWalletBalanceExceedsLimit uint64 = 254050
	OrderPayOrderAmountExceedsLimit        uint64 = 254060
	PayInvoiceCreditCardNotSupported       uint64 = 254070

	OrganizationUserVerifyPermissionDenied uint64 = 255000
	OrganizationUserVerifyUserNotFound     uint64 = 255010
	OrganizationUserVerifyUserFindFailed   uint64 = 255020
	OrganizationUserVerifyUserUpdateFailed uint64 = 255030
	OrganizationUserVerifySuccess          uint64 = 255040

	UserChangeEmailPermissionDenied  uint64 = 256000
	UserChangeEmailEmailAlreadyExist uint64 = 256010
	UserChangeEmailUserNotFound      uint64 = 256020
	UserChangeEmailUserFindError     uint64 = 256030
	UserChangeEmailUserIsNotActive   uint64 = 256040
	UserChangeEmailUserIsSuspended   uint64 = 256050
	UserChangeEmailUserUpdateError   uint64 = 256060
	UserChangeEmailSuccess           uint64 = 256070

	ProfileEmailCannotUsable          uint64 = 257000
	ProfileEmailCodeExpired           uint64 = 257010
	ProfileEmailCodeInvalid           uint64 = 257020
	ProfileEmailUpdateFailed          uint64 = 257030
	ProfileEmailUpdateSuccess         uint64 = 257040
	ProfileEmailUpdateEmailSent       uint64 = 257050
	ProfilePhoneNumberSameWithCurrent uint64 = 257060
	ProfilePhoneNumberCannotUsable    uint64 = 257070
	ProfilePhoneNumberCodeExpired     uint64 = 257080
	ProfilePhoneNumberCodeInvalid     uint64 = 257090
	ProfilePhoneNumberUpdateFailed    uint64 = 257100
	ProfilePhoneNumberUpdateSuccess   uint64 = 257110
	ProfilePhoneNumberUpdateSMSSent   uint64 = 257120
	InvoiceGetBillCompanyFailed       uint64 = 258000
	InvoiceGetBillProviderFailed      uint64 = 258010
	InvoiceGetInvoiceFailed           uint64 = 258020
	InvoiceUpdateInvoiceFailed        uint64 = 258040
	InvoiceDeleteInvoiceFailed        uint64 = 258050
	InvoicePayInvoiceFailed           uint64 = 258060
	InvoiceCreateInvoiceFailed        uint64 = 258070
	InvoicePayyBillProviderFindFailed uint64 = 258080

	UnknownError uint64 = 300000
)

var mapCodeToMessage = map[uint64]string{
	Unauthenticated:                                                "UNAUTHENTICATED",
	TokenNotActive:                                                 "TOKEN_NOT_ACTIVE",
	TokenExpired:                                                   "TOKEN_EXPIRED",
	EmailNotVerified:                                               "EMAIL_NOT_VERIFIED",
	MobileNotVerified:                                              "MOBILE_NOT_VERIFIED",
	KycNotVerified:                                                 "KYC_NOT_VERIFIED",
	UserSuspended:                                                  "USER_SUSPENDED",
	AuthCheckTokenKycProviderNotFound:                              "AUTH_CHECK_TOKEN_KYC_PROVIDER_NOT_FOUND",
	AuthCheckTokenKycProviderFindFailed:                            "AUTH_CHECK_TOKEN_KYC_PROVIDER_FIND_FAILED",
	AuthCheckTokenKycProviderSecretNotFound:                        "AUTH_CHECK_TOKEN_KYC_PROVIDER_SECRET_NOT_FOUND",
	AuthCheckTokenKycCheckFailed:                                   "AUTH_CHECK_TOKEN_KYC_CHECK_FAILED",
	BankAccountCreatePermissionDenied:                              "BANK_ACCOUNT_CREATE_PERMISSION_DENIED",
	BankAccountCreateFailed:                                        "BANK_ACCOUNT_CREATE_FAILED",
	BankAccountCreateIbanRequired:                                  "BANK_ACCOUNT_CREATE_IBAN_REQUIRED",
	BankAccountCreateNameRequired:                                  "BANK_ACCOUNT_CREATE_NAME_REQUIRED",
	BankAccountCreateCurrencyRequired:                              "BANK_ACCOUNT_CREATE_CURRENCY_REQUIRED",
	BankAccountCreateTitleRequired:                                 "BANK_ACCOUNT_CREATE_TITLE_REQUIRED",
	BankAccountCreateBankNotFound:                                  "BANK_ACCOUNT_CREATE_BANK_NOT_FOUND",
	BankAccountCreateBankFindFailed:                                "BANK_ACCOUNT_CREATE_BANK_FIND_FAILED",
	BankAccountCreateCurrencyNotFound:                              "BANK_ACCOUNT_CREATE_CURRENCY_NOT_FOUND",
	BankAccountCreateCurrencyFindFailed:                            "BANK_ACCOUNT_CREATE_CURRENCY_FIND_FAILED",
	BankAccountCreateIbanInvalid:                                   "BANK_ACCOUNT_CREATE_IBAN_INVALID",
	BankAccountCreateFindDuplicateFromNameFailed:                   "BANK_ACCOUNT_CREATE_FIND_DUPLICATE_FROM_NAME_FAILED",
	BankAccountCreateNameNotUnique:                                 "BANK_ACCOUNT_CREATE_NAME_NOT_UNIQUE",
	BankAccountCreateFindDuplicateFromIbanFailed:                   "BANK_ACCOUNT_CREATE_FIND_DUPLICATE_FROM_IBAN_FAILED",
	BankAccountCreateIbanNotUnique:                                 "BANK_ACCOUNT_CREATE_IBAN_NOT_UNIQUE",
	BankAccountCreateSuccess:                                       "BANK_ACCOUNT_CREATE_SUCCESS",
	BankAccountListPermissionDenied:                                "BANK_ACCOUNT_LIST_PERMISSION_DENIED",
	BankAccountListFailed:                                          "BANK_ACCOUNT_LIST_FAILED",
	BankAccountBankNotFound:                                        "BANK_ACCOUNT_BANK_NOT_FOUND",
	BankAccountUpdatePermissionDenied:                              "BANK_ACCOUNT_UPDATE_PERMISSION_DENIED",
	BankAccountUpdateFailed:                                        "BANK_ACCOUNT_UPDATE_FAILED",
	BankAccountUpdateSuccess:                                       "BANK_ACCOUNT_UPDATE_SUCCESS",
	BankAccountUpdateFindDuplicateFromNameFailed:                   "BANK_ACCOUNT_UPDATE_FIND_DUPLICATE_FROM_NAME_FAILED",
	BankAccountUpdateNameNotUnique:                                 "BANK_ACCOUNT_UPDATE_NAME_NOT_UNIQUE",
	BankAccountGetPermissionDenied:                                 "BANK_ACCOUNT_GET_PERMISSION_DENIED",
	BankAccountGetNotFound:                                         "BANK_ACCOUNT_GET_NOT_FOUND",
	BankAccountGetFailed:                                           "BANK_ACCOUNT_GET_FAILED",
	BankAccountDeletePermissionDenied:                              "BANK_ACCOUNT_DELETE_PERMISSION_DENIED",
	BankAccountDeleteNotFound:                                      "BANK_ACCOUNT_DELETE_NOT_FOUND",
	BankAccountDeleteFailed:                                        "BANK_ACCOUNT_DELETE_FAILED",
	BankAccountDeleteSuccess:                                       "BANK_ACCOUNT_DELETE_SUCCESS",
	OrganizationBankAccountsGetPermissionDenied:                    "ORGANIZATION_BANK_ACCOUNTS_GET_PERMISSION_DENIED",
	OrganizationBankAccountsGetWalletIdRequired:                    "ORGANIZATION_BANK_ACCOUNTS_GET_WALLET_ID_REQUIRED",
	OrganizationBankAccountsGetWalletNotFound:                      "ORGANIZATION_BANK_ACCOUNTS_GET_WALLET_NOT_FOUND",
	OrganizationBankAccountsGetWalletFailed:                        "ORGANIZATION_BANK_ACCOUNTS_GET_WALLET_FAILED",
	OrganizationBankAccountsGetNotFound:                            "ORGANIZATION_BANK_ACCOUNTS_GET_NOT_FOUND",
	OrganizationBankAccountsGetFailed:                              "ORGANIZATION_BANK_ACCOUNTS_GET_FAILED",
	OrderCreatePermissionDenied:                                    "ORDER_CREATE_PERMISSION_DENIED",
	OrderCreateBasketItemAmountNotEqualToBasketItemAmount:          "ORDER_CREATE_BASKET_ITEM_AMOUNT_NOT_EQUAL_TO_BASKET_ITEM_AMOUNT",
	OrderCreateOrganizationNotFound:                                "ORDER_CREATE_ORGANIZATION_NOT_FOUND",
	OrderCreateOrganizationFailed:                                  "ORDER_CREATE_ORGANIZATION_FAILED",
	OrderCreateOrderPaymentTermCreateFailed:                        "ORDER_CREATE_ORDER_PAYMENT_TERM_CREATE_FAILED",
	OrderCreateOrderPaymentTermAmountNotEqualToOrderAmount:         "ORDER_CREATE_ORDER_PAYMENT_TERM_AMOUNT_NOT_EQUAL_TO_ORDER_AMOUNT",
	OrderCreateRelationCreateFailed:                                "ORDER_CREATE_RELATION_CREATE_FAILED",
	OrderCreateGetOrganizationForCheckoutCustomizationFailed:       "ORDER_CREATE_GET_ORGANIZATION_FOR_CHECKOUT_CUSTOMIZATION_FAILED",
	OrderCreatePaymentPageCreateFailed:                             "ORDER_CREATE_PAYMENT_PAGE_CREATE_FAILED",
	OrderCreateOrderPaymentPageUpdateFailed:                        "ORDER_CREATE_ORDER_PAYMENT_PAGE_UPDATE_FAILED",
	OrderCreateFailed:                                              "ORDER_CREATE_FAILED",
	OrderCreateSuccess:                                             "ORDER_CREATE_SUCCESS",
	OrderGetNotFound:                                               "ORDER_GET_NOT_FOUND",
	OrderGetFailed:                                                 "ORDER_GET_FAILED",
	OrderStatusStreamGetOrderFailed:                                "ORDER_STATUS_STREAM_GET_ORDER_FAILED",
	OrderStatusStreamGetOrderRelationFailed:                        "ORDER_STATUS_STREAM_GET_ORDER_RELATION_FAILED",
	OrderStatusStreamGetOrganizationFailed:                         "ORDER_STATUS_STREAM_GET_ORGANIZATION_FAILED",
	OrderStatusStreamGetOrderNotFound:                              "ORDER_STATUS_STREAM_GET_ORDER_NOT_FOUND",
	OrderStatusStreamSendFailed:                                    "ORDER_STATUS_STREAM_SEND_FAILED",
	OrderRetryOrderNotFound:                                        "ORDER_RETRY_ORDER_NOT_FOUND",
	OrderRetryOrderAlreadyPaid:                                     "ORDER_RETRY_ORDER_ALREADY_PAID",
	OrderRetryOrderRelationNotFound:                                "ORDER_RETRY_ORDER_RELATION_NOT_FOUND",
	OrderRetryOrderFailed:                                          "ORDER_RETRY_ORDER_FAILED",
	OrderRetryOrderRelationFailed:                                  "ORDER_RETRY_ORDER_RELATION_FAILED",
	OrderRetryOrderGetOrganizationFailed:                           "ORDER_RETRY_ORDER_GET_ORGANIZATION_FAILED",
	OrderRetryOrderUpdateOrderFailed:                               "ORDER_RETRY_ORDER_UPDATE_ORDER_FAILED",
	OrderRetryOrderUpdateOrderRelationFailed:                       "ORDER_RETRY_ORDER_UPDATE_ORDER_RELATION_FAILED",
	OrderPayOrderCardNotFound:                                      "ORDER_PAY_ORDER_CARD_NOT_FOUND",
	OrderPayOrderCardFailed:                                        "ORDER_PAY_ORDER_CARD_FAILED",
	OrderPayOrderOrganizationNotAllowPayment:                       "ORDER_PAY_ORDER_ORGANIZATION_NOT_ALLOW_PAYMENT",
	OrderPayOrderStatusNotWaitingForPayment:                        "ORDER_PAY_ORDER_STATUS_NOT_WAITING_FOR_PAYMENT",
	OrderPayOrderVposCurrencyNotFound:                              "ORDER_PAY_ORDER_VPOS_CURRENCY_NOT_FOUND",
	OrderPayOrderVposCurrencyGetFailed:                             "ORDER_PAY_ORDER_VPOS_CURRENCY_GET_FAILED",
	OrderPayOrderVposGetFailed:                                     "ORDER_PAY_ORDER_VPOS_GET_FAILED",
	OrderPayOrderCardSchemeGetFailed:                               "ORDER_PAY_ORDER_CARD_SCHEME_GET_FAILED",
	OrderPayOrderCardSchemeNotAllowed:                              "ORDER_PAY_ORDER_CARD_SCHEME_NOT_ALLOWED",
	OrderPayOrderUserWalletCreateFailed:                            "ORDER_PAY_ORDER_USER_WALLET_CREATE_FAILED",
	OrderPayOrderUpdateOrderRelationFailed:                         "ORDER_PAY_ORDER_UPDATE_ORDER_RELATION_FAILED",
	OrderPayOrderPaymentTermGetFailed:                              "ORDER_PAY_ORDER_PAYMENT_TERM_GET_FAILED",
	OrderPayOrderVposMarketplaceNotActive:                          "ORDER_PAY_ORDER_VPOS_MARKETPLACE_NOT_ACTIVE",
	OrderPayOrderCreateOrderDetailFailed:                           "ORDER_PAY_ORDER_CREATE_ORDER_DETAIL_FAILED",
	OrderPayOrderCreateOrderAcquirerTryFailed:                      "ORDER_PAY_ORDER_CREATE_ORDER_ACQUIRER_TRY_FAILED",
	OrderPayOrderCommitFailed:                                      "ORDER_PAY_ORDER_COMMIT_FAILED",
	OrderGetOrderInfoOrderNotFound:                                 "ORDER_GET_ORDER_INFO_ORDER_NOT_FOUND",
	OrderGetOrderInfoFailed:                                        "ORDER_GET_ORDER_INFO_FAILED",
	OrderGetOrderInfoOrganizationNotFound:                          "ORDER_GET_ORDER_INFO_ORGANIZATION_NOT_FOUND",
	OrderGetOrderInfoOrganizationFailed:                            "ORDER_GET_ORDER_INFO_ORGANIZATION_FAILED",
	OrderOrderDetailOrderNotFound:                                  "ORDER_ORDER_DETAIL_ORDER_NOT_FOUND",
	OrderOrderDetailFailed:                                         "ORDER_ORDER_DETAIL_FAILED",
	OrderOrderDetailOrganizationNotFound:                           "ORDER_ORDER_DETAIL_ORGANIZATION_NOT_FOUND",
	OrderOrderDetailOrganizationFailed:                             "ORDER_ORDER_DETAIL_ORGANIZATION_FAILED",
	OrderOrderDetailPaymentPageNotFound:                            "ORDER_ORDER_DETAIL_PAYMENT_PAGE_NOT_FOUND",
	OrderOrderDetailPaymentPageFailed:                              "ORDER_ORDER_DETAIL_PAYMENT_PAGE_FAILED",
	OrderOrderDetailPaymentTermsFailed:                             "ORDER_ORDER_DETAIL_PAYMENT_TERMS_FAILED",
	OrderOrderPaymentDetailOrderDetailNotFound:                     "ORDER_ORDER_PAYMENT_DETAIL_ORDER_DETAIL_NOT_FOUND",
	OrderOrderPaymentDetailFailed:                                  "ORDER_ORDER_PAYMENT_DETAIL_FAILED",
	OrderGetOrderListFailed:                                        "ORDER_GET_ORDER_LIST_FAILED",
	OrderGetOrderTXsGetOrderNotFound:                               "ORDER_GET_ORDER_TXS_GET_ORDER_NOT_FOUND",
	OrderGetOrderTXsGetOrderFailed:                                 "ORDER_GET_ORDER_TXS_GET_ORDER_FAILED",
	OrderGetOrderTXsFailed:                                         "ORDER_GET_ORDER_TXS_FAILED",
	OrderGetOrderTXsSenderWalletNotFound:                           "ORDER_GET_ORDER_TXS_SENDER_WALLET_NOT_FOUND",
	OrderGetOrderTXsSenderWalletFailed:                             "ORDER_GET_ORDER_TXS_SENDER_WALLET_FAILED",
	OrderGetOrderTXsReceiverWalletNotFound:                         "ORDER_GET_ORDER_TXS_RECEIVER_WALLET_NOT_FOUND",
	OrderGetOrderTXsReceiverWalletFailed:                           "ORDER_GET_ORDER_TXS_RECEIVER_WALLET_FAILED",
	OrderRefundOrderPermissionDenied:                               "ORDER_REFUND_ORDER_PERMISSION_DENIED",
	OrderRefundGetOrderNotFound:                                    "ORDER_REFUND_GET_ORDER_NOT_FOUND",
	OrderRefundGetOrderFailed:                                      "ORDER_REFUND_GET_ORDER_FAILED",
	OrderRefundOrderTypeDeposit:                                    "ORDER_REFUND_ORDER_TYPE_DEPOSIT",
	OrderRefundOrderStatusIsNotPaid:                                "ORDER_REFUND_ORDER_STATUS_IS_NOT_PAID",
	OrderRefundAmountIsCannotBeGreaterThanOrderAmount:              "ORDER_REFUND_AMOUNT_IS_CANNOT_BE_GREATER_THAN_ORDER_AMOUNT",
	OrderRefundOrderRelationNotFound:                               "ORDER_REFUND_ORDER_RELATION_NOT_FOUND",
	OrderRefundOrderRelationFailed:                                 "ORDER_REFUND_ORDER_RELATION_FAILED",
	OrderRefundVPOSNotFound:                                        "ORDER_REFUND_VPOS_NOT_FOUND",
	OrderRefundVPOSFailed:                                          "ORDER_REFUND_VPOS_FAILED",
	OrderRefundOrderDetailNotFound:                                 "ORDER_REFUND_ORDER_DETAIL_NOT_FOUND",
	OrderRefundOrderDetailFailed:                                   "ORDER_REFUND_ORDER_DETAIL_FAILED",
	OrderRefundOrderFailed:                                         "ORDER_REFUND_ORDER_FAILED",
	OrderRefundOrganizationWalletNotFound:                          "ORDER_REFUND_ORGANIZATION_WALLET_NOT_FOUND",
	OrderRefundOrganizationWalletFailed:                            "ORDER_REFUND_ORGANIZATION_WALLET_FAILED",
	OrderRefundPosWalletNotFound:                                   "ORDER_REFUND_POS_WALLET_NOT_FOUND",
	OrderRefundPosWalletFailed:                                     "ORDER_REFUND_POS_WALLET_FAILED",
	OrderRefundUserCardWalletNotFound:                              "ORDER_REFUND_USER_CARD_WALLET_NOT_FOUND",
	OrderRefundUserCardWalletFailed:                                "ORDER_REFUND_USER_CARD_WALLET_FAILED",
	OrderRefundOrderUpdateOrganizationWalletFailed:                 "ORDER_REFUND_ORDER_UPDATE_ORGANIZATION_WALLET_FAILED",
	OrderRefundOrderUpdatePosWalletFailed:                          "ORDER_REFUND_ORDER_UPDATE_POS_WALLET_FAILED",
	OrderRefundOrderCreateTxPosToUserFailed:                        "ORDER_REFUND_ORDER_CREATE_TX_POS_TO_USER_FAILED",
	OrderRefundOrderUpdateUserCardWalletFailed:                     "ORDER_REFUND_ORDER_UPDATE_USER_CARD_WALLET_FAILED",
	OrderRefundOrderUpdateOrderFailed:                              "ORDER_REFUND_ORDER_UPDATE_ORDER_FAILED",
	OrderCancelOrderPermissionDenied:                               "ORDER_CANCEL_ORDER_PERMISSION_DENIED",
	OrderCancelOrderGetOrderNotFound:                               "ORDER_CANCEL_ORDER_GET_ORDER_NOT_FOUND",
	OrderCancelOrderGetOrderFailed:                                 "ORDER_CANCEL_ORDER_GET_ORDER_FAILED",
	OrderCancelCannotCancelDepositOrder:                            "ORDER_CANCEL_CANNOT_CANCEL_DEPOSIT_ORDER",
	OrderCancelOrderGetUserNotFound:                                "ORDER_CANCEL_ORDER_GET_USER_NOT_FOUND",
	OrderCancelOrderGetUserFailed:                                  "ORDER_CANCEL_ORDER_GET_USER_FAILED",
	OrderCancelOrderIsNotPaid:                                      "ORDER_CANCEL_ORDER_IS_NOT_PAID",
	OrderCancelOrderGetOrderRelationNotFound:                       "ORDER_CANCEL_ORDER_GET_ORDER_RELATION_NOT_FOUND",
	OrderCancelOrderGetOrderRelationFailed:                         "ORDER_CANCEL_ORDER_GET_ORDER_RELATION_FAILED",
	OrderCancelOrderGetVPOSNotFound:                                "ORDER_CANCEL_ORDER_GET_VPOS_NOT_FOUND",
	OrderCancelOrderGetVPOSFailed:                                  "ORDER_CANCEL_ORDER_GET_VPOS_FAILED",
	OrderCancelOrderFailed:                                         "ORDER_CANCEL_ORDER_FAILED",
	OrderCancelOrderGetOrganizationWalletNotFound:                  "ORDER_CANCEL_ORDER_GET_ORGANIZATION_WALLET_NOT_FOUND",
	OrderCancelOrderGetOrganizationWalletFailed:                    "ORDER_CANCEL_ORDER_GET_ORGANIZATION_WALLET_FAILED",
	OrderCancelOrderGetPosWalletNotFound:                           "ORDER_CANCEL_ORDER_GET_POS_WALLET_NOT_FOUND",
	OrderCancelOrderGetPosWalletFailed:                             "ORDER_CANCEL_ORDER_GET_POS_WALLET_FAILED",
	OrderCancelOrderGetUserCardWalletNotFound:                      "ORDER_CANCEL_ORDER_GET_USER_CARD_WALLET_NOT_FOUND",
	OrderCancelOrderGetUserCardWalletFailed:                        "ORDER_CANCEL_ORDER_GET_USER_CARD_WALLET_FAILED",
	OrderCancelOrderCreateTxOrgToPosFailed:                         "ORDER_CANCEL_ORDER_CREATE_TX_ORG_TO_POS_FAILED",
	OrderCancelOrderUpdateOrganizationWalletFailed:                 "ORDER_CANCEL_ORDER_UPDATE_ORGANIZATION_WALLET_FAILED",
	OrderCancelOrderUpdatePosWalletFailed:                          "ORDER_CANCEL_ORDER_UPDATE_POS_WALLET_FAILED",
	OrderCancelOrderCreateTxPosToUserFailed:                        "ORDER_CANCEL_ORDER_CREATE_TX_POS_TO_USER_FAILED",
	OrderCancelOrderUpdateUserCardWalletFailed:                     "ORDER_CANCEL_ORDER_UPDATE_USER_CARD_WALLET_FAILED",
	OrderCancelOrderUpdateOrderFailed:                              "ORDER_CANCEL_ORDER_UPDATE_ORDER_FAILED",
	OrderPaymentDetailRequestInvalidArgument:                       "ORDER_PAYMENT_DETAIL_REQUEST_INVALID_ARGUMENT",
	OrderPaymentDetailRequestNotFound:                              "ORDER_PAYMENT_DETAIL_REQUEST_NOT_FOUND",
	OrderPaymentDetailRequestFailed:                                "ORDER_PAYMENT_DETAIL_REQUEST_FAILED",
	OfficierBirthDayError:                                          "OFFICIER_BIRTHDAY_ERROR",
	OfficierGetAppoitedAtError:                                     "OFFICIER_GET_APPOINTED_AT_ERROR",
	OfficierGetResignedAtError:                                     "OFFICIER_GET_RESIGNED_AT_ERROR",
	ShareholderPeopleBirthDayError:                                 "SHAREHOLDER_PEOPLE_BIRTHDAY_ERROR",
	ShareholderPeopleGetAppoitedAtError:                            "SHAREHOLDER_PEOPLE_GET_APPOINTED_AT_ERROR",
	ShareholderPeopleGetResignedAtError:                            "SHAREHOLDER_PEOPLE_GET_RESIGNED_AT_ERROR",
	CardListPermissionDenied:                                       "CARD_LIST_PERMISSION_DENIED",
	CardListNotFound:                                               "CARD_LIST_NOT_FOUND",
	CardListFailed:                                                 "CARD_LIST_FAILED",
	CardListTokenizationServiceListFailed:                          "CARD_LIST_TOKENIZATION_SERVICE_LIST_FAILED",
	CardListTokenizationServiceListNotFound:                        "CARD_LIST_TOKENIZATION_SERVICE_LIST_NOT_FOUND",
	CardGetPermissionDenied:                                        "CARD_GET_PERMISSION_DENIED",
	CardGetNotFound:                                                "CARD_GET_NOT_FOUND",
	CardGetFailed:                                                  "CARD_GET_FAILED",
	CardGetIdRequired:                                              "CARD_GET_ID_REQUIRED",
	CardCreatePermissionDenied:                                     "CARD_CREATE_PERMISSION_DENIED",
	CardCreateConversationIdAlreadyExists:                          "CARD_CREATE_CONVERSATION_ID_ALREADY_EXISTS",
	CardCreateFailed:                                               "CARD_CREATE_FAILED",
	CardCreateUserNotFound:                                         "CARD_CREATE_USER_NOT_FOUND",
	CardCreateSuccess:                                              "CARD_CREATE_SUCCESS",
	CardUpdatePermissionDenied:                                     "CARD_UPDATE_PERMISSION_DENIED",
	CardUpdateFailed:                                               "CARD_UPDATE_FAILED",
	CardUpdateCardNotFound:                                         "CARD_UPDATE_CARD_NOT_FOUND",
	CardUpdateSuccess:                                              "CARD_UPDATE_SUCCESS",
	CardDeleteCardNotFound:                                         "CARD_DELETE_CARD_NOT_FOUND",
	CardDeleteFailed:                                               "CARD_DELETE_FAILED",
	CardDeleteSuccess:                                              "CARD_DELETE_SUCCESS",
	CardAlreadyExist:                                               "CARD_ALREADY_EXIST",
	TokenizationServiceListPermissionDenied:                        "TOKENIZATION_SERVICE_LIST_PERMISSION_DENIED",
	TermSequenceMustBeOnlyInteger:                                  "TERM_SEQUENCE_MUST_BE_ONLY_INTERGER",
	TokenizationServiceListNotFound:                                "TOKENIZATION_SERVICE_LIST_NOT_FOUND",
	TokenizationServiceListFailed:                                  "TOKENIZATION_SERVICE_LIST_FAILED",
	TokenizationServiceCardListFailed:                              "TOKENIZATION_SERVICE_CARD_LIST_FAILED",
	TokenCreateFailed:                                              "TOKEN_CREATE_FAILED",
	TokenCreatePermissionDenied:                                    "TOKEN_CREATE_PERMISSION_DENIED",
	TokenCreateExpireDateBetweenZeroTo9999Days:                     "TOKEN_CREATE_EXPIRE_DATE_BETWEEN_ZERO_TO_9999_DAYS",
	TokenCreateApiKeyNotFound:                                      "TOKEN_CREATE_API_KEY_NOT_FOUND",
	TokenCreateApiKeyUserNotFound:                                  "TOKEN_CREATE_API_KEY_USER_NOT_FOUND",
	TokenCreateApiKeyUserOrganizationNotFound:                      "TOKEN_CREATE_API_KEY_USER_ORGANIZATION_NOT_FOUND",
	TokenCreateInvalidIpAddress:                                    "TOKEN_CREATE_INVALID_IP_ADDRESS",
	TokenCreateApiUserCreateFailed:                                 "TOKEN_CREATE_API_USER_CREATE_FAILED",
	TokenCreateApiUserGetFailed:                                    "TOKEN_CREATE_API_USER_GET_FAILED",
	TokenCreateFailedJwtGeneration:                                 "TOKEN_CREATE_FAILED_JWT_GENERATION",
	TokenCreateSuccess:                                             "TOKEN_CREATE_SUCCESS",
	TokenListPermissionDenied:                                      "TOKEN_LIST_PERMISSION_DENIED",
	TokenListFailed:                                                "TOKEN_LIST_FAILED",
	TokenEnablePermissionDenied:                                    "TOKEN_ENABLE_PERMISSION_DENIED",
	TokenEnableTokenNotFound:                                       "TOKEN_ENABLE_TOKEN_NOT_FOUND",
	TokenEnableFindTokenFailed:                                     "TOKEN_ENABLE_FIND_TOKEN_FAILED",
	TokenEnableFailed:                                              "TOKEN_ENABLE_FAILED",
	TokenEnableSuccess:                                             "TOKEN_ENABLE_SUCCESS",
	TokenDisablePermissionDenied:                                   "TOKEN_DISABLE_PERMISSION_DENIED",
	TokenDisableTokenNotFound:                                      "TOKEN_DISABLE_TOKEN_NOT_FOUND",
	TokenDisableFindTokenFailed:                                    "TOKEN_DISABLE_FIND_TOKEN_FAILED",
	TokenDisableFailed:                                             "TOKEN_DISABLE_FAILED",
	TokenDisableSuccess:                                            "TOKEN_DISABLE_SUCCESS",
	TokenDeletePermissionDenied:                                    "TOKEN_DELETE_PERMISSION_DENIED",
	TokenDeleteTokenNotFound:                                       "TOKEN_DELETE_TOKEN_NOT_FOUND",
	TokenDeleteFindTokenFailed:                                     "TOKEN_DELETE_FIND_TOKEN_FAILED",
	TokenDeleteFailed:                                              "TOKEN_DELETE_FAILED",
	TokenDeleteSuccess:                                             "TOKEN_DELETE_SUCCESS",
	ApiKeyGeneratePermissionDenied:                                 "API_KEY_GENERATE_PERMISSION_DENIED",
	ApiKeyGenerateFailed:                                           "API_KEY_GENERATE_FAILED",
	TransactionListPermissionDenied:                                "TRANSACTION_LIST_PERMISSION_DENIED",
	TransactionListFailed:                                          "TRANSACTION_LIST_FAILED",
	TransactionListSenderWalletNotFound:                            "TRANSACTION_LIST_SENDER_WALLET_NOT_FOUND",
	TransactionListSenderWalletFindFailed:                          "TRANSACTION_LIST_SENDER_WALLET_FIND_FAILED",
	TransactionListReceiverWalletNotFound:                          "TRANSACTION_LIST_RECEIVER_WALLET_NOT_FOUND",
	TransactionListReceiverWalletFindFailed:                        "TRANSACTION_LIST_RECEIVER_WALLET_FIND_FAILED",
	TransactionShortcutNotFound:                                    "TRANSACTION_SHORTCUT_NOT_FOUND",
	TransactionShortcutListFailed:                                  "TRANSACTION_SHORTCUT_LIST_FAILED",
	TransactionShortcutListSenderWalletNotFound:                    "TRANSACTION_SHORTCUT_LIST_SENDER_WALLET_NOT_FOUND",
	TransactionShortcutListSenderWalletFindFailed:                  "TRANSACTION_SHORTCUT_LIST_SENDER_WALLET_FIND_FAILED",
	TransactionShortcutListReceiverWalletNotFound:                  "TRANSACTION_SHORTCUT_LIST_RECEIVER_WALLET_NOT_FOUND",
	TransactionShortcutListReceiverWalletFindFailed:                "TRANSACTION_SHORTCUT_LIST_RECEIVER_WALLET_FIND_FAILED",
	TransactionBankWithdrawWalletNotFound:                          "TRANSACTION_BANK_WITHDRAW_WALLET_NOT_FOUND",
	TransactionBankWithdrawWalletFindFailed:                        "TRANSACTION_BANK_WITHDRAW_WALLET_FIND_FAILED",
	TransactionBankWithdrawPermissionDenied:                        "TRANSACTION_BANK_WITHDRAW_PERMISSION_DENIED",
	TransactionBankWithdrawUserNotFound:                            "TRANSACTION_BANK_WITHDRAW_USER_NOT_FOUND",
	TransactionBankWithdrawUserFindFailed:                          "TRANSACTION_BANK_WITHDRAW_USER_FIND_FAILED",
	TransactionBankWithdrawOrganizationNotFound:                    "TRANSACTION_BANK_WITHDRAW_ORGANIZATION_NOT_FOUND",
	TransactionBankWithdrawOrganizationFindFailed:                  "TRANSACTION_BANK_WITHDRAW_ORGANIZATION_FIND_FAILED",
	TransactionBankWithdrawBankAccountNotFound:                     "TRANSACTION_BANK_WITHDRAW_BANK_ACCOUNT_NOT_FOUND",
	TransactionBankWithdrawBankAccountFindFailed:                   "TRANSACTION_BANK_WITHDRAW_BANK_ACCOUNT_FIND_FAILED",
	TransactionBankWithdrawBankAccountCreateFailed:                 "TRANSACTION_BANK_WITHDRAW_BANK_ACCOUNT_CREATE_FAILED",
	TransactionBankWithdrawWalletCreateFailed:                      "TRANSACTION_BANK_WITHDRAW_WALLET_CREATE_FAILED",
	TransactionBankWithdrawAmountLessThanZero:                      "TRANSACTION_BANK_WITHDRAW_AMOUNT_LESS_THAN_ZERO",
	TransactionBankWithdrawAmountLessThanWalletBalance:             "TRANSACTION_BANK_WITHDRAW_AMOUNT_LESS_THAN_WALLET_BALANCE",
	TransactionBankWithdrawTxCreateFailed:                          "TRANSACTION_BANK_WITHDRAW_TX_CREATE_FAILED",
	TransactionBankWithdrawShortcutCreateFailed:                    "TRANSACTION_BANK_WITHDRAW_SHORTCUT_CREATE_FAILED",
	TransactionBankWithdrawSuccess:                                 "TRANSACTION_BANK_WITHDRAW_SUCCESS",
	TransactionNotFound:                                            "TRANSACTION_NOT_FOUND",
	TransactionGetFailed:                                           "TRANSACTION_GET_FAILED",
	TransactionGetTXCurrencyNotFound:                               "TRANSACTION_GET_TX_CURRENCY_NOT_FOUND",
	TransactionGetTXCurrencyFindFailed:                             "TRANSACTION_GET_TX_CURRENCY_FIND_FAILED",
	TransactionW2WSendSenderWalletNotFound:                         "TRANSACTION_W2W_SEND_SENDER_WALLET_NOT_FOUND",
	TransactionW2WSendUserLimitFindFailed:                          "TRANSACTION_W2W_SEND_USER_LIMIT_FIND_FAILED",
	TransactionW2WSendConversationIDAlreadyExists:                  "TRANSACTION_W2W_SEND_CONVERSATION_ID_ALREADY_EXISTS",
	TransactionW2WSendOtpCodeUsed:                                  "TRANSACTION_W2W_SEND_OTP_CODE_USED",
	TransactionW2WSendOtpCodeInvalid:                               "TRANSACTION_W2W_SEND_OTP_CODE_INVALID",
	TransactionW2WCantSameSenderAndReceiver:                        "TRANSACTION_W2W_CANT_SAME_SENDER_AND_RECEIVER",
	TransactionW2WSendSenderWalletBalanceZero:                      "TRANSACTION_W2W_SEND_SENDER_WALLET_BALANCE_ZERO",
	TransactionW2WSendSenderWalletFindFailed:                       "TRANSACTION_W2W_SEND_SENDER_WALLET_FIND_FAILED",
	TransactionW2WSendSenderWalletNotBelongToUser:                  "TRANSACTION_W2W_SEND_SENDER_WALLET_NOT_BELONG_TO_USER",
	TransactionW2WSendSenderWalletNotBelongToOrganization:          "TRANSACTION_W2W_SEND_SENDER_WALLET_NOT_BELONG_TO_ORGANIZATION",
	TransactionW2WSendReceiverWalletNotFound:                       "TRANSACTION_W2W_SEND_RECEIVER_WALLET_NOT_FOUND",
	TransactionW2WSendReceiverWalletFindFailed:                     "TRANSACTION_W2W_SEND_RECEIVER_WALLET_FIND_FAILED",
	TransactionW2WSendLimitNotFound:                                "TRANSACTION_W2W_SEND_LIMIT_NOT_FOUND",
	TransactionW2WSendLimitFindFailed:                              "TRANSACTION_W2W_SEND_LIMIT_FIND_FAILED",
	TransactionW2WSendDifferentCurrency:                            "TRANSACTION_W2W_SEND_DIFFERENT_CURRENCY",
	TransactionW2WSendAmountNeedGreaterThanZero:                    "TRANSACTION_W2W_SEND_AMOUNT_NEED_GREATER_THAN_ZERO",
	TransactionW2WSendAmountGreaterThanWalletBalance:               "TRANSACTION_W2W_SEND_AMOUNT_GREATER_THAN_WALLET_BALANCE",
	TransactionW2WSendMade:                                         "TRANSACTION_W2W_SEND_MADE",
	TransactionW2WSendOtpCodeRequired:                              "TRANSACTION_W2W_SEND_OTP_CODE_REQUIRED",
	TransactionShortcutGetFailed:                                   "TRANSACTION_SHORTCUT_GET_FAILED",
	TransactionShortcutUpdateFailed:                                "TRANSACTION_SHORTCUT_UPDATE_FAILED",
	TransactionShortcutDeleteFailed:                                "TRANSACTION_SHORTCUT_DELETE_FAILED",
	TransactionShortcutDeleteSuccess:                               "TRANSACTION_SHORTCUT_DELETE_SUCCESS",
	TransactionShortcutCloneFailed:                                 "TRANSACTION_SHORTCUT_CLONE_FAILED",
	WalletCreatePermissionDenied:                                   "WALLET_CREATE_PERMISSION_DENIED",
	WalletConversationIDAlreadyExists:                              "WALLET_CONVERSATION_ID_ALREADY_EXISTS",
	WalletCreateFailed:                                             "WALLET_CREATE_FAILED",
	WalletAlreadyExists:                                            "WALLET_ALREADY_EXISTS",
	WalletCreateSuccess:                                            "WALLET_CREATE_SUCCESS",
	WalletUpdatePermissionDenied:                                   "WALLET_UPDATE_PERMISSION_DENIED",
	WalletUpdateWalletNotFound:                                     "WALLET_UPDATE_WALLET_NOT_FOUND",
	WalletUpdateFailed:                                             "WALLET_UPDATE_FAILED",
	WalletUpdateWaasProviderNotFound:                               "WALLET_UPDATE_WAAS_PROVIDER_NOT_FOUND",
	WalletUpdateGetWaasWalletFailed:                                "WALLET_UPDATE_GET_WAAS_WALLET_FAILED",
	WalletUpdateWaasPersonalAccountCreateFailed:                    "WALLET_UPDATE_WAAS_PERSONAL_ACCOUNT_CREATE_FAILED",
	WalletUpdateSuccess:                                            "WALLET_UPDATE_SUCCESS",
	WalletListPermissionDenied:                                     "WALLET_LIST_PERMISSION_DENIED",
	WalletListWalletNotFound:                                       "WALLET_LIST_WALLET_NOT_FOUND",
	WalletListFindFailed:                                           "WALLET_LIST_FIND_FAILED",
	WalletGetPermissionDenied:                                      "WALLET_GET_PERMISSION_DENIED",
	WalletGetWalletNotFound:                                        "WALLET_GET_WALLET_NOT_FOUND",
	WalletGetFailed:                                                "WALLET_GET_FAILED",
	CurrencyListFailed:                                             "CURRENCY_LIST_FAILED",
	OrganizationUserCreatePermissionDenied:                         "ORGANIZATION_USER_CREATE_PERMISSION_DENIED",
	OrganizationCreateUserConversationIdAlreadyExists:              "ORGANIZATION_CREATE_USER_CONVERSATION_ID_ALREADY_EXISTS",
	OrganizationUserCreateOrganizationNotFound:                     "ORGANIZATION_USER_CREATE_ORGANIZATION_NOT_FOUND",
	OrganizationUserCreateOrganizationFindFailed:                   "ORGANIZATION_USER_CREATE_ORGANIZATION_FIND_FAILED",
	OrganizationUserCreateUserAlreadyExists:                        "ORGANIZATION_USER_CREATE_USER_ALREADY_EXISTS",
	OrganizationUserCreateUserCreateFailed:                         "ORGANIZATION_USER_CREATE_USER_CREATE_FAILED",
	OrganizationUserCreateLimitUserCreateFailed:                    "ORGANIZATION_USER_CREATE_LIMIT_USER_CREATE_FAILED",
	OrganizationUserCreateSuccess:                                  "ORGANIZATION_USER_CREATE_SUCCESS",
	OrganizationUserTokenCreatePermissionDenied:                    "ORGANIZATION_USER_TOKEN_CREATE_PERMISSION_DENIED",
	OrganizationUserTokenCreateOrganizationNotFound:                "ORGANIZATION_USER_TOKEN_CREATE_ORGANIZATION_NOT_FOUND",
	OrganizationUserTokenCreateOrganizationFindFailed:              "ORGANIZATION_USER_TOKEN_CREATE_ORGANIZATION_FIND_FAILED",
	OrganizationUserTokenCreateUserNotFound:                        "ORGANIZATION_USER_TOKEN_CREATE_USER_NOT_FOUND",
	OrganizationUserTokenCreateUserFindFailed:                      "ORGANIZATION_USER_TOKEN_CREATE_USER_FIND_FAILED",
	OrganizationUserTokenCreateTokenGenerateFailed:                 "ORGANIZATION_USER_TOKEN_CREATE_TOKEN_GENERATE_FAILED",
	OrganizationUserTokenCreateTokenCreateFailed:                   "ORGANIZATION_USER_TOKEN_CREATE_TOKEN_CREATE_FAILED",
	OrganizationUserTokenCreateSuccess:                             "ORGANIZATION_USER_TOKEN_CREATE_SUCCESS",
	OrganizationBusinessCreateAccountCreateFailed:                  "ORGANIZATION_BUSINESS_CREATE_ACCOUNT_CREATE_FAILED",
	OrganizationBusinessCreateAccountMetaCreateFailed:              "ORGANIZATION_BUSINESS_CREATE_ACCOUNT_META_CREATE_FAILED",
	OrganizationBusinessCreateAccountCreateSuccess:                 "ORGANIZATION_BUSINESS_CREATE_ACCOUNT_CREATE_SUCCESS",
	CardIssueSuccess:                                               "CARD_ISSUE_SUCCESS",
	CardIssuePermissionDenied:                                      "CARD_ISSUE_PERMISSION_DENIED",
	CardIssueFailed:                                                "CARD_ISSUE_FAILED",
	CardIssueUserNotFound:                                          "CARD_ISSUE_USER_NOT_FOUND",
	UserUpdateSucces:                                               "USER_UPDATE_SUCCESS",
	UserValidationNameFail:                                         "USER_VALIDATION_NAME_FAIL",
	UserValidationSurNameFail:                                      "USER_VALIDATION_SUR_NAME_FAIL",
	UserValidationFirstNameFail:                                    "USER_VALIDATION_FIRST_NAME_FAIL",
	UserValidationLastNameFail:                                     "USER_VALIDATION_LAST_NAME_FAIL",
	CompanyCreateSuccess:                                           "COMPANY_CREATE_SUCCESS",
	CompanyCreateFail:                                              "COMPANY_CREATE_FAIL",
	CompanyCreatePermissionDenied:                                  "COMPANY_CREATE_PERMISSION_DENIED",
	CompanyUpdateSuccess:                                           "COMPANY_UPDATE_SUCCESS",
	CompanyUpdateFail:                                              "COMPANY_UPDATE_FAIL",
	CompanyUpdatePermissionDenied:                                  "COMPANY_UPDATE_PERMITION_DENIED",
	CardUpdateInvalidPIN:                                           "CARD_UPDATE_INVALID_PIN",
	CardUpdateInvalidLimit:                                         "CARD_UPDATE_INVALID_LIMIT",
	OrderCreateInvalidAmount:                                       "ORDER_CREATE_INVALID_AMOUNT",
	OrderCreateBasketItemInvalidPrice:                              "ORDER_CREATE_BASKET_ITEM_INVALID_PRICE",
	OrderCreateBasketItemInvalidQuantity:                           "ORDER_CREATE_BASKET_ITEM_INVALID_QUANTITY",
	OrderCreateInvalidTaxAmount:                                    "ORDER_CREATE_INVALID_TAX_AMOUNT",
	OrderCreateInvalidEmail:                                        "ORDER_CREATE_INVALID_EMAIL",
	OrganizationCreateUserInvalidEmail:                             "ORGANIZATION_CREATE_USER_INVALID_EMAIL",
	OrderCreateBuyerInvalidEmail:                                   "ORDER_CREATE_BUYER_INVALID_EMAIL",
	OrderCreateSubOrganizationInvalidEmail:                         "ORDER_CREATE_SUB_ORGANIZATION_INVALID_EMAIL",
	OrganizationCreateUserEmailIsRequired:                          "ORGANIZATION_CREATE_USER_EMAIL_IS_REQUIRED",
	MagicLoginInvalidEmail:                                         "MAGIC_LOGIN_INVALID_EMAIL",
	MagicTestLoginInvalidEmail:                                     "MAGIC_TEST_LOGIN_INVALID_EMAIL",
	TFAuthInvalidEmail:                                             "TF_AUTH_INVALID_EMAIL",
	RegisterInvalidEmail:                                           "REGISTER_INVALID_EMAIL",
	TransactionDepositUserNotFound:                                 "TRANSACTION_DEPOSIT_USER_NOT_FOUND",
	TransactionDepositUserFindFailed:                               "TRANSACTION_DEPOSIT_USER_FIND_FAILED",
	TransactionDepositOrderCreateFailed:                            "TRANSACTION_DEPOSIT_ORDER_CREATE_FAILED",
	TransactionDepositStarted:                                      "TRANSACTION_DEPOSIT_STARTED",
	TransactionDepositWalletNotFound:                               "TRANSACTION_DEPOSIT_WALLET_NOT_FOUND",
	TransactionDepositWalletFindFailed:                             "TRANSACTION_DEPOSIT_WALLET_FIND_FAILED",
	TransactionDepositWalletCurrencyNotFound:                       "TRANSACTION_DEPOSIT_WALLET_CURRENCY_NOT_FOUND",
	TransactionDepositWalletCurrencyFindFailed:                     "TRANSACTION_DEPOSIT_WALLET_CURRENCY_FIND_FAILED",
	CurrencyNotFound:                                               "CURRENCY_ID_NOT_FOUND",
	CurrencyDisabled:                                               "CURRENCY_DISABLED",
	MetaNameNotFound:                                               "META_NAME_NOT_FOUND",
	MetaDataNotFound:                                               "META_DATA_NOT_FOUND",
	TwoDOrderCallbackFailed:                                        "TWO_D_ORDER_CALLBACK_FAILED",
	FeeListFailed:                                                  "FEE_LIST_FAILED",
	FeeNotFound:                                                    "FEE_NOT_FOUND",
	FeeCreateSuccess:                                               "FEE_CREATE_SUCCESS",
	FeeCreateFailed:                                                "FEE_CREATE_FAILED",
	FeeCreatePermissionDenied:                                      "FEE_CREATE_PERMISSION_DENIED",
	FeeUpdateSuccess:                                               "FEE_UPDATE_SUCCESS",
	FeeUpdateFailed:                                                "FEE_UPDATE_FAILED",
	FeeUpdatePermissionDenied:                                      "FEE_UPDATE_PERMISSION_DENIED",
	FeeDeleteSuccess:                                               "FEE_DELETE_SUCCESS",
	FeeDeleteFailed:                                                "FEE_DELETE_FAILED",
	FeeDeletePermissionDenied:                                      "FEE_DELETE_PERMISSION_DENIED",
	FeeUserIsNotOrganizationUser:                                   "FEE_USER_IS_NOT_ORGANIZATION_USER",
	FeeCreateInvalidName:                                           "FEE_CREATE_INVALID_NAME",
	FeeCreateInvalidAction:                                         "FEE_CREATE_INVALID_ACTION",
	FeeCreateInvalidAmount:                                         "FEE_CREATE_INVALID_AMOUNT",
	FeeCreateInvalidPercentage:                                     "FEE_CREATE_INVALID_PERCENTAGE",
	FeeUpdateInvalidName:                                           "FEE_UPDATE_INVALID_NAME",
	FeeUpdateInvalidAction:                                         "FEE_UPDATE_INVALID_ACTION",
	FeeUpdateInvalidAmount:                                         "FEE_UPDATE_INVALID_AMOUNT",
	FeeUpdateInvalidPercentage:                                     "FEE_UPDATE_INVALID_PERCENTAGE",
	AddDeviceFailed:                                                "ADD_DEVICE_FAILED",
	AddIpFailed:                                                    "ADD_IP_FAILED",
	AddTrustedDeviceSuccess:                                        "ADD_TRUSTED_DEVICE_SUCCESS",
	AddTrustedIpSuccess:                                            "ADD_TRUSTED_IP_SUCCESS",
	UpdateDeviceFailed:                                             "UPDATE_DEVICE_FAILED",
	DeleteTrustedDeviceSuccess:                                     "DELETE_TRUSTED_DEVICE_SUCCESS",
	DeleteTrustedIpSuccess:                                         "DELETE_TRUSTED_IP_SUCCESS",
	ProfileCreatePermissionDenied:                                  "PROFILE_CREATE_PERMISSION_DENIED",
	ProfileGetPermissionDenied:                                     "PROFILE_GET_PERMISSION_DENIED",
	ProfileDeletePermissionDenied:                                  "PROFILE_DELETE_PERMISSION_DENIED",
	ProfileUpdatePermissionDenied:                                  "PROFILE_UPDATE_PERMISSION_DENIED",
	DeviceAlreadyExist:                                             "DEVICE_ALREADY_EXIST",
	IpAlreadyExist:                                                 "IP_ALREADY_EXIST",
	TransactionShortcutSameReceiverAndSender:                       "TRANSACTION_SHORTCUT_SAME_RECEIVER_AND_SENDER",
	BankAccountTakeConsentFailed:                                   "BANK_ACCOUNT_TAKE_CONSENT_FAILED",
	BankAccountTakeConsentPermissionDenied:                         "BANK_ACCOUNT_TAKE_CONSENT_PERMISSION_DENIED",
	BankAccountTakeConsentSuccess:                                  "BANK_ACCOUNT_TAKE_CONSENT_SUCCESS",
	BankAccountTakeConsentBankAccountNotFound:                      "BANK_ACCOUNT_TAKE_CONSENT_BANK_ACCOUNT_NOT_FOUND",
	BankAccountCancelConsentBankAccountNotFound:                    "BANK_ACCOUNT_CANCEL_CONSENT_BANK_ACCOUNT_NOT_FOUND",
	BankAccountCancelConsentFailed:                                 "BANK_ACCOUNT_CANCEL_CONSENT_FAILED",
	BankAccountCancelConsentConsentNotFound:                        "BANK_ACCOUNT_CANCEL_CONSENT_CONSENT_NOT_FOUND",
	BankAccountCancelConsentSuccess:                                "BANK_ACCOUNT_CANCEL_CONSENT_SUCCESS",
	BankAccountGetConsentStatusConsentNotFound:                     "BANK_ACCOUNT_GET_CONSENT_STATUS_CONSENT_NOT_FOUND",
	BankAccountGetConsentStatusFailed:                              "BANK_ACCOUNT_GET_CONSENT_STATUS_FAILED",
	TransactionBankWithdrawIbanCannotBeEmpty:                       "TRANSACTION_BANK_WITHDRAW_IBAN_CANNOT_BE_EMPTY",
	TransactionBankWithdrawIbanIsNotValid:                          "TRANSACTION_BANK_WITHDRAW_IBAN_IS_NOT_VALID",
	TransactionBankWithdrawBankAccountTitleCannotBeEmpty:           "TRANSACTION_BANK_WITHDRAW_BANK_ACCOUNT_TITLE_CANNOT_BE_EMPTY",
	TransactionBankWithdrawBankAccountTitleIsNotValid:              "TRANSACTION_BANK_WITHDRAW_BANK_ACCOUNT_TITLE_IS_NOT_VALID",
	TransactionBankWithdrawBankAccountIdOrIbanCannotBeEmpty:        "TRANSACTION_BANK_WITHDRAW_BANK_ACCOUNT_ID_OR_IBAN_CANNOT_BE_EMPTY",
	TransactionBankWithdrawConversationIdAlreadyExists:             "TRANSACTION_BANK_WITHDRAW_CONVERSATION_ID_ALREADY_EXISTS",
	TransactionBankWithdrawBankAccountIdAndIbanCannotBeBothFilled:  "TRANSACTION_BANK_WITHDRAW_BANK_ACCOUNT_ID_AND_IBAN_CANNOT_BE_BOTH_FILLED",
	AuthSignUpInvalidEmail:                                         "AUTH_SIGN_UP_INVALID_EMAIL",
	AuthSignUpPasswordNotMatch:                                     "AUTH_SIGN_UP_PASSWORD_NOT_MATCH",
	AuthSignUpPasswordNotStrong:                                    "AUTH_SIGN_UP_PASSWORD_NOT_STRONG",
	AuthSignUpInvalidFirstName:                                     "AUTH_SIGN_UP_INVALID_FIRST_NAME",
	AuthSignUpInvalidLastName:                                      "AUTH_SIGN_UP_INVALID_LAST_NAME",
	AuthSignUpUserAlreadyExists:                                    "AUTH_SIGN_UP_USER_ALREADY_EXISTS",
	AuthSignUpFailed:                                               "AUTH_SIGN_UP_FAILED",
	AuthSignUpSuccess:                                              "AUTH_SIGN_UP_SUCCESS",
	AuthSignUpSuccessLoginFailed:                                   "AUTH_SIGN_UP_SUCCESS_LOGIN_FAILED",
	AuthActivateInvalidCode:                                        "AUTH_ACTIVATE_INVALID_CODE",
	AuthActivateUserNotFound:                                       "AUTH_ACTIVATE_USER_NOT_FOUND",
	AuthActivateFailed:                                             "AUTH_ACTIVATE_FAILED",
	AuthActivateUserAlreadyActive:                                  "AUTH_ACTIVATE_USER_ALREADY_ACTIVE",
	AuthActivateSuccess:                                            "AUTH_ACTIVATE_SUCCESS",
	AuthIdentifyInvalidEmail:                                       "AUTH_IDENTIFY_INVALID_EMAIL",
	AuthIdentifyUserNotFound:                                       "AUTH_IDENTIFY_USER_NOT_FOUND",
	AuthIdentifyFailed:                                             "AUTH_IDENTIFY_FAILED",
	AuthIdentifyUserNotActive:                                      "AUTH_IDENTIFY_USER_NOT_ACTIVE",
	AuthIdentifyUserIPNotAllowed:                                   "AUTH_IDENTIFY_USER_IP_NOT_ALLOWED",
	AuthIdentifySuccess:                                            "AUTH_IDENTIFY_SUCCESS",
	AuthAuthenticateEmailRequired:                                  "AUTH_AUTHENTICATE_EMAIL_REQUIRED",
	AuthAuthenticateInvalidEmail:                                   "AUTH_AUTHENTICATE_INVALID_EMAIL",
	AuthAuthenticatePasswordRequired:                               "AUTH_AUTHENTICATE_PASSWORD_REQUIRED",
	AuthAuthenticateUserNotFound:                                   "AUTH_AUTHENTICATE_USER_NOT_FOUND",
	AuthAuthenticateFailed:                                         "AUTH_AUTHENTICATE_FAILED",
	AuthAuthenticateUserIPNotAllowed:                               "AUTH_AUTHENTICATE_USER_IP_NOT_ALLOWED",
	AuthAuthenticateUserNotActive:                                  "AUTH_AUTHENTICATE_USER_NOT_ACTIVE",
	AuthAuthenticateInvalidPassword:                                "AUTH_AUTHENTICATE_INVALID_PASSWORD",
	AuthAuthenticateOrganizationNotFound:                           "AUTH_AUTHENTICATE_ORGANIZATION_NOT_FOUND",
	AuthAuthenticateSuccess:                                        "AUTH_AUTHENTICATE_SUCCESS",
	AuthCreatePinEmailRequired:                                     "AUTH_CREATE_PIN_EMAIL_REQUIRED",
	AuthCreatePinInvalidEmail:                                      "AUTH_CREATE_PIN_INVALID_EMAIL",
	AuthCreatePinUserNotFound:                                      "AUTH_CREATE_PIN_USER_NOT_FOUND",
	AuthCreatePinFailed:                                            "AUTH_CREATE_PIN_FAILED",
	AuthCreatePinUserNotActive:                                     "AUTH_CREATE_PIN_USER_NOT_ACTIVE",
	AuthCreatePinUserPinAuthNotActive:                              "AUTH_CREATE_PIN_USER_PIN_AUTH_NOT_ACTIVE",
	AuthCreatePinUserIPNotAllowed:                                  "AUTH_CREATE_PIN_USER_IP_NOT_ALLOWED",
	AuthCreatePinSuccess:                                           "AUTH_CREATE_PIN_SUCCESS",
	AuthPinAuthEmailRequired:                                       "AUTH_PIN_AUTH_EMAIL_REQUIRED",
	AuthPinAuthInvalidEmail:                                        "AUTH_PIN_AUTH_INVALID_EMAIL",
	AuthPinAuthPinRequired:                                         "AUTH_PIN_AUTH_PIN_REQUIRED",
	AuthPinAuthUserNotFound:                                        "AUTH_PIN_AUTH_USER_NOT_FOUND",
	AuthPinAuthUserNotActive:                                       "AUTH_PIN_AUTH_USER_NOT_ACTIVE",
	AuthPinAuthUserIPNotAllowed:                                    "AUTH_PIN_AUTH_USER_IP_NOT_ALLOWED",
	AuthPinAuthPinExpired:                                          "AUTH_PIN_AUTH_PIN_EXPIRED",
	AuthPinAuthFailed:                                              "AUTH_PIN_AUTH_FAILED",
	AuthPinAuthSuccess:                                             "AUTH_PIN_AUTH_SUCCESS",
	AuthTwoFactorAuthEmailRequired:                                 "AUTH_TWO_FACTOR_AUTH_EMAIL_REQUIRED",
	AuthTwoFactorAuthInvalidEmail:                                  "AUTH_TWO_FACTOR_AUTH_INVALID_EMAIL",
	AuthTwoFactorAuthCodeRequired:                                  "AUTH_TWO_FACTOR_AUTH_CODE_REQUIRED",
	AuthTwoFactorAuthUserNotFound:                                  "AUTH_TWO_FACTOR_AUTH_USER_NOT_FOUND",
	AuthTwoFactorAuthFailed:                                        "AUTH_TWO_FACTOR_AUTH_FAILED",
	AuthTwoFactorAuthUserNotActive:                                 "AUTH_TWO_FACTOR_AUTH_USER_NOT_ACTIVE",
	AuthTwoFactorAuthUserTwoFactorAuthNotActive:                    "AUTH_TWO_FACTOR_AUTH_USER_TWO_FACTOR_AUTH_NOT_ACTIVE",
	AuthTwoFactorAuthUserIPNotAllowed:                              "AUTH_TWO_FACTOR_AUTH_USER_IP_NOT_ALLOWED",
	AuthTwoFactorAuthInvalidCode:                                   "AUTH_TWO_FACTOR_AUTH_INVALID_CODE",
	AuthTwoFactorAuthSuccess:                                       "AUTH_TWO_FACTOR_AUTH_SUCCESS",
	AuthMagicLoginEmailRequired:                                    "AUTH_MAGIC_LOGIN_EMAIL_REQUIRED",
	AuthMagicLoginInvalidEmail:                                     "AUTH_MAGIC_LOGIN_INVALID_EMAIL",
	AuthMagicLoginUserNotFound:                                     "AUTH_MAGIC_LOGIN_USER_NOT_FOUND",
	AuthMagicLoginFailed:                                           "AUTH_MAGIC_LOGIN_FAILED",
	AuthMagicLoginUserNotActive:                                    "AUTH_MAGIC_LOGIN_USER_NOT_ACTIVE",
	AuthMagicLoginUserIPNotAllowed:                                 "AUTH_MAGIC_LOGIN_USER_IP_NOT_ALLOWED",
	AuthMagicLoginSuccess:                                          "AUTH_MAGIC_LOGIN_SUCCESS",
	AuthVerifyTokenTokenRequired:                                   "AUTH_VERIFY_TOKEN_TOKEN_REQUIRED",
	AuthVerifyTokenTokenExpired:                                    "AUTH_VERIFY_TOKEN_TOKEN_EXPIRED",
	AuthVerifyTokenInvalid:                                         "AUTH_VERIFY_TOKEN_INVALID",
	AuthVerifyTokenUserNotFound:                                    "AUTH_VERIFY_TOKEN_USER_NOT_FOUND",
	AuthVerifyTokenUserIPNotAllowed:                                "AUTH_VERIFY_TOKEN_USER_IP_NOT_ALLOWED",
	AuthVerifyTokenUserDifferentAgent:                              "AUTH_VERIFY_TOKEN_USER_DIFFERENT_AGENT",
	AuthVerifyTokenFailed:                                          "AUTH_VERIFY_TOKEN_FAILED",
	AuthForgotPasswordEmailRequired:                                "AUTH_FORGOT_PASSWORD_EMAIL_REQUIRED",
	AuthForgotPasswordInvalidEmail:                                 "AUTH_FORGOT_PASSWORD_INVALID_EMAIL",
	AuthForgotPasswordUserNotFound:                                 "AUTH_FORGOT_PASSWORD_USER_NOT_FOUND",
	AuthForgotPasswordUserNotActive:                                "AUTH_FORGOT_PASSWORD_USER_NOT_ACTIVE",
	AuthForgotPasswordUserAlreadySent:                              "AUTH_FORGOT_PASSWORD_USER_ALREADY_SENT",
	AuthForgotPasswordUserIPNotAllowed:                             "AUTH_FORGOT_PASSWORD_USER_IP_NOT_ALLOWED",
	AuthForgotPasswordFailed:                                       "AUTH_FORGOT_PASSWORD_FAILED",
	AuthForgotPasswordSuccess:                                      "AUTH_FORGOT_PASSWORD_SUCCESS",
	AuthResetPasswordEmailRequired:                                 "AUTH_RESET_PASSWORD_EMAIL_REQUIRED",
	AuthResetPasswordInvalidEmail:                                  "AUTH_RESET_PASSWORD_INVALID_EMAIL",
	AuthResetPasswordCodeRequired:                                  "AUTH_RESET_PASSWORD_CODE_REQUIRED",
	AuthResetPasswordPasswordRequired:                              "AUTH_RESET_PASSWORD_PASSWORD_REQUIRED",
	AuthResetPasswordPasswordConfirmRequired:                       "AUTH_RESET_PASSWORD_PASSWORD_CONFIRM_REQUIRED",
	AuthResetPasswordPasswordNotMatch:                              "AUTH_RESET_PASSWORD_PASSWORD_NOT_MATCH",
	AuthResetPasswordPasswordNotStrong:                             "AUTH_RESET_PASSWORD_PASSWORD_NOT_STRONG",
	AuthResetPasswordOtpRequired:                                   "AUTH_RESET_PASSWORD_OTP_REQUIRED",
	AuthResetPasswordOtpInvalid:                                    "AUTH_RESET_PASSWORD_OTP_INVALID",
	AuthResetPasswordUserNotFound:                                  "AUTH_RESET_PASSWORD_USER_NOT_FOUND",
	AuthResetPasswordFailed:                                        "AUTH_RESET_PASSWORD_FAILED",
	AuthResetPasswordUserNotActive:                                 "AUTH_RESET_PASSWORD_USER_NOT_ACTIVE",
	AuthResetPasswordUserIPNotAllowed:                              "AUTH_RESET_PASSWORD_USER_IP_NOT_ALLOWED",
	AuthResetPasswordCodeExpired:                                   "AUTH_RESET_PASSWORD_CODE_EXPIRED",
	AuthResetPasswordSuccess:                                       "AUTH_RESET_PASSWORD_SUCCESS",
	AuthLogoutTokenRequired:                                        "AUTH_LOGOUT_TOKEN_REQUIRED",
	AuthLogoutSuccess:                                              "AUTH_LOGOUT_SUCCESS",
	TransactionShortcutUpdateReceiverDescriptorCannotBeEmpty:       "TRANSACTION_SHORTCUT_UPDATE_RECEIVER_DESCRIPTOR_CANNOT_BE_EMPTY",
	TransactionShortcutUpdateReceiverIbanCannotBeEmpty:             "TRANSACTION_SHORTCUT_UPDATE_RECEIVER_IBAN_CANNOT_BE_EMPTY",
	TransactionShortcutUpdateReceiverIbanIsNotValid:                "TRANSACTION_SHORTCUT_UPDATE_RECEIVER_IBAN_IS_NOT_VALID",
	TransactionShortcutUpdateReceiverBankAccountTitleCannotBeEmpty: "TRANSACTION_SHORTCUT_UPDATE_RECEIVER_BANK_ACCOUNT_TITLE_CANNOT_BE_EMPTY",
	OrderConversationIdAlreadyExists:                               "ORDER_CONVERSATION_ID_ALREADY_EXISTS",
	LimitMaxMonthlyTxnCountReached:                                 "LIMIT_MAX_MONTHLY_TXN_COUNT_REACHED",
	LimitMaxDailyTxnCountReached:                                   "LIMIT_MAX_DAILY_TXN_COUNT_REACHED",
	LimitMaxMonthlyTxnAmtReached:                                   "LIMIT_MAX_MONTHLY_TXN_AMT_REACHED",
	LimitMaxDailyTxnAmtReached:                                     "LIMIT_MAX_DAILY_TXN_AMT_REACHED",
	LimitMaxIncomeAmtReached:                                       "LIMIT_MAX_INCOME_AMT_REACHED",
	LimitMaxExpenseAmtReached:                                      "LIMIT_MAX_EXPENSE_AMT_REACHED",
	LimitMaxWithdrawalAmtReached:                                   "LIMIT_MAX_WITHDRAWAL_AMT_REACHED",
	LimitMaxTopupAmtReached:                                        "LIMIT_MAX_TOPUP_AMT_REACHED",
	LimitMaxWalletBalanceReached:                                   "LIMIT_MAX_WALLET_BALANCE_REACHED",
	LimitOneTimeMaxTopupAmtReached:                                 "LIMIT_ONE_TIME_MAX_TOPUP_AMT_REACHED",
	LimitOneTimeMaxTxnAmtReached:                                   "LIMIT_ONE_TIME_MAX_TXN_AMT_REACHED",
	LimitOneTimeMaxTxnCountReached:                                 "LIMIT_ONE_TIME_MAX_TXN_COUNT_REACHED",
	LimitOneTimeMaxWithdrawalAmtReached:                            "LIMIT_ONE_TIME_MAX_WITHDRAWAL_AMT_REACHED",
	LimitTransactionAmountGreaterThanMaxTopupAmt:                   "LIMIT_TRANSACTION_AMOUNT_GREATER_THAN_MAX_TOPUP_AMT",
	LimitTransactionAmountGreaterThanMaxDailyTxnAmt:                "LIMIT_TRANSACTION_AMOUNT_GREATER_THAN_MAX_DAILY_TXN_AMT",
	LimitTransactionAmountGreaterThanMaxMonthlyTxnAmt:              "LIMIT_TRANSACTION_AMOUNT_GREATER_THAN_MAX_MONTHLY_TXN_AMT",
	LimitTransactionAmountGreaterThanMaxExpenseAmt:                 "LIMIT_TRANSACTION_AMOUNT_GREATER_THAN_MAX_EXPENSE_AMT",
	LimitTransactionAmountGreaterThanOneTimeMaxTxnAmt:              "LIMIT_TRANSACTION_AMOUNT_GREATER_THAN_ONE_TIME_MAX_TXN_AMT",
	LimitTransactionAmountGreaterThanMaxIncomeAmt:                  "LIMIT_TRANSACTION_AMOUNT_GREATER_THAN_MAX_INCOME_AMT",
	LimitMaxWalletCountReached:                                     "LIMIT_MAX_WALLET_COUNT_REACHED",
	TransactionW2WSendWaasProviderNotFound:                         "TRANSACTION_W2W_SEND_WAAS_PROVIDER_NOT_FOUND",
	TransactionW2WSendWaasProviderFindFailed:                       "TRANSACTION_W2W_SEND_WAAS_PROVIDER_FIND_FAILED",
	TransactionW2WSendWaasUserNotFound:                             "TRANSACTION_W2W_SEND_WAAS_USER_NOT_FOUND",
	TransactionW2WSendWaasUserFindFailed:                           "TRANSACTION_W2W_SEND_WAAS_USER_FIND_FAILED",
	TransactionW2WSendWaasWalletNotFound:                           "TRANSACTION_W2W_SEND_WAAS_WALLET_NOT_FOUND",
	TransactionW2WSendWaasWalletFindFailed:                         "TRANSACTION_W2W_SEND_WAAS_WALLET_FIND_FAILED",
	OrderAddPaymentTermPermissionDenied:                            "ORDER_ADD_PAYMENT_TERM_PERMISSION_DENIED",
	OrderAddPaymentTermNotFound:                                    "ORDER_ADD_PAYMENT_TERM_NOT_FOUND",
	OrderAddPaymentTermFailed:                                      "ORDER_ADD_PAYMENT_TERM_FAILED",
	OrderAddPaymentTermCurrencyNotFound:                            "ORDER_ADD_PAYMENT_TERM_CURRENCY_NOT_FOUND",
	OrderAddPaymentTermCurrencyFailed:                              "ORDER_ADD_PAYMENT_TERM_CURRENCY_FAILED",
	OrderAddPaymentTermSuccess:                                     "ORDER_ADD_PAYMENT_TERM_SUCCESS",
	OrderRemovePaymentTermPermissionDenied:                         "ORDER_REMOVE_PAYMENT_TERM_PERMISSION_DENIED",
	OrderRemovePaymentTermNotFound:                                 "ORDER_REMOVE_PAYMENT_TERM_NOT_FOUND",
	OrderRemovePaymentTermFailed:                                   "ORDER_REMOVE_PAYMENT_TERM_FAILED",
	OrderRemovePaymentTermIsPaid:                                   "ORDER_REMOVE_PAYMENT_TERM_IS_PAID",
	OrderRemovePaymentTermSuccess:                                  "ORDER_REMOVE_PAYMENT_TERM_SUCCESS",
	OrderUpdatePaymentTermPermissionDenied:                         "ORDER_UPDATE_PAYMENT_TERM_PERMISSION_DENIED",
	OrderUpdatePaymentTermNotFound:                                 "ORDER_UPDATE_PAYMENT_TERM_NOT_FOUND",
	OrderUpdatePaymentTermFailed:                                   "ORDER_UPDATE_PAYMENT_TERM_FAILED",
	OrderUpdatePaymentTermSuccess:                                  "ORDER_UPDATE_PAYMENT_TERM_SUCCESS",
	OrderUpdatePermissionDenied:                                    "ORDER_UPDATE_PERMISSION_DENIED",
	OrderUpdateOrderNotFound:                                       "ORDER_UPDATE_ORDER_NOT_FOUND",
	OrderUpdateOrderFindFailed:                                     "ORDER_UPDATE_ORDER_FIND_FAILED",
	OrderUpdateOrderSuccess:                                        "ORDER_UPDATE_ORDER_SUCCESS",
	TokenApiUserDeleteFailed:                                       "TOKEN_API_USER_DELETE_FAILED",
	TransferSendMoneyWalletNotFound:                                "TRANSFER_SEND_MONEY_WALLET_NOT_FOUND",
	TransferSendMoneyWalletFindFailed:                              "TRANSFER_SEND_MONEY_WALLET_FIND_FAILED",
	TransferSendMoneyQuotationFailed:                               "TRANSFER_SEND_MONEY_QUOTATION_FAILED",
	TransferSendMoneyInsufficientBalance:                           "TRANSFER_SEND_MONEY_INSUFFICIENT_BALANCE",
	TransferSendMoneyFailed:                                        "TRANSFER_SEND_MONEY_FAILED",
	TransferSendMoneySuccess:                                       "TRANSFER_SEND_MONEY_SUCCESS",
	TransferSendMoneyTransactionCreateFailed:                       "TRANSFER_SEND_MONEY_TRANSACTION_CREATE_FAILED",
	TransferSendMoneyLimitFailed:                                   "TRANSFER_SEND_MONEY_LIMIT_FAILED",
	TransactionIssueMoneyConversationIDAlreadyExists:               "TRANSACTION_ISSUE_MONEY_CONVERSATION_ID_ALREADY_EXISTS",
	TransactionIssueMoneyWalletNotFound:                            "TRANSACTION_ISSUE_MONEY_WALLET_NOT_FOUND",
	TransactionIssueMoneyWalletGetFailed:                           "TRANSACTION_ISSUE_MONEY_WALLET_GET_FAILED",
	TransactionIssueMoneyWalletCurrencyNotMatch:                    "TRANSACTION_ISSUE_MONEY_WALLET_CURRENCY_NOT_MATCH",
	TransactionIssueMoneyAmountMustBePositive:                      "TRANSACTION_ISSUE_MONEY_AMOUNT_MUST_BE_POSITIVE",
	TransactionIssueMoneyWalletUpdateFailed:                        "TRANSACTION_ISSUE_MONEY_WALLET_UPDATE_FAILED",
	TransactionIssueMoneyReferenceIdNotValid:                       "TRANSACTION_ISSUE_MONEY_REFERENCE_ID_NOT_VALID",
	TransactionIssueMoneyTransactionCreateFailed:                   "TRANSACTION_ISSUE_MONEY_TRANSACTION_CREATE_FAILED",
	TransactionIssueMoneyTransactionCommitFailed:                   "TRANSACTION_ISSUE_MONEY_TRANSACTION_COMMIT_FAILED",
	TransactionIssueMoneySuccess:                                   "TRANSACTION_ISSUE_MONEY_SUCCESS",
	TransactionIssueMoneyPermissionDenied:                          "TRANSACTION_ISSUE_MONEY_PERMISSION_DENIED",
	TransactionIssueMoneyOrganizationWalletGetFailed:               "TRANSACTION_ISSUE_MONEY_ORGANIZATION_WALLET_GET_FAILED",
	TransactionBurnMoneyPermissionDenied:                           "TRANSACTION_BURN_MONEY_PERMISSION_DENIED",
	TransactionBurnMoneyConversationIDAlreadyExists:                "TRANSACTION_BURN_MONEY_CONVERSATION_ID_ALREADY_EXISTS",
	TransactionBurnMoneyWalletNotFound:                             "TRANSACTION_BURN_MONEY_WALLET_NOT_FOUND",
	TransactionBurnMoneyWalletGetFailed:                            "TRANSACTION_BURN_MONEY_WALLET_GET_FAILED",
	TransactionBurnMoneyWalletCurrencyNotMatch:                     "TRANSACTION_BURN_MONEY_WALLET_CURRENCY_NOT_MATCH",
	TransactionBurnMoneyAmountMustBePositive:                       "TRANSACTION_BURN_MONEY_AMOUNT_MUST_BE_POSITIVE",
	TransactionBurnMoneyWalletBalanceLessThanZero:                  "TRANSACTION_BURN_MONEY_WALLET_BALANCE_LESS_THAN_ZERO",
	TransactionBurnMoneyWalletUpdateFailed:                         "TRANSACTION_BURN_MONEY_WALLET_UPDATE_FAILED",
	TransactionBurnMoneyReferenceIdNotValid:                        "TRANSACTION_BURN_MONEY_REFERENCE_ID_NOT_VALID",
	TransactionBurnMoneyTransactionCreateFailed:                    "TRANSACTION_BURN_MONEY_TRANSACTION_CREATE_FAILED",
	TransactionBurnMoneyTransactionCommitFailed:                    "TRANSACTION_BURN_MONEY_TRANSACTION_COMMIT_FAILED",
	TransactionBurnMoneySuccess:                                    "TRANSACTION_BURN_MONEY_SUCCESS",
	OrderPayWithWalletOrderNotFound:                                "ORDER_PAY_WITH_WALLET_ORDER_NOT_FOUND",
	OrderPayWithWalletOrderFindFailed:                              "ORDER_PAY_WITH_WALLET_ORDER_FIND_FAILED",
	OrderPayWithWalletOrderAlreadyPaid:                             "ORDER_PAY_WITH_WALLET_ORDER_ALREADY_PAID",
	OrderPayWithWalletOrderAlreadyCancelled:                        "ORDER_PAY_WITH_WALLET_ORDER_ALREADY_CANCELLED",
	OrderPayWithWalletOrderAlreadyRefunded:                         "ORDER_PAY_WITH_WALLET_ORDER_ALREADY_REFUNDED",
	OrderPayWithWalletOrderAlreadyPartiallyRefunded:                "ORDER_PAY_WITH_WALLET_ORDER_ALREADY_PARTIALLY_REFUNDED",
	OrderPayWithWalletUserWalletNotFound:                           "ORDER_PAY_WITH_WALLET_USER_WALLET_NOT_FOUND",
	OrderPayWithWalletUserWalletFindFailed:                         "ORDER_PAY_WITH_WALLET_USER_WALLET_FIND_FAILED",
	OrderPayWithWalletInsufficientBalance:                          "ORDER_PAY_WITH_WALLET_INSUFFICIENT_BALANCE",
	OrderPayWithWalletOrganizationWalletNotFound:                   "ORDER_PAY_WITH_WALLET_ORGANIZATION_WALLET_NOT_FOUND",
	OrderPayWithWalletOrganizationWalletFindFailed:                 "ORDER_PAY_WITH_WALLET_ORGANIZATION_WALLET_FIND_FAILED",
	OrderPayWithWalletSuccess:                                      "ORDER_PAY_WITH_WALLET_SUCCESS",
	OrderPayWithWalletFailed:                                       "ORDER_PAY_WITH_WALLET_FAILED",
	OrderPayWithWalletPaymentTermsNotFound:                         "ORDER_PAY_WITH_WALLET_PAYMENT_TERMS_NOT_FOUND",
	OrderPayWithWalletPaymentTermsFindFailed:                       "ORDER_PAY_WITH_WALLET_PAYMENT_TERMS_FIND_FAILED",
	OrderPayWithWalletUserWalletUpdateFailed:                       "ORDER_PAY_WITH_WALLET_USER_WALLET_UPDATE_FAILED",
	OrderPayWithWalletOrganizationWalletUpdateFailed:               "ORDER_PAY_WITH_WALLET_ORGANIZATION_WALLET_UPDATE_FAILED",
	OrganizationSetLimitUserPermissionDenied:                       "ORGANIZATION_SET_LIMIT_USER_PERMISSION_DENIED",
	OrganizationSetLimitUserUserNotFound:                           "ORGANIZATION_SET_LIMIT_USER_USER_NOT_FOUND",
	OrganizationSetLimitUserUserFindFailed:                         "ORGANIZATION_SET_LIMIT_USER_USER_FIND_FAILED",
	OrganizationSetLimitUserLimitNotFound:                          "ORGANIZATION_SET_LIMIT_USER_LIMIT_NOT_FOUND",
	OrganizationSetLimitUserLimitFindFailed:                        "ORGANIZATION_SET_LIMIT_USER_LIMIT_FIND_FAILED",
	OrganizationSetLimitFailed:                                     "ORGANIZATION_SET_LIMIT_FAILED",
	OrganizationSetLimitSuccess:                                    "ORGANIZATION_SET_LIMIT_SUCCESS",
	OrganizationGetUserLimitPermissionDenied:                       "ORGANIZATION_GET_USER_LIMIT_PERMISSION_DENIED",
	OrganizationGetUserLimitUserNotFound:                           "ORGANIZATION_GET_USER_LIMIT_USER_NOT_FOUND",
	OrganizationGetUserLimitUserFindFailed:                         "ORGANIZATION_GET_USER_LIMIT_USER_FIND_FAILED",
	LimitNotFound:                                                  "LIMIT_NOT_FOUND",
	LimitGetFailed:                                                 "LIMIT_GET_FAILED",
	CardBinNotFound:                                                "CARD_BIN_NOT_FOUND",
	CardBinFindFailed:                                              "CARD_BIN_FIND_FAILED",
	TransferWithdrawalRequestListFailed:                            "TRANSFER_WITHDRAWAL_REQUEST_LIST_FAILED",
	TransferWithdrawalCreateWalletNotFound:                         "TRANSFER_WITHDRAWAL_CREATE_WALLET_NOT_FOUND",
	TransferWithdrawalCreateWalletFindFailed:                       "TRANSFER_WITHDRAWAL_CREATE_WALLET_FIND_FAILED",
	TransferWithdrawalCreateAmountMustBeGreaterThanZero:            "TRANSFER_WITHDRAWAL_CREATE_AMOUNT_MUST_BE_GREATER_THAN_ZERO",
	TransferWithdrawalCreateInsufficientBalance:                    "TRANSFER_WITHDRAWAL_CREATE_INSUFFICIENT_BALANCE",
	TransferWithdrawalCreateTransferWalletNotFound:                 "TRANSFER_WITHDRAWAL_CREATE_TRANSFER_WALLET_NOT_FOUND",
	TransferWithdrawalCreateTransferWalletFindFailed:               "TRANSFER_WITHDRAWAL_CREATE_TRANSFER_WALLET_FIND_FAILED",
	TransferWithdrawalCreateMoneyTransferCreateFailed:              "TRANSFER_WITHDRAWAL_CREATE_MONEY_TRANSFER_CREATE_FAILED",
	TransferWithdrawalCreateWalletUpdateFailed:                     "TRANSFER_WITHDRAWAL_CREATE_WALLET_UPDATE_FAILED",
	TransferWithdrawalCreateTransactionCreateFailed:                "TRANSFER_WITHDRAWAL_CREATE_TRANSACTION_CREATE_FAILED",
	TransferWithdrawalStatusMoneyTransferNotFound:                  "TRANSFER_WITHDRAWAL_STATUS_MONEY_TRANSFER_NOT_FOUND",
	TransferWithdrawalStatusMoneyTransferFindFailed:                "TRANSFER_WITHDRAWAL_STATUS_MONEY_TRANSFER_FIND_FAILED",
	TransferWithdrawalStatusUserNotFound:                           "TRANSFER_WITHDRAWAL_STATUS_USER_NOT_FOUND",
	TransferWithdrawalStatusUserFindFailed:                         "TRANSFER_WITHDRAWAL_STATUS_USER_FIND_FAILED",
	TransferWithdrawalStatusCurrencyNotFound:                       "TRANSFER_WITHDRAWAL_STATUS_CURRENCY_NOT_FOUND",
	TransferWithdrawalStatusCurrencyFindFailed:                     "TRANSFER_WITHDRAWAL_STATUS_CURRENCY_FIND_FAILED",
	TransferWithdrawalStatusSuccess:                                "TRANSFER_WITHDRAWAL_STATUS_SUCCESS",
	TransferWithdrawalCreateSuccess:                                "TRANSFER_WITHDRAWAL_CREATE_SUCCESS",
	TransferWithdrawalCreateReceiverOrganizationNotFound:           "TRANSFER_WITHDRAWAL_CREATE_RECEIVER_ORGANIZATION_NOT_FOUND",
	TransferWithdrawalCreateReceiverOrganizationFindFailed:         "TRANSFER_WITHDRAWAL_CREATE_RECEIVER_ORGANIZATION_FIND_FAILED",
	TransferWithdrawalCreateTransactionCommitFailed:                "TRANSFER_WITHDRAWAL_CREATE_TRANSACTION_COMMIT_FAILED",
	TransferWithdrawalApplyTransactionNotFound:                     "TRANSFER_WITHDRAWAL_APPLY_TRANSACTION_NOT_FOUND",
	TransferWithdrawalApplyTransactionFindFailed:                   "TRANSFER_WITHDRAWAL_APPLY_TRANSACTION_FIND_FAILED",
	TransferWithdrawalApplyTransactionUpdateFailed:                 "TRANSFER_WITHDRAWAL_APPLY_TRANSACTION_UPDATE_FAILED",
	TransferWithdrawalApplyMoneyTransferUpdateFailed:               "TRANSFER_WITHDRAWAL_APPLY_MONEY_TRANSFER_UPDATE_FAILED",
	TransferWithdrawalApplyTransactionCommitFailed:                 "TRANSFER_WITHDRAWAL_APPLY_TRANSACTION_COMMIT_FAILED",
	TransferWithdrawalApplySuccess:                                 "TRANSFER_WITHDRAWAL_APPLY_SUCCESS",
	TransferWithdrawalApplyTransferWalletNotFound:                  "TRANSFER_WITHDRAWAL_APPLY_TRANSFER_WALLET_NOT_FOUND",
	TransferWithdrawalApplyTransferWalletFindFailed:                "TRANSFER_WITHDRAWAL_APPLY_TRANSFER_WALLET_FIND_FAILED",
	TransferWithdrawalApplyInsufficientBalance:                     "TRANSFER_WITHDRAWAL_APPLY_INSUFFICIENT_BALANCE",
	TransferWithdrawalApplyTransferWalletUpdateFailed:              "TRANSFER_WITHDRAWAL_APPLY_TRANSFER_WALLET_UPDATE_FAILED",
	TransferWithdrawalCancelMoneyTransferNotFound:                  "TRANSFER_WITHDRAWAL_CANCEL_MONEY_TRANSFER_NOT_FOUND",
	TransferWithdrawalCancelMoneyTransferFindFailed:                "TRANSFER_WITHDRAWAL_CANCEL_MONEY_TRANSFER_FIND_FAILED",
	TransferWithdrawalCancelTransactionNotFound:                    "TRANSFER_WITHDRAWAL_CANCEL_TRANSACTION_NOT_FOUND",
	TransferWithdrawalCancelTransactionFindFailed:                  "TRANSFER_WITHDRAWAL_CANCEL_TRANSACTION_FIND_FAILED",
	TransferWithdrawalCancelTransactionUpdateFailed:                "TRANSFER_WITHDRAWAL_CANCEL_TRANSACTION_UPDATE_FAILED",
	TransferWithdrawalCancelMoneyTransferUpdateFailed:              "TRANSFER_WITHDRAWAL_CANCEL_MONEY_TRANSFER_UPDATE_FAILED",
	TransferWithdrawalCancelTransferWalletNotFound:                 "TRANSFER_WITHDRAWAL_CANCEL_TRANSFER_WALLET_NOT_FOUND",
	TransferWithdrawalCancelTransferWalletFindFailed:               "TRANSFER_WITHDRAWAL_CANCEL_TRANSFER_WALLET_FIND_FAILED",
	TransferWithdrawalCancelInsufficientBalance:                    "TRANSFER_WITHDRAWAL_CANCEL_INSUFFICIENT_BALANCE",
	TransferWithdrawalCancelOrganizationFindFailed:                 "TRANSFER_WITHDRAWAL_CANCEL_ORGANIZATION_FIND_FAILED",
	TransferWithdrawalCancelTransactionCreateFailed:                "TRANSFER_WITHDRAWAL_CANCEL_TRANSACTION_CREATE_FAILED",
	TransferWithdrawalCancelTransactionCommitFailed:                "TRANSFER_WITHDRAWAL_CANCEL_TRANSACTION_COMMIT_FAILED",
	TransferWithdrawalCancelSuccess:                                "TRANSFER_WITHDRAWAL_CANCEL_SUCCESS",
	TransferWithdrawalCancelTransferWalletUpdateFailed:             "TRANSFER_WITHDRAWAL_CANCEL_TRANSFER_WALLET_UPDATE_FAILED",
	TransferWithdrawalCancelUserWalletNotFound:                     "TRANSFER_WITHDRAWAL_CANCEL_USER_WALLET_NOT_FOUND",
	TransferWithdrawalCancelUserWalletFindFailed:                   "TRANSFER_WITHDRAWAL_CANCEL_USER_WALLET_FIND_FAILED",
	TransferWithdrawalCancelUserWalletUpdateFailed:                 "TRANSFER_WITHDRAWAL_CANCEL_USER_WALLET_UPDATE_FAILED",
	TransferWithdrawalStatusWalletNotFound:                         "TRANSFER_WITHDRAWAL_STATUS_WALLET_NOT_FOUND",
	TransferWithdrawalStatusWalletFindFailed:                       "TRANSFER_WITHDRAWAL_STATUS_WALLET_FIND_FAILED",
	TransferTopupMoneyWalletNotFound:                               "TRANSFER_TOPUP_MONEY_WALLET_NOT_FOUND",
	TransferTopupMoneyWalletFindFailed:                             "TRANSFER_TOPUP_MONEY_WALLET_FIND_FAILED",
	TransferTopupMoneyCurrencyNotFound:                             "TRANSFER_TOPUP_MONEY_CURRENCY_NOT_FOUND",
	TransferTopupMoneyCurrencyFindFailed:                           "TRANSFER_TOPUP_MONEY_CURRENCY_FIND_FAILED",
	TransferTopupMoneyCurrencyMismatch:                             "TRANSFER_TOPUP_MONEY_CURRENCY_MISMATCH",
	TransferTopupMoneyQuotationExpired:                             "TRANSFER_TOPUP_MONEY_QUOTATION_EXPIRED",
	TransactionGetTxDocumentFailed:                                 "TRANSACTION_GET_TX_DOCUMENT_FAILED",
	TransactionGetTxDocumentSuccess:                                "TRANSACTION_GET_TX_DOCUMENT_SUCCESS",
	OrganizationDesignOrganizationNotFound:                         "ORGANIZATION_DESIGN_ORGANIZATION_NOT_FOUND",
	OrganizationDesignNotFound:                                     "ORGANIZATION_DESIGN_NOT_FOUND",
	OrganizationDesignFindFailed:                                   "ORGANIZATION_DESIGN_FIND_FAILED",
	OrganizationDesignInvalidType:                                  "ORGANIZATION_DESIGN_INVALID_TYPE",
	AuthMagicLoginUserSuspended:                                    "AUTH_MAGIC_LOGIN_USER_SUSPENDED",
	TransferSendMoneyUserSuspended:                                 "TRANSFER_SEND_MONEY_USER_SUSPENDED",
	TransactionW2WSendReceiverUserNotFound:                         "TRANSACTION_W2W_SEND_RECEIVER_USER_NOT_FOUND",
	TransactionW2WSendReceiverUserFindFailed:                       "TRANSACTION_W2W_SEND_RECEIVER_USER_FIND_FAILED",
	TransactionW2WSendReceiverUserSuspended:                        "TRANSACTION_W2W_SEND_RECEIVER_USER_SUSPENDED",
	TransactionBankWithdrawUserSuspended:                           "TRANSACTION_BANK_WITHDRAW_USER_SUSPENDED",
	GenerateOTPUserNotFound:                                        "GENERATE_OTP_USER_NOT_FOUND",
	GenerateOTPUserFindError:                                       "GENERATE_OTP_USER_FIND_ERROR",
	GenerateOTPSuspendedUser:                                       "GENERATE_OTP_SUSPENDED_USER",
	GenerateOTPInactiveUser:                                        "GENERATE_OTP_INACTIVE_USER",
	GenerateOTPOrganizationNotFound:                                "GENERATE_OTP_ORGANIZATION_NOT_FOUND",
	GenerateOTPOrganizationFindError:                               "GENERATE_OTP_ORGANIZATION_FIND_ERROR",
	GenerateOTPCommunicationProviderNotFound:                       "GENERATE_OTP_COMMUNICATION_PROVIDER_NOT_FOUND",
	GenerateOTPCommunicationProviderFindError:                      "GENERATE_OTP_COMMUNICATION_PROVIDER_FIND_ERROR",
	GenerateOTPCreateError:                                         "GENERATE_OTP_CREATE_ERROR",
	GenerateOTPSuccess:                                             "GENERATE_OTP_SUCCESS",
	GenerateOTPOrganizationOTPActionNotFound:                       "GENERATE_OTP_ORGANIZATION_OTP_ACTION_NOT_FOUND",
	GenerateOTPOrganizationOTPActionFindError:                      "GENERATE_OTP_ORGANIZATION_OTP_ACTION_FIND_ERROR",
	GenerateOTPOtpNotActive:                                        "GENERATE_OTP_OTP_NOT_ACTIVE",
	MobileVerificationPhoneUpdateError:                             "MOBILE_VERIFICATION_PHONE_UPDATE_ERROR",
	MobileVerificationPhoneExists:                                  "MOBILE_VERIFICATION_PHONE_EXISTS",
	MobileVerificationPhoneUnusable:                                "MOBILE_VERIFICATION_PHONE_UNUSABLE",
	VerifyOTPCodeRequired:                                          "VERIFY_OTP_CODE_REQUIRED",
	VerifyOTPReferenceIdRequired:                                   "VERIFY_OTP_REFERENCE_ID_REQUIRED",
	VerifyOTPNotFound:                                              "VERIFY_OTP_NOT_FOUND",
	VerifyOTPFindError:                                             "VERIFY_OTP_FIND_ERROR",
	VerifyOTPAlreadyUsed:                                           "VERIFY_OTP_ALREADY_USED",
	VerifyOTPCodeNotValid:                                          "VERIFY_OTP_CODE_NOT_VALID",
	VerifyOTPUpdateError:                                           "VERIFY_OTP_UPDATE_ERROR",
	VerifyOTPSuccess:                                               "VERIFY_OTP_SUCCESS",
	AuthSmsOTPIdentificationPhoneRequired:                          "AUTH_SMS_OTP_IDENTIFICATION_PHONE_REQUIRED",
	AuthSmsOTPIdentificationUserNotFound:                           "AUTH_SMS_OTP_IDENTIFICATION_USER_NOT_FOUND",
	AuthSmsOTPIdentificationFailed:                                 "AUTH_SMS_OTP_IDENTIFICATION_FAILED",
	AuthSmsOTPIdentificationUserNotActive:                          "AUTH_SMS_OTP_IDENTIFICATION_USER_NOT_ACTIVE",
	AuthSmsOTPIdentificationUserIPNotAllowed:                       "AUTH_SMS_OTP_IDENTIFICATION_USER_IP_NOT_ALLOWED",
	AuthSmsOTPIdentificationUserMobileVerified:                     "AUTH_SMS_OTP_IDENTIFICATION_USER_MOBILE_VERIFIED",
	AuthSmsOTPIdentificationOTPNotActive:                           "AUTH_SMS_OTP_IDENTIFICATION_OTP_NOT_ACTIVE",
	AuthSmsOTPIdentificationSuccess:                                "AUTH_SMS_OTP_IDENTIFICATION_SUCCESS",
	AuthSmsOTPAuthenticateCodeRequired:                             "AUTH_SMS_OTP_AUTHENTICATE_CODE_REQUIRED",
	AuthSmsOTPAuthenticateReferenceIdRequired:                      "AUTH_SMS_OTP_AUTHENTICATE_REFERENCE_ID_REQUIRED",
	AuthSmsOTPAuthenticateUserNotActive:                            "AUTH_SMS_OTP_AUTHENTICATE_USER_NOT_ACTIVE",
	AuthSmsOTPAuthenticateUserIPNotAllowed:                         "AUTH_SMS_OTP_AUTHENTICATE_USER_IP_NOT_ALLOWED",
	AuthSmsOTPAuthenticateCodeNotValid:                             "AUTH_SMS_OTP_AUTHENTICATE_CODE_NOT_VALID",
	AuthSmsOTPAuthenticateFailed:                                   "AUTH_SMS_OTP_AUTHENTICATE_FAILED",
	AuthSmsOTPAuthenticateSuccess:                                  "AUTH_SMS_OTP_AUTHENTICATE_SUCCESS",
	SecurityImageUpdateError:                                       "SECURITY_IMAGE_UPDATE_ERROR",
	SecurityImageUpdateSuccess:                                     "SECURITY_IMAGE_UPDATE_SUCCESS",
	PayInvoiceInvoiceNotFound:                                      "PAY_INVOICE_INVOICE_NOT_FOUND",
	PayInvoiceInvoiceFindFailed:                                    "PAY_INVOICE_INVOICE_FIND_FAILED",
	PayInvoiceOrderCreateFailed:                                    "PAY_INVOICE_ORDER_CREATE_FAILED",
	PayInvoiceInvoiceUpdateFailed:                                  "PAY_INVOICE_INVOICE_UPDATE_FAILED",
	PayInvoiceOrderCreateSuccess:                                   "PAY_INVOICE_ORDER_CREATE_SUCCESS",
	CommunicationSendMessageSuccess:                                "COMMUNICATION_SEND_MESSAGE_SUCCESS",
	CommunicationSendMessageFailed:                                 "COMMUNICATION_SEND_MESSAGE_FAILED",
	BillTypeNotFound:                                               "BILL_TYPE_NOT_FOUND",
	BillCompanyNotFound:                                            "BILL_COMPANY_NOT_FOUND",
	PayInvoiceOrderFindFailed:                                      "PAY_INVOICE_ORDER_FIND_FAILED",
	PayInvoiceWalletBalanceInsufficient:                            "PAY_INVOICE_WALLET_BALANCE_INSUFFICIENT",
	PayInvoiceOrganizationFindFailed:                               "PAY_INVOICE_ORGANIZATION_FIND_FAILED",
	PayInvoiceWalletNotFound:                                       "PAY_INVOICE_WALLET_NOT_FOUND",
	PayInvoiceWalletFindFailed:                                     "PAY_INVOICE_WALLET_FIND_FAILED",
	PayInvoiceWalletUpdateFailed:                                   "PAY_INVOICE_WALLET_UPDATE_FAILED",
	PayInvoiceTransactionCreateFailed:                              "PAY_INVOICE_TRANSACTION_CREATE_FAILED",
	PayInvoiceSuccess:                                              "PAY_INVOICE_SUCCESS",
	PayInvoiceBillCompanyFindFailed:                                "PAY_INVOICE_BILL_COMPANY_FIND_FAILED",
	TransactionDepositOrganizationFindFailed:                       "TRANSACTION_DEPOSIT_ORGANIZATION_FIND_FAILED",
	TransactionDepositUserLimitFindFailed:                          "TRANSACTION_DEPOSIT_USER_LIMIT_FIND_FAILED",
	TransactionDepositWalletBalanceLimitReached:                    "TRANSACTION_DEPOSIT_WALLET_BALANCE_LIMIT_REACHED",
	TransactionDepositWalletOneTimeTopupLimitExceeded:              "TRANSACTION_DEPOSIT_WALLET_ONE_TIME_TOPUP_LIMIT_EXCEEDED",
	TransactionDepositWalletTopupLimitExceeded:                     "TRANSACTION_DEPOSIT_WALLET_TOPUP_LIMIT_EXCEEDED",
	OrderPayOrderWalletNotFound:                                    "ORDER_PAY_ORDER_WALLET_NOT_FOUND",
	OrderPayOrderWalletGetFailed:                                   "ORDER_PAY_ORDER_WALLET_GET_FAILED",
	OrderPayOrderWalletCurrencyNotFound:                            "ORDER_PAY_ORDER_WALLET_CURRENCY_NOT_FOUND",
	OrderPayOrderWalletCurrencyGetFailed:                           "ORDER_PAY_ORDER_WALLET_CURRENCY_GET_FAILED",
	OrderPayUserLimitGetFailed:                                     "ORDER_PAY_USER_LIMIT_GET_FAILED",
	OrderPayOrderWalletBalanceExceedsLimit:                         "ORDER_PAY_ORDER_WALLET_BALANCE_EXCEEDS_LIMIT",
	OrderPayOrderAmountExceedsLimit:                                "ORDER_PAY_ORDER_AMOUNT_EXCEEDS_LIMIT",
	PayInvoiceCreditCardNotSupported:                               "PAY_INVOICE_CREDIT_CARD_NOT_SUPPORTED",
	OrganizationUserVerifyPermissionDenied:                         "ORGANIZATION_USER_VERIFY_PERMISSION_DENIED",
	OrganizationUserVerifyUserNotFound:                             "ORGANIZATION_USER_VERIFY_USER_NOT_FOUND",
	OrganizationUserVerifyUserFindFailed:                           "ORGANIZATION_USER_VERIFY_USER_FIND_FAILED",
	OrganizationUserVerifyUserUpdateFailed:                         "ORGANIZATION_USER_VERIFY_USER_UPDATE_FAILED",
	OrganizationUserVerifySuccess:                                  "ORGANIZATION_USER_VERIFY_SUCCESS",
	UserChangeEmailPermissionDenied:                                "USER_CHANGE_EMAIL_PERMISSION_DENIED",
	UserChangeEmailEmailAlreadyExist:                               "USER_CHANGE_EMAIL_EMAIL_ALREADY_EXIST",
	UserChangeEmailUserNotFound:                                    "USER_CHANGE_EMAIL_USER_NOT_FOUND",
	UserChangeEmailUserFindError:                                   "USER_CHANGE_EMAIL_USER_FIND_ERROR",
	UserChangeEmailUserIsNotActive:                                 "USER_CHANGE_EMAIL_USER_IS_NOT_ACTIVE",
	UserChangeEmailUserIsSuspended:                                 "USER_CHANGE_EMAIL_USER_IS_SUSPENDED",
	UserChangeEmailUserUpdateError:                                 "USER_CHANGE_EMAIL_USER_UPDATE_ERROR",
	UserChangeEmailSuccess:                                         "USER_CHANGE_EMAIL_SUCCESS",
	ProfileEmailCannotUsable:                                       "PROFILE_EMAIL_CANNOT_USABLE",
	ProfileEmailCodeExpired:                                        "PROFILE_EMAIL_CODE_EXPIRED",
	ProfileEmailCodeInvalid:                                        "PROFILE_EMAIL_CODE_INVALID",
	ProfileEmailUpdateFailed:                                       "PROFILE_EMAIL_UPDATE_FAILED",
	ProfileEmailUpdateSuccess:                                      "PROFILE_EMAIL_UPDATE_SUCCESS",
	ProfileEmailUpdateEmailSent:                                    "PROFILE_EMAIL_UPDATE_EMAIL_SENT",
	ProfilePhoneNumberSameWithCurrent:                              "PROFILE_PHONE_NUMBER_SAME_WITH_CURRENT",
	ProfilePhoneNumberCannotUsable:                                 "PROFILE_PHONE_NUMBER_CANNOT_USABLE",
	ProfilePhoneNumberCodeExpired:                                  "PROFILE_PHONE_NUMBER_CODE_EXPIRED",
	ProfilePhoneNumberCodeInvalid:                                  "PROFILE_PHONE_NUMBER_CODE_INVALID",
	ProfilePhoneNumberUpdateFailed:                                 "PROFILE_PHONE_NUMBER_UPDATE_FAILED",
	ProfilePhoneNumberUpdateSuccess:                                "PROFILE_PHONE_NUMBER_UPDATE_SUCCESS",
	ProfilePhoneNumberUpdateSMSSent:                                "PROFILE_PHONE_NUMBER_UPDATE_SMS_SENT",
	InvoiceGetBillCompanyFailed:                                    "INVOICE_GET_BILL_COMPANY_FAILED",
	InvoiceGetBillProviderFailed:                                   "INVOICE_GET_BILL_PROVIDER_FAILED",
	InvoiceGetInvoiceFailed:                                        "INVOICE_GET_INVOICE_FAILED",
	InvoiceUpdateInvoiceFailed:                                     "INVOICE_UPDATE_INVOICE_FAILED",
	InvoiceDeleteInvoiceFailed:                                     "INVOICE_DELETE_INVOICE_FAILED",
	InvoicePayInvoiceFailed:                                        "INVOICE_PAY_INVOICE_FAILED",
	InvoiceCreateInvoiceFailed:                                     "INVOICE_CREATE_INVOICE_FAILED",
	InvoicePayyBillProviderFindFailed:                              "INVOICE_PAYY_BILL_PROVIDER_FIND_FAILED",
	UnknownError:                                                   "UNKNOWN",
}

type TapsilatError struct {
	error
}

func CodeString(code uint64) string {
	return fmt.Sprintf("%d", code)
}

func CodeMessage(code uint64) string {
	return mapCodeToMessage[code]
}

func GetWithMessage(message string) uint64 {
	for code, msg := range mapCodeToMessage {
		if msg == message {
			return code
		}
	}
	return 0
}

func CodeWithMessage(code uint64, message string) string {
	return fmt.Sprintf("%d:%s", code, message)
}

func GrpcError(code uint64) string {
	return fmt.Sprintf("%d&%s", code, mapCodeToMessage[code])
}

func GetMap() map[uint64]string {
	return mapCodeToMessage
}

func ErrorWithCode(code uint64) error {
	message, ok := mapCodeToMessage[code]
	if !ok {
		return TapsilatError{errors.New("unknown error code")}
	}

	return TapsilatError{errors.New(message)}
}
