package auth

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	Check(ctx context.Context, t trace.Tracer, email string) error
	Authenticate(ctx context.Context, t trace.Tracer, email, password string) (string, bool, bool, error)
	VerifyToken(token string, ctx context.Context, t trace.Tracer) (*dtos.AdminAuthResponseDTO, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Check(ctx context.Context, t trace.Tracer, email string) error {
	ctx = helpers.PushToTrace(t, ctx, "auth.service.Check", "auth.service.Check")
	return s.repository.check(ctx, t, email)
}

func (s *service) Authenticate(ctx context.Context, t trace.Tracer, email, password string) (string, bool, bool, error) {
	ctx = helpers.PushToTrace(t, ctx, "auth.service.Authenticate", "auth.service.Authenticate")
	return s.repository.authenticate(ctx, t, email, password)
}

func (s *service) VerifyToken(token string, ctx context.Context, t trace.Tracer) (*dtos.AdminAuthResponseDTO, error) {
	ctx = helpers.PushToTrace(t, ctx, "auth.service.VerifyToken", "auth.service.VerifyToken")

	return s.repository.verifyToken(token, ctx, t)
}
