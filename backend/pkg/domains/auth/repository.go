package auth

import (
	"context"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/eventer"
	"github.com/parsguru/pars-vue/pkg/hasher"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/nat"
	"github.com/parsguru/pars-vue/pkg/ptr"
	"github.com/parsguru/pars-vue/pkg/redis"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"

	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type Repository interface {
	check(ctx context.Context, t trace.Tracer, email string) error
	authenticate(ctx context.Context, t trace.Tracer, email, password string) (string, bool, bool, error)
	verifyToken(token string, ctx context.Context, t trace.Tracer) (*dtos.AdminAuthResponseDTO, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) check(ctx context.Context, t trace.Tracer, email string) error {
	ctx = helpers.PushToTrace(t, ctx, "auth.repository.Check", "auth.repository.Check")
	var admin entities.Admin
	if err := r.db.Model(&entities.Admin{}).
		Where(query.BuildQuery(query.WhereEmail), email).
		First(&admin).Error; err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Admin Login Not Found",
			Message:        "Error : Admin Not Found",
			Entity:         "admin",
			Type:           "error",
			Proto:          "http",
			AdminID:        admin.ID,
			OrganizationID: admin.OrganizationID,
			Ip:             state.CurrentIP(ctx),
		})
		return errors.New("check your entered information it seems to be incorrect")
	}

	if admin.Status != "active" {
		monolog.CreateLog(&entities.Log{
			Title:          "Admin Not Active",
			Message:        "Error : Admin Not Active",
			Entity:         "admin",
			Type:           "error",
			Proto:          "http",
			AdminID:        admin.ID,
			OrganizationID: admin.OrganizationID,
			Ip:             state.CurrentIP(ctx),
		})

		return errors.New("check your entered information it seems to be incorrect")
	}

	return nil
}

func (r *repository) authenticate(ctx context.Context, t trace.Tracer, email, password string) (string, bool, bool, error) {
	ctx = helpers.PushToTrace(t, ctx, "auth.repository.Authenticate", "auth.repository.Authenticate")
	var admin entities.Admin

	if err := r.db.Model(&entities.Admin{}).
		Where(query.BuildQuery(query.WhereEmail), email).
		First(&admin).Error; err != nil {
		monolog.CreateLog(&entities.Log{
			Title:   "Admin Not Found",
			Message: "Error : Admin Not Found",
			Entity:  "admin",
			Type:    "error",
			Proto:   "http",
			Ip:      state.CurrentIP(ctx),
		})

		return "", false, false, errors.New("check your entered information it seems to be incorrect")
	}

	if admin.Status != "active" {
		monolog.CreateLog(&entities.Log{
			Title:          "Admin Not Active",
			Message:        "Authenticate Error : Admin Not Active",
			Entity:         "admin",
			Type:           "error",
			Proto:          "http",
			AdminID:        admin.ID,
			OrganizationID: admin.OrganizationID,
			Ip:             state.CurrentIP(ctx),
		})
		return "", false, false, errors.New("check your entered information it seems to be incorrect")
	}

	if !utils.Compare(admin.Password, password) {
		monolog.CreateLog(&entities.Log{
			Title:          "Admin Login Failed",
			Message:        "Error : Admin Login Failed - Password Incorrect",
			Entity:         "admin",
			Type:           "error",
			Proto:          "http",
			AdminID:        admin.ID,
			OrganizationID: admin.OrganizationID,
			Ip:             state.CurrentIP(ctx),
		})

		entities.AuthenticationAttempt{
			Method:         entities.AuthenticationAttemptMethodPassword,
			Status:         entities.AuthenticationAttemptStatusFailed,
			IP:             state.CurrentIP(ctx),
			UserID:         admin.ID,
			OrganizationID: admin.OrganizationID,
		}.Create(ctx, r.db)

		var failedAttemptinLast15Minutes int64
		r.db.WithContext(ctx).
			Model(&entities.AuthenticationAttempt{}).
			Where("user_id = ? AND created_at > ?", admin.ID, time.Now().Add(-15*time.Minute)).
			Count(&failedAttemptinLast15Minutes)

		if failedAttemptinLast15Minutes >= 5 {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Suspended",
				Message:        "Too many failed attempts in last 15 minutes",
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				AdminID:        admin.ID,
				OrganizationID: admin.OrganizationID,
				Ip:             state.CurrentIP(ctx),
			})

			admin.Suspended = ptr.Bool(true)
			admin.SuspendReason = "TOO_MANY_FAILED_ATTEMPTS_IN_LAST_15_MINUTES"
			r.db.WithContext(ctx).
				Model(&entities.Admin{}).
				Where(query.BuildQuery(query.WhereID), admin.ID).
				Updates(&admin)
		}

		return "", false, false, errors.New("check your entered information it seems to be incorrect")
	}

	var org entities.Organization
	if err := r.db.Model(&entities.Organization{}).
		Where(query.BuildQuery(query.WhereID), admin.OrganizationID).
		First(&org).Error; err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Organization Not Found",
			Message:        "Authenticate Error : Organization Not Found",
			Entity:         "admin",
			Type:           "error",
			Proto:          "http",
			AdminID:        admin.ID,
			OrganizationID: admin.OrganizationID,
			Ip:             state.CurrentIP(ctx),
		})
		return "", false, false, errors.New("check your entered information it seems to be incorrect")
	}

	jwt := utils.JwtWrapper{
		Issuer:    config.ReadValue().JwtIssuer,
		SecretKey: config.ReadValue().JwtSecret,
	}

	jwttoken, err := jwt.GenerateAdminJWT(admin.ID.String(), org.ID.String(), admin.Email, ptr.BoolValue(org.Main))
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Error Generating Token",
			Message:        "Error : Error Generating Token",
			Entity:         "auth",
			Type:           "error",
			Proto:          "http",
			AdminID:        admin.ID,
			OrganizationID: admin.OrganizationID,
			Ip:             state.CurrentIP(ctx),
		})

		return "", false, false, errors.New("error generating token")
	}

	redis.KeySet(hasher.UnreversibleHash(fmt.Sprintf("pars-admin-auth-token-%v", admin.ID)), jwttoken, time.Hour*time.Duration(config.ReadValue().JwtExpire))

	nc := nat.NatConn()
	subject := fmt.Sprintf("event.admin.%s", admin.ID.String())

	msg := nats.Msg{
		Subject: subject,
		Header: nats.Header{
			"event.type": {
				"login.event",
			},
		},
	}
	if err := nc.PublishMsg(&msg); err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "NATS Publish Error",
			Message:        "Admin login event publish failed error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Entity:         "auth",
			Ip:             state.CurrentIP(ctx),
			AdminID:        admin.ID,
			OrganizationID: admin.OrganizationID,
		})
	}

	// payload := stamp.StampEventData{
	// 	OrganizationID: admin.OrganizationID.String(),
	// 	Content:        fmt.Sprintf("%s logged in to the system at %s", admin.ID.String(), time.Now().Format(time.RFC3339)),
	// 	Entity:         "auth",
	// 	Action:         "login",
	// 	UserAgent:      state.CurrentAdminAgent(ctx),
	// 	IpAaddress:     state.CurrentIP(ctx),
	// 	ReferenceID:    uuid.NewString(),
	// 	ContentType:    entities.StampTypeTimestamp,
	// }

	// stamp.PushStampEvent(payload)

	return jwttoken, false, false, nil
}

func (r *repository) verifyToken(token string, ctx context.Context, t trace.Tracer) (*dtos.AdminAuthResponseDTO, error) {
	ctx = helpers.PushToTrace(t, ctx, "auth.repository.VerifyToken", "auth.repository.VerifyToken")
	jwt := utils.JwtWrapper{
		Issuer:    config.ReadValue().JwtIssuer,
		SecretKey: config.ReadValue().JwtSecret,
	}
	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return nil, err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	if !jwt.ValidateToken(token) {
		return nil, errors.New("invalid token")
	}

	claims, err := jwt.ParseToken(token)
	if err != nil {
		return nil, err
	}

	var admin entities.Admin
	if err := r.db.WithContext(ctx).
		Model(&entities.Admin{}).
		Where(query.BuildQuery(query.WhereID), claims.ID).
		First(&admin).Error; err != nil {
		return nil, errors.New("check your entered information it seems to be incorrect")
	}

	var org entities.Organization
	if err := r.db.WithContext(ctx).
		Model(&entities.Organization{}).
		Where(query.BuildQuery(query.WhereID), admin.OrganizationID).
		First(&org).Error; err != nil {
		return nil, errors.New("check your entered information it seems to be incorrect")
	}

	var adminRole entities.Role
	var permissions []entities.Permission
	if err := r.db.WithContext(ctx).
		Model(&entities.Role{}).
		Where(query.BuildQuery(query.WhereID), admin.RoleID).
		First(&adminRole).Error; err != nil {
		return nil, err
	}
	if err := r.db.WithContext(ctx).
		Model(&entities.Permission{}).
		Where("role_id = ?", adminRole.ID).
		Find(&permissions).Error; err != nil {
		return nil, err
	}
	var adminpermissionlist []dtos.AdminRolePermissionDto
	for _, v := range permissions {
		adminpermissionlist = append(adminpermissionlist, dtos.AdminRolePermissionDto{
			Entity: v.Entity,
			Create: v.Create,
			Read:   v.Read,
			Update: v.Update,
			Delete: v.Delete,
		})
	}
	var role dtos.AdminRoleDto
	role.Name = adminRole.Name
	role.Permissions = adminpermissionlist

	var organization entities.Organization
	if err := r.db.WithContext(ctx).
		Model(organization).
		Where(query.BuildQuery(query.WhereID), admin.OrganizationID).
		First(&organization).Error; err != nil {
		return nil, err
	}

	admin.LastLoginTime = time.Now()
	admin.LastLoginIP = state.CurrentIP(ctx)
	if err := tx.WithContext(ctx).
		Model(&entities.Admin{}).
		Where(query.BuildQuery(query.WhereID), admin.ID).
		Updates(&admin).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	eventer.PushEvent("admin.magic_link", admin.ID.String())
	expire := fmt.Sprintf("%v", claims.ExpiresAt)
	return &dtos.AdminAuthResponseDTO{
		ID:       admin.ID.String(),
		Email:    admin.Email,
		Name:     admin.Name,
		Role:     role,
		PinLogin: ptr.BoolValue(admin.PinLogin),
		Tfa:      ptr.BoolValue(admin.Tfa),
		Locale:   admin.Locale,
		Organization: dtos.AdminOrganizationDto{
			Name: organization.Name,
			Logo: organization.Logo,
		},
		Expire: expire,
	}, nil
}
