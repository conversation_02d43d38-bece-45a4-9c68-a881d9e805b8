package parsgpt

import (
	"context"
	"math"

	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	saveResultComeFromParsGpt(ctx context.Context, out *entities.ParsGptResult) error
	getSimulateByID(ctx context.Context, id string) (entities.ParsGptResult, error)
	rateSimulate(ctx context.Context, id string, rate int) error
	getSimulateByPrompt(ctx context.Context, prompt string) entities.ParsGptResult
	getAllSimulates(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) saveResultComeFromParsGpt(ctx context.Context, out *entities.ParsGptResult) error {
	return r.db.WithContext(ctx).Create(out).Error
}

func (r *repository) getSimulateByID(ctx context.Context, id string) (entities.ParsGptResult, error) {
	var pars_gpt_result entities.ParsGptResult
	if err := r.db.WithContext(ctx).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Where("id = ?", id).First(&pars_gpt_result).Error; err != nil {
		return pars_gpt_result, err
	}

	return pars_gpt_result, nil
}

func (r *repository) rateSimulate(ctx context.Context, id string, rate int) error {
	return r.db.WithContext(ctx).
		Model(&entities.ParsGptResult{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Where("id = ?", id).
		Update("rate_of_result", rate).Error
}

func (r *repository) getSimulateByPrompt(ctx context.Context, prompt string) entities.ParsGptResult {
	var gpt_result entities.ParsGptResult

	r.db.WithContext(ctx).
		Model(&entities.ParsGptResult{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Where("prompt = ?", prompt).
		First(&gpt_result)

	return gpt_result
}

func (r *repository) getAllSimulates(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error) {
	var (
		gpt_result []entities.ParsGptResult
		base_query *gorm.DB
		count      int64
		response   []dtos.ResponseForParsGPT
	)

	base_query = r.db.WithContext(ctx).
		Model(&entities.ParsGptResult{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx))

	if err := base_query.
		Count(&count).Error; err != nil {
		return nil, err
	}

	q := base_query.Limit(perpage).
		Offset((page - 1) * perpage).
		Order("created_at DESC").
		Find(&gpt_result)

	if q.Error != nil {
		return nil, q.Error
	}

	for _, v := range gpt_result {
		var s_gpt_resp dtos.ResponseForParsGPT

		s_gpt_resp.ID = v.ID.String()
		s_gpt_resp.CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		s_gpt_resp.Prompt = v.Prompt
		s_gpt_resp.JSONResult = v.JSONResult
		s_gpt_resp.Temperature = v.Temperature
		s_gpt_resp.OrganizationID = v.OrganizationID.String()
		s_gpt_resp.AdminID = v.AdminID.String()
		s_gpt_resp.RateOfResult = v.RateOfResult

		response = append(response, s_gpt_resp)
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(perpage))),
		Rows:       response,
	}, nil
}
