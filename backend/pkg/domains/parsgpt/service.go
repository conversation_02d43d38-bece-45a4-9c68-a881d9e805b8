package parsgpt

import (
	"context"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/openai"
	"github.com/parsguru/pars-vue/pkg/state"
)

type Service interface {
	CreateSimulate(ctx context.Context, req dtos.RequestForCreateSimulateWithParsAI) (dtos.ResponseForParsGptResult, error)
	GetSimulateByID(ctx context.Context, id string) (entities.ParsGptResult, error)
	RateSimulate(ctx context.Context, req dtos.RequestForParsGptRate) error
	GetAllSimulates(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateSimulate(ctx context.Context, req dtos.RequestForCreateSimulateWithParsAI) (dtos.ResponseForParsGptResult, error) {
	var (
		pars_gpt_result          entities.ParsGptResult
		resp_for_pars_gpt_result dtos.ResponseForParsGptResult
	)

	//-----> if prompt is exist in our db
	control_query := s.repository.getSimulateByPrompt(ctx, req.Prompt)
	if control_query.ID != uuid.Nil {
		resp_for_pars_gpt_result.ID = control_query.ID.String()
		resp_for_pars_gpt_result.JsonResult = control_query.JSONResult
		return resp_for_pars_gpt_result, nil
	}

	request_body := entities.ChatCompletionRequest{
		Model: config.ReadValue().OpenAI.Model,
		Messages: []entities.ChatCompletionMessage{
			// {
			// 	Role:    "system",
			// 	Content: "prompts.SYSTEM_PROMPT_LAYOUT_X",
			// },
			{
				Role:    "user",
				Content: req.Prompt,
			},
		},
		Temperature: 0.3,
	}

	resp, err := openai.DoSomethingWithAI(&request_body)
	if err != nil {
		return resp_for_pars_gpt_result, err
	}

	var json_result = resp.Choices[0].Message.Content

	pars_gpt_result.Prompt = req.Prompt
	pars_gpt_result.JSONResult = json_result
	pars_gpt_result.Temperature = 0.3
	pars_gpt_result.OrganizationID = state.CurrentAdminOrganization(ctx)
	pars_gpt_result.AdminID = state.CurrentAdminUser(ctx)
	pars_gpt_result.RateOfResult = 0

	if err := s.repository.saveResultComeFromParsGpt(ctx, &pars_gpt_result); err != nil {
		return resp_for_pars_gpt_result, err
	}

	resp_for_pars_gpt_result.ID = pars_gpt_result.ID.String()
	resp_for_pars_gpt_result.JsonResult = json_result

	return resp_for_pars_gpt_result, nil
}

func (s *service) GetSimulateByID(ctx context.Context, id string) (entities.ParsGptResult, error) {
	return s.repository.getSimulateByID(ctx, id)
}

func (s *service) RateSimulate(ctx context.Context, req dtos.RequestForParsGptRate) error {
	return s.repository.rateSimulate(ctx, req.ID, req.Rate)
}

func (s *service) GetAllSimulates(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error) {
	return s.repository.getAllSimulates(ctx, page, perpage)
}
