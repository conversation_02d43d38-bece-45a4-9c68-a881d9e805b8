package dash

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	Get(ctx context.Context) (dtos.ResponseForDashboard, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Get(ctx context.Context) (dtos.ResponseForDashboard, error) {
	var (
		simulates        []entities.Simulate
		resp             dtos.ResponseForDashboard
		evaluation_count int64
		err              error
	)

	if err = r.db.Model(&entities.Simulate{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Order("created_at DESC").
		Find(&simulates).Error; err != nil {
		return resp, err
	}

	if err = r.db.Model(&entities.Evaluations{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Count(&evaluation_count).Error; err != nil {
		return resp, err
	}

	resp.TotalSimulateCount = len(simulates)
	resp.TotalEvaluationCount = int(evaluation_count)

	if len(simulates) != 0 {
		resp.LastSimulate.ID = simulates[0].ID
		resp.LastSimulate.Name = simulates[0].Name
		resp.LastSimulate.CreatedAt = simulates[0].CreatedAt.Format("2006-01-02 15:04:05")
	}

	return resp, nil
}
