package store

import (
	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	Create(payload *dtos.StoreInsertRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	Update(payload *dtos.StoreUpdateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	Delete(payload *dtos.StoreDeleteRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	Select(payload *dtos.StoreConditionRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResult, error)
	Upsert(payload *dtos.StoreUpsertRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	DbCreate(payload *dtos.StoreDbCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	DbDrop(payload *dtos.StoreDbDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	DbList(payload *dtos.StoreDbListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	TableCreate(payload *dtos.StoreTableCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	TableDrop(payload *dtos.StoreTableDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	TableList(payload *dtos.StoreTableListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreTableListResponse, error)
	ColumnCreate(payload *dtos.StoreColumnCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	ColumnDrop(payload *dtos.StoreColumnDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	ColumnList(payload *dtos.StoreColumnListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreColumnListResponse, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Create(payload *dtos.StoreInsertRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.Create(payload, ctx, t)
}

func (s *service) Update(payload *dtos.StoreUpdateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.Update(payload, ctx, t)
}

func (s *service) Delete(payload *dtos.StoreDeleteRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.Delete(payload, ctx, t)
}

func (s *service) Select(payload *dtos.StoreConditionRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResult, error) {
	return s.repository.Select(payload, ctx, t)
}

func (s *service) Upsert(payload *dtos.StoreUpsertRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.Upsert(payload, ctx, t)
}

func (s *service) DbCreate(payload *dtos.StoreDbCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.DbCreate(payload, ctx, t)
}

func (s *service) DbDrop(payload *dtos.StoreDbDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.DbDrop(payload, ctx, t)
}

func (s *service) DbList(payload *dtos.StoreDbListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.DbList(payload, ctx, t)
}

func (s *service) TableCreate(payload *dtos.StoreTableCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.TableCreate(payload, ctx, t)
}

func (s *service) TableDrop(payload *dtos.StoreTableDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.TableDrop(payload, ctx, t)
}

func (s *service) TableList(payload *dtos.StoreTableListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreTableListResponse, error) {
	return s.repository.TableList(payload, ctx, t)
}

func (s *service) ColumnCreate(payload *dtos.StoreColumnCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.ColumnCreate(payload, ctx, t)
}

func (s *service) ColumnDrop(payload *dtos.StoreColumnDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	return s.repository.ColumnDrop(payload, ctx, t)
}

func (s *service) ColumnList(payload *dtos.StoreColumnListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreColumnListResponse, error) {
	return s.repository.ColumnList(payload, ctx, t)
}
