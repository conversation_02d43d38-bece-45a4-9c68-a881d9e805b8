package store

import (
	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type Repository interface {
	Create(payload *dtos.StoreInsertRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	Update(payload *dtos.StoreUpdateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	Delete(payload *dtos.StoreDeleteRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	Select(payload *dtos.StoreConditionRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResult, error)
	Upsert(payload *dtos.StoreUpsertRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	DbCreate(payload *dtos.StoreDbCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	DbDrop(payload *dtos.StoreDbDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	DbList(payload *dtos.StoreDbListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	TableCreate(payload *dtos.StoreTableCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	TableDrop(payload *dtos.StoreTableDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	TableList(payload *dtos.StoreTableListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreTableListResponse, error)
	ColumnCreate(payload *dtos.StoreColumnCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	ColumnDrop(payload *dtos.StoreColumnDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error)
	ColumnList(payload *dtos.StoreColumnListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreColumnListResponse, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(payload *dtos.StoreInsertRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	err := r.db.Create(payload).Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) Update(payload *dtos.StoreUpdateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	err := r.db.Model(payload).Updates(payload).Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) Delete(payload *dtos.StoreDeleteRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	err := r.db.Delete(payload).Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) Select(payload *dtos.StoreConditionRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResult, error) {
	var rows []dtos.StoreRow
	err := r.db.Where(payload).Find(&rows).Error
	if err != nil {
		return &dtos.StoreResult{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResult{Success: true, StoreRows: rows}, nil
}

func (r *repository) Upsert(payload *dtos.StoreUpsertRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	err := r.db.Save(payload).Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) DbCreate(payload *dtos.StoreDbCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	err := r.db.Exec("CREATE DATABASE " + payload.Db).Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) DbDrop(payload *dtos.StoreDbDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	err := r.db.Exec("DROP DATABASE " + payload.Db).Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) DbList(payload *dtos.StoreDbListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	var dbs []string
	err := r.db.Raw("SHOW DATABASES").Scan(&dbs).Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true, Message: "Databases: " + dbs[0]}, nil
}

func (r *repository) TableCreate(payload *dtos.StoreTableCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	err := r.db.Exec("CREATE TABLE " + payload.Table + " (" + buildColumnDefinitions(payload.Kv) + ")").Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) TableDrop(payload *dtos.StoreTableDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	err := r.db.Exec("DROP TABLE " + payload.Table).Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) TableList(payload *dtos.StoreTableListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreTableListResponse, error) {
	var tables []string
	err := r.db.Raw("SHOW TABLES").Scan(&tables).Error
	if err != nil {
		return &dtos.StoreTableListResponse{}, err
	}
	return &dtos.StoreTableListResponse{Tables: tables}, nil
}

func (r *repository) ColumnCreate(payload *dtos.StoreColumnCreateRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	err := r.db.Exec("ALTER TABLE " + payload.Table + " ADD COLUMN " + buildColumnDefinitions(payload.Kv)).Error
	if err != nil {
		return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) ColumnDrop(payload *dtos.StoreColumnDropRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreResponse, error) {
	for _, kv := range payload.Kv {
		err := r.db.Exec("ALTER TABLE " + payload.Table + " DROP COLUMN " + kv.K).Error
		if err != nil {
			return &dtos.StoreResponse{Success: false, Message: err.Error()}, nil
		}
	}
	return &dtos.StoreResponse{Success: true}, nil
}

func (r *repository) ColumnList(payload *dtos.StoreColumnListRequest, ctx *gin.Context, t trace.Tracer) (*dtos.StoreColumnListResponse, error) {
	var columns []string
	err := r.db.Raw("SHOW COLUMNS FROM " + payload.Table).Scan(&columns).Error
	if err != nil {
		return &dtos.StoreColumnListResponse{}, err
	}
	return &dtos.StoreColumnListResponse{Columns: columns}, nil
}

func buildColumnDefinitions(kvs []dtos.Kv) string {
	result := ""
	for i, kv := range kvs {
		if i > 0 {
			result += ", "
		}
		result += kv.K + " " + kv.V
	}
	return result
}
