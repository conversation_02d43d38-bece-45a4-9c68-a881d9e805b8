package nodes

import (
	"errors"
	"io"
	"net/http"
	"strings"

	"github.com/gorules/zen-go"
)

type curlNode struct {
}

func (a curlNode) Handle(request zen.NodeRequest) (zen.NodeResponse, error) {
	/*
			 inputs: [
		    { label: 'URL', name: 'url', control: 'text' },
		    { label: 'Method', name: 'method', control: 'text' },
		    { label: 'Headers', name: 'headers', control: 'text' },
		    { label: 'Body', name: 'body', control: 'text' },
		    { label: 'Key', name: 'key', control: 'text' },
		  ]
	*/

	url, err := zen.GetNodeFieldRaw[string](request, "url")
	if err != nil {
		return zen.NodeResponse{}, err
	}

	method, err := zen.GetNodeFieldRaw[string](request, "method")
	if err != nil {
		return zen.NodeResponse{}, err
	}

	if method != "GET" && method != "POST" {
		return zen.NodeResponse{}, errors.New("invalid method")
	}

	headers, err := zen.GetNodeFieldRaw[string](request, "headers")
	if err != nil {
		return zen.NodeResponse{}, err
	}

	body, err := zen.GetNodeFieldRaw[string](request, "body")
	if err != nil {
		return zen.NodeResponse{}, err
	}

	key, err := zen.GetNodeFieldRaw[string](request, "key")
	if err != nil {
		return zen.NodeResponse{}, err
	}

	output := make(map[string]any)

	client := &http.Client{}
	req, err := http.NewRequest(method, url, strings.NewReader(body))
	if err != nil {
		return zen.NodeResponse{}, err
	}

	// headers: Content-Type:application/json;Authorization:'Bearer key'
	for _, header := range strings.Split(headers, ";") {
		parts := strings.Split(header, ":")
		if len(parts) != 2 {
			return zen.NodeResponse{}, errors.New("invalid header format")
		}

		req.Header.Add(parts[0], parts[1])
	}

	resp, err := client.Do(req)
	if err != nil {
		return zen.NodeResponse{}, err
	}

	// body to string

	strBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return zen.NodeResponse{}, err
	}

	output[key] = string(strBody)

	return zen.NodeResponse{Output: output}, nil
}
