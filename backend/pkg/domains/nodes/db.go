package nodes

import (
	"database/sql"
	"encoding/json"
	"fmt"

	"github.com/gorules/zen-go"
	_ "github.com/lib/pq"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"gorm.io/gorm"
)

type dbNode struct {
}

const (
	GetNodeFieldRawProblem = "An error was detected in the %s field. Please review the configuration."
)

func (a dbNode) Handle(request zen.NodeRequest) (zen.NodeResponse, error) {
	var (
		dsn, engine, query, key string
		use_saved_data          bool = false
		err                     error
		output                  = make(map[string]any)
	)

	// -----> sim_id is not obligatory
	org_id, _, _, _, err := helpers.GetBaseIDsFromInput(request)
	if err != nil {
		output["_code"] = "400"
		output["error"] = err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	// -----> if use_saved_data is true, we gonna complete the field of part of the form ourselves
	use_saved_data, err = zen.GetNodeFieldRaw[bool](request, "use_saved_data")
	if err != nil && err.Error() != "path does not exist" {
		output["_code"] = "400"
		output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "use_saved_data")
		return zen.NodeResponse{Output: output}, nil
	}

	dsn, err = zen.GetNodeFieldRaw[string](request, "dsn")
	if err != nil {
		output["_code"] = "400"
		output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "dsn")
		return zen.NodeResponse{Output: output}, nil
	}

	if !use_saved_data {
		engine, err = zen.GetNodeFieldRaw[string](request, "engine")
		if err != nil {
			output["_code"] = "400"
			output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "engine")
			return zen.NodeResponse{Output: output}, nil
		}
	}

	query, err = zen.GetNodeFieldRaw[string](request, "query")
	if err != nil {
		output["_code"] = "400"
		output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "query")
		return zen.NodeResponse{Output: output}, nil
	}

	key, err = zen.GetNodeFieldRaw[string](request, "key")
	if err != nil && err.Error() != "path does not exist" {
		output["_code"] = "400"
		output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "key")
		return zen.NodeResponse{Output: output}, nil
	}
	if key == "" {
		key = "result"
	}

	if use_saved_data {
		var m_eng = map[uint]string{
			1: "mysql",
			2: "postgres",
		}

		db := database.ClientDB()
		var db_node_setting entities.DBNodeSetting
		if err := db.Model(entities.DBNodeSetting{}).
			Where("key = ?", dsn).
			Where("organization_id = ?", org_id).
			First(&db_node_setting).Error; err != nil {
			output["_code"] = "400"
			if err == gorm.ErrRecordNotFound {
				output["error"] = "dsn not found"
			} else {
				output["error"] = err.Error()
			}
			return zen.NodeResponse{Output: output}, nil
		}

		dsn = db_node_setting.DSN
		engine = m_eng[db_node_setting.DBEngine]

	}

	// -----> SQL Start
	db, err := sql.Open(engine, dsn)
	if err != nil {
		output["_code"] = "400"
		output["error"] = err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	rows, err := db.Query(query)
	if err != nil {
		output["_code"] = "400"
		output["error"] = err.Error()
		return zen.NodeResponse{Output: output}, nil
	}
	defer rows.Close()

	cols, err := rows.Columns()
	if err != nil {
		output["_code"] = "400"
		output["error"] = err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	vals := make([]interface{}, len(cols))
	for i := range cols {
		vals[i] = new(interface{})
	}
	var result []map[string]interface{}
	for rows.Next() {
		err = rows.Scan(vals...)
		if err != nil {
			output["_code"] = "400"
			output["error"] = err.Error()
			return zen.NodeResponse{Output: output}, nil
		}

		row := make(map[string]interface{})
		for i, col := range cols {
			val := *vals[i].(*interface{})
			row[col] = val
		}

		result = append(result, row)
	}

	last_result, err := AddFieldToRawMessage(request.Input, result, key)
	if err != nil {
		output["_code"] = "400"
		output["error"] = err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	return zen.NodeResponse{Output: last_result}, nil
}

func AddFieldToRawMessage(raw json.RawMessage, additional []map[string]interface{}, key string) (json.RawMessage, error) {
	first := make(map[string]interface{})
	if err := json.Unmarshal(raw, &first); err != nil {
		return nil, fmt.Errorf("failed to unmarshal json.RawMessage: %w", err)
	}
	second := make(map[string]interface{})
	for key, value := range first {
		if key == "$nodes" {
			continue
		} else {
			second[key] = value
		}
	}

	second[key] = additional
	second["_code"] = "200"

	updatedJSON, err := json.Marshal(second)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal updated map: %w", err)
	}

	return json.RawMessage(updatedJSON), nil
}
