package nodes

import (
	"strconv"
	"time"

	"github.com/gorules/zen-go"
	"github.com/parsguru/pars-vue/pkg/helpers"
)

type timeoutNode struct {
}

const (
	DefaultTimeout = "1"
)

func (a timeoutNode) Handle(request zen.NodeRequest) (zen.NodeResponse, error) {
	var (
		output = make(map[string]any)
	)

	_, _, _, m, err := helpers.GetBaseIDsFromInput(request)
	if err != nil {
		output["error"] = "timeout_error_1000: " + err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	output = m

	time_from_req, err := zen.GetNodeFieldRaw[string](request, "time")
	if err != nil && err.Error() != "path does not exist" {
		output["error"] = "timeout_error_1001"
		return zen.NodeResponse{Output: output}, nil
	}
	if time_from_req == "" {
		time_from_req = DefaultTimeout
	}

	convert_time_from_req, err := strconv.Atoi(time_from_req)
	if err != nil {
		output["error"] = "timeout_error_1002"
		return zen.NodeResponse{Output: output}, nil
	}

	time.Sleep(time.Duration(convert_time_from_req) * time.Second)

	output["timeout_result"] = "success"

	return zen.NodeResponse{Output: output}, nil

}
