package nodes

import (
	"errors"

	"github.com/gorules/zen-go"
)

type NodeHandler interface {
	<PERSON>le(request zen.NodeRequest) (zen.NodeResponse, error)
}

var customNodes = map[string]NodeHandler{
	"curlNode":    curlNode{},
	"dbNode":      dbNode{},
	"smsNode":     smsNode{},
	"mailNode":    mailNode{},
	"timeoutNode": timeoutNode{},
}

func CustomNodeHandler(request zen.NodeRequest) (zen.NodeResponse, error) {
	nodeHandler, ok := customNodes[request.Node.Kind]
	if !ok {
		return zen.NodeResponse{}, errors.New("component not found")
	}

	return nodeHandler.Handle(request)
}
