package nodes

import (
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/gorules/zen-go"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"github.com/parsguru/pars-vue/pkg/job"
	"gorm.io/gorm"
)

type mailNode struct {
}

const (
	DefaultNumberOfRepeats = "100"
	DefaultMessage         = "PARS: This is an empty alert message!"
	DefaultLastXMinutes    = "60"
)

func (a mailNode) Handle(request zen.NodeRequest) (zen.NodeResponse, error) {
	var (
		current_user entities.User
		new_alert    entities.Alert
		alerts       []entities.Alert
		output       = make(map[string]any)
	)

	org_id, sim_id, user_id, m, err := helpers.GetBaseIDsFromInput(request)
	if err != nil {
		output["error"] = err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	output = m

	incoming_code_from_req, ok := m["code"].(string)
	if !ok {
		output["error"] = "something went wrong"
		return zen.NodeResponse{Output: output}, nil
	}

	code, err := zen.GetNodeFieldRaw[string](request, "code")
	if err != nil {
		output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "code")
		return zen.NodeResponse{Output: output}, nil
	}
	if !(code == "400" || code == "200") {
		output["error"] = CodeProblem
		return zen.NodeResponse{Output: output}, nil
	}

	number_of_repeats, err := zen.GetNodeFieldRaw[string](request, "number_of_repeats")
	if err != nil && err.Error() != "path does not exist" {
		output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "number_of_repeats")
		return zen.NodeResponse{Output: output}, nil
	}
	if number_of_repeats == "" {
		number_of_repeats = DefaultNumberOfRepeats
	}

	message, err := zen.GetNodeFieldRaw[string](request, "message")
	if err != nil && err.Error() != "path does not exist" {
		output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "message")
		return zen.NodeResponse{Output: output}, nil
	}
	if message == "" {
		message = DefaultMessage
	}

	last_x_minutes, err := zen.GetNodeFieldRaw[string](request, "last_x_minutes")
	if err != nil && err.Error() != "path does not exist" {
		output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "last_x_minutes")
		return zen.NodeResponse{Output: output}, nil
	}
	if last_x_minutes == "" {
		last_x_minutes = DefaultLastXMinutes
	}

	key, err := zen.GetNodeFieldRaw[string](request, "key")
	if err != nil && err.Error() != "path does not exist" {
		output["error"] = fmt.Sprintf(GetNodeFieldRawProblem, "key")
		return zen.NodeResponse{Output: output}, nil
	}
	if key == "" {
		key = "result"
	}

	// -----> In the end, we should create alert in the database
	tx := database.ClientDB().Begin()
	defer tx.Rollback()

	// find user
	if err := tx.Where("id = ?", uuid.MustParse(user_id)).First(&current_user).Error; err != nil {
		output["error"] = err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	new_alert.OrganizationID = uuid.MustParse(org_id)
	new_alert.SimulateID = uuid.MustParse(sim_id)
	new_alert.AlertType = 1
	new_alert.Code = incoming_code_from_req

	if err := tx.Create(&new_alert).Error; err != nil {
		output["error"] = err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	// -----> Control Start
	convert_last_x_minutes, _ := strconv.Atoi(last_x_minutes)
	convert_time := time.Now().Add(-time.Minute * time.Duration(convert_last_x_minutes))
	convert_number_of_repeats, _ := strconv.Atoi(number_of_repeats)

	if err := tx.Model(entities.Alert{}).
		Where("simulate_id = ? AND organization_id = ?", sim_id, org_id).
		Where("code = ?", code).
		Where("created_at > ?", convert_time).
		Where("be_processed = ?", false).
		Where("alert_type = ?", 1).
		Find(&alerts).Error; err != nil && err != gorm.ErrRecordNotFound {
		output["error"] = err.Error()
		return zen.NodeResponse{Output: output}, nil
	}

	if len(alerts) >= convert_number_of_repeats {
		if err := tx.Model(entities.Alert{}).
			Where("simulate_id = ? AND organization_id = ?", sim_id, org_id).
			Where("code = ?", code).
			Where("created_at > ?", convert_time).
			Where("alert_type = ?", 1).
			Update("be_processed", true).Error; err != nil {
			output["error"] = err.Error()
			return zen.NodeResponse{Output: output}, nil
		}
		job.SendMail(current_user.EmailForAlert, "alert Email", message)
		output["_mail_node_message"] = "mail has been sent"
	} else {
		output["_mail_node_message"] = "continue"
	}
	tx.Commit()

	return zen.NodeResponse{Output: output}, nil
}
