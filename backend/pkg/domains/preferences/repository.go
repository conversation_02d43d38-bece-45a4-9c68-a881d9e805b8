package preferences

import (
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type Repository interface {
	addDSN(req dtos.RequestForAddDSN, ctx *gin.Context, t trace.Tracer) error
	getDSN(ctx *gin.Context, t trace.Tracer) ([]dtos.ResponseForDSN, error)
	deleteDSN(id string, ctx *gin.Context, t trace.Tracer) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) addDSN(req dtos.RequestForAddDSN, ctx *gin.Context, t trace.Tracer) error {
	var (
		db_node_setting         entities.DBNodeSetting
		control_db_node_setting []entities.DBNodeSetting
	)

	r.db.WithContext(ctx).
		Where(query.WhereOrganizationID, state.CurrentAdminOrganization(ctx)).
		Find(&control_db_node_setting)

	if len(control_db_node_setting) >= 3 {
		return errors.New("you can add up to 3 keys")
	}

	for _, v := range control_db_node_setting {
		if v.Key == req.Key {
			return errors.New("key already exists")
		}
	}

	db_node_setting.DSN = req.DSN
	db_node_setting.Key = req.Key
	db_node_setting.DBEngine = uint(req.DBEngine)
	db_node_setting.OrganizationID = state.CurrentAdminOrganization(ctx)
	db_node_setting.AdminID = state.CurrentAdminUser(ctx)

	if err := r.db.WithContext(ctx).
		Create(&db_node_setting).Error; err != nil {
		return err
	}

	return nil
}

func (r *repository) getDSN(ctx *gin.Context, t trace.Tracer) ([]dtos.ResponseForDSN, error) {
	var (
		db_node_settings []entities.DBNodeSetting
		resp             []dtos.ResponseForDSN
	)

	r.db.WithContext(ctx).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Find(&db_node_settings)

	for _, v := range db_node_settings {
		resp = append(resp, dtos.ResponseForDSN{
			ID:                 v.ID,
			CreatedAt:          v.CreatedAt.Format("2006-01-02 15:04:05"),
			DSN:                v.DSN,
			Key:                v.Key,
			DBEngine:           v.DBEngine,
			LastDSNCheckStatus: v.LastDSNCheckStatus,
			LastDSNCheckAt:     v.LastDSNCheckAt.Format("2006-01-02 15:04:05"),
		})
	}

	return resp, nil
}

func (r *repository) deleteDSN(id string, ctx *gin.Context, t trace.Tracer) error {
	var db_node_setting entities.DBNodeSetting

	if err := r.db.WithContext(ctx).
		Where("organization_id = ? AND id = ?", state.CurrentAdminOrganization(ctx), id).
		First(&db_node_setting).Error; err != nil {
		return err
	}

	if err := r.db.WithContext(ctx).
		Delete(&db_node_setting).Error; err != nil {
		return err
	}

	return nil
}
