package preferences

import (
	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	AddDSN(req dtos.RequestForAddDSN, ctx *gin.Context, t trace.Tracer) error
	GetDSN(ctx *gin.Context, t trace.Tracer) ([]dtos.ResponseForDSN, error)
	DeleteDSN(id string, ctx *gin.Context, t trace.Tracer) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) AddDSN(req dtos.RequestForAddDSN, ctx *gin.Context, t trace.Tracer) error {
	return s.repository.addDSN(req, ctx, t)
}

func (s *service) GetDSN(ctx *gin.Context, t trace.Tracer) ([]dtos.ResponseForDSN, error) {
	return s.repository.getDSN(ctx, t)
}

func (s *service) DeleteDSN(id string, ctx *gin.Context, t trace.Tracer) error {
	return s.repository.deleteDSN(id, ctx, t)
}
