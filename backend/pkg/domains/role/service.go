package role

import (
	"context"
	"errors"

	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	Create(payload *dtos.RoleCreateDto, ctx context.Context, t trace.Tracer) error
	List(page, perpage int, organization_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
	Get(id string, ctx context.Context, t trace.Tracer) (*dtos.RoleDto, error)
	Update(id string, payload *dtos.RoleUpdateDto, ctx context.Context, t trace.Tracer) error
	Delete(id string, ctx context.Context, t trace.Tracer) error
	Export(organization_id string, ctx context.Context, t trace.Tracer) ([]entities.Role, []entities.Permission, error)
	Import(payload *dtos.RoleImportDto, ctx context.Context, t trace.Tracer) error
}
type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Create(payload *dtos.RoleCreateDto, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "role.service.Create", "role.service.Create")

	role := &entities.Role{
		Name:           payload.Name,
		OrganizationID: helpers.ParseID(payload.OrganizationID),
	}

	// check already exists
	exist, err := s.repository.CheckNameExist(nil, role.OrganizationID, role.Name, ctx, t)
	if err != nil {
		return err
	}
	if exist {
		return errors.New("Role name already exists")
	}

	return s.repository.Create(role, payload.Permissions, ctx, t)
}

func (s *service) List(page, perpage int, organization_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "role.service.List", "role.service.List")

	roles, err := s.repository.List(page, perpage, organization_id, ctx, t)
	if err != nil {
		return nil, err
	}
	return roles, nil
}

func (s *service) Get(id string, ctx context.Context, t trace.Tracer) (*dtos.RoleDto, error) {
	ctx = helpers.PushToTrace(t, ctx, "role.service.Get", "role.service.Get")

	return s.repository.Get(id, ctx, t)
}

func (s *service) Update(id string, payload *dtos.RoleUpdateDto, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "role.service.Update", "role.service.Update")

	role := &entities.Role{
		Name:           payload.Name,
		OrganizationID: helpers.ParseID(payload.OrganizationID),
		Base: entities.Base{
			Labels: payload.Labels,
		},
	}

	// check already exists
	exist, err := s.repository.CheckNameExist(&id, role.OrganizationID, role.Name, ctx, t)
	if err != nil {
		return err
	}
	if exist {
		return errors.New("Role name already exists")
	}

	return s.repository.Update(id, role, payload.Permissions, ctx, t)
}

func (s *service) Delete(id string, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "role.service.Delete", "role.service.Delete")

	cannotBeDeleted, _, err := s.repository.CheckRelationsExist(id, ctx, t)
	if err != nil {
		return err
	}

	if cannotBeDeleted {
		return errors.New("roles.cannot_deleted")
	}

	return s.repository.Delete(id, ctx, t)
}

func (s *service) Export(organization_id string, ctx context.Context, t trace.Tracer) ([]entities.Role, []entities.Permission, error) {
	ctx = helpers.PushToTrace(t, ctx, "role.service.Export", "role.service.Export")

	return s.repository.Export(organization_id, ctx, t)
}

func (s *service) Import(payload *dtos.RoleImportDto, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "role.service.Import", "role.service.Import")

	return s.repository.Import(payload.OrganizationID, payload.Roles, payload.Permissions, ctx, t)
}
