package role

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/constants"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/eventer"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/ptr"
	"github.com/parsguru/pars-vue/pkg/state"

	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type Repository interface {
	Create(role *entities.Role, permissions []dtos.RolePermissionDto, ctx context.Context, t trace.Tracer) error
	List(page, perpage int, organization_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
	Get(id string, ctx context.Context, t trace.Tracer) (*dtos.RoleDto, error)
	Update(id string, role *entities.Role, permissions []dtos.RolePermissionDto, ctx context.Context, t trace.Tracer) error
	Delete(id string, ctx context.Context, t trace.Tracer) error
	Export(organization_id string, ctx context.Context, t trace.Tracer) ([]entities.Role, []entities.Permission, error)
	Import(organization_id string, roles []entities.Role, permissions []entities.Permission, ctx context.Context, t trace.Tracer) error
	CheckRelationsExist(id string, ctx context.Context, t trace.Tracer) (bool, map[string]error, error)
	CheckNameExist(id *string, orgId uuid.UUID, name string, ctx context.Context, tracer trace.Tracer) (bool, error)
}

type repository struct {
	db         *gorm.DB
	entityName string
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db:         db,
		entityName: "role",
	}
}

func (r *repository) Create(role *entities.Role, permissions []dtos.RolePermissionDto, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "role.repository.Create", "role.repository.Create")
	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	if err := tx.WithContext(ctx).Model(&entities.Role{}).Create(role).Error; err != nil {
		tx.Rollback()
		return err
	}

	for _, permissionData := range permissions {
		permission := entities.Permission{
			RoleID: role.ID,
			Entity: permissionData.Entity,
			Create: permissionData.Create,
			Read:   permissionData.Read,
			Update: permissionData.Update,
			Delete: permissionData.Delete,
		}
		if err := tx.WithContext(ctx).Model(&entities.Permission{}).Create(&permission).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil

}

func (r *repository) List(page, perpage int, organization_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "role.repository.List", "role.repository.List")

	var count int64
	var roles []entities.Role

	condition := query.QueryBuilder{
		Keys: []query.QueryKey{
			{
				Key:    query.WhereOrganizationID,
				Values: []any{state.CurrentAdminOrganization(ctx)},
				Skip:   state.AmIAuthorized(ctx) || organization_id != "",
			},
			{
				Key:    query.WhereOrganizationID,
				Values: []any{organization_id},
				Skip:   organization_id == "",
			},
			{
				Key:    query.WhereOrganizationIDIN,
				Values: []any{helpers.GetCurrentOrganizationWithChildOrgs(r.db, ctx)},
				Skip:   organization_id == "" || state.AmIAuthorized(ctx),
			},
		},
	}

	sql, data := condition.GetQueriesWithValues()

	r.db.WithContext(ctx).Model(&entities.Role{}).Where(sql, data...).Count(&count)
	if err := r.db.WithContext(ctx).Model(&entities.Role{}).Where(sql, data...).Limit(perpage).Offset((page - 1) * perpage).Find(&roles).Error; err != nil {
		return nil, err
	}

	list := []dtos.RoleListDto{}
	for _, role := range roles {
		var org entities.Organization
		r.db.WithContext(ctx).Model(&entities.Organization{}).Where(query.BuildQuery(query.WhereID), role.OrganizationID).First(&org)
		rol := dtos.RoleListDto{
			ID:           role.ID.String(),
			HashID:       role.HashID,
			Name:         role.Name,
			Organization: org.Name,
			Labels:       role.Labels,
		}
		list = append(list, rol)
	}

	totalPages := int(math.Ceil(float64(count) / float64(perpage)))

	paginatedData := &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: totalPages,
		Rows:       list,
	}
	return paginatedData, nil

}

func (r *repository) Get(id string, ctx context.Context, t trace.Tracer) (*dtos.RoleDto, error) {
	ctx = helpers.PushToTrace(t, ctx, "role.repository.Get", "role.repository.Get")

	var role entities.Role
	if state.AmIAuthorized(ctx) {
		if err := r.db.WithContext(ctx).Model(entities.Role{}).Where(query.BuildQuery(query.WhereID), id).First(&role).Error; err != nil {
			return nil, err
		}
	} else {
		if err := r.db.WithContext(ctx).Model(entities.Role{}).Where(query.BuildQuery(query.WhereID, query.WhereOrganizationID), id, state.CurrentAdminOrganization(ctx)).First(&role).Error; err != nil {
			return nil, err
		}
	}
	var permissionList []entities.Permission
	if err := r.db.WithContext(ctx).Model(entities.Permission{}).Where("role_id = ?", role.ID).Find(&permissionList).Error; err != nil {
		return nil, err
	}
	permissions := []dtos.RolePermissionDto{}
	for _, permission := range permissionList {
		permissions = append(permissions, dtos.RolePermissionDto{
			Entity: permission.Entity,
			Create: permission.Create,
			Read:   permission.Read,
			Update: permission.Update,
			Delete: permission.Delete,
		})
	}

	for _, permission := range permissionList {
		for _, rolePermission := range constants.EntityList {
			if permission.Entity == rolePermission {
				continue
			} else {
				if !isInArray(rolePermission, permissions) {
					permissions = append(permissions, dtos.RolePermissionDto{
						Entity: rolePermission,
						Create: false,
						Read:   false,
						Update: false,
						Delete: false,
					})
				}
			}
		}
	}
	payload := dtos.RoleDto{
		ID:             role.ID.String(),
		OrganizationID: role.OrganizationID.String(),
		HashID:         role.HashID,
		Name:           role.Name,
		Permissions:    permissions,
		Labels:         role.Labels,
	}
	return &payload, nil
}

func isInArray(val string, arr []dtos.RolePermissionDto) bool {
	for _, v := range arr {
		if v.Entity == val {
			return true
		}
	}
	return false
}

func (r *repository) Update(id string, payload *entities.Role, permissions []dtos.RolePermissionDto, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "role.repository.Update", "role.repository.Update")
	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	if err := tx.WithContext(ctx).Model(entities.Role{}).Where(query.BuildQuery(query.WhereID), id).Updates(payload).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("record not found")
		}
		return err
	}

	var permissionList []entities.Permission
	if err := r.db.WithContext(ctx).Model(entities.Permission{}).Where("role_id = ?", id).Find(&permissionList).Error; err != nil {
		tx.Rollback()
		return err
	}

	for _, permission := range permissionList {
		if err := tx.WithContext(ctx).Unscoped().Model(entities.Permission{}).Where(query.BuildQuery(query.WhereID), permission.ID).Delete(&permission).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	for _, permissionData := range permissions {

		if constants.IsIncludedInEntityList(permissionData.Entity) {

			permission := entities.Permission{
				RoleID: helpers.ParseID(id),
				Entity: permissionData.Entity,
				Create: permissionData.Create,
				Read:   permissionData.Read,
				Update: permissionData.Update,
				Delete: permissionData.Delete,
			}
			if err := tx.WithContext(ctx).Model(&entities.Permission{}).Create(&permission).Error; err != nil {
				tx.Rollback()
				return err
			}
		}

	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}
	eventer.PushEvent("role.update", id)

	return nil
}

func (r *repository) Delete(id string, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "role.repository.Delete", "role.repository.Delete")
	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()
	var role entities.Role
	if err := r.db.WithContext(ctx).Model(entities.Role{}).Where(query.BuildQuery(query.WhereID), id).First(&role).Error; err != nil {
		return err
	}

	if ptr.BoolValue(role.Locked) {
		return errors.New("this role can't be deleted because it is locked")
	}

	if err := tx.WithContext(ctx).Model(entities.Role{}).Where(query.BuildQuery(query.WhereID), id).Delete(&entities.Role{}).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("record not found")
		}
		return err
	}
	// delete role permissions
	if err := tx.WithContext(ctx).Unscoped().Model(entities.Permission{}).Where("role_id = ?", id).Delete(&entities.Permission{}).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("record not found")
		}
		return err
	}

	if err := tx.WithContext(ctx).Unscoped().Model(entities.Role{}).Where(query.BuildQuery(query.WhereID), id).Delete(&entities.Role{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r *repository) Export(organization_id string, ctx context.Context, t trace.Tracer) ([]entities.Role, []entities.Permission, error) {
	ctx = helpers.PushToTrace(t, ctx, "role.repository.Export", "role.repository.Export")

	if !state.AmIAuthorized(ctx) {
		if state.CurrentAdminOrganization(ctx).String() != organization_id {
			return nil, nil, errors.New("you are not authorized to perform this action")
		}
	}

	var roles []entities.Role
	var permissions []entities.Permission
	condition := query.QueryBuilder{
		Keys: []query.QueryKey{
			{
				Key:    query.WhereOrganizationID,
				Values: []any{organization_id},
			},
		},
	}

	sql, data := condition.GetQueriesWithValues()

	if err := r.db.WithContext(ctx).Model(&entities.Role{}).Where(sql, data...).Find(&roles).Error; err != nil {
		return nil, nil, err
	}

	var roleIDs []string
	if err := r.db.WithContext(ctx).Model(&entities.Role{}).Where(sql, data...).Pluck("id", &roleIDs).Error; err != nil {
		return nil, nil, err
	}

	if err := r.db.WithContext(ctx).Model(&entities.Permission{}).Where("role_id IN (?)", roleIDs).Find(&permissions).Error; err != nil {
		return nil, nil, err
	}

	return roles, permissions, nil
}

func (r *repository) Import(organization_id string, roles []entities.Role, permissions []entities.Permission, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "role.repository.Import", "role.repository.Import")

	if !state.AmIAuthorized(ctx) {
		if state.CurrentAdminOrganization(ctx).String() != organization_id {
			return errors.New("you are not authorized to perform this action")
		}
	}

	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	for _, role := range roles {
		role.OrganizationID = helpers.ParseID(organization_id)
		if err := tx.WithContext(ctx).Model(&entities.Role{}).Create(&role).Error; err != nil {
			tx.Rollback()
			return err
		}

	}

	for _, permission := range permissions {
		if err := tx.WithContext(ctx).Model(&entities.Permission{}).Create(&permission).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

func (r *repository) CheckRelationsExist(id string, ctx context.Context, t trace.Tracer) (bool, map[string]error, error) {
	foundMap := make(map[string]error)

	findRelation := func(model interface{}, tableName string) error {
		err := r.db.WithContext(ctx).Model(model).Where("role_id = ?", id).First(model).Error
		if err == nil {
			foundMap[tableName] = errors.New(tableName + " found")
			return nil
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		return nil
	}

	for _, modelToCheck := range []struct {
		Model     interface{}
		TableName string
	}{
		{&entities.Admin{}, "Admin"},
		{&entities.Permission{}, "Permission"},
	} {
		if err := findRelation(modelToCheck.Model, modelToCheck.TableName); err != nil {
			return true, nil, err
		}
	}

	if len(foundMap) > 0 {
		return true, foundMap, nil
	}

	return false, nil, nil
}

func (r *repository) CheckNameExist(id *string, orgId uuid.UUID, name string, ctx context.Context, tracer trace.Tracer) (bool, error) {
	var entity entities.Role
	if err := r.db.WithContext(ctx).Model(&entities.Role{}).Where(query.BuildQuery(query.WhereName, query.WhereOrganizationID), name, orgId).First(&entity).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return false, err
	}

	if id != nil {
		if *id == entity.ID.String() && name == entity.Name {
			return false, nil
		}
	}

	return true, nil
}
