package retry

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/google/uuid"
	"github.com/gorules/zen-go"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/domains/nodes"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Repository interface {
	retry(ctx context.Context, req dtos.RequestForRetry) error
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func readTestFile(key string) ([]byte, error) {
	return []byte(key), nil
}

func (r *repository) retry(ctx context.Context, req dtos.RequestForRetry) error {
	var (
		simulate_log             entities.SimulateLog
		new_retry, control_retry entities.Retry
		try                      int = 0
	)
	if err := r.db.Model(&entities.SimulateLog{}).
		Where("id = ? AND organization_id = ?", req.RetriedSimulateLogID[0], state.CurrentAdminOrganization(ctx)).
		First(&simulate_log).Error; err != nil {
		return err
	}

	new_retry.OrganizationID = simulate_log.OrganizationID
	new_retry.AdminID = state.CurrentAdminUser(ctx)
	new_retry.RetriedSimulateLogID = simulate_log.ID
	new_retry.RetriedSimulateID = simulate_log.SimulateID
	new_retry.Request = simulate_log.Request

	r.db.Model(&entities.Retry{}).
		Where("retried_simulate_log_id = ?", simulate_log.ID).
		Where("organization_id = ?", simulate_log.OrganizationID).
		Order("created_at desc").
		First(&control_retry)

	if control_retry.ID != uuid.Nil {
		if control_retry.XTry >= 3 {
			return errors.New("retry limit exceeded")
		}
		try = control_retry.XTry + 1
	} else {
		try = 1
	}

	defer func() {
		new_retry.XTry = try
		if err := r.db.WithContext(ctx).Create(&new_retry).Error; err != nil {
			return
		}
		simulate_log.XTry = try
		simulate_log.IsRetried = true
		if err := r.db.WithContext(ctx).Debug().Save(&simulate_log).Error; err != nil {
			return
		}
	}()

	var (
		current_simulate entities.Simulate
		m_node           []dtos.Node
		m_edge           []dtos.Edge
	)

	if err := r.db.Where(query.WhereID, simulate_log.SimulateID).
		Where(query.WhereOrganizationID, simulate_log.OrganizationID).
		Preload(clause.Associations).
		First(&current_simulate).Error; err != nil {
		new_retry.Message = err.Error()
		new_retry.Status = "failed"
		new_retry.Response = ""
		return err
	}

	for _, v := range current_simulate.Nodes {
		var s_node dtos.Node
		if v.Content != nil {
			s_node.Content = v.Content
		} else {
			s_node.Content = v.FunctionContent
		}
		s_node.ID = v.NodeID
		s_node.Name = v.Name
		s_node.Position = v.Position
		s_node.Type = v.Type

		m_node = append(m_node, s_node)
	}

	for _, v := range current_simulate.Edges {
		var s_edge dtos.Edge
		s_edge.ID = v.ID
		s_edge.SourceHandle = v.SourceHandle
		s_edge.SourceID = v.SourceID
		s_edge.TargetID = v.TargetID
		s_edge.Type = v.Type

		m_edge = append(m_edge, s_edge)
	}

	content_map := make(map[string]any)

	content_map["nodes"] = m_node
	content_map["edges"] = m_edge

	graph, err := json.Marshal(content_map)
	if err != nil {
		new_retry.Message = err.Error()
		new_retry.Status = "failed"
		new_retry.Response = ""
		return err
	}

	new_retry.Request = simulate_log.Request

	engine := zen.NewEngine(zen.EngineConfig{
		Loader:            readTestFile,
		CustomNodeHandler: nodes.CustomNodeHandler,
	})

	defer engine.Dispose()
	zenOutput, err := engine.EvaluateWithOpts(
		string(graph),
		[]byte(simulate_log.Request),
		zen.EvaluationOptions{Trace: true},
	)
	if err != nil {
		new_retry.Message = err.Error()
		new_retry.Status = "failed"
		new_retry.Response = ""
		return err
	}

	new_retry.Response = string(zenOutput.Result)
	new_retry.Status = "success"
	new_retry.Message = "retry is success"

	return nil
}
