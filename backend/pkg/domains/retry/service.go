package retry

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/dtos"
)

type Service interface {
	Retry(ctx context.Context, req dtos.RequestForRetry) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Retry(ctx context.Context, req dtos.RequestForRetry) error {
	return s.repository.retry(ctx, req)
}
