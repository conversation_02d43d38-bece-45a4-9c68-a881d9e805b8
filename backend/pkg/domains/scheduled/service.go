package scheduled

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/dtos"
)

type Service interface {
	CreateScheduledWork(ctx context.Context, req dtos.RequestForCreateScheduledWork) error
	DeleteScheduledWork(ctx context.Context, id string) error
	GetScheduledWorks(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error)
	GetSingleScheduledWork(ctx context.Context, id string) (dtos.ResponseForScheduledWork, error)
	GetAllScheduledWorkLogs(ctx context.Context, page, perpage int, scheduled_id string) (*dtos.PaginatedData, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) CreateScheduledWork(ctx context.Context, req dtos.RequestForCreateScheduledWork) error {
	return s.repository.createScheduledWork(ctx, req)
}

func (s *service) DeleteScheduledWork(ctx context.Context, id string) error {
	return s.repository.deleteScheduledWork(ctx, id)
}

func (s *service) GetScheduledWorks(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error) {
	return s.repository.getScheduledWorks(ctx, page, perpage)
}

func (s *service) GetSingleScheduledWork(ctx context.Context, id string) (dtos.ResponseForScheduledWork, error) {
	return s.repository.getSingleScheduledWork(ctx, id)
}

func (s *service) GetAllScheduledWorkLogs(ctx context.Context, page, perpage int, scheduled_id string) (*dtos.PaginatedData, error) {
	return s.repository.getAllScheduledWorkLogs(ctx, page, perpage, scheduled_id)
}
