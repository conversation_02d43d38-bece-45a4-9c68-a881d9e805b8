package scheduled

import (
	"context"
	"errors"
	"log"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	createScheduledWork(ctx context.Context, req dtos.RequestForCreateScheduledWork) error
	deleteScheduledWork(ctx context.Context, id string) error
	getScheduledWorks(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error)
	getSingleScheduledWork(ctx context.Context, id string) (dtos.ResponseForScheduledWork, error)

	getAllScheduledWorkLogs(ctx context.Context, page, perpage int, scheduled_id string) (*dtos.PaginatedData, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) createScheduledWork(ctx context.Context, req dtos.RequestForCreateScheduledWork) error {
	var (
		scheduledWork    entities.ScheduledWork
		scheduledWorkLog entities.ScheduledWorkLog
	)

	if req.HowManyTimeToTry <= 0 {
		return errors.New("retry count must be greater than 0")
	}

	converted_each_x_time, err := time.Parse("15:04", req.EachXTime)
	if err != nil {
		log.Println("err: ", err.Error())
		return err
	}

	converted_start_date, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return err
	}

	converted_end_date, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return err
	}

	scheduledWork.OrganizationID = state.CurrentAdminOrganization(ctx)
	scheduledWork.AdminID = state.CurrentAdminUser(ctx)
	scheduledWork.SimulateID = uuid.MustParse(req.SimulateID)
	scheduledWork.NameOfWork = req.NameOfWork
	scheduledWork.Request = req.Request
	scheduledWork.EachXTime = converted_each_x_time
	scheduledWork.StartDate = converted_start_date
	scheduledWork.EndDate = converted_end_date
	scheduledWork.HowManyTimeToTry = req.HowManyTimeToTry

	tx := r.db.Begin()

	if err := tx.WithContext(ctx).
		Create(&scheduledWork).Error; err != nil {
		tx.Rollback()
		return err
	}

	scheduledWorkLog.ScheduledWorkID = scheduledWork.ID

	if err := tx.Model(&entities.ScheduledWorkLog{}).
		WithContext(ctx).
		Create(&scheduledWorkLog).Error; err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}

func (r *repository) deleteScheduledWork(ctx context.Context, id string) error {
	var scheduled_work entities.ScheduledWork

	r.db.WithContext(ctx).
		Where("id = ? AND organization_id = ?", id, state.CurrentAdminOrganization(ctx)).
		First(&scheduled_work)

	if scheduled_work.ID == uuid.Nil {
		return errors.New("scheduled work not found")
	}

	if err := r.db.WithContext(ctx).
		Where("id = ? AND organization_id = ?", id, state.CurrentAdminOrganization(ctx)).
		Delete(&scheduled_work).Error; err != nil {

		return err
	}

	return nil
}

func (r *repository) getScheduledWorks(ctx context.Context, page, perpage int) (*dtos.PaginatedData, error) {

	var (
		count           int64
		base_query      *gorm.DB
		scheduled_works []entities.ScheduledWork
		resp            []dtos.ResponseForScheduledWork
	)
	base_query = r.db.WithContext(ctx).
		Model(&entities.ScheduledWork{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx))

	if err := base_query.
		Count(&count).Error; err != nil {
		return nil, err
	}

	q := base_query.Limit(perpage).
		Offset((page - 1) * perpage).
		Order("created_at DESC").
		Find(&scheduled_works)

	if q.Error != nil {
		return nil, q.Error
	}

	for _, v := range scheduled_works {
		var single_resp dtos.ResponseForScheduledWork
		single_resp.ID = v.ID.String()
		single_resp.CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		single_resp.NameOfWork = v.NameOfWork
		single_resp.Request = v.Request
		single_resp.EachXTime = v.EachXTime.Format("15:04")
		single_resp.StartDate = v.StartDate.Format("2006-01-02")
		single_resp.EndDate = v.EndDate.Format("2006-01-02")
		single_resp.HowManyTimeToTry = v.HowManyTimeToTry
		single_resp.SimulateID = v.SimulateID.String()
		single_resp.OrganizationID = v.OrganizationID.String()
		single_resp.AdminID = v.AdminID.String()

		resp = append(resp, single_resp)
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(perpage))),
		Rows:       resp,
	}, nil
}

func (r *repository) getSingleScheduledWork(ctx context.Context, id string) (dtos.ResponseForScheduledWork, error) {
	var (
		resp               dtos.ResponseForScheduledWork
		scheduled_work     entities.ScheduledWork
		scheduled_work_log entities.ScheduledWorkLog
	)
	if err := r.db.WithContext(ctx).
		Model(&entities.ScheduledWork{}).
		Where("id = ? AND organization_id = ?", id, state.CurrentAdminOrganization(ctx)).
		First(&scheduled_work).Error; err != nil {
		return resp, err
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.ScheduledWorkLog{}).
		Where("scheduled_work_id = ?", scheduled_work.ID).
		First(&scheduled_work_log).Error; err != nil {
		return resp, err
	}

	resp.ID = scheduled_work.ID.String()
	resp.CreatedAt = scheduled_work.CreatedAt.Format("2006-01-02 15:04:05")
	resp.NameOfWork = scheduled_work.NameOfWork
	resp.Request = scheduled_work.Request
	resp.EachXTime = scheduled_work.EachXTime.Format("15:04")
	resp.StartDate = scheduled_work.StartDate.Format("2006-01-02")
	resp.EndDate = scheduled_work.EndDate.Format("2006-01-02")
	resp.HowManyTimeToTry = scheduled_work.HowManyTimeToTry
	resp.SimulateID = scheduled_work.SimulateID.String()
	resp.OrganizationID = scheduled_work.OrganizationID.String()
	resp.AdminID = scheduled_work.AdminID.String()

	resp.TotalWork = scheduled_work_log.TotalWork
	resp.TotalSuccess = scheduled_work_log.TotalSuccess
	resp.TotalError = scheduled_work_log.TotalError

	return resp, nil
}

func (r *repository) getAllScheduledWorkLogs(ctx context.Context, page, perpage int, scheduled_id string) (*dtos.PaginatedData, error) {
	var (
		count      int64
		base_query *gorm.DB
		logs       []entities.SimulateLog
		resp       []dtos.ResponseForSimulateLog
	)
	base_query = r.db.WithContext(ctx).
		Model(&entities.SimulateLog{}).
		Where("scheduled_id = ?", scheduled_id).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx))

	if err := base_query.
		Count(&count).Error; err != nil {
		return nil, err
	}

	q := base_query.Limit(perpage).
		Offset((page - 1) * perpage).
		Order("created_at DESC").
		Find(&logs)

	if q.Error != nil {
		return nil, q.Error
	}

	for _, v := range logs {
		var single_log dtos.ResponseForSimulateLog

		single_log.ID = v.ID.String()
		single_log.SimulateID = v.SimulateID.String()
		single_log.OrganizationID = v.OrganizationID.String()
		single_log.AdminID = v.AdminID.String()
		single_log.CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		single_log.Type = v.Type
		single_log.Proto = v.Proto
		single_log.Message = v.Message
		single_log.Domain = v.Domain
		single_log.Request = v.Request
		single_log.Response = v.Response

		resp = append(resp, single_log)
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(perpage))),
		Rows:       resp,
	}, nil
}
