package log

import (
	"context"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	List(page, perpage int, user_id uuid.UUID, entity, entity_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
	Read(id string, ctx context.Context, t trace.Tracer) (*dtos.LogDTO, error)

	GetSimulateLogs(page, perpage int, simulate_id, scheduled_id, log_type, protocol, date, status string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
	GetSingleSimulateLog(ctx context.Context, t trace.Tracer, log_id string) (dtos.ResponseForSimulateLog, error)

	GetRetriedLog(ctx context.Context, retried_log_id string, t trace.Tracer) (dtos.ResponseForRetry, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) List(page, perpage int, user_id uuid.UUID, entity, entity_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.service.List", "log.service.List")

	return s.repository.List(page, perpage, user_id, entity, entity_id, ctx, t)
}

func (s *service) Read(id string, ctx context.Context, t trace.Tracer) (*dtos.LogDTO, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.service.Read", "log.service.Read")

	return s.repository.Read(id, ctx, t)
}

func (s *service) GetSimulateLogs(page, perpage int, simulate_id, scheduled_id, log_type, protocol, date, status string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.service.GetSimulateLogs", "log.service.GetSimulateLogs")

	return s.repository.getSimulateLogs(page, perpage, simulate_id, scheduled_id, log_type, protocol, date, status, ctx, t)
}

func (s *service) GetSingleSimulateLog(ctx context.Context, t trace.Tracer, log_id string) (dtos.ResponseForSimulateLog, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.service.GetSingleSimulateLog", "log.service.GetSingleSimulateLog")
	return s.repository.getSingleSimulateLog(ctx, t, log_id)
}

func (s *service) GetRetriedLog(ctx context.Context, retried_log_id string, t trace.Tracer) (dtos.ResponseForRetry, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.service.GetRetriedLog", "log.service.GetRetriedLog")
	return s.repository.getRetriedLog(ctx, retried_log_id, t)
}
