package log

import (
	"context"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type Repository interface {
	List(page, perpage int, user_id uuid.UUID, entity, entity_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
	Create(log *entities.Log, ctx context.Context, t trace.Tracer) error
	Read(id string, ctx context.Context, t trace.Tracer) (*dtos.LogDTO, error)

	getSimulateLogs(page, perpage int, simulate_id, scheduled_id, log_type, protocol, date, status string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
	getSingleSimulateLog(ctx context.Context, t trace.Tracer, log_id string) (dtos.ResponseForSimulateLog, error)

	getRetriedLog(ctx context.Context, retried_log_id string, t trace.Tracer) (dtos.ResponseForRetry, error)
}

type repository struct {
	db         *gorm.DB
	entityName string
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db:         db,
		entityName: "log",
	}
}

func (r *repository) List(page, perpage int, user_id uuid.UUID, entity, entity_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.repository.List", "log.repository.List")
	var count int64
	var logs []entities.Log
	var list []dtos.LogListItemDTO
	condition := query.QueryBuilder{
		Keys: []query.QueryKey{
			{
				Key:    query.WhereEntity,
				Values: []interface{}{entity},
				Skip:   entity == "",
			},
			{
				Key:    query.WhereType,
				Values: []interface{}{"info"},
				Skip:   state.AmIAuthorized(ctx),
			},
		},
	}

	sql, data := condition.GetQueriesWithValues()

	base_query := r.db.WithContext(ctx).Where(sql, data...).Where("organization_id = ?", state.CurrentAdminOrganization(ctx))

	req := base_query.
		Limit(perpage).
		Offset((page - 1) * perpage).
		Order(query.OrderByCreatedAtDesc).
		Find(&logs)

	base_query.
		Where(sql, data...).
		Count(&count)

	if req.Error != nil {
		return nil, req.Error
	}

	for _, log := range logs {
		item := dtos.LogListItemDTO{
			ID:             log.ID.String(),
			Title:          log.Title,
			Entity:         log.Entity,
			Type:           log.Type,
			Ip:             log.Ip,
			Proto:          log.Proto,
			OrganizationID: log.OrganizationID.String(),
		}
		list = append(list, item)
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      req.RowsAffected,
		TotalPages: int(math.Ceil(float64(count) / float64(perpage))),
		Rows:       list,
	}, nil
}

func (r *repository) Create(log *entities.Log, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "log.repository.Create", "log.repository.Create")
	return r.db.WithContext(ctx).Model(&entities.Log{}).Create(&log).Error
}

func (r *repository) Read(id string, ctx context.Context, t trace.Tracer) (*dtos.LogDTO, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.repository.Read", "log.repository.Read")

	var log entities.Log

	if err := r.db.WithContext(ctx).
		Where(query.BuildQuery(query.WhereID), id).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		First(&log).Error; err != nil {
		return nil, err
	}

	logdto := dtos.LogDTO{
		ID:             log.ID.String(),
		Title:          log.Title,
		Entity:         log.Entity,
		EntityID:       log.EntityID.String(),
		Type:           log.Type,
		Ip:             log.Ip,
		Proto:          log.Proto,
		Message:        log.Message,
		CreatedAt:      log.CreatedAt.Format("2006-01-02 15:04:05"),
		OrganizationID: log.OrganizationID.String(),
	}

	if log.AdminID != uuid.Nil {
		var admin entities.User
		if err := r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), log.AdminID).First(&admin).Error; err != nil {
			return nil, err
		}
		logdto.Admin = admin.Name

	} else {
		logdto.Admin = "-"
	}

	return &logdto, nil

}

func (r *repository) getSimulateLogs(page, perpage int, simulate_id, scheduled_id, log_type, protocol, date, status string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.repository.getSimulateLogs", "log.repository.getSimulateLogs")

	var (
		simulate_logs []entities.SimulateLog
		response      []dtos.ResponseForSimulateLog
		count         int64
		now           time.Time = time.Now()
		base_query    *gorm.DB
	)

	base_query = r.db.WithContext(ctx).
		Model(&entities.SimulateLog{}).
		Where("simulate_id = ? AND organization_id = ?", simulate_id, state.CurrentAdminOrganization(ctx))

	if log_type != "" {
		base_query = base_query.Where("type = ?", log_type)
	}
	if protocol != "" {
		base_query = base_query.Where("proto = ?", protocol)
	}
	if status != "" {
		if status == "1" {
			base_query = base_query.Where("is_retried = ?", true)
		} else {
			base_query = base_query.Where("is_retried = ?", false)
		}
	}

	/*
		today = 1
		this week = 2
		this month = 3
		All time = 4
	*/

	var t_d int

	if date != "" {
		switch date {
		case "1":
			t_d = 1
		case "2":
			t_d = 7
		case "3":
			t_d = 30
		default:
			t_d = 1
		}

		if t_d > 0 {
			base_query = base_query.Where("created_at BETWEEN ? AND ?", now.AddDate(0, 0, -t_d), now)
		}
	}

	if scheduled_id != "" {
		base_query = base_query.Where("scheduled_id = ?", scheduled_id)
	}

	if err := base_query.
		Count(&count).Error; err != nil {
		return nil, err
	}

	q := base_query.Limit(perpage).
		Offset((page - 1) * perpage).
		Order("created_at DESC").
		Find(&simulate_logs)

	for _, v := range simulate_logs {
		var resp dtos.ResponseForSimulateLog
		resp.ID = v.ID.String()
		resp.Message = v.Message
		resp.Type = v.Type
		resp.Proto = v.Proto
		resp.Source = v.Source
		resp.CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		resp.SimulateID = v.SimulateID.String()
		resp.OrganizationID = v.OrganizationID.String()
		resp.AdminID = v.AdminID.String()
		resp.IsRetried = v.IsRetried
		resp.XTry = v.XTry

		var retry []entities.Retry
		if err := r.db.WithContext(ctx).
			Model(&entities.Retry{}).
			Where("retried_simulate_log_id = ? AND organization_id = ?", v.ID, state.CurrentAdminOrganization(ctx)).
			Find(&retry).Error; err != nil && err != gorm.ErrRecordNotFound {
			return nil, err
		}

		for _, v := range retry {
			var retry_resp dtos.RetryResponse
			retry_resp.ID = v.ID.String()
			retry_resp.Status = v.Status
			retry_resp.XTry = v.XTry
			retry_resp.Message = v.Message
			retry_resp.Request = v.Request
			retry_resp.Response = v.Response

			resp.RetryData = append(resp.RetryData, retry_resp)
		}

		response = append(response, resp)
	}

	if q.Error != nil {
		return nil, q.Error
	}

	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(perpage))),
		Rows:       response,
	}, nil
}

func (r *repository) getSingleSimulateLog(ctx context.Context, t trace.Tracer, log_id string) (dtos.ResponseForSimulateLog, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.repository.getSingleSimulateLog", "log.repository.getSingleSimulateLog")
	var (
		simulate_log         entities.SimulateLog
		current_organization entities.Organization
		current_admin        entities.Admin
		current_simulate     entities.Simulate
		current_scheduled    entities.ScheduledWork
		response             dtos.ResponseForSimulateLog
	)
	if err := r.db.WithContext(ctx).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Where("id = ?", log_id).
		First(&simulate_log).Error; err != nil {
		return response, err
	}

	if err := r.db.WithContext(ctx).
		Where("id = ?", simulate_log.SimulateID).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		First(&current_simulate).Error; err != nil {
		return response, err
	}

	if err := r.db.WithContext(ctx).
		Where("id = ?", state.CurrentAdminOrganization(ctx)).
		First(&current_organization).Error; err != nil {
		return response, err
	}

	if err := r.db.WithContext(ctx).
		Where("id = ?", simulate_log.AdminID).
		First(&current_admin).Error; err != nil {
		return response, err
	}

	if simulate_log.IsScheduled {
		if err := r.db.WithContext(ctx).
			Where("id = ?", simulate_log.ScheduledID).
			Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
			First(&current_scheduled).Error; err != nil {
			return response, err
		}
	}

	response.ID = simulate_log.ID.String()
	response.SimulateID = simulate_log.SimulateID.String()
	response.OrganizationID = current_organization.ID.String()
	response.AdminID = current_admin.ID.String()
	response.AdminName = current_admin.Name
	response.SimulateName = current_simulate.Name
	response.OrganizationName = current_organization.Name
	response.Type = simulate_log.Type
	response.Proto = simulate_log.Proto
	response.Source = simulate_log.Source
	response.Domain = simulate_log.Domain
	response.Message = simulate_log.Message
	response.CreatedAt = simulate_log.CreatedAt.Format("2006-01-02 15:04:05")
	response.Request = simulate_log.Request
	response.Response = simulate_log.Response
	response.IsRetried = simulate_log.IsRetried
	response.IsScheduled = simulate_log.IsScheduled
	response.ScheduledID = simulate_log.ScheduledID.String()
	response.ScheduledNameOfWork = current_scheduled.NameOfWork
	response.XTry = simulate_log.XTry

	return response, nil
}

func (r *repository) getRetriedLog(ctx context.Context, retried_log_id string, t trace.Tracer) (dtos.ResponseForRetry, error) {
	ctx = helpers.PushToTrace(t, ctx, "log.repository.getRetriedLog", "log.repository.getRetriedLog")

	var (
		retried_log entities.Retry
		resp        dtos.ResponseForRetry
	)

	if err := r.db.WithContext(ctx).
		Model(&entities.Retry{}).
		Where("id = ? AND organization_id = ?", retried_log_id, state.CurrentAdminOrganization(ctx)).
		First(&retried_log).Error; err != nil {
		return resp, err
	}

	resp.ID = retried_log.ID.String()
	resp.CreatedAt = retried_log.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Status = retried_log.Status
	resp.XTry = retried_log.XTry
	resp.Message = retried_log.Message
	resp.Request = retried_log.Request
	resp.Response = retried_log.Response

	return resp, nil
}
