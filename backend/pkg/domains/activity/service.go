package activity

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	Create(payload *dtos.ActivityCreateDTO, ctx context.Context, t trace.Tracer) error
	List(page, perPage int, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Create(payload *dtos.ActivityCreateDTO, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "activity.service.Create", "activity.service.Create")
	activity := entities.Activity{
		Action:         payload.Action,
		Entity:         payload.Entity,
		IpAddress:      payload.IpAddress,
		AdminID:        payload.AdminID,
		OrganizationID: payload.OrganizationID,
	}
	return s.repository.Create(&activity, ctx, t)
}

func (s *service) List(page, perpage int, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "activity.service.List", "activity.service.List")
	return s.repository.List(page, perpage, ctx, t)
}
