package activity

import (
	"context"
	"math"

	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type Repository interface {
	Create(activity *entities.Activity, ctx context.Context, t trace.Tracer) error
	List(page, perpage int, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) Create(activity *entities.Activity, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "activity.repository.Create", "activity.repository.Create")
	return r.db.WithContext(ctx).Create(activity).Error
}

func (r *repository) List(page, perpage int, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "activity.repository.list", "activity.repository.list")
	var (
		count      int64
		activities []entities.Activity
	)

	base_query := r.db.WithContext(ctx).
		Model(&entities.Activity{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx))

	base_query.Count(&count)

	if err := base_query.
		Limit(perpage).
		Offset((page - 1) * perpage).
		Order(query.OrderByCreatedAtDesc).
		Find(&activities).Error; err != nil {
		return nil, err
	}

	totalPages := int(math.Ceil(float64(count) / float64(perpage)))
	var list []dtos.ActivityDto
	for _, activity := range activities {
		activity := dtos.ActivityDto{
			Action:    activity.Action,
			Entity:    activity.Entity,
			IpAddress: activity.IpAddress,
			CreatedAt: activity.CreatedAt.Local().Format("2006-01-02 15:04:05"),
		}
		list = append(list, activity)
	}
	return &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: totalPages,
		Rows:       list,
	}, nil

}
