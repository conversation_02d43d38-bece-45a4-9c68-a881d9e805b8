package simulate

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	Simulate(payload *dtos.Simulate, ctx *gin.Context, t trace.Tracer) (dtos.ResponseSimulate, error)
	UpdateOrCreateSimulate(payload *dtos.Simulate, ctx *gin.Context, t trace.Tracer) (string, error)
	UpdateSimulateName(ctx *gin.Context, req dtos.RequestForUpdateSimulateName, t trace.Tracer) error
	GetSimulations(page, perpage int, version_id string, ctx *gin.Context, t trace.Tracer) (dtos.PaginatedData, error)
	GetSimulationRules(page, perpage int, ctx *gin.Context, t trace.Tracer) (dtos.PaginatedData, error)
	SimulateRule(simulate_id string, ctx *gin.Context, t trace.Tracer) (dtos.SimulateResponse, error)
	SimulateDetail(ctx *gin.Context, simulate_id string, t trace.Tracer) (dtos.SimulateResponseForDetail, error)
	DeleteSimulate(simulate_id string, ctx context.Context, t trace.Tracer) error
	SimByID(ctx context.Context, req dtos.RequestForSimByID, t trace.Tracer) (dtos.ResponseSimulate, error)

	AddDefaultRequest(ctx *gin.Context, t trace.Tracer, req dtos.RequestForDefaultRequest) error
	GetDefaultRequest(ctx *gin.Context, t trace.Tracer, simulate_id, def_req_type string) (dtos.ResponseForDefaultRequest, error) 
	DeleteDefaultRequest(ctx *gin.Context, t trace.Tracer, id string) error
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

// DeleteSimulate implements Service.
func (s *service) DeleteSimulate(simulate_id string, ctx context.Context, t trace.Tracer) error {
	helpers.PushToTrace(t, ctx, "simulate.service.DeleteSimulate", "simulate.service.DeleteSimulate")
	return s.repository.DeleteSimulate(simulate_id, ctx, t)
}

func (s *service) SimByID(ctx context.Context, req dtos.RequestForSimByID, t trace.Tracer) (dtos.ResponseSimulate, error) {
	return s.repository.SimByID(ctx, req, t)
}

// UpdateSimulate implements Service.
func (s *service) UpdateOrCreateSimulate(payload *dtos.Simulate, ctx *gin.Context, t trace.Tracer) (string, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.UpdateOrCreateSimulate", "simulate.service.UpdateOrCreateSimulate")
	return s.repository.UpdateOrCreateSimulate(payload, ctx, t)
}

func (s *service) UpdateSimulateName(ctx *gin.Context, req dtos.RequestForUpdateSimulateName, t trace.Tracer) error {
	helpers.PushToTrace(t, ctx, "simulate.service.UpdateSimulateName", "simulate.service.UpdateSimulateName")
	return s.repository.SimulateUpdateName(ctx, req, t)
}

// GetSimulations implements Service.
func (s *service) GetSimulations(page, perpage int, version_id string, ctx *gin.Context, t trace.Tracer) (dtos.PaginatedData, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.GetSimulations", "simulate.service.GetSimulations")

	return s.repository.GetSimulations(page, perpage, version_id, ctx, t)
}

// getSimulationRules implements Service.
func (s *service) GetSimulationRules(page, perpage int, ctx *gin.Context, t trace.Tracer) (dtos.PaginatedData, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.GetSimulationRules", "simulate.service.GetSimulationRules")

	return s.repository.GetSimulationRules(page, perpage, ctx, t)
}

// Simulate implements Service.
func (s *service) Simulate(payload *dtos.Simulate, ctx *gin.Context, t trace.Tracer) (dtos.ResponseSimulate, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.Simulate", "simulate.service.Simulate")

	return s.repository.Simulate(payload, ctx, t)
}

// RuleSimulate implements Service.
func (s *service) SimulateRule(simulate_id string, ctx *gin.Context, t trace.Tracer) (dtos.SimulateResponse, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.SimulateRule", "simulate.service.SimulateRule")

	return s.repository.SimulateRule(simulate_id, ctx, t)
}

func (s *service) SimulateDetail(ctx *gin.Context, simulate_id string, t trace.Tracer) (dtos.SimulateResponseForDetail, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.SimulateDetail", "simulate.service.SimulateDetail")

	return s.repository.SimulateDetail(ctx, simulate_id, t)
}

func (s *service) AddDefaultRequest(ctx *gin.Context, t trace.Tracer, req dtos.RequestForDefaultRequest) error {
	helpers.PushToTrace(t, ctx, "simulate.service.AddDefaultRequest", "simulate.service.AddDefaultRequest")
	return s.repository.AddDefaultRequest(ctx, t, req)
}

func (s *service) GetDefaultRequest(ctx *gin.Context, t trace.Tracer, simulate_id, def_req_type string) (dtos.ResponseForDefaultRequest, error) {
	helpers.PushToTrace(t, ctx, "simulate.service.GetDefaultRequest", "simulate.service.GetDefaultRequest")
	return s.repository.getDefaultRequest(ctx, t, simulate_id, def_req_type)
}

func (s *service) DeleteDefaultRequest(ctx *gin.Context, t trace.Tracer, id string) error {
	helpers.PushToTrace(t, ctx, "simulate.service.DeleteDefaultRequest", "simulate.service.DeleteDefaultRequest")
	return s.repository.DeleteDefaultRequest(ctx, t, id)
}
