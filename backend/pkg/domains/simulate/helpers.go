package simulate

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
)

func getSimulates(page, perpage int, db *gorm.DB, preload []string, version_id string, ctx *gin.Context) (dtos.PaginatedData, error) {
	var (
		simulates   []entities.Simulate
		simulateDTO []dtos.SimulateResponse
		res         dtos.PaginatedData
		total_count int64
	)

	var d = db
	var c = db

	if version_id == "" {
		d = d.Raw(`
			SELECT * FROM (
				SELECT DISTINCT ON (version_id) * 
				FROM simulates 
				WHERE organization_id = ? AND deleted_at IS NULL 
				ORDER BY version_id, created_at DESC
			) AS latest_versions
			ORDER BY created_at DESC
			LIMIT ? OFFSET ?
		`, state.CurrentAdminOrganization(ctx), perpage, (page-1)*perpage)

		c = c.Raw(`
		SELECT COUNT(*) 
		FROM (
			SELECT DISTINCT ON (version_id) 1 
			FROM simulates 
			WHERE organization_id = ? AND deleted_at IS NULL
		) AS distinct_versions
		`, state.CurrentAdminOrganization(ctx))

	} else {
		d = d.Order(query.OrderByCreatedAtDesc).
			Where(query.WhereOrganizationID, state.CurrentAdminOrganization(ctx)).
			Where("version_id = ?", version_id).
			Limit(perpage).
			Offset((page - 1) * perpage)

		c = c.Model(&entities.Simulate{}).
			Where(query.WhereOrganizationID, state.CurrentAdminOrganization(ctx)).
			Where("version_id = ?", version_id)

	}

	for _, v := range preload {
		d.Preload(v)
	}

	if err := d.Find(&simulates).Error; err != nil {
		return res, err
	}

	if err := c.Count(&total_count).Error; err != nil {
		return res, err
	}

	for _, v := range simulates {
		var (
			term          dtos.SimulateResponse
			nodes         []dtos.Node
			edges         []dtos.Edge
			current_admin entities.Admin
		)

		if err := db.Model(&entities.Admin{}).
			Where("id = ?", v.AdminID).
			First(&current_admin).Error; err != nil {
			return res, err
		}

		term.ID = v.ID
		term.Name = v.Name
		for _, e := range v.Nodes {
			var node dtos.Node
			if e.Content != nil {
				node.Content = e.Content
			} else {
				node.Content = e.FunctionContent
			}
			node.ID = e.NodeID
			node.Name = e.Name
			node.Position = e.Position
			node.Type = e.Type
			nodes = append(nodes, node)
		}
		for _, v := range v.Edges {
			var term dtos.Edge
			term.ID = v.ID
			term.SourceHandle = v.SourceHandle
			term.SourceID = v.SourceID
			term.TargetID = v.TargetID
			term.Type = v.Type

			edges = append(edges, term)
		}
		term.Nodes = nodes
		term.Edges = edges
		term.Context = v.Context
		term.OrganizationID = v.OrganizationID.String()
		term.AdminID = v.AdminID.String()
		term.CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		term.ChangedBy = current_admin.Name
		term.VersionName = v.VersionName
		term.VersionID = v.VersionID.String()
		term.AccessToken = v.AccessToken

		simulateDTO = append(simulateDTO, term)
	}
	res = dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      total_count,
		TotalPages: int(math.Ceil(float64(total_count) / float64(perpage))),
		Rows:       simulateDTO,
	}
	if len(simulateDTO) == 0 {
		res.Rows = []interface{}{}
	}
	return res, nil
}

func setOutput(o dtos.ResponseSimulate, simulate entities.Simulate) (entities.SimulateOutput, error) {
	var output entities.SimulateOutput
	var err error

	output.SimulateID = simulate.ID
	output.Performance = o.Performance
	output.OrganizationID = simulate.OrganizationID
	output.AdminID = simulate.AdminID

	var traceJSONbyte []byte
	var resulJSONtbyte []byte

	var traceJSONmap map[string]any
	var resulJSONtmap map[string]any

	traceJSONbyte, err = o.Trace.MarshalJSON()
	if err != nil {
		return output, err
	}
	resulJSONtbyte, err = o.Result.MarshalJSON()
	if err != nil {
		return output, err
	}
	err = json.Unmarshal(traceJSONbyte, &traceJSONmap)
	if err != nil {
		return output, err
	}
	err = json.Unmarshal(resulJSONtbyte, &resulJSONtmap)
	if err != nil {
		return output, err
	}
	output.Trace = traceJSONmap
	output.Result = resulJSONtmap

	return output, err
}

func GetNode(node dtos.Node, simulate_id, version_id, org_id, admin_id uuid.UUID, version_name string) (entities.Node, error) {
	var err error

	jsonContentByte, err := json.Marshal(node.Content)
	if err != nil {
		return entities.Node{}, err
	}
	var data map[string]any
	json.Unmarshal(jsonContentByte, &data)
	nodeEntity := entities.Node{
		NodeID:         node.ID,
		Name:           node.Name,
		Position:       node.Position,
		Type:           node.Type,
		Content:        data,
		SimulateID:     simulate_id,
		VersionID:      version_id,
		OrganizationID: org_id,
		AdminID:        admin_id,
		VersionName:    version_name,
	}
	if node.Type == "functionNode" {
		if m, ok := node.Content.(map[string]interface{}); ok {
			if source, exists := m["source"]; exists {
				if sourceStr, isString := source.(string); isString {
					nodeEntity.FunctionContent = sourceStr
				} else {
					nodeEntity.FunctionContent = ""
				}
			} else {
				nodeEntity.FunctionContent = ""
			}
		} else {
			nodeEntity.FunctionContent = ""
		}
	}

	return nodeEntity, err
}

func GetEdge(edge dtos.Edge, simulate_id, version_id, org_id, admin_id uuid.UUID, version_name string, status int) entities.Edge {

	edgeEntity := entities.Edge{
		SourceID:       edge.SourceID,
		TargetID:       edge.TargetID,
		Type:           edge.Type,
		SourceHandle:   edge.SourceHandle,
		SimulateID:     simulate_id,
		VersionID:      version_id,
		OrganizationID: org_id,
		AdminID:        admin_id,
		VersionName:    version_name,
	}

	if status == 2 {
		edgeEntity.ID = uuid.New().String()
	} else {
		edgeEntity.ID = edge.ID
	}

	return edgeEntity
}

func getContext(payload *dtos.Simulate) (entities.Attrs, error) {
	var context entities.Attrs
	contextPayload, err := json.Marshal(payload.Context)
	if err != nil {
		return context, err
	}

	err = json.Unmarshal(contextPayload, &context)
	if err != nil {
		return context, err
	}
	return context, nil
}

func setSimulate(payload *dtos.Simulate, ctx *gin.Context) (entities.Simulate, error) {
	var simulate entities.Simulate

	context, err := getContext(payload)
	if err != nil {
		return simulate, err
	}
	new := make(map[string]interface{})
	for key, value := range context {
		if key == "_organization_id" || key == "_simulate_id" || key == "_user_id" || key == "_code" || key == "_sms_node_message" || key == "_mail_node_message" {
			continue
		} else {
			new[key] = value
		}
	}
	simulate.Context = new
	if payload.Name != "" {
		simulate.Name = payload.Name
	} else {
		simulate.Name = uuid.NewString()
	}
	simulate.OrganizationID = state.CurrentAdminOrganization(ctx)
	simulate.AdminID = state.CurrentAdminUser(ctx)

	return simulate, nil
}

func GetNewVersion(old_version string) (string, int, error) {
	var result string
	var version_number int
	y, m, v, err := ResolveVersion(old_version)
	if err != nil {
		return result, version_number, err
	}

	now := time.Now()

	if old_version == "" {
		return fmt.Sprintf("v%d.%d.%d", now.Year(), int(now.Month()), 0), version_number, nil
	}

	if now.Year() > y || (y == now.Year() && m == 12 && now.Month() == time.January) {
		y = now.Year()
		m = 1
		v = 0
	} else if int(now.Month()) > m {
		m++
		v = 0
	} else {
		v++
	}

	result = fmt.Sprintf("v%d.%d.%d", y, m, v)

	return result, v, nil
}

// example version: v2024.11.1
func ResolveVersion(version string) (int, int, int, error) {
	strings := strings.Split(version, ".")
	var err error
	var year, month, version_number int

	if version == "" {
		return 0, 0, 0, nil
	}

	year, err = strconv.Atoi(strings[0][1:])
	if err != nil {
		log.Println("Error parsing year:", err)
		return 0, 0, 0, err
	}

	month, err = strconv.Atoi(strings[1])
	if err != nil {
		log.Println("Error parsing month:", err)
		return 0, 0, 0, err
	}

	version_number, err = strconv.Atoi(strings[2])
	if err != nil {
		log.Println("Error parsing month:", err)
		return 0, 0, 0, err
	}

	return year, month, version_number, nil
}
