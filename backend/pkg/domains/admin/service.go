package admin

import (
	"context"
	"errors"
	"strings"

	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	Create(payload *dtos.AdminCreateDto, ctx context.Context, t trace.Tracer) error
	List(page, perpage int, organization_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
	Update(id string, payload *dtos.AdminUpdateDto, ctx context.Context, t trace.Tracer) error
	Delete(id string, ctx context.Context, t trace.Tracer) error
	GetByID(id string, ctx context.Context, t trace.Tracer) (*entities.Admin, error)
	Details(id string, ctx context.Context, t trace.Tracer) (*dtos.AdminDetailDTO, error)
	ForceLogout(id string, ctx context.Context, t trace.Tracer) error
}
type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Create(payload *dtos.AdminCreateDto, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "admin.service.Create", "admin.service.Create")

	// check email already exists
	exist, err := s.repository.CheckEmailExist(nil, payload.Email, ctx, t)
	if err != nil {
		return err
	}
	if exist {
		return errors.New("this email address is being used by another admin")
	}

	admin := &entities.Admin{
		FirstName:      strings.TrimSpace(payload.FirstName),
		LastName:       strings.TrimSpace(payload.LastName),
		Name:           payload.FirstName + " " + payload.LastName,
		Email:          payload.Email,
		Password:       utils.Bcrypt(payload.Password),
		OrganizationID: helpers.ParseID(payload.OrganizationID),
		RoleID:         helpers.ParseID(payload.RoleID),
		Audit: entities.Audit{
			MakedBy:   state.CurrentAdminUser(ctx),
			MakedDate: helpers.Time(),
		},
	}

	return s.repository.Create(admin, ctx, t)
}

func (s *service) List(page, perpage int, organization_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "admin.service.List", "admin.service.List")
	return s.repository.List(page, perpage, organization_id, ctx, t)
}

func (s *service) Update(id string, payload *dtos.AdminUpdateDto, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "admin.service.Update", "admin.service.Update")

	// check email already exists
	exist, err := s.repository.CheckEmailExist(&id, payload.Email, ctx, t)
	if err != nil {
		return err
	}
	if exist {
		return errors.New("this email address is being used by another admin")
	}

	admin := &entities.Admin{
		Name:           payload.FirstName + " " + payload.LastName,
		FirstName:      payload.FirstName,
		LastName:       payload.LastName,
		Email:          payload.Email,
		OrganizationID: helpers.ParseID(payload.OrganizationID),
		RoleID:         helpers.ParseID(payload.RoleID),
		Suspended:      payload.Suspended,
		SuspendReason:  payload.SuspendReason,
		Audit: entities.Audit{
			UpdatedBy:   state.CurrentAdminUser(ctx),
			UpdatedDate: helpers.Time(),
		},
	}

	if payload.Password != "" {
		admin.Password = utils.Bcrypt(payload.Password)
	}

	return s.repository.Update(id, admin, ctx, t)
}

func (s *service) Delete(id string, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "admin.service.Delete", "admin.service.Delete")

	cannotBeDeleted, _, err := s.repository.CheckRelationsExist(id, ctx, t)
	if err != nil {
		return err
	}

	if cannotBeDeleted {
		return errors.New("admins.cannot_deleted")
	}

	return s.repository.Delete(id, ctx, t)
}

func (s *service) GetByID(id string, ctx context.Context, t trace.Tracer) (*entities.Admin, error) {
	ctx = helpers.PushToTrace(t, ctx, "admin.service.GetByID", "admin.service.GetByID")
	return s.repository.GetByID(id, ctx, t)
}

func (s *service) Details(id string, ctx context.Context, t trace.Tracer) (*dtos.AdminDetailDTO, error) {
	ctx = helpers.PushToTrace(t, ctx, "admin.service.Details", "admin.service.Details")
	return s.repository.Details(id, ctx, t)
}

func (s *service) ForceLogout(id string, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "admin.service.ForceLogout", "admin.service.ForceLogout")
	return s.repository.ForceLogout(id, ctx, t)
}
