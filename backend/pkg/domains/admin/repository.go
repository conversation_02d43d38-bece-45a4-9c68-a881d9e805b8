package admin

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"time"

	"github.com/nats-io/nats.go"
	"github.com/parsguru/pars-vue/internal/cache"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/eventer"
	"github.com/parsguru/pars-vue/pkg/hasher"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/nat"
	"github.com/parsguru/pars-vue/pkg/ptr"
	"github.com/parsguru/pars-vue/pkg/redis"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type Repository interface {
	Create(admin *entities.Admin, ctx context.Context, t trace.Tracer) error
	List(page, perpage int, organization_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error)
	Update(id string, admin *entities.Admin, ctx context.Context, t trace.Tracer) error
	Delete(id string, ctx context.Context, t trace.Tracer) error
	GetByID(id string, ctx context.Context, t trace.Tracer) (*entities.Admin, error)
	Details(id string, ctx context.Context, t trace.Tracer) (*dtos.AdminDetailDTO, error)
	CheckEmailExist(id *string, email string, ctx context.Context, tracer trace.Tracer) (bool, error)
	CheckRelationsExist(id string, ctx context.Context, t trace.Tracer) (bool, map[string]error, error)
	ForceLogout(id string, ctx context.Context, t trace.Tracer) error
}

type repository struct {
	db         *gorm.DB
	entityName string
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db:         db,
		entityName: "admin",
	}
}

func (r *repository) Create(admin *entities.Admin, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "admin.repository.Create", "admin.repository.Create")
	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	if err := tx.WithContext(ctx).Model(&entities.Admin{}).Create(admin).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	// clear cache list
	cacheListKey := fmt.Sprintf("cachekeys:organization:%s:admin:list", state.CurrentAdminOrganization(ctx))
	cacheKeys, err := cache.SMembers(cacheListKey)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Admin Create Cache SMembers",
			Message:        "Error while getting cache keys with cache.SMembers: " + err.Error(),
			Entity:         "admin",
			Type:           "error",
			Proto:          "http",
			Ip:             state.CurrentIP(ctx),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		})
	}

	for _, cacheKey := range cacheKeys {
		if err := cache.Del(cacheKey); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Create Cache Del",
				Message:        "Error while deleting cache with cache.Del: " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}
	}

	return nil
}

func (r *repository) List(page, perpage int, organization_id string, ctx context.Context, t trace.Tracer) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "admin.repository.List", "admin.repository.List")

	var count int64
	var admins []entities.Admin
	var list []dtos.AdminListDTO

	condition := query.QueryBuilder{
		Keys: []query.QueryKey{
			{
				Key:    query.WhereOrganizationID,
				Values: []any{state.CurrentAdminOrganization(ctx)},
				Skip:   state.AmIAuthorized(ctx) || organization_id != "",
			},
			{
				Key:    query.WhereOrganizationID,
				Values: []any{organization_id},
				Skip:   organization_id == "",
			},
			{
				Key:    query.WhereOrganizationIDIN,
				Values: []any{helpers.GetCurrentOrganizationWithChildOrgs(r.db, ctx)},
				Skip:   organization_id == "" || state.AmIAuthorized(ctx),
			},
		},
	}

	sql, sqldata := condition.GetQueriesWithValues()

	r.db.WithContext(ctx).Model(&entities.Admin{}).Where(sql, sqldata...).Count(&count)

	cacheListKey := fmt.Sprintf("cachekeys:organization:%s:admin:list", state.CurrentAdminOrganization(ctx))
	cacheKey := fmt.Sprintf("organization:%s:admins:query:%s:querydata:%d:page:%d:perpage:%d", state.CurrentAdminOrganization(ctx), sql, sqldata, page, perpage)

	data, err := cache.Get(cacheKey)
	if err != nil {

		if err := r.db.WithContext(ctx).Model(&entities.Admin{}).Where(sql, sqldata...).Limit(perpage).Offset((page - 1) * perpage).Order(query.OrderByCreatedAtDesc).Find(&admins).Error; err != nil {
			return nil, err
		}

		for _, admin := range admins {
			adminRole := &entities.Role{}
			organization := &entities.Organization{}
			r.db.WithContext(ctx).Model(adminRole).Where(query.BuildQuery(query.WhereID), admin.RoleID).First(adminRole)
			r.db.WithContext(ctx).Model(organization).Where(query.BuildQuery(query.WhereID), admin.OrganizationID).First(organization)
			user := dtos.AdminListDTO{
				ID:           admin.ID.String(),
				HashID:       admin.HashID,
				Name:         admin.Name,
				FirstName:    admin.FirstName,
				LastName:     admin.LastName,
				Email:        admin.Email,
				Organization: organization.Name,
				Role:         adminRole.Name,
				CreatedAt:    admin.CreatedAt.Format("2006-01-02 15:04:05"),
				UpdatedAt:    admin.UpdatedAt.Format("2006-01-02 15:04:05"),
			}
			list = append(list, user)
		}

		jsonData, err := json.Marshal(list)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin List Cache Marshal Error",
				Message:        "Error : Admin List Cache Marshal Error : " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}

		if err := cache.Set(cacheKey, string(jsonData), time.Minute*5); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin List Cache Set Error",
				Message:        "Error : Admin List Cache Set Error : " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}

		if err := cache.SAdd(cacheListKey, cacheKey); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin List Cache SAdd",
				Message:        "Error while adding cache with cache.SAdd: " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Ip:             state.CurrentIP(ctx),
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}

	} else {
		if err := json.Unmarshal([]byte(data), &list); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin List Cache Unmarshal Error",
				Message:        "Error : Admin List Cache Unmarshal Error : " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}
	}

	totalPages := int(math.Ceil(float64(count) / float64(perpage)))

	paginated := &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: totalPages,
		Rows:       &list,
	}
	return paginated, nil
}

func (r *repository) Update(id string, admin *entities.Admin, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "admin.repository.Update", "admin.repository.Update")
	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	var currentAdmin entities.Admin
	r.db.WithContext(ctx).Model(&entities.Admin{}).Where(query.BuildQuery(query.WhereID), id).First(&currentAdmin)

	if err := tx.WithContext(ctx).Model(&entities.Admin{}).Where(query.BuildQuery(query.WhereID), id).UpdateColumns(admin).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	eventer.PushEvent("admin.update", id)

	if err := cache.Del("admin:" + id); err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Admin update cache delete error",
			Message:        "Admin update cache delete error : " + err.Error(),
			Entity:         "admin",
			Type:           "error",
			Ip:             state.CurrentIP(ctx),
			Proto:          "http",
			EntityID:       helpers.ParseID(id),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		})
	}

	cache.Del("admin:" + currentAdmin.Email)
	// clear cache list
	cacheListKey := fmt.Sprintf("cachekeys:organization:%s:admin:list", state.CurrentAdminOrganization(ctx))
	cacheKeys, err := cache.SMembers(cacheListKey)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Admin Update Cache SMembers",
			Message:        "Error while getting cache keys with cache.SMembers: " + err.Error(),
			Entity:         "admin",
			Type:           "error",
			Proto:          "http",
			Ip:             state.CurrentIP(ctx),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		})
	}

	for _, cacheKey := range cacheKeys {
		if err := cache.Del(cacheKey); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Update Cache Del",
				Message:        "Error while deleting cache with cache.Del: " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}
	}

	return nil
}

func (r *repository) Delete(id string, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "admin.repository.Delete", "admin.repository.Delete")
	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	if err := tx.WithContext(ctx).Where(query.BuildQuery(query.WhereID), id).Model(&entities.Admin{}).Delete(&entities.Admin{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := cache.Del("admin:" + id); err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Admin delete cache delete error",
			Message:        "Admin delete cache delete error : " + err.Error(),
			Entity:         "admin",
			Type:           "error",
			Ip:             state.CurrentIP(ctx),
			Proto:          "http",
			EntityID:       helpers.ParseID(id),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		})
	}

	// clear cache list
	cacheListKey := fmt.Sprintf("cachekeys:organization:%s:admin:list", state.CurrentAdminOrganization(ctx))
	cacheKeys, err := cache.SMembers(cacheListKey)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Admin Delete Cache SMembers",
			Message:        "Error while getting cache keys with cache.SMembers: " + err.Error(),
			Entity:         "admin",
			Type:           "error",
			Proto:          "http",
			Ip:             state.CurrentIP(ctx),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		})
	}

	for _, cacheKey := range cacheKeys {
		if err := cache.Del(cacheKey); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Delete Cache Del",
				Message:        "Error while deleting cache with cache.Del: " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}
	}

	return nil
}

func (r *repository) GetByID(id string, ctx context.Context, t trace.Tracer) (*entities.Admin, error) {
	ctx = helpers.PushToTrace(t, ctx, "admin.repository.GetByID", "admin.repository.GetByID")

	var admin entities.Admin
	data, err := cache.Get("admin:" + id)
	if err != nil {
		if err := r.db.WithContext(ctx).Model(&entities.Admin{}).Where(query.BuildQuery(query.WhereID), id).First(&admin).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("record not found")
			}
			return nil, err
		}
		admin.Password = ""

		jsonData, err := json.Marshal(admin)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin GetByID Cache Marshal Error",
				Message:        "Error : Admin Cache Marshal Error : " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        admin.ID,
				OrganizationID: admin.OrganizationID,
			})
		}

		if err := cache.Set("admin:"+admin.ID.String(), string(jsonData), time.Minute*5); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin GetByID Cache Set Error",
				Message:        "Error : Admin Cache Set Error : " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        admin.ID,
				OrganizationID: admin.OrganizationID,
			})
		}
	} else {
		if err := json.Unmarshal([]byte(data), &admin); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin GetByID Cache Unmarshal Error",
				Message:        "Error : Admin Cache Unmarshal Error : " + err.Error(),
				Entity:         "admin",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        admin.ID,
				OrganizationID: admin.OrganizationID,
			})
		}
	}

	admin.Password = ""

	return &admin, nil
}

func (r *repository) CheckEmailExist(id *string, email string, ctx context.Context, tracer trace.Tracer) (bool, error) {
	var entity entities.Admin
	if err := r.db.WithContext(ctx).Model(&entities.Admin{}).Where(query.BuildQuery(query.WhereEmail), email).First(&entity).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil
		}
		return false, err
	}

	if id != nil {
		if *id == entity.ID.String() && email == entity.Email {
			return false, nil
		}
	}

	return true, nil
}

func (r *repository) CheckRelationsExist(id string, ctx context.Context, t trace.Tracer) (bool, map[string]error, error) {
	foundMap := make(map[string]error)

	findRelation := func(model interface{}, tableName string) error {
		err := r.db.WithContext(ctx).Model(model).Where("admin_id = ?", id).First(model).Error
		if err == nil {
			foundMap[tableName] = errors.New(tableName + " found")
			return nil
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		return nil
	}

	for _, modelToCheck := range []struct {
		Model     interface{}
		TableName string
	}{
		// TODO: think about those:
		//{&entities.Activity{}, "Activity"},
		//{&entities.Event{}, "Event"},
		//{&entities.Log{}, "Log"},
		//{&entities.FlowRelation{}, "FlowRelation"},
	} {
		if err := findRelation(modelToCheck.Model, modelToCheck.TableName); err != nil {
			return true, nil, err
		}
	}

	if len(foundMap) > 0 {
		return true, foundMap, nil
	}

	return false, nil, nil
}

func (r *repository) Details(id string, ctx context.Context, t trace.Tracer) (*dtos.AdminDetailDTO, error) {
	ctx = helpers.PushToTrace(t, ctx, "admin.repository.Details", "admin.repository.Details")

	var admin entities.Admin
	if state.AmIAuthorized(ctx) {
		if err := r.db.WithContext(ctx).Model(&entities.Admin{}).Where(query.BuildQuery(query.WhereID), id).First(&admin).Error; err != nil {
			return nil, err
		}
	} else {
		if err := r.db.WithContext(ctx).Model(&entities.Admin{}).Where(query.BuildQuery(query.WhereID, query.WhereOrganizationID), id, state.CurrentAdminOrganization(ctx)).First(&admin).Error; err != nil {
			return nil, err
		}
	}

	var organization entities.Organization
	r.db.WithContext(ctx).Model(&entities.Organization{}).Where(query.BuildQuery(query.WhereID), admin.OrganizationID).First(&organization)

	var role entities.Role
	r.db.WithContext(ctx).Model(&entities.Role{}).Where(query.BuildQuery(query.WhereID), admin.RoleID).First(&role)

	payload := &dtos.AdminDetailDTO{
		ID:            admin.ID,
		CreatedAt:     admin.CreatedAt.Format("2006-01-02 15:04:05"),
		Name:          admin.FirstName + " " + admin.LastName,
		Email:         admin.Email,
		Labels:        admin.Labels,
		Role:          role.Name,
		Organization:  organization.Name,
		Suspended:     ptr.BoolValue(admin.Suspended),
		SuspendReason: admin.SuspendReason,
	}

	var activities []entities.Activity
	r.db.WithContext(ctx).Model(&entities.Activity{}).Where(query.BuildQuery(query.WhereAdminID), admin.ID).Order(query.OrderByCreatedAtDesc).Limit(10).Find(&activities)

	for _, activity := range activities {
		item := dtos.AdminDetailActivityDTO{
			Date:      activity.CreatedAt.Format("2006-01-02 15:04:05"),
			Action:    activity.Action,
			Entity:    activity.Entity,
			IpAddress: activity.IpAddress,
		}

		payload.Activities = append(payload.Activities, item)
	}

	return payload, nil
}

func (r *repository) ForceLogout(id string, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "admin.repository.ForceLogout", "admin.repository.ForceLogout")

	if !state.AmIAuthorized(ctx) {
		return errors.New("you are not authorized to perform this action")
	}

	var admin entities.Admin
	if err := r.db.WithContext(ctx).Model(&entities.Admin{}).Where(query.BuildQuery(query.WhereID), id).First(&admin).Error; err != nil {
		return err
	}

	if state.CurrentAdminUser(ctx) == admin.ID {
		return errors.New("you can not force logout yourself")
	}

	redis.Del(hasher.UnreversibleHash(fmt.Sprintf("mono-admin-auth-token-%v", admin.ID)))

	nc := nat.NatConn()
	subject := fmt.Sprintf("event.admin.%s", admin.ID.String())

	msg := nats.Msg{
		Subject: subject,
		Header: nats.Header{
			"event.type": {
				"login.event",
			},
		},
	}

	if err := nc.PublishMsg(&msg); err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "NATS Publish Error",
			Message:        "Admin logout event publish failed error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Entity:         "auth",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        admin.ID,
			OrganizationID: admin.OrganizationID,
		})
	}

	return nil
}
