package statistics

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Repository interface {
	getSimulateStatistics(ctx context.Context, simulate_id string) (dtos.ResponseForSimulateStatistics, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) getSimulateStatistics(ctx context.Context, simulate_id string) (dtos.ResponseForSimulateStatistics, error) {
	var (
		simulate entities.Simulate
		resp     dtos.ResponseForSimulateStatistics
		result   struct {
			SuccessCount int64 `json:"success_count"`
			ErrorCount   int64 `json:"error_count"`
			HTTPCount    int64 `json:"http_count"`
			GRPCCount    int64 `json:"grpc_count"`
		}
	)

	if err := r.db.WithContext(ctx).
		Where(query.WhereID, simulate_id).
		Where(query.WhereOrganizationID, state.CurrentAdminOrganization(ctx)).
		Preload(clause.Associations).
		First(&simulate).Error; err != nil {
		return resp, err
	}

	resp.TotalNodeCount = len(simulate.Nodes)
	resp.TotalEdgeCount = len(simulate.Edges)

	r.db.Model(&entities.SimulateLog{}).
		Select(`
		SUM(CASE WHEN type = 'success' THEN 1 ELSE 0 END) AS success_count,
		SUM(CASE WHEN type = 'error' THEN 1 ELSE 0 END) AS error_count,
		SUM(CASE WHEN proto = 'grpc' THEN 1 ELSE 0 END) AS grpc_count,
		SUM(CASE WHEN proto = 'http' THEN 1 ELSE 0 END) AS http_count
		`).
		Where("simulate_id = ?", simulate.ID).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Scan(&result)

	resp.TotalSuccessCount = int(result.SuccessCount)
	resp.TotalErrorCount = int(result.ErrorCount)
	resp.TotalHTTPCount = int(result.HTTPCount)
	resp.TotalGRPCCount = int(result.GRPCCount)

	return resp, nil
}
