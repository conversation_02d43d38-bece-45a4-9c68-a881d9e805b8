package statistics

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/dtos"
)

type Service interface {
	GetSimulateStatistics(ctx context.Context, simulate_id string) (dtos.ResponseForSimulateStatistics, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GetSimulateStatistics(ctx context.Context, simulate_id string) (dtos.ResponseForSimulateStatistics, error) {
	return s.repository.getSimulateStatistics(ctx, simulate_id)
}
