package maintenance

import (
	"context"
	"errors"
	"log"

	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"github.com/parsguru/pars-vue/pkg/redis"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type Repository interface {
	Enable(ctx context.Context, t trace.Tracer) error
	Disable(ctx context.Context, t trace.Tracer) error
	GetStatus(ctx context.Context, t trace.Tracer) (bool, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo() Repository {
	return &repository{
		db: database.ClientDB(),
	}
}

func (r *repository) Enable(ctx context.Context, t trace.Tracer) error {
	if !state.AmIAuthorized(ctx) {
		return errors.New("you are not authorized to perform this action")
	}

	if err := redis.KeySet("mono_maintenance", "true", 0); err != nil {
		log.Println("error setting maintenance mode", err)
		return err
	}

	return nil
}

func (r *repository) Disable(ctx context.Context, t trace.Tracer) error {
	if !state.AmIAuthorized(ctx) {
		return errors.New("you are not authorized to perform this action")
	}

	if err := redis.KeySet("mono_maintenance", "false", 0); err != nil {
		return err
	}

	return nil

}

func (r *repository) GetStatus(ctx context.Context, t trace.Tracer) (bool, error) {

	val, err := redis.KeyGet("mono_maintenance")
	if err != nil {
		log.Println("error getting maintenance mode status", err)
		if err = redis.KeySet("mono_maintenance", "false", 0); err != nil {
			log.Println("error setting default maintenance mode", err)
			return false, err
		}
		return false, err
	}

	if val == "true" {
		return true, nil
	}

	return false, nil

}
