package maintenance

import (
	"context"

	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	Enable(ctx context.Context, t trace.Tracer) error
	Disable(ctx context.Context, t trace.Tracer) error
	GetStatus(ctx context.Context, t trace.Tracer) (bool, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Enable(ctx context.Context, t trace.Tracer) error {
	return s.repository.Enable(ctx, t)
}

func (s *service) Disable(ctx context.Context, t trace.Tracer) error {
	return s.repository.Disable(ctx, t)
}

func (s *service) GetStatus(ctx context.Context, t trace.Tracer) (bool, error) {
	return s.repository.GetStatus(ctx, t)
}
