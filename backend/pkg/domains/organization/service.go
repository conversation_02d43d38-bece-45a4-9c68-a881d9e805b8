package organization

import (
	"context"
	"errors"

	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
)

type Service interface {
	Get(ctx context.Context, t trace.Tracer, id string) (*entities.Organization, error)
	List(ctx context.Context, t trace.Tracer, page, perpage int, organization_id, name string) (*dtos.PaginatedData, error)
	Create(ctx context.Context, t trace.Tracer, req *dtos.OrganizationCreateDto) error
	Delete(ctx context.Context, t trace.Tracer, id string) error
	Update(id string, payload *dtos.OrganizationUpdateDto, ctx context.Context, t trace.Tracer) error
	Detail(id string, ctx context.Context, t trace.Tracer) (*dtos.OrganizationDetailDto, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) Get(ctx context.Context, t trace.Tracer, id string) (*entities.Organization, error) {
	ctx = helpers.PushToTrace(t, ctx, "organization.service.Get", "organization.service.Get")
	return s.repository.get(ctx, t, id)
}

func (s *service) Create(ctx context.Context, t trace.Tracer, req *dtos.OrganizationCreateDto) error {
	ctx = helpers.PushToTrace(t, ctx, "organization.service.Create", "organization.service.Create")
	return s.repository.create(ctx, t, req)
}

func (s *service) List(ctx context.Context, t trace.Tracer, page, perpage int, organization_id, name string) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "organization.service.List", "organization.service.List")
	return s.repository.list(ctx, t, page, perpage, organization_id, name)
}

func (s *service) Delete(ctx context.Context, t trace.Tracer, id string) error {
	ctx = helpers.PushToTrace(t, ctx, "organization.service.Delete", "organization.service.Delete")

	cannotBeDeleted, _, err := s.repository.checkRelationsExist(ctx, t, id)
	if err != nil {
		return err
	}

	if cannotBeDeleted {
		return errors.New("organization cannot delete")
	}

	return s.repository.delete(ctx, t, id)
}

func (s *service) Update(id string, payload *dtos.OrganizationUpdateDto, ctx context.Context, t trace.Tracer) error {
	ctx = helpers.PushToTrace(t, ctx, "organization.service.Update", "organization.service.Update")

	// flows needs entity itself not the update map, so we need to get it first
	org, err := s.repository.get(ctx, t, id)
	if err != nil {
		return err
	}

	if payload.Name != nil {
		if len(*payload.Name) < 2 {
			return errors.New("minimum 2 character")
		}
		org.Name = *payload.Name
	}

	if payload.ParentID != nil {
		parentId := helpers.ParseID(*payload.ParentID)

		orgIsSubOrg, err := s.repository.checkOrgIsSubOrg(ctx, t, id, parentId)
		if err != nil {
			return err
		}

		if orgIsSubOrg {
			return errors.New("organization_is_sub_org")
		}
		org.ParentID = parentId
	}

	if payload.Logo != nil {
		org.Logo = helpers.StringRtp(payload.Logo)
	}

	if payload.Favicon != nil {
		org.Favicon = helpers.StringRtp(payload.Favicon)
	}

	org.Audit = entities.Audit{
		UpdatedBy:   state.CurrentAdminUser(ctx),
		UpdatedDate: helpers.Time(),
	}

	return s.repository.update(ctx, t, id, org)
}

func (s *service) Detail(id string, ctx context.Context, t trace.Tracer) (*dtos.OrganizationDetailDto, error) {
	ctx = helpers.PushToTrace(t, ctx, "organization.service.Detail", "organization.service.Detail")

	return s.repository.detail(id, ctx, t)
}
