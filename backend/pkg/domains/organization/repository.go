package organization

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/internal/cache"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/eventer"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type Repository interface {
	get(ctx context.Context, t trace.Tracer, id string) (*entities.Organization, error)
	detail(id string, ctx context.Context, t trace.Tracer) (*dtos.OrganizationDetailDto, error)
	create(ctx context.Context, t trace.Tracer, req *dtos.OrganizationCreateDto) error
	list(ctx context.Context, t trace.Tracer, page, perpage int, organization_id, name string) (*dtos.PaginatedData, error)
	delete(ctx context.Context, t trace.Tracer, id string) error
	update(ctx context.Context, t trace.Tracer, id string, org *entities.Organization) error
	checkRelationsExist(ctx context.Context, t trace.Tracer, id string) (bool, map[string]error, error)
	checkOrgIsSubOrg(ctx context.Context, t trace.Tracer, orgID string, parentOrgID uuid.UUID) (bool, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) get(ctx context.Context, t trace.Tracer, id string) (*entities.Organization, error) {
	ctx = helpers.PushToTrace(t, ctx, "organization.repository.Update", "organization.repository.Update")

	var org entities.Organization
	if err := r.db.WithContext(ctx).
		Model(org).
		Where(query.BuildQuery(query.WhereID), id).
		First(&org).Error; err != nil {
		return nil, err
	}

	var currentAdminOrg entities.Organization
	if err := r.db.WithContext(ctx).
		Model(&entities.Organization{}).
		Where(query.BuildQuery(query.WhereID), state.CurrentAdminOrganization(ctx)).
		First(&currentAdminOrg).Error; err != nil {
		return nil, err
	}

	if state.AmIAuthorized(ctx) {
		return &org, nil
	} else {
		if state.CurrentAdminOrganization(ctx).String() == id || org.ParentID == state.CurrentAdminOrganization(ctx) {
			return &org, nil
		} else {
			return nil, fmt.Errorf("you are not authorized to get this organization")
		}
	}
}

func (r *repository) detail(id string, ctx context.Context, t trace.Tracer) (*dtos.OrganizationDetailDto, error) {
	ctx = helpers.PushToTrace(t, ctx, "organization.repository.Detail", "organization.repository.Detail")

	var org entities.Organization
	if err := r.db.WithContext(ctx).
		Model(&entities.Organization{}).
		Where(query.BuildQuery(query.WhereID), id).
		First(&org).Error; err != nil {
		return nil, err
	}

	var CountAdmins int64
	if err := r.db.WithContext(ctx).
		Model(&entities.Admin{}).
		Where(query.BuildQuery(query.WhereOrganizationID), org.ID).
		Count(&CountAdmins).Error; err != nil {
		return nil, err
	}

	var CountSubOrganizations int64
	if err := r.db.WithContext(ctx).Model(&entities.Organization{}).Where(query.BuildQuery(query.WhereParentID), org.ID).Count(&CountSubOrganizations).Error; err != nil {
		return nil, err
	}

	response := dtos.OrganizationDetailDto{
		ID:                    org.ID.String(),
		CreatedAt:             org.CreatedAt.Format("2006-01-02"),
		Name:                  org.Name,
		Logo:                  org.Logo,
		TotalAdmins:           CountAdmins,
		TotalSubOrganizations: CountSubOrganizations,
	}

	return &response, nil
}

func (r *repository) create(ctx context.Context, t trace.Tracer, req *dtos.OrganizationCreateDto) error {
	ctx = helpers.PushToTrace(t, ctx, "organization.repository.Create", "organization.repository.Create")

	tx := r.db.Begin()

	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	tenantID := r.uniqTenantID()

	org := entities.Organization{
		Name:     req.Organization.Name,
		Logo:     req.Organization.Logo,
		TenantID: tenantID,
		Audit: entities.Audit{
			MakedBy:   state.CurrentAdminUser(ctx),
			MakedDate: helpers.Time(),
		},
	}

	if req.Organization.ParentID != "" {
		org.ParentID = helpers.ParseID(req.Organization.ParentID)
	} else {
		org.ParentID = uuid.Nil
	}

	if err := tx.WithContext(ctx).
		Model(&entities.Organization{}).
		Create(&org).Error; err != nil {
		tx.Rollback()
		return err
	}

	role := entities.Role{
		Name:           req.Role.Name,
		OrganizationID: org.ID,
	}

	if err := tx.WithContext(ctx).
		Model(&entities.Role{}).
		Create(&role).Error; err != nil {
		tx.Rollback()
		return err
	}

	for _, perm := range req.Role.Permissions {
		rolePermission := entities.Permission{
			RoleID: role.ID,
			Entity: perm.Entity,
			Read:   perm.Read,
			Create: perm.Create,
			Update: perm.Update,
			Delete: perm.Delete,
		}
		if err := tx.WithContext(ctx).
			Model(&entities.Permission{}).
			Create(&rolePermission).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	admin := entities.Admin{
		Name:           req.Admin.FirstName + " " + req.Admin.LastName,
		FirstName:      req.Admin.FirstName,
		LastName:       req.Admin.LastName,
		Email:          req.Admin.Email,
		OrganizationID: org.ID,
		RoleID:         role.ID,
		Locale:         req.Admin.Locale,
	}

	if err := tx.WithContext(ctx).
		Model(&entities.Admin{}).
		Create(&admin).Error; err != nil {
		tx.Rollback()
		if err == gorm.ErrDuplicatedKey {
			return errors.New("dublicate email")
		}
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	// clear cache list
	cacheListKey := fmt.Sprintf("cachekeys:organization:%s:list", state.CurrentAdminOrganization(ctx))
	cacheKeys, err := cache.SMembers(cacheListKey)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Organization Create Cache SMembers",
			Message:        "Error while getting cache keys with cache.SMembers: " + err.Error(),
			Entity:         "organization",
			Type:           "error",
			Proto:          "http",
			Ip:             state.CurrentIP(ctx),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		})
	}

	for _, cacheKey := range cacheKeys {
		if err := cache.Del(cacheKey); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization Create Cache Del",
				Message:        "Error while deleting cache with cache.Del: " + err.Error(),
				Entity:         "organization",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}
	}

	eventer.PushEvent("organization.create", org.ID.String())

	return nil
}

func (r *repository) list(ctx context.Context, t trace.Tracer, page, perpage int, organization_id, name string) (*dtos.PaginatedData, error) {
	ctx = helpers.PushToTrace(t, ctx, "organization.repository.List", "organization.repository.List")

	var count int64
	var orgs []entities.Organization
	var list []dtos.OrganizationDto

	condition := query.QueryBuilder{
		Keys: []query.QueryKey{
			{
				Key:    query.WhereIDOrParentID,
				Values: []any{state.CurrentAdminOrganization(ctx), state.CurrentAdminOrganization(ctx)},
				Skip:   state.AmIAuthorized(ctx),
			},
			{
				Key:    query.WhereID,
				Values: []any{organization_id},
				Skip:   organization_id == "",
			},
			{
				Key:    query.WhereNameILike,
				Values: []any{fmt.Sprintf("%%%s%%", name)},
				Skip:   name == "",
			},
		},
	}

	sql, sqldata := condition.GetQueriesWithValues()
	r.db.WithContext(ctx).Model(&entities.Organization{}).Where(sql, sqldata...).Count(&count)

	cacheListKey := fmt.Sprintf("cachekeys:organization:%s:list", state.CurrentAdminOrganization(ctx))
	cacheKey := fmt.Sprintf("organization:%s:list:query:%s:querydata:%d:page:%d:perpage:%d", state.CurrentAdminOrganization(ctx), sql, sqldata, page, perpage)

	data, err := cache.Get(cacheKey)

	if err != nil {
		if err := r.db.WithContext(ctx).Limit(perpage).Offset((page-1)*perpage).Where(sql, sqldata...).Order(query.OrderByCreatedAtDesc).Find(&orgs).Error; err != nil {
			return nil, err
		}

		for _, org := range orgs {
			item := dtos.OrganizationDto{
				ID:        org.ID.String(),
				HashID:    org.HashID,
				Name:      org.Name,
				Labels:    org.Labels,
				CreatedAt: org.CreatedAt.Format("2006-01-02 15:04:05"),
				UpdatedAt: org.UpdatedAt.Format("2006-01-02 15:04:05"),
			}

			if org.ParentID != uuid.Nil {
				var parent entities.Organization
				r.db.WithContext(ctx).Where(query.BuildQuery(query.WhereID), org.ParentID).First(&parent)
				item.Parent = parent.Name
			} else {
				item.Parent = "-"
			}
			list = append(list, item)
		}

		jsonData, err := json.Marshal(list)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization List Cache Marshal Error",
				Message:        "Error : Organization List Cache Marshal Error : " + err.Error(),
				Entity:         "organization",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}

		if err := cache.Set(cacheKey, string(jsonData), 5*time.Minute); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization List Cache Set",
				Message:        "Error while setting cache with cache.Set: " + err.Error(),
				Entity:         "organization",
				Type:           "error",
				Ip:             state.CurrentIP(ctx),
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}

		if err := cache.SAdd(cacheListKey, cacheKey); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization List Cache SAdd",
				Message:        "Error while adding cache with cache.SAdd: " + err.Error(),
				Entity:         "organization",
				Type:           "error",
				Ip:             state.CurrentIP(ctx),
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}

	} else {
		if err := json.Unmarshal([]byte(data), &list); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization List Cache Unmarshal",
				Message:        "Error while unmarshalling cache with json.Unmarshal: " + err.Error(),
				Entity:         "organization",
				Type:           "error",
				Ip:             state.CurrentIP(ctx),
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}
	}

	totalPages := int(math.Ceil(float64(count) / float64(perpage)))

	paginatedData := &dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: totalPages,
		Rows:       list,
	}
	return paginatedData, nil
}

func (r *repository) delete(ctx context.Context, t trace.Tracer, id string) error {
	ctx = helpers.PushToTrace(t, ctx, "organization.repository.Delete", "organization.repository.Delete")
	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}
	log.Println("repo delete 1")
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	if err := tx.WithContext(ctx).
		Model(&entities.Organization{}).
		Where(query.BuildQuery(query.WhereID), id).
		Delete(&entities.Organization{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	cacheListKey := fmt.Sprintf("cachekeys:organization:%s:list", state.CurrentAdminOrganization(ctx))
	cacheKeys, err := cache.SMembers(cacheListKey)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Organization Delete Cache SMembers",
			Message:        "Error while getting cache keys with cache.SMembers: " + err.Error(),
			Entity:         "organization",
			Type:           "error",
			Proto:          "http",
			Ip:             state.CurrentIP(ctx),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		})
	}

	for _, cacheKey := range cacheKeys {
		if err := cache.Del(cacheKey); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization Delete Cache Del",
				Message:        "Error while deleting cache with cache.Del: " + err.Error(),
				Entity:         "organization",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}
	}

	return nil
}

func (r *repository) update(ctx context.Context, t trace.Tracer, id string, org *entities.Organization) error {
	ctx = helpers.PushToTrace(t, ctx, "organization.repository.Update", "organization.repository.Update")
	tx := r.db.Begin()
	if err := tx.Error; err != nil {
		tx.Rollback()
		return err
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}

		if err := tx.Commit().Error; err != nil {
			log.Println("error while committing transaction", err)
			tx.Rollback()
		}
	}()

	organizationOld := entities.Organization{}
	r.db.WithContext(ctx).Model(&entities.Organization{}).Where(query.BuildQuery(query.WhereID), id).First(&organizationOld)

	if err := tx.WithContext(ctx).
		Model(&entities.Organization{}).
		Where(query.BuildQuery(query.WhereID), id).
		Updates(map[string]interface{}{
			"name":            org.Name,
			"parent_id":       org.ParentID,
			"logo":            org.Logo,
			"labels":          org.Labels,
			"checkout_domain": org.CheckoutDomain,
			"domain_address":  org.DomainAddress,
			"timezone":        org.Timezone,
			"favicon":         org.Favicon,
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	eventer.PushEvent("organization.update", id)

	monolog.CreateLog(&entities.Log{
		Title:          "Organization Update",
		Message:        "Organization updated successfully",
		Entity:         "organization",
		Type:           "info",
		Proto:          "http",
		EntityID:       org.ID,
		Ip:             state.CurrentIP(ctx),
		AdminID:        state.CurrentAdminUser(ctx),
		OrganizationID: state.CurrentAdminOrganization(ctx),
	})

	cacheListKey := fmt.Sprintf("cachekeys:organization:%s:list", state.CurrentAdminOrganization(ctx))
	cacheKeys, err := cache.SMembers(cacheListKey)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "Organization Update Cache SMembers",
			Message:        "Error while getting cache keys with cache.SMembers: " + err.Error(),
			Entity:         "organization",
			Type:           "error",
			Proto:          "http",
			Ip:             state.CurrentIP(ctx),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		})
	}

	for _, cacheKey := range cacheKeys {
		if err := cache.Del(cacheKey); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization Update Cache Del",
				Message:        "Error while deleting cache with cache.Del: " + err.Error(),
				Entity:         "organization",
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(ctx),
				AdminID:        state.CurrentAdminUser(ctx),
				OrganizationID: state.CurrentAdminOrganization(ctx),
			})
		}
	}

	return nil
}

func (r *repository) uniqTenantID() uint64 {
	tenantID, _ := utils.GenerateRandomUint64(8)
	var exitstingOrg int64
	r.db.Model(&entities.Organization{}).Where("tenant_id = ?", tenantID).Count(&exitstingOrg)
	if exitstingOrg > 0 {
		r.uniqTenantID()
	}
	return tenantID
}

func (r *repository) checkRelationsExist(ctx context.Context, t trace.Tracer, id string) (bool, map[string]error, error) {
	foundMap := make(map[string]error)

	findRelation := func(model interface{}, tableName string) error {
		err := r.db.WithContext(ctx).Model(model).Where(query.BuildQuery(query.WhereOrganizationID), id).First(model).Error
		if err == nil {
			foundMap[tableName] = errors.New(tableName + " found")
			return nil
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		return nil
	}

	// TODO: Check sub-organizations?

	for _, modelToCheck := range []struct {
		Model     interface{}
		TableName string
	}{
		{&entities.Admin{}, "admins"},
		{&entities.EntityBlob{}, "entity_blobs"},
		// {&entities.Role{}, "roles"},
		{&entities.Token{}, "tokens"},
	} {
		if err := findRelation(modelToCheck.Model, modelToCheck.TableName); err != nil {
			return true, nil, err
		}
	}

	if len(foundMap) > 0 {
		return true, foundMap, nil
	}

	return false, nil, nil
}

func (r *repository) checkOrgIsSubOrg(ctx context.Context, t trace.Tracer, orgID string, parentOrgID uuid.UUID) (bool, error) {
	checked := make(map[uuid.UUID]bool)

	for parentOrgID != uuid.Nil {
		if checked[parentOrgID] { // Check for circular references
			return false, errors.New("error when checking if the organization is a sub-organization")
		}
		checked[parentOrgID] = true

		var org entities.Organization
		if err := r.db.WithContext(ctx).
			Model(&entities.Organization{}).
			Where(query.BuildQuery(query.WhereID), parentOrgID).
			Find(&org).Error; err != nil {
			return false, err
		}

		if org.ID.String() == orgID {
			return true, nil
		}

		parentOrgID = org.ParentID
	}

	return false, nil
}
