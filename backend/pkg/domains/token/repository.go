package token

import (
	"context"
	"math"
	"time"

	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
)

type Repository interface {
	generateToken(ctx context.Context, req dtos.RequestForGenerateAppToken) (string, error)
	deleteToken(ctx context.Context, id string) error
	getAllTokens(ctx context.Context, page, perpage int) (dtos.PaginatedData, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) generateToken(ctx context.Context, req dtos.RequestForGenerateAppToken) (string, error) {
	var new_application_token entities.TokenForApplication

	var converted_exp int
	if converted_exp < 1 {
		converted_exp = 30
	} else if converted_exp > 9999 {
		converted_exp = 9999
	} else {
		converted_exp = req.ExpireDay
	}

	new_application_token.Name = req.Name
	new_application_token.OrganizationID = state.CurrentAdminOrganization(ctx)
	new_application_token.AdminID = state.CurrentAdminUser(ctx)
	new_application_token.ExpireAt = time.Now().Add(time.Hour * 24 * time.Duration(converted_exp))
	//new_application_token.Application = req.Application

	// if len(req.Sections) == 0 {
	// 	return "", fmt.Errorf("sections can not be empty")
	// }

	token, err := generateToken(ctx, converted_exp)
	if err != nil {
		return "", err
	}
	new_application_token.Token = token

	tx := r.db.Begin()

	if err := tx.WithContext(ctx).
		Create(&new_application_token).Error; err != nil {
		tx.Rollback()
		return "", err
	}

	// for _, v := range req.Sections {
	// 	var application_token_authorizations entities.ApplicationTokenAuthorization
	// 	application_token_authorizations.ApplicationTokenID = new_application_token.ID
	// 	application_token_authorizations.Section = v
	// 	application_token_authorizations.OrganizationID = state.CurrentAdminOrganization(ctx)

	// 	if err := r.db.WithContext(ctx).
	// 		Create(&application_token_authorizations).Error; err != nil {
	// 		tx.Rollback()
	// 		return "", err
	// 	}
	// }

	tx.Commit()

	return "", nil
}

func (r *repository) deleteToken(ctx context.Context, id string) error {
	var current_application_token entities.TokenForApplication

	tx := r.db.Begin()
	defer tx.Rollback()

	if err := tx.WithContext(ctx).
		Where("id = ? AND organization_id = ?", id, state.CurrentAdminOrganization(ctx)).
		First(&current_application_token).Error; err != nil {
		return err
	}

	if err := tx.WithContext(ctx).
		Delete(&current_application_token).Error; err != nil {
		return err
	}

	if err := tx.WithContext(ctx).
		Where("application_token_id = ? AND organization_id = ?", id, state.CurrentAdminOrganization(ctx)).
		Delete(&entities.ApplicationTokenAuthorization{}).Error; err != nil {
		return err
	}

	tx.Commit()

	return nil
}

func (r *repository) getAllTokens(ctx context.Context, page, perpage int) (dtos.PaginatedData, error) {
	var (
		tokens []entities.TokenForApplication
		resp   []dtos.ResponseForGetAllAppToken
		count  int64
	)

	if err := r.db.WithContext(ctx).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Order("created_at desc").
		Find(&tokens).Error; err != nil {
		return dtos.PaginatedData{}, err
	}

	if err := r.db.WithContext(ctx).
		Model(&entities.TokenForApplication{}).
		Where("organization_id = ?", state.CurrentAdminOrganization(ctx)).
		Count(&count).Error; err != nil {
		return dtos.PaginatedData{}, err
	}

	for _, token := range tokens {
		var current_admin entities.Admin
		r.db.WithContext(ctx).
			Model(&entities.Admin{}).
			Where("id=?", token.AdminID.String()).
			First(&current_admin)

		resp = append(resp, dtos.ResponseForGetAllAppToken{
			ID:         token.ID.String(),
			Name:       token.Name,
			Created_at: token.CreatedAt.Format("2006-01-02 15:04:05"),
			Created_by: current_admin.Name,
			ExpireAt:   token.ExpireAt.Format("2006-01-02 15:04:05"),
			Token:      token.Token,
		})
	}

	return dtos.PaginatedData{
		Page:       int64(page),
		PerPage:    int64(perpage),
		Total:      count,
		TotalPages: int(math.Ceil(float64(count) / float64(perpage))),
		Rows:       resp,
	}, nil
}
