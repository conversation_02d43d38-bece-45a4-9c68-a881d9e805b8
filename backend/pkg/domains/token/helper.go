package token

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
)

func generateToken(ctx context.Context, expire_at int) (string, error) {
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().JwtSecret,
		Issuer:    config.ReadValue().JwtIssuer,
	}

	token, err := jwt.GenerateApplicationJWT(ctx, state.CurrentAdminOrganization(ctx).String(), expire_at)

	return token, err
}
