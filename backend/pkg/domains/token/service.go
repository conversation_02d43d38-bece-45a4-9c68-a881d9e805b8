package token

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/dtos"
)

type Service interface {
	GenerateToken(ctx context.Context, req dtos.RequestForGenerateAppToken) (string, error)
	DeleteToken(ctx context.Context, id string) error
	GetAllTokens(ctx context.Context, page int, perpage int) (dtos.PaginatedData, error)
}

type service struct {
	repository Repository
}

func NewService(r Repository) Service {
	return &service{
		repository: r,
	}
}

func (s *service) GenerateToken(ctx context.Context, req dtos.RequestForGenerateAppToken) (string, error) {
	return s.repository.generateToken(ctx, req)
}

func (s *service) DeleteToken(ctx context.Context, id string) error {
	return s.repository.deleteToken(ctx, id)
}

func (s *service) GetAllTokens(ctx context.Context, page int, perpage int) (dtos.PaginatedData, error) {
	return s.repository.getAllTokens(ctx, page, perpage)
}
