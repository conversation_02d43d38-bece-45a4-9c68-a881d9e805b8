package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"time"

	"github.com/parsguru/pars-vue/pkg/hasher"
	"github.com/redis/go-redis/v9"
)

var client *redis.Client

func InitRedis(host, port, password string, db int) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     net.JoinHostPort(host, port),
		Password: password,
		DB:       db,
	})
	client = rdb
}

func RedisClient() *redis.Client {
	return client
}

func AdminUserSessionCheck(id string, token string) bool {
	val, err := client.Get(context.Background(), hasher.UnreversibleHash(fmt.Sprintf("pars-admin-auth-token-%v", id))).Result()
	if err != nil {
		return false
	}
	return val == token
}

func UserSessionCheck(id string, token string) bool {
	val, err := client.Get(context.Background(), hasher.UnreversibleHash(fmt.Sprintf("pars-user-auth-token-%v", id))).Result()
	if err != nil {
		return false
	}
	return val == token
}

func KeySet(key string, value string, expiration time.Duration) error {
	if err := client.Set(context.Background(), key, value, expiration).Err(); err != nil {
		return err
	}
	return nil
}

func KeyGet(key string) (string, error) {
	val, err := client.Get(context.Background(), key).Result()
	if err != nil {
		return "", err
	}
	return val, nil
}

func KeyDelete(key string) error {
	if err := client.Del(context.Background(), key).Err(); err != nil {
		return err
	}
	return nil
}

func KeyExists(key string) (bool, error) {
	val, err := client.Exists(context.Background(), key).Result()
	if err != nil {
		return false, err
	}
	return val == 1, nil
}

func Set(key string, value interface{}, expire time.Duration) error {
	return client.Set(context.Background(), key, value, expire).Err()
}

func SetJson(key string, value interface{}, expire time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return client.Set(context.Background(), key, data, expire).Err()
}

func GetJson(key string, value interface{}) error {
	data, err := client.Get(context.Background(), key).Bytes()
	if err != nil {
		return err
	}
	return json.Unmarshal(data, value)
}

func Get(key string) (string, error) {
	return client.Get(context.Background(), key).Result()
}

func Del(key string) error {
	return client.Del(context.Background(), key).Err()
}

func Exists(key string) (bool, error) {
	val, err := client.Exists(context.Background(), key).Result()
	if err != nil {
		return false, err
	}
	return val > 0, nil
}

func SAdd(key string, value interface{}) error {
	return client.SAdd(context.Background(), key, value).Err()
}

func SMembers(key string) ([]string, error) {
	return client.SMembers(context.Background(), key).Result()
}

func SDelete(key string, value interface{}) error {
	return client.SRem(context.Background(), key, value).Err()
}

func Keys(prefix string) ([]string, error) {
	keys, err := client.Keys(context.Background(), prefix).Result()
	if err != nil {
		return nil, err
	}
	return keys, nil
}

func Increment(key string) (int64, error) {
	return client.Incr(context.Background(), key).Result()
}
