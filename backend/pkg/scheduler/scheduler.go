package scheduler

import (
	"encoding/json"
	"log"
	"time"

	"github.com/gorules/zen-go"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/domains/nodes"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func readTestFile(key string) ([]byte, error) {
	return []byte(key), nil
}

func StartScheduler(db *gorm.DB) {
	go func() {
		for {
			var (
				scheduled_work []entities.ScheduledWork
				now            = time.Now()
				current_hour   = now.Hour()
			)

			if err := db.Model(&entities.ScheduledWork{}).
				Where("start_date <= ? AND end_date >= ?", now, now).
				Where("DATE(last_run_date) != ?", now.Format("2006-01-02")).
				Where("EXTRACT(HOUR FROM each_x_time) = ?", current_hour).
				Find(&scheduled_work).Error; err != nil {
				log.Println("Error fetching scheduled jobs:", err)
			}

			for _, s := range scheduled_work {
				result, err := runIt(db, s.HowManyTimeToTry, s.ID.String())
				if err != nil {
					createLog(db, s.ID.String(), err.Error(), "")
				} else {
					createLog(db, s.ID.String(), "", string(result))
				}
			}

			time.Sleep(time.Minute)
		}
	}()
}

func runIt(db *gorm.DB, how_many_time_to_try int, scheduled_work_id string) (json.RawMessage, error) {
	var (
		scheduled_work entities.ScheduledWork
		result         json.RawMessage
	)

	if err := db.Model(&entities.ScheduledWork{}).
		Where("id = ?", scheduled_work_id).
		First(&scheduled_work).Error; err != nil {
		return result, err
	}

	var (
		m_node           []dtos.Node
		m_edge           []dtos.Edge
		current_simulate entities.Simulate
		content_map      = make(map[string]any)
	)

	if err := db.Model(&entities.Simulate{}).
		Where("id = ?", scheduled_work.SimulateID).
		Where(query.WhereOrganizationID, scheduled_work.OrganizationID).
		Preload(clause.Associations).
		First(&current_simulate).Error; err != nil {
		return result, err
	}

	for _, v := range current_simulate.Nodes {
		var s_node dtos.Node
		if v.Content != nil {
			s_node.Content = v.Content
		} else {
			s_node.Content = v.FunctionContent
		}
		s_node.ID = v.NodeID
		s_node.Name = v.Name
		s_node.Position = v.Position
		s_node.Type = v.Type

		m_node = append(m_node, s_node)
	}

	for _, v := range current_simulate.Edges {
		var s_edge dtos.Edge
		s_edge.ID = v.ID
		s_edge.SourceHandle = v.SourceHandle
		s_edge.SourceID = v.SourceID
		s_edge.TargetID = v.TargetID
		s_edge.Type = v.Type

		m_edge = append(m_edge, s_edge)
	}

	content_map["nodes"] = m_node
	content_map["edges"] = m_edge

	graph, err := json.Marshal(content_map)
	if err != nil {
		log.Println("Error fetching simulate:", err)
		return result, err
	}

	engine := zen.NewEngine(zen.EngineConfig{
		Loader:            readTestFile,
		CustomNodeHandler: nodes.CustomNodeHandler,
	})

	defer engine.Dispose()

	// for i := 0; i < how_many_time_to_try; i++ {

	// }

	zenOutput, err := engine.EvaluateWithOpts(
		string(graph),
		[]byte(scheduled_work.Request),
		zen.EvaluationOptions{Trace: true},
	)
	if err != nil {
		return result, err
	}

	result = zenOutput.Result

	return result, nil
}

func createLog(db *gorm.DB, scheduled_work_id, err, result string) error {
	var (
		new_simulate_log           entities.SimulateLog
		scheduled_work             entities.ScheduledWork
		current_scheduled_work_log entities.ScheduledWorkLog
	)

	tx := db.Begin()

	if err := tx.Model(&entities.ScheduledWork{}).
		Where("id = ?", scheduled_work_id).
		First(&scheduled_work).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err != "" {
		new_simulate_log.Type = "error"
		new_simulate_log.Message = err
	} else {
		new_simulate_log.Type = "success"
		new_simulate_log.Message = "Simulate is success"
	}

	new_simulate_log.SimulateID = scheduled_work.SimulateID
	new_simulate_log.OrganizationID = scheduled_work.OrganizationID
	new_simulate_log.AdminID = scheduled_work.AdminID
	new_simulate_log.Proto = "http"
	new_simulate_log.Request = scheduled_work.Request
	new_simulate_log.Response = result
	new_simulate_log.IsRetried = false
	new_simulate_log.IsScheduled = true
	new_simulate_log.ScheduledID = scheduled_work.ID
	new_simulate_log.XTry = 0

	if err := tx.Model(&entities.SimulateLog{}).
		Create(&new_simulate_log).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Model(&entities.ScheduledWorkLog{}).
		Where("scheduled_work_id = ?", scheduled_work.ID).
		First(&current_scheduled_work_log).Error; err != nil {
		tx.Rollback()
		return err
	}

	current_scheduled_work_log.TotalWork += 1
	if err != "" {
		current_scheduled_work_log.TotalError += 1
	} else {
		current_scheduled_work_log.TotalSuccess += 1
	}

	if err := tx.Model(&entities.ScheduledWorkLog{}).
		Where("id = ?", current_scheduled_work_log.ID).
		Updates(map[string]interface{}{
			"total_work":    current_scheduled_work_log.TotalWork,
			"total_error":   current_scheduled_work_log.TotalError,
			"total_success": current_scheduled_work_log.TotalSuccess,
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Model(&entities.ScheduledWork{}).
		Where("id = ?", scheduled_work.ID).
		Update("last_run_date", time.Now()).Error; err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()

	return nil
}
