package monolog

import (
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
)

type MonologData struct {
	Title    string `json:"title"`
	Message  string `json:"message"`
	Entity   string `json:"entity"`
	Type     string `json:"type"` // info, error
	Ip       string `json:"ip"`
	Proto    string `json:"proto"` // http, grpc
	EntityID string `json:"entity_id"`
	UserID   string `json:"user_id"`
	AdminID  string `json:"admin_id"`
}

func CreateLog(payload *entities.Log) {

	// payload.IsApi = helpers.BoolAddr(state.CurrentUserIsApiUser(ctx))
	db := database.ClientDB()
	db.Model(&entities.Log{}).Create(payload)

	// if payload.Type == "error" {
	// sentrySpan := sentry.StartSpan(ctx, payload.Title, sentry.TransactionName(payload.Title))
	// sentry.CurrentHub().ConfigureScope(func(scope *sentry.Scope) {
	// 	scope.SetTag("entity", payload.Entity)
	// 	scope.SetTag("type", payload.Type)
	// 	scope.SetExtra("payload", payload)
	// })
	// sentry.CurrentHub().CaptureException(fmt.Errorf("(%s) %s: %s", payload.Entity, payload.Title, payload.Message))
	// defer sentrySpan.Finish()
	// }

}

func (m *MonologData) PushToSentry() {
	// sentrySpan := sentry.StartSpan(context.Background(), m.Title, sentry.TransactionName(m.Title))
	// sentry.CurrentHub().ConfigureScope(func(scope *sentry.Scope) {
	// 	scope.SetTag("entity", m.Entity)
	// 	scope.SetTag("type", m.Type)
	// 	scope.SetExtra("payload", m)
	// })
	// sentry.CurrentHub().CaptureException(fmt.Errorf("(%s) %s: %s", m.Entity, m.Title, m.Message))
	// defer sentrySpan.Finish()
}
