package job

import (
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/mail"
	simplemail "github.com/xhit/go-simple-mail"
)

var (
	from   = config.ReadValue().Smtp.From
	sender = config.ReadValue().Smtp.Sender
	reply  = config.ReadValue().Smtp.Reply
)

func SendMail(to, subject, body string) error {
	smtpClient := mail.MailClient()
	defer smtpClient.Close()

	email := simplemail.NewMSG()
	email.SetFrom(from)
	email.AddTo(to)
	email.SetSubject(subject)
	email.SetSender(sender)
	email.SetReplyTo(reply)
	email.AddAlternative(simplemail.TextPlain, body)
	err := email.Send(smtpClient)
	if err != nil {
		return err
	}
	return nil
}
