package dtos

import (
	"github.com/google/uuid"
)

type RequestForAddDSN struct {
	Key      string `json:"key" example:"my-psql-dsn"`
	DSN      string `json:"dsn" example:"postgres://user:password@localhost:5432/dbname"`
	DBEngine int    `json:"db_engine" example:"2"` // 1: mysql, 2: postgres ...
}

type ResponseForDSN struct {
	ID                 uuid.UUID `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt          string    `json:"created_at" example:"2021-01-01T00:00:00Z"`
	Key                string    `json:"key" example:"my-psql-dsn"`
	DSN                string    `json:"dsn" example:"postgres://user:password@localhost:5432/dbname"`
	DBEngine           uint      `json:"db_engine" example:"2"`
	LastDSNCheckStatus uint      `json:"last_dsn_check_status" example:"1"`
	LastDSNCheckAt     string    `json:"last_dsn_check_at" example:"2021-01-01T00:00:00Z"`
}
