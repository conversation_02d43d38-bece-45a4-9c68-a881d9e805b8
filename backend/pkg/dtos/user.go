package dtos

import (
	"github.com/google/uuid"
)

type UserCreateDto struct {
	Email       string `json:"email" binding:"required" example:"<EMAIL>"`
	Name        string `json:"name" binding:"required" example:"<PERSON>"`
	AccountUUID string `json:"account_uuid" binding:"required" example:"f0d8f8c4-c8e0-4e5a-b8e7-2d07cbddda59"`
	RoleUUID    string `json:"role_uuid" binding:"required" example:"f0d8f8c4-c8e0-4e5a-b8e7-2d07cbddda59"`
}

type UserUpdateDto struct {
	Email       string `json:"email" binding:"required" example:"<EMAIL>"`
	Name        string `json:"name" binding:"required" example:"John Doe"`
	AccountUUID string `json:"account_uuid" binding:"required" example:"f0d8f8c4-c8e0-4e5a-b8e7-2d07cbddda59"`
	RoleUUID    string `json:"role_uuid" binding:"required" example:"f0d8f8c4-c8e0-4e5a-b8e7-2d07cbddda59"`
	Tfa         *bool  `json:"tfa"  example:"true"`
	TfaKey      string `json:"tfa_key,omitempty"  example:"123456"`
}
type UserTFAUpdateDto struct {
	Tfa    *bool  `json:"tfa" binding:"required" example:"true"`
	TfaKey string `json:"tfa_key,omitempty" binding:"required" example:"123456"`
}

type UserLoginDTO struct {
	Email string `json:"email" binding:"required" example:"<EMAIL>"`
}

type UserTfaLoginDTO struct {
	Email string `json:"email" binding:"required" example:"<EMAIL>"`
	Code  string `json:"code" binding:"required" example:"123456"`
}

type UserOTPRequest struct {
	Code string `json:"code" binding:"required" example:"123456"`
}

type CurrentUserDTO struct {
	UUID   uuid.UUID `json:"uuid"`
	Email  string    `json:"email"`
	Name   string    `json:"name"`
	TFA    bool      `json:"twofa"`
	TFAKey string    `json:"tfa_key"`
}

type UserTokenVerifyDTO struct {
	Token string `json:"token" binding:"required" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJFbWFpbCI6IiIsIklEIjoiIiwiT3JnYW5pe"`
}

type UserAuthResponseDTO struct {
	UUID   uuid.UUID   `json:"uuid"`
	Email  string      `json:"email"`
	Name   string      `json:"name"`
	Locale string      `json:"locale"`
	Tfa    *bool       `json:"tfa"`
	Role   UserRoleDto `json:"role"`
}

type UserRoleDto struct {
	Name        string                  `json:"name"`
	Permissions []UserRolePermissionDto `json:"permissions"`
}
type UserRolePermissionDto struct {
	Entity string `json:"entity"`
	Create bool   `json:"create"`
	Read   bool   `json:"read"`
	Update bool   `json:"update"`
	Delete bool   `json:"delete"`
}

type UserDTO struct {
	UUID         uuid.UUID `json:"uuid"`
	CreatedAt    string    `json:"created_at"`
	UpdatedAt    string    `json:"updated_at"`
	Email        string    `json:"email"`
	Name         string    `json:"name"`
	Organization string    `json:"organization"`
	Account      string    `json:"account"`
	Role         string    `json:"role"`
}

type UserLocaleDTO struct {
	Locale string `json:"locale" binding:"required" example:"en"`
}
