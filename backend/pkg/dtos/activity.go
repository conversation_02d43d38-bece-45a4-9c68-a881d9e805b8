package dtos

import "github.com/google/uuid"

type ActivityCreateDTO struct {
	Action         string    `json:"action"`
	Entity         string    `json:"entity"`
	IpAddress      string    `json:"ip_address"`
	AdminID        uuid.UUID `json:"admin_id"`
	OrganizationID uuid.UUID `json:"organization_id"`
} // ActivityCreateDTO

type ActivityDto struct {
	Action    string `json:"action"`
	Entity    string `json:"entity"`
	CreatedAt string `json:"created_at"`
	IpAddress string `json:"ip_address"`
} // ActivityDto
