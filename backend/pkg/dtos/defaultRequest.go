package dtos

import "github.com/google/uuid"

type RequestForDefaultRequest struct {
	Value          string `json:"value" example:"{\"key\":\"value\"}"`
	SimulateID     string `json:"simulate_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID string `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Type           uint   `json:"type" example:"1"`
}

type RequestForGetDefaultRequest struct {
	SimulateID string `json:"simulate_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Type       uint   `json:"type" example:"1"`
}

type DefaultRequestForSwagger struct {
	ID             uuid.UUID `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Value          string    `json:"value" example:"{\"key\":\"value\"}"`
	SimulateID     uuid.UUID `json:"simulate_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID uuid.UUID `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Type           uint      `json:"type" example:"1"`
}

type ResponseForDefaultRequest struct {
	ID               string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	SimulateID       string `json:"simulate_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID   string `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AdminID          string `json:"admin_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationName string `json:"organization_name" example:"mono"`
	AdminName        string `json:"admin_name" example:"mono"`
	SimulateName     string `json:"simulate_name" example:"mono"`
	Value            string `json:"value" example:"{\"key\":\"value\"}"`
	CreatedAt        string `json:"created_at" example:"2021-01-01 00:00:00"`
}
