package dtos

import (
	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/entities"
)

type AdminCreateDto struct {
	Email          string `json:"email" validate:"required,email" example:"<EMAIL>"`
	FirstName      string `json:"first_name" validate:"required" example:"John"`
	LastName       string `json:"last_name" validate:"required" example:"Doe"`
	Password       string `json:"password" validate:"required" example:"password"`
	RoleID         string `json:"role_id" validate:"required,uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID string `json:"organization_id" validate:"required,uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
} // @name AdminCreateDto

type AdminUpdateDto struct {
	Email          string `json:"email" validate:"required,email" example:"<EMAIL>"`
	Password       string `json:"password" example:"password"`
	OrganizationID string `json:"organization_id" validate:"required,uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	FirstName      string `json:"first_name" validate:"required" example:"John"`
	LastName       string `json:"last_name" validate:"required" example:"Doe"`
	RoleID         string `json:"role_id" validate:"required,uuid" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Suspended      *bool  `json:"suspended" example:"true"`
	SuspendReason  string `json:"suspend_reason" example:"reason"`
} // @name AdminUpdateDto

type AdminTFAUpdateDto struct {
	Tfa    *bool  `json:"tfa" binding:"required" example:"true"`
	TfaKey string `json:"tfa_key,omitempty" binding:"required" example:"123456"`
} // @name AdminTFAUpdateDto

type AdminLoginDTO struct {
	Email string `json:"email" binding:"required" example:"<EMAIL>"`
} // @name AdminLoginDTO

type AdminTfaLoginDTO struct {
	Email string `json:"email" binding:"required" example:"<EMAIL>"`
	Code  string `json:"code" binding:"required" example:"123456"`
} // @name AdminTfaLoginDTO

type AdminOTPRequest struct {
	Code string `json:"code" binding:"required" example:"123456"`
} // @name AdminOTPRequest

type CurrentAdminDTO struct {
	ID     string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Email  string `json:"email" example:"<EMAIL>"`
	Name   string `json:"name" example:"John Doe"`
	TFA    bool   `json:"twofa" example:"true"`
	TFAKey string `json:"tfa_key" example:"123456"`
} // @name CurrentAdminDTO

type AdminTokenVerifyDTO struct {
	Token string `json:"token" binding:"required" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJFbWFpbCI6IiIsIklEIjoiIiwiT3JnYW5pe"`
} // @name AdminTokenVerifyDTO

// {"name":"Internal Testing","email":"<EMAIL>","role":{"name":"Admin","permissions":[{"entity":"organization","create":true,"read":true,"update":true,"delete":true},{"entity":"account","create":true,"read":true,"update":true,"delete":true},"entity":"transaction","create":true,"read":true,"update":true,"delete":true},{"entity":"user","create":true,"read":true,"update":true,"delete":true},{"entity":"role","create":true,"read":true,"update":true,"delete":true},{"entity":"file","create":true,"read":true,"update":true,"delete":true},{"entity":"flow","create":true,"read":true,"update":true,"delete":true},{"entity":"bank","create":true,"read":true,"update":true,"delete":true},{"entity":"currency","create":true,"read":true,"update":true,"delete":true},{"entity":"card","create":true,"read":true,"update":true,"delete":true},{"entity":"wallet","create":true,"read":true,"update":true,"delete":true},{"entity":"feature","create":true,"read":true,"update":true,"delete":true},{"entity":"credit","create":true,"read":true,"update":true,"delete":true},{"entity":"bin","create":true,"read":true,"update":true,"delete":true},{"entity":"mcc","create":true,"read":true,"update":true,"delete":true},{"entity":"action","create":true,"read":true,"update":true,"delete":true},{"entity":"bingroup","create":true,"read":true,"update":true,"delete":true},{"entity":"product","create":true,"read":true,"update":true,"delete":true},{"entity":"order","create":true,"read":true,"update":true,"delete":true},{"entity":"commission_group","create":true,"read":true,"update":true,"delete":true},{"entity":"channel","create":true,"read":true,"update":true,"delete":true},{"entity":"vpos","create":true,"read":true,"update":true,"delete":true},{"entity":"log","create":true,"read":true,"update":true,"delete":true},{"entity":"rule","create":true,"read":true,"update":true,"delete":true},{"entity":"department","create":true,"read":true,"update":true,"delete":true},{"entity":"event","create":true,"read":true,"update":true,"delete":true},{"entity":"admin","create":true,"read":true,"update":true,"delete":true},{"entity":"session","create":true,"read":true,"update":true,"delete":true},{"entity":"bank_account","create":true,"read":true,"update":true,"delete":true},{"entity":"acquirer_commission","create":true,"read":true,"update":true,"delete":true},{"entity":"acquirer_commission_group","create":true,"read":true,"update":true,"delete":true}]},"tfa":false,"organization":{"name":"Mono Acquiring Admin","logo":""},"department":"Admins","token":"token","expire":"2022-12-09 12:18:59 +0300 +03"}
type AdminAuthResponseDTO struct {
	ID           string               `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Email        string               `json:"email" example:"<EMAIL>"`
	Name         string               `json:"name" example:"John Doe"`
	Locale       string               `json:"locale" example:"en"`
	Tfa          bool                 `json:"tfa" example:"true"`
	PinLogin     bool                 `json:"pin_login" example:"true"`
	Role         AdminRoleDto         `json:"role"`
	Organization AdminOrganizationDto `json:"organization"`
	Expire       string               `json:"expire" example:"2022-12-09 12:18:59 +0300 +03"`
} //@name AdminAuthResponseDTO

type AdminOrganizationDto struct {
	Name string `json:"name" example:"Mono Acquiring Admin"`
	Logo string `json:"logo" example:"logoURL"`
} // @name AdminOrganizationDto

type AdminRoleDto struct {
	Name        string                   `json:"name" example:"Admin"`
	Permissions []AdminRolePermissionDto `json:"permissions"` // this is a list of permissions
} // @name AdminRoleDto

type AdminRolePermissionDto struct {
	Entity string `json:"entity" example:"order"`
	Create bool   `json:"create" example:"true"`
	Read   bool   `json:"read" example:"true"`
	Update bool   `json:"update" example:"true"`
	Delete bool   `json:"delete" example:"true"`
} // @name AdminRolePermissionDto

type AdminListDTO struct {
	ID           string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	HashID       string `json:"hash_id" example:"dvoıpawıfd2309jwckE'^RDF'w2"`
	CreatedAt    string `json:"created_at" example:"2020-12-09 12:18:59 +0300 +03"`
	UpdatedAt    string `json:"updated_at" example:"2020-12-09 12:18:59 +0300 +03"`
	Email        string `json:"email" example:"<EMAIL>"`
	FirstName    string `json:"first_name" example:"John"`
	LastName     string `json:"last_name" example:"Doe"`
	Name         string `json:"name" example:"John Doe"`
	Organization string `json:"organization" example:"Mono Acquiring Admin"`
	Role         string `json:"role" example:"Admin"`
} // @name AdminListDTO

type AdminLocaleDTO struct {
	Locale string `json:"locale" binding:"required" example:"en"`
} // @name AdminLocaleDTO

type AdminGetDTO struct {
	ID             string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	HashID         string `json:"hash_id" example:"dvoıpawıfd2309jwckE'^RDF'w2"`
	Name           string `json:"name" example:"John Doe"`
	Email          string `json:"email" example:"<EMAIL>"`
	Status         string `json:"status" example:"active"`
	Locale         string `json:"locale" example:"en"`
	Tfa            bool   `json:"tfa" example:"true"`
	TfaKey         string `json:"tfa_key" example:"123456"`
	RoleID         string `json:"role_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	DepartmentID   string `json:"department_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AccountID      string `json:"account_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID string `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
} // @name AdminGetDTO

type AdminPinLoginDTO struct {
	Email string `json:"email" example:"<EMAIL>"`
}

type AdminPinVerifyDTO struct {
	Pin string `json:"pin" example:"123456"`
}

type AuthV2IdentifyRequest struct {
	Email string `json:"email" example:"john@milton"`
} //@name AuthV2IdentifyRequest

type AuthV2AuthenticateRequest struct {
	Email    string `json:"email" example:"john@milton"`
	Password string `json:"password" example:"123456"`
	// TfaCode  string `json:"tfa_code" example:"123456"`
} //@name AuthV2AuthenticateRequest

type AuthV2AuthenticateResponse struct {
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"`
	Tfa   bool   `json:"tfa" example:"true"`
	Pin   bool   `json:"pin" example:"true"`
} //@ name AuthV2AuthenticateResponse

type AuthV2PinRequest struct {
	Email string `json:"email" example:"john@milton"`
	Pin   string `json:"pin" example:"123456"`
} //@name AuthV2PinRequest

type AuthV2PinResponse struct {
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"`
} //@name AuthV2PinResponse

type AuthV2TfaRequest struct {
	Email   string `json:"email" example:"john@milton"`
	TfaCode string `json:"tfa_code" example:"123456"`
} //@name AuthV2TfaRequest

type AuthV2TfaResponse struct {
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"`
} //@name AuthV2TfaResponse

type AuthV2ForgotPasswordRequest struct {
	Email string `json:"email" example:"john@milton"`
} //@name AuthV2ForgotPasswordRequest

type AuthV2ResetPasswordRequest struct {
	Code                 string `json:"code" example:"123456"`
	Password             string `json:"password" example:"123456"`
	PasswordConfirmation string `json:"password_confirmation" example:"123456"`
} //@name AuthV2ResetPasswordRequest

type AuthV2VerifyAuthRequest struct {
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"`
} //@name AuthV2VerifyAuthRequest

type AuthV2VerifyAuthResponse struct {
	User   *AdminAuthResponseDTO `json:"user"`
	Expire string                `json:"expire" example:"2022-12-09 12:18:59 +0300 +03"`
} //@name AuthV2VerifyAuthResponse

type AdminExportDto struct {
	OrganizationID string `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type AdminImportDto struct {
	Admins         []entities.Admin `json:"admins"`
	OrganizationID string           `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type AdminDetailDTO struct {
	ID            uuid.UUID                `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt     string                   `json:"created_at" example:"2020-12-09 12:18:59 +0300 +03"`
	Name          string                   `json:"name" example:"John Doe"`
	Email         string                   `json:"email" example:"<EMAIL>"`
	Role          string                   `json:"role" example:"Admin"`
	Organization  string                   `json:"organization" example:"Mono Acquiring Admin"`
	Department    string                   `json:"department" example:"Admins"`
	Account       string                   `json:"account" example:"John Doe"`
	Labels        []string                 `json:"labels" example:"label,label2"`
	Suspended     bool                     `json:"suspended" example:"true"`
	SuspendReason string                   `json:"suspend_reason" example:"reason"`
	Activities    []AdminDetailActivityDTO `json:"activities"`
} // @name AdminDetailDTO

type AdminDetailActivityDTO struct {
	Date      string `json:"date" example:"2020-12-09 12:18:59 +0300 +03"`
	Action    string `json:"action" example:"create"`
	Entity    string `json:"entity" example:"bank"`
	IpAddress string `json:"ip_address" example:"127.0.0.1"`
}

type AdminInpersonateDTO struct {
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"`
	Name  string `json:"name" example:"John Doe"`
}
