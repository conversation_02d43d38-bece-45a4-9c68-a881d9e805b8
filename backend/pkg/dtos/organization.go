package dtos

import (
	"time"

	"github.com/parsguru/pars-vue/pkg/entities"
)

type OrganizationCreateDto struct {
	Organization struct {
		Name     string `json:"name" example:"MonoPayments"`
		ParentID string `json:"parent_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
		Logo     string `json:"logo" example:"https://monopayments.com/logo.png"`
	} `json:"organization"`
	Admin struct {
		Names     string `json:"name" example:"<PERSON>"`
		FirstName string `json:"first_name" example:"<PERSON>"`
		LastName  string `json:"last_name" example:"Doe"`
		Email     string `json:"email" example:"<EMAIL>"`
		Locale    string `json:"locale" example:"en"`
	} `json:"admin"`
	Role struct {
		Name        string                `json:"name" example:"superadmin"`
		Permissions []entities.Permission `json:"permissions"  swaggertype:"array,string" example:"permission1,permission2"`
	} `json:"role"`
} // @name OrganizationCreateDto

type OrganizationUpdateDto struct {
	Name          *string `json:"name" example:"MonoPayments"`
	ParentID      *string `json:"parent_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Logo          *string `json:"logo" example:"https://monopayments.com/logo.png"`
	Favicon       *string `json:"favicon" example:"https://monopayments.com/favicon.ico"`
	Timezone      *string `json:"timezone" example:"Etc/UTC"`
	DomainAddress *string `json:"domain_address" example:"monopayments.com"`
} // @name OrganizationUpdateDto

type OrganizationDto struct {
	ID        string   `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name      string   `json:"name" example:"MonoPayments"`
	HashID    string   `json:"hash_id" example:"kKE4ZvqV7Pl5d8eA8RD8xyGJz2oWNw"`
	Labels    []string `json:"labels" example:"monopayments"`
	Parent    string   `json:"parent" example:"MonoPayments Parent"`
	CreatedAt string   `json:"created_at" example:"2022-01-01T00:00:00Z"`
	UpdatedAt string   `json:"updated_at" example:"2022-01-01T00:00:00Z"`
} // @name OrganizationDto

type OrganizationDetailDto struct {
	ID                    string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt             string `json:"created_at" example:"2022-01-01T00:00:00Z"`
	Name                  string `json:"name" example:"MonoPayments"`
	Logo                  string `json:"logo" example:"https://monopayments.com/logo.png"`
	TotalAdmins           int64  `json:"total_admins" example:"1"`
	TotalSubOrganizations int64  `json:"total_sub_organizations" example:"1"`
} // @name OrganizationDetailDto

type Organization struct {
	ID            string    `json:"id" gorm:"primary_key"`
	CreatedAt     time.Time `json:"created_at"`
	Name          string    `json:"name"`
	SimulateCount int       `json:"simulate_count"`
	//Users         []User    `json:"users"`
}

type ResponseForOrganizations struct {
	ID        string `json:"id"`
	CreatedAt string `json:"created_at"`
	Name      string `json:"name"`
	Main      bool   `json:"main"`
	Current   bool   `json:"current"`
}

type ResponseForOrganizationDetail struct {
	ID                     string `json:"id"`
	CreatedAt              string `json:"created_at"`
	Name                   string `json:"name"`
	ParentOrganizationID   string `json:"parent_organization_id"`
	ParentOrganizationName string `json:"parent_organization_name"`
	Main                   bool   `json:"main"`
	TotalSimulateCount     int64  `json:"total_simulate_count"`
	TotalVersionCount      int64  `json:"total_version_count"`
	TotalEvaluateCount     int64  `json:"total_evaluate_count"`
	TotalUserCount         int64  `json:"total_user_count"`
	TotalSuccessCount      int64  `json:"total_success_count"`
	TotalErrorCount        int64  `json:"total_error_count"`
}
type OrganizationChangeName struct {
	ID   string `json:"id" binding:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name string `json:"name" binding:"required" example:"mono payments"`
}
type AddUserToOrganization struct {
	UserID         string `json:"user_id" binding:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID string `json:"organization_id" binding:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}
type RemoveUserFromOrganization struct {
	UserID         string `json:"user_id" binding:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID string `json:"organization_id" binding:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}
type CreateOrganization struct {
	Name                 string `json:"name" binding:"required" example:"mono payments"`
	ParentOrganizationID string `json:"parent_organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type RequestForUpdateOrganization struct {
	ID   string `json:"id" binding:"required" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name string `json:"name" binding:"required" example:"mono payments edit"`
}
