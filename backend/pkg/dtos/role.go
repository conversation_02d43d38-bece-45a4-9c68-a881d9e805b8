package dtos

import (
	"github.com/lib/pq"
	"github.com/parsguru/pars-vue/pkg/entities"
)

type RoleListDto struct {
	ID           string         `json:"id" example:"5f1d7b1b-9f78-4d7b-9248-d2f933d5e9e7"`
	HashID       string         `json:"hash_id" example:"4nWK9/iP/0IywDmnQ7GF9jKBkZ/5OsXKSRmDnbRHL40LSdwz"`
	Name         string         `json:"name" example:"mono-acquiring-admin"`
	Organization string         `json:"organization" example:"mono-acquiring-admin"`
	Labels       pq.StringArray `json:"labels" swaggertype:"array,string" example:"label,label2"`
} // @name RoleListDto

type RoleCreateDto struct {
	Name           string              `json:"name" validate:"required" example:"superadmin"`
	OrganizationID string              `json:"organization_id" validate:"required" example:"5f1d7b1b-9f78-4d7b-9248-d2f933d5e9e7"`
	Permissions    []RolePermissionDto `json:"permissions"`
} // @name RoleCreateDto

type RoleDto struct {
	ID             string              `json:"id" example:"5f1d7b1b-9f78-4d7b-9248-d2f933d5e9e7"`
	OrganizationID string              `json:"organization_id" example:"5f1d7b1b-9f78-4d7b-9248-d2f933d5e9e7"`
	HashID         string              `json:"hash_id" example:"4nWK9/iP/0IywDmnQ7GF9jKBkZ/5OsXKSRmDnbRHL40LSdwz"`
	Name           string              `json:"name" example:"superadmin"`
	Permissions    []RolePermissionDto `json:"permissions"`
	Labels         pq.StringArray      `json:"labels" swaggertype:"array,string" example:"label,label2"`
} // @name RoleDto
type RolePermissionDto struct {
	Entity string `json:"entity" example:"user"`
	Create bool   `json:"create" example:"true"`
	Read   bool   `json:"read" example:"true"`
	Update bool   `json:"update" example:"true"`
	Delete bool   `json:"delete" example:"true"`
} // @name RolePermissionDto

type RoleUpdateDto struct {
	Name           string `json:"name" validate:"required" example:"superadmin"`
	OrganizationID string `json:"organization_id" validate:"required" example:"5f1d7b1b-9f78-4d7b-9248-d2f933d5e9e7"`

	Permissions []RolePermissionDto `json:"permissions"`
	Labels      pq.StringArray      `json:"labels" swaggertype:"array,string" example:"label,label2"`
} // @name RoleUpdateDto

type RoleFlowDTO struct {
	Name        string              `json:"name" example:"superadmin"`
	Permissions []RolePermissionDto `json:"permissions"`
	Labels      pq.StringArray      `json:"labels" swaggertype:"array,string" example:"label,label2"`
} // @name RoleFlowDTO

type RoleExportDto struct {
	OrganizationID string `json:"organization_id" example:"5f1d7b1b-9f78-4d7b-9248-d2f933d5e9e7"`
}

type RoleImportDto struct {
	OrganizationID string                `json:"organization_id" example:"5f1d7b1b-9f78-4d7b-9248-d2f933d5e9e7"`
	Roles          []entities.Role       `json:"roles"`
	Permissions    []entities.Permission `json:"permissions"`
}
