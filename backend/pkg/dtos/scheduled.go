package dtos

type RequestForCreateScheduledWork struct {
	NameOfWork       string `json:"name_of_work"`
	EachXTime        string `json:"each_x_time"`
	Request          string `json:"request"`
	StartDate        string `json:"start_date"`
	EndDate          string `json:"end_date"`
	HowManyTimeToTry int    `json:"how_many_time_to_try"`
	SimulateID       string `json:"simulate_id"`
}

type ResponseForScheduledWork struct {
	ID               string `json:"id"`
	CreatedAt        string `json:"created_at"`
	SimulateID       string `json:"simulate_id"`
	OrganizationID   string `json:"organization_id"`
	AdminID          string `json:"admin_id"`
	NameOfWork       string `json:"name_of_work"`
	EachXTime        string `json:"each_x_time"`
	Request          string `json:"request"`
	StartDate        string `json:"start_date"`
	EndDate          string `json:"end_date"`
	HowManyTimeToTry int    `json:"how_many_time_to_try"`
	TotalWork        int    `json:"total_work"`
	TotalSuccess     int    `json:"total_success"`
	TotalError       int    `json:"total_error"`
}
