package dtos

type Store interface {
}

type StoreConditionRequest struct {
	Db       string `json:"db"`
	Table    string `json:"table"`
	Kv       []Kv   `json:"kv"`
	Limit    int64  `json:"limit"`
	Offset   int64  `json:"offset"`
	OrderBy  string `json:"order_by"`
	GroupBy  string `json:"group_by"`
	RawQuery string `json:"raw_query"`
}

type Kv struct {
	K string `json:"k"`
	V string `json:"v"`
}

type StoreResult struct {
	Success      bool       `json:"success"`
	Message      string     `json:"message"`
	AffectedRows int64      `json:"affected_rows"`
	StoreRows    []StoreRow `json:"store_rows"`
}

type StoreRow struct {
	Kv []Kv `json:"kv"`
}

type StoreInsertRequest struct {
	Db    string `json:"db"`
	Table string `json:"table"`
	Kv    []Kv   `json:"kv"`
}

type StoreUpdateRequest struct {
	Db    string `json:"db"`
	Table string `json:"table"`
	Kv    []Kv   `json:"kv"`
}

type StoreUpsertRequest struct {
	Db    string `json:"db"`
	Table string `json:"table"`
	Kv    []Kv   `json:"kv"`
}

type StoreDeleteRequest struct {
	Db         string `json:"db"`
	Table      string `json:"table"`
	Kv         []Kv   `json:"kv"`
	SoftDelete bool   `json:"soft_delete"`
}

type StoreDbCreateRequest struct {
	Db    string `json:"db"`
	Table string `json:"table"`
	Kv    []Kv   `json:"kv"`
}

type StoreDbDropRequest struct {
	Db string `json:"db"`
}

type StoreTableCreateRequest struct {
	Db    string `json:"db"`
	Table string `json:"table"`
	Kv    []Kv   `json:"kv"`
}

type StoreTableDropRequest struct {
	Db    string `json:"db"`
	Table string `json:"table"`
}

type StoreDbListRequest struct {
	Db string `json:"db"`
}

type StoreTableListRequest struct {
	Db string `json:"db"`
}

type StoreTableListResponse struct {
	Tables []string `json:"tables"`
}

type StoreResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type StoreColumnCreateRequest struct {
	Db    string `json:"db"`
	Table string `json:"table"`
	Kv    []Kv   `json:"kv"`
}

type StoreColumnDropRequest struct {
	Db    string `json:"db"`
	Table string `json:"table"`
	Kv    []Kv   `json:"kv"`
}

type StoreColumnListRequest struct {
	Db    string `json:"db"`
	Table string `json:"table"`
}

type StoreColumnListResponse struct {
	Columns []string `json:"columns"`
}
