package dtos

import (
	"encoding/json"

	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/entities"
)

type Simulate struct {
	ID             string         `json:"id,omitempty" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Name           string         `json:"name,omitempty" example:"my_simulation"`
	Context        map[string]any `json:"context"`
	Content        map[string]any `json:"content" binding:"required"`
	Status         int            `json:"status" example:"1"`
	DefaultRequest map[string]any `json:"default_request" example:"{\"value\":\"my_value\",\"type\":1}"`
}
type ResponseSimulate struct {
	Performance string           `json:"performance"`
	Result      json.RawMessage  `json:"result"`
	Trace       *json.RawMessage `json:"trace"`
}
type ResponseSimulateForSwagger struct {
	Performance string  `json:"performance" example:"0.1s"`
	Result      string  `json:"result"`
	Trace       *string `json:"trace"`
} // @name ResponseSimulateForSwagger

type Node struct {
	Label    string             `json:"label,omitempty" example:"request"`
	Name     string             `json:"name" example:"my_request"`
	ID       string             `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Position map[string]float64 `json:"position"`
	Type     string             `json:"type" example:"inputNode"`
	Content  any                `json:"content,omitempty"`
}

type Edge struct {
	ID           string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	SourceID     string `json:"sourceId" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Type         string `json:"type" example:"edge"`
	TargetID     string `json:"targetId" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	SourceHandle string `json:"sourceHandle,omitempty" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
}

type SimulateResponse struct {
	ID             uuid.UUID                `json:"id"`
	CreatedAt      string                   `json:"created_at"`
	ChangedBy      string                   `json:"changed_by"`
	Name           string                   `json:"name"`
	Nodes          []Node                   `json:"nodes"`
	Edges          []Edge                   `json:"edges"`
	Context        entities.Attrs           `json:"context"`
	SimulateOutput *entities.SimulateOutput `json:"simulate_output,omitempty"`
	OrganizationID string                   `json:"organization_id"`
	AdminID        string                   `json:"admin_id"`
	VersionID      string                   `json:"version_id"`
	VersionName    string                   `json:"version_name"`
	AccessToken    string                   `json:"access_token"`
}

type SimulateResponseForDetail struct {
	ID               uuid.UUID `json:"id"`
	CreatedAt        string    `json:"created_at"`
	ChangedBy        string    `json:"changed_by"`
	Name             string    `json:"name"`
	OrganizationID   string    `json:"organization_id"`
	OrganizationName string    `json:"organization_name"`
	AdminID          string    `json:"admin_id"`
	AdminName        string    `json:"admin_name"`
	VersionID        string    `json:"version_id"`
	VersionNumber    int       `json:"version_number"`
	VersionName      string    `json:"version_name"`
	AccessToken      string    `json:"access_token"`
	LastWorkingTime  string    `json:"last_working_time"`
	Charts           struct {
		Http    int `json:"http"`
		Grpc    int `json:"grpc"`
		Error   int `json:"error"`
		Success int `json:"success"`
	} `json:"charts"`
	Includes []struct {
		NodeName string `json:"node_name"`
		Count    int    `json:"count"`
	} `json:"includes"`
}

type SimulateResponseForSwagger struct {
	ID             uuid.UUID                 `json:"id" gorm:"primary_key" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt      string                    `json:"created_at" example:"2021-01-01T00:00:00Z"`
	Name           string                    `json:"name" example:"my_simulation"`
	Nodes          []Node                    `json:"nodes"`
	Edges          []Edge                    `json:"edges"`
	Context        entities.Attrs            `json:"context"`
	SimulateOutput SimulateOutputForSwaggger `json:"simulate_output,omitempty"`
	OrganizationID string                    `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	VersionID      string                    `json:"version_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	VersionName    string                    `json:"version_name" example:"v1.0.0"`
	AccessToken    string                    `json:"access_token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"`
} // @name SimulateResponseForSwagger

type SimulateOutputForSwaggger struct {
	ID          uuid.UUID      `json:"ID" gorm:"primary_key"`
	Performance string         `json:"performance"`
	Result      entities.Attrs `json:"result"`
	Trace       entities.Attrs `json:"trace"`
	SimulateID  uuid.UUID      `json:"-"`
}

type RequestForCreateSimulateWithParsAI struct {
	Prompt string `json:"prompt" example:"my_prompt"`
}

type ResponseForParsGptResult struct {
	ID         string `json:"id"`
	JsonResult string `json:"json_result"`
}

type RequestForParsGptRate struct {
	ID   string `json:"id"`
	Rate int    `json:"rate"`
}

type RequestForUpdateSimulateName struct {
	Name       string `json:"name"`
	SimulateID string `json:"simulate_id"`
}

type RequestForSimByID struct {
	Token   string         `json:"token"`
	Context map[string]any `json:"context"`
	Domain  string         `json:"domain"`
}
