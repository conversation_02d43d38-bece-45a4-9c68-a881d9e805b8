package dtos

import "github.com/google/uuid"

type ResponseForDashboard struct {
	TotalEvaluationCount int `json:"total_evaluation_count" example:"12"`
	TotalSimulateCount   int `json:"total_simulate_count" example:"8"`

	LastSimulate _Simulate `json:"last_simulate"`
}

type FavoriteNode struct {
	Label string `json:"label" example:"Node Label"`
	Name  string `json:"name" example:"Node Name"`
	Type  string `json:"type" example:"Node Type"`
}

type _Simulate struct {
	ID        uuid.UUID `json:"ID" gorm:"primary_key" example:"123e4567-e89b-12d3-a456-************"`
	CreatedAt string    `json:"created_at" example:"2024-11-26T15:04:05Z"`
	Name      string    `json:"name" example:"Simulation Name"`
}
