package dtos

import "github.com/google/uuid"

type SimulateLogForSwagger struct {
	ID             uuid.UUID `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	SimulateID     uuid.UUID `json:"simulate_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID uuid.UUID `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Type           string    `json:"type" example:"error"`
	Proto          string    `json:"proto" example:"grpc"`
	Message        string    `json:"message" example:"error message"`
	Request        string    `json:"request" example:"{\"key\":\"value\"}"`
	Response       string    `json:"response" example:"{\"key\":\"value\"}"`
}
