package dtos

type LogListItemDTO struct {
	ID             string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Title          string `json:"title" example:"Log Title"`
	Entity         string `json:"entity" example:"order"`
	Type           string `json:"type" example:"info"` // info,  error
	Ip             string `json:"ip" example:"127.0.0.1"`
	Proto          string `json:"proto" example:"http"` // http, gRPC
	OrganizationID string `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
} // @name LogListItemDTO

type LogDTO struct {
	ID             string `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Title          string `json:"title" example:"Log Title"`
	Entity         string `json:"entity" example:"order"`
	EntityID       string `json:"entity_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	Type           string `json:"type" example:"info"` // info,  error
	Ip             string `json:"ip" example:"127.0.0.1"`
	Proto          string `json:"proto" example:"http"` // http, gRPC
	User           string `json:"user" example:"user"`
	Admin          string `json:"admin" example:"admin"`
	Message        string `json:"message" example:"Log Message"`
	OrganizationID string `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	CreatedAt      string `json:"created_at" example:"2020-01-01 00:00:00"`
} // @name LogDTO

type ResponseForSimulateLog struct {
	ID                  string          `json:"id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	SimulateID          string          `json:"simulate_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	OrganizationID      string          `json:"organization_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	AdminID             string          `json:"admin_id" example:"f0a0a1e9-69bd-4bef-b8c6-4e8c0d3a1212"`
	SimulateName        string          `json:"simulate_name"`
	OrganizationName    string          `json:"organization_name"`
	AdminName           string          `json:"admin_name"`
	Type                string          `json:"type" example:"info"`
	Proto               string          `json:"proto"`
	Source              string          `json:"source" example:"editor"`
	Domain              string          `json:"domain" example:"example.com"`
	Message             string          `json:"message" example:"Log Message"`
	CreatedAt           string          `json:"created_at" example:"2020-01-01 00:00:00"`
	Request             string          `json:"request"`
	Response            string          `json:"response"`
	IsRetried           bool            `json:"is_retried"`
	IsScheduled         bool            `json:"is_scheduled"`
	ScheduledID         string          `json:"scheduled_id"`
	ScheduledNameOfWork string          `json:"scheduled_name_of_work"`
	XTry                int             `json:"x_try"`
	RetryData           []RetryResponse `json:"retry_data"`
} // @name ResponseForSimulateLog

type RetryResponse struct {
	ID       string `json:"id"`
	Status   string `json:"status"`
	XTry     int    `json:"x_try"`
	Message  string `json:"message"`
	Request  string `json:"request"`
	Response string `json:"response"`
}
