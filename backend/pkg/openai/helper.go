package openai

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"

	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/entities"
)

func DoRequest(req_body *entities.ChatCompletionRequest) (*entities.ChatCompletionResponse, error) {
	var res_body entities.ChatCompletionResponse
	json_data, err := json.Marshal(req_body)
	if err != nil {
		return &res_body, err
	}

	req, err := http.NewRequest(
		"POST",
		config.ReadValue().OpenAI.ApiURL,
		bytes.NewBuffer(json_data),
	)
	if err != nil {
		return &res_body, err
	}

	var api_key string

	if api_key = config.ReadValue().OpenAI.OpenAIApiKey; api_key == "" {
		return &res_body, errors.New("openai api key empty")
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+api_key)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return &res_body, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return &res_body, errors.New("openai api status code error")
	}

	if err := json.NewDecoder(resp.Body).Decode(&res_body); err != nil {
		return &res_body, err
	}

	if len(res_body.Choices) == 0 {
		return &res_body, errors.New("openai api error, empty response")
	}

	return &res_body, nil
}

func StringJSONConvert(j string) (string, error) {
	var raw string
	err := json.Unmarshal([]byte(j), &raw)
	if err != nil {
		return "", err
	}
	var jsonData interface{}
	err = json.Unmarshal([]byte(raw), &jsonData)
	if err != nil {
		return "", err
	}
	prettyJSON, err := json.MarshalIndent(jsonData, "", "  ")
	if err != nil {
		return "", err
	}
	return string(prettyJSON), nil
}
