package localizer

import (
	"encoding/json"

	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/parsguru/pars-vue/pkg/embed"
	"golang.org/x/text/language"
)

func GetTranslated(keyword string, langTag string, templateDate map[string]interface{}) string {
	bundle := i18n.NewBundle(language.English)

	bundle.RegisterUnmarshalFunc("json", i18n.UnmarshalFunc(json.Unmarshal))
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/en.json")
	bundle.LoadMessageFileFS(embed.LocaleFS, "locale/tr.json")

	localizer := i18n.NewLocalizer(bundle, langTag)

	option := i18n.LocalizeConfig{
		MessageID: keyword,
		// DefaultMessage: &i18n.Message{
		// 	ID:    "hello_world",
		// 	Other: "Hello, World!",
		// },
		TemplateData: templateDate,
	}

	localizedMessage, _ := localizer.Localize(&option)

	return localizedMessage
}
