package utils

import (
	"crypto/rand"
	"math/big"
)

const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
const numCharset = "123456789"
const transferCharset = "ABCDEFGHJKMNPRTYZ"

// generate random string with numbers and letters 32 length
func GenerateRandomString() (string, error) {
	b := make([]byte, 32)
	for i := range b {
		randIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		b[i] = charset[randIndex.Int64()]
	}
	return string(b), nil
}

func GenerateRandomNumber(length int) (string, error) {
	b := make([]byte, length)
	for i := range b {
		randIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(numCharset))))
		if err != nil {
			return "", err
		}
		b[i] = numCharset[randIndex.Int64()]
	}
	return string(b), nil
}

func GenerateRandomStringNumber(length int) (string, error) {
	b := make([]byte, length)
	for i := range b {
		randIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", err
		}
		b[i] = charset[randIndex.Int64()]
	}
	return string(b), nil
}

func GenerateRandomNumberWithLength(length int) (uint64, error) {
	min := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(length-1)), nil)
	max := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(length)), nil)

	randomInt, err := rand.Int(rand.Reader, new(big.Int).Sub(max, min))
	if err != nil {
		return uint64(0), err
	}

	randomInt.Add(randomInt, min)

	return randomInt.Uint64(), nil
}

func GenerateRandomTransferCode() (string, error) {
	b := make([]byte, 8)
	for i := range b {
		if i < 2 {
			randIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(transferCharset))))
			if err != nil {
				return "", err
			}
			b[i] = transferCharset[randIndex.Int64()]
		} else {
			randIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(numCharset))))
			if err != nil {
				return "", err
			}
			b[i] = numCharset[randIndex.Int64()]
		}
	}
	return string(b), nil
}

func GenerateRandomUint64(length int) (uint64, error) {
	min := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(length-1)), nil)
	max := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(length)), nil)

	randomInt, err := rand.Int(rand.Reader, new(big.Int).Sub(max, min))
	if err != nil {
		return uint64(0), err
	}

	randomInt.Add(randomInt, min)

	return randomInt.Uint64(), nil
}
