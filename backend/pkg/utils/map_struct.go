package utils

import (
	"encoding/json"
)

func MapToStruct(mapData any, structData any) error {
	jsonStr, err := json.Marshal(mapData)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(jsonStr, structData); err != nil {
		return err
	}
	return nil
}

func StructToMap(obj interface{}) (newMap map[string]interface{}, err error) {
	data, err := json.Marshal(obj) // Convert to a json string

	if err != nil {
		return
	}

	err = json.Unmarshal(data, &newMap) // Convert to a map
	return
}
