package utils

import "strings"

// GetDomainAddressFromUrl extracts the domain from a given URL string.
// It handles URLs with and without schemes and ports.
func GetDomainAddressFromUrl(url string) string {
	if url == "" {
		return ""
	}

	// Remove the scheme if present
	if strings.Contains(url, "://") {
		url = strings.SplitN(url, "://", 2)[1]
	}

	// Remove the port if present
	if strings.Contains(url, ":") {
		url = strings.SplitN(url, ":", 2)[0]
	}

	// Remove any trailing slashes
	url = strings.TrimSuffix(url, "/")

	return url
}
