package utils

import (
	"encoding/json"
	"fmt"

	"github.com/go-playground/validator/v10"
)

func Validate(err error) string {
	if validationErrs, ok := err.(validator.ValidationErrors); ok {
		var errorMessage string
		e := validationErrs[0]
		switch e.Tag() {
		case "required":
			errorMessage = fmt.Sprintf("the field %s is required", e.Field())
		case "email":
			errorMessage = fmt.Sprintf("the field %s have to be email", e.<PERSON>())
		}

		return errorMessage
	} else if marshallingErr, ok := err.(*json.UnmarshalTypeError); ok {
		return fmt.Sprintf("The field %s must be a %s", marshallingErr.Field, marshallingErr.Type.String())
	}

	return "something went wrong"
}
