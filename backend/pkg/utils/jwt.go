package utils

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/redis"
	// "github.com/parsguru/pars-vue/pkg/config"
)

// JwtWrapper wraps the signing key and the issuer
type JwtWrapper struct {
	SecretKey string
	Issuer    string
	Expire    int
}

// JwtClaim adds email as a claim to the token
type JwtClaim struct {
	Email          string
	ID             string
	OrganizationID string
	Main           bool
	jwt.RegisteredClaims
}

type JWTSimulateClaim struct {
	SimulateID     string
	OrganizationID string
	AdminID        string
	VersionID      string
	VersionName    string
	jwt.RegisteredClaims
}

type JWTApplicationClaim struct {
	OrganizationID string
	// ApplicationID  int
	// Sections       []string
	jwt.RegisteredClaims
}

type JwtUserClaim struct {
	ID             string
	Name           string
	Email          string
	OrganizationID string
	IPAddress      string
	jwt.RegisteredClaims
}

// GenerateToken generates a jwt token
func (j *JwtWrapper) GenerateAdminJWT(id, organizationID, email string, main bool) (string, error) {
	claims := &JwtClaim{
		Email:          email,
		ID:             id,
		OrganizationID: organizationID,
		Main:           main,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Local().Add(time.Hour * time.Duration(config.ReadValue().JwtExpire))),
			Issuer:    j.Issuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)

	signedToken, err := token.SignedString([]byte(j.SecretKey))
	if err != nil {
		return "", err
	}

	return signedToken, nil
}

func (j *JwtWrapper) GenerateUserJWT(id, email, ip_address, agent string, expire *jwt.NumericDate) (string, error) {
	claims := &JwtUserClaim{
		Email:     email,
		ID:        id,
		IPAddress: ip_address,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: expire,
			Issuer:    j.Issuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)

	signedToken, err := token.SignedString([]byte(j.SecretKey))
	if err != nil {
		return "", err
	}

	return signedToken, nil
}

func (j *JwtWrapper) GenerateSimulateJWT(c context.Context, sim_id, org_id, admin_id, version_id, version_name string, expire_at int) (string, error) {
	claims := &JWTSimulateClaim{
		SimulateID:     sim_id,
		OrganizationID: org_id,
		AdminID:        admin_id,
		VersionID:      version_id,
		VersionName:    version_name,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Local().Add(time.Hour * 24 * time.Duration(expire_at))),
			Issuer:    j.Issuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)
	signedToken, err := token.SignedString([]byte(j.SecretKey))
	if err != nil {
		return "", err
	}

	return signedToken, nil
}

func (j *JwtWrapper) GenerateApplicationJWT(c context.Context, org_id string, expire_at int) (string, error) {
	claims := &JWTApplicationClaim{
		OrganizationID: org_id,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Local().Add(time.Hour * 24 * time.Duration(expire_at))),
			Issuer:    j.Issuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)
	signedToken, err := token.SignedString([]byte(j.SecretKey))
	if err != nil {
		return "", err
	}

	return signedToken, nil
}

func (j *JwtWrapper) ValidateToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JwtClaim)
	if !redis.AdminUserSessionCheck(claims.ID, tokenString) {
		return false
	}

	if claims.ExpiresAt.Local().Unix() < time.Now().Local().Unix() {
		return false
	}

	return token.Valid
}

func (j *JwtWrapper) ValidateUserToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JwtClaim)

	if claims.ExpiresAt.Local().Unix() < time.Now().Local().Unix() {
		return false
	}

	return token.Valid
}

func (j *JwtWrapper) ValidateSimulateToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JWTSimulateClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JWTSimulateClaim)

	if claims.ExpiresAt.Local().Unix() < time.Now().Local().Unix() {
		return false
	}

	return token.Valid
}

func (j *JwtWrapper) ValidateApplicationToken(tokenString string) bool {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JWTApplicationClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return false
	}

	claims, _ := token.Claims.(*JWTApplicationClaim)

	if claims.ExpiresAt.Local().Unix() < time.Now().Local().Unix() {
		return false
	}

	return token.Valid
}

func (j *JwtWrapper) ParseToken(tokenString string) (claims *JwtClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)

	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JwtClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) ParseUserToken(tokenString string) (claims *JwtUserClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtUserClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JwtUserClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) ParseAdminToken(Bearertoken string) (claims *JwtClaim, err error) {
	tokenString := strings.Split(Bearertoken, "Bearer ")[1]
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JwtClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)

	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JwtClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) ParseSimulateToken(tokenString string) (claims *JWTSimulateClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JWTSimulateClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JWTSimulateClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) ParseApplicationToken(tokenString string) (claims *JWTApplicationClaim, err error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&JWTApplicationClaim{},
		func(token *jwt.Token) (interface{}, error) {
			return []byte(j.SecretKey), nil
		},
	)
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*JWTApplicationClaim)
	if !ok {
		return nil, fmt.Errorf("claims not JwtClaim")
	}

	return claims, nil
}

func (j *JwtWrapper) ParseUser(c *gin.Context) *JwtUserClaim {
	user := c.MustGet(gin.AuthUserKey).(*JwtUserClaim)
	return user
}
