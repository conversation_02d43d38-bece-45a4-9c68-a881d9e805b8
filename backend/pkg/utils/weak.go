package utils

import (
	"regexp"
)

func IsStrong(password string) bool {
	if len(password) < 8 {
		return false
	}

	upperRegex := regexp.MustCompile(`[A-Z]`)
	if !upperRegex.MatchString(password) {
		return false
	}

	lowerRegex := regexp.MustCompile(`[a-z]`)
	if !lowerRegex.MatchString(password) {
		return false
	}

	digitRegex := regexp.MustCompile(`[0-9]`)
	if !digitRegex.MatchString(password) {
		return false
	}

	specialCharRegex := regexp.MustCompile(`[!@#$%^&*()_+{}\[\]:;<>,.?~\\/-]`)
	if !specialCharRegex.MatchString(password) {
		return false
	}

	if_password_pwned := GetIfPwnedList(password)

	return !if_password_pwned
}
