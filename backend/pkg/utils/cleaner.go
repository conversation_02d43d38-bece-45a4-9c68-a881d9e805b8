package utils

import (
	"regexp"
	"strings"
)

const (
	PayloadTypeCreditCard     = "creditcard"
	PayloadTypeEmail          = "email"
	PayloadTypePhone          = "phone"
	PayloadTypeName           = "name"
	PayloadTypeIdentityNumber = "identitynumber"
)

func CleanString(payload, payloadtype string) string {
	if payloadtype == PayloadTypeCreditCard {
		re := regexp.MustCompile(`(\d{6})\d+(\d{4})`)
		return re.ReplaceAllString(payload, `$1******$2`)
	}
	if payloadtype == PayloadTypeEmail {
		re := regexp.MustCompile(`(?i)([a-z0-9._%+\-]+)@([a-z0-9.\-]+\.[a-z]{2,})`)
		return re.ReplaceAllString(payload, `$1@***`)
	}
	if payloadtype == PayloadTypePhone {
		re := regexp.MustCompile(`(\d{3})\d+(\d{4})`)
		return re.ReplaceAllString(payload, `$1******$2`)
	}
	if payloadtype == PayloadTypeName {
		list := strings.Split(payload, " ")

		for i, word := range list {
			first := string([]rune(word)[:1])
			list[i] = first + strings.Repeat("*", len(word)-1)
		}

		return strings.Join(list, " ")
	}
	if payloadtype == PayloadTypeIdentityNumber {
		re := regexp.MustCompile(`(\d{3})\d+(\d{3})`)
		return re.ReplaceAllString(payload, `$1******$2`)
	}

	return payload
}
