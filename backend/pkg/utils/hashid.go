package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"

	"github.com/parsguru/pars-vue/pkg/config"
)

var iv = []byte{15, 43, 57, 99, 85, 38, 23, 74, 87, 35, 63, 18, 66, 32, 14, 75}

func Encode(b []byte) string {
	return base64.StdEncoding.EncodeToString(b)
}

func Decode(s string) []byte {
	data, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		panic(err)
	}
	return data
}

func Encrypt(text string) (string, error) {
	block, err := aes.NewCipher([]byte(config.ReadValue().HashSalt))
	if err != nil {
		return "", err
	}
	plainText := []byte(text)
	cfb := cipher.NewCFBEncrypter(block, iv)
	cipherText := make([]byte, len(plainText))
	cfb.XORKeyStream(cipherText, plainText)
	return Encode(cipherText), nil
}

func Decrypt(text string) (string, error) {
	block, err := aes.NewCipher([]byte(config.ReadValue().HashSalt))
	if err != nil {
		return "", err
	}
	cipherText := Decode(text)
	cfb := cipher.NewCFBDecrypter(block, iv)
	plainText := make([]byte, len(cipherText))
	cfb.XORKeyStream(plainText, cipherText)
	return string(plainText), nil
}

func GenerateHashid(text string) string {
	hashid, _ := Encrypt(text)
	return hashid
}

func HashidCheck(hash string, id string) bool {
	decoded, err := Decrypt(hash)
	if err != nil {
		return false
	}
	return decoded == id
}
