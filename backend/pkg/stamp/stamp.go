package stamp

import (
	"github.com/nats-io/nats.go"
)

// func IsOnline(org_id string) bool {
// 	db := database.ClientDB()

// 	conn, err := grpc.NewClient(
// 		net.JoinHostPort(config.ReadValue().Stamp.Host, config.ReadValue().Stamp.Port),
// 		grpc.WithTransportCredentials(insecure.NewCredentials()),
// 	)
// 	if err != nil {
// 		log.Println("gRPC sunucusuna bağlanılamadı: ", err)
// 		return false
// 	}

// 	c := stamp.NewStampServiceClient(conn)
// 	var countStampProviders int64
// 	var stampProvider entities.StampProvider
// 	db.Model(&entities.StampProvider{}).Where("organization_id = ? AND enabled = true", org_id).Count(&countStampProviders)
// 	db.Model(&entities.StampProvider{}).Where("organization_id = ? AND enabled = true", org_id).First(&stampProvider)

// 	defer conn.Close()

// 	res, err := c.Available(context.Background(), &stamp.AvailableRequest{
// 		StampProviderHost: stampProvider.ProviderHost,
// 		StampProviderPort: stampProvider.ProviderPort,
// 		StampUserId:       stampProvider.ProviderUser,
// 		StampUserPassword: stampProvider.ProviderPass,
// 		StampType:         stampProvider.GetTypeName(),
// 	})
// 	if err != nil {
// 		return false
// 	}

// 	return res.Available
// }

// func CreateStamp(org_id, content, entity, action, client_id, ip_address, user_agent, reference_id string, content_type uint64) error {
// 	db := database.ClientDB()
// 	conn, err := grpc.NewClient(
// 		net.JoinHostPort(config.ReadValue().Stamp.Host, config.ReadValue().Stamp.Port),
// 		grpc.WithTransportCredentials(insecure.NewCredentials()),
// 	)
// 	if err != nil {
// 		log.Println("gRPC sunucusuna bağlanılamadı: ", err)
// 		return err
// 	}
// 	defer conn.Close()

// 	c := stamp.NewStampServiceClient(conn)
// 	var countStampProviders int64
// 	var stampProvider entities.StampProvider
// 	db.Model(&entities.StampProvider{}).Where("organization_id = ? AND enabled = true", org_id).Count(&countStampProviders)
// 	db.Model(&entities.StampProvider{}).Where("organization_id = ? AND enabled = true", org_id).First(&stampProvider)

// 	if countStampProviders == 0 {
// 		return nil
// 	}

// 	response, err := c.Do(context.Background(), &stamp.DoRequest{
// 		StampProviderHost: stampProvider.ProviderHost,
// 		StampProviderPort: stampProvider.ProviderPort,
// 		StampUserId:       stampProvider.ProviderUser,
// 		StampUserPassword: stampProvider.ProviderPass,
// 		StampType:         stampProvider.GetTypeName(),
// 		Content:           content,
// 	})
// 	if err != nil {
// 		log.Println("DoStamp çağrısı başarısız: ", err)
// 		return err
// 	}

// 	b64Result := base64.StdEncoding.EncodeToString(response.Result)

// 	blob := entities.EntityBlob{
// 		Type:            content_type,
// 		Entity:          entity,
// 		Action:          action,
// 		ContentSent:     content,
// 		ContentReceived: b64Result,
// 		ReferenceID:     reference_id,
// 		IpAddress:       ip_address,
// 		UserAgent:       user_agent,
// 		ClientID:        client_id,
// 		OrganizationID:  stampProvider.OrganizationID,
// 	}

// 	if err := db.Model(&entities.EntityBlob{}).Create(&blob).Error; err != nil {
// 		monolog.CreateLog(&entities.Log{
// 			Title:   "Stamp",
// 			Message: "Stamp EntityBlob Create Error " + err.Error(),
// 			Entity:  entity,
// 			Type:    "error",
// 		})
// 	}

// 	return nil
// }

type StampEventData struct {
	OrganizationID string `json:"organization_id"`
	Content        string `json:"content"`
	Entity         string `json:"entity"`
	Action         string `json:"action"`
	ClientID       string `json:"client_id"`
	IpAaddress     string `json:"ip_address"`
	UserAgent      string `json:"user_agent"`
	ReferenceID    string `json:"reference_id"`
	ContentType    uint64 `json:"content_type"`
}

var nc *nats.Conn

// func PushStampEvent(payload StampEventData) {
// 	if nc == nil {
// 		ncli, _ := nats.Connect(config.ReadValue().Nats.Url, nats.Name("mono-acquiring-admin"+"_"+global.GetAppID()))
// 		nc = ncli
// 	}
// 	json, _ := json.Marshal(payload)

// 	if syslog.GetSyslogWriter() != nil {
// 		syslog.GetSyslogWriter().Info(string(json))
// 	}

// 	if err := nc.Publish("stamp", json); err != nil {
// 		log.Println("Nats publish error: ", err)
// 	}
// }

// func StampEventListener() {
// 	ncli := nat.NatConn()
// 	_, err := ncli.QueueSubscribe("stamp", "workers", func(m *nats.Msg) {
// 		var eventData StampEventData
// 		if err := json.Unmarshal(m.Data, &eventData); err != nil {
// 			monolog.CreateLog(&entities.Log{
// 				Title:          "Stamp",
// 				Message:        "Stamp not created : " + err.Error(),
// 				Entity:         "stamp",
// 				Type:           "error",
// 				Proto:          "nats",
// 				OrganizationID: helpers.ParseID(eventData.OrganizationID),
// 			})
// 		}

// 		if err := CreateStamp(
// 			eventData.OrganizationID, eventData.Content, eventData.Entity,
// 			eventData.Action, eventData.ClientID, eventData.IpAaddress,
// 			eventData.UserAgent, eventData.ReferenceID, eventData.ContentType,
// 		); err != nil {
// 			monolog.CreateLog(&entities.Log{
// 				Title:          "Stamp",
// 				Message:        "Stamp not created : " + err.Error(),
// 				Entity:         "stamp",
// 				Type:           "error",
// 				Proto:          "nats",
// 				OrganizationID: helpers.ParseID(eventData.OrganizationID),
// 			})
// 		}

// 		monolog.CreateLog(&entities.Log{
// 			Title:          "Stamp",
// 			Message:        "Stamp created",
// 			Entity:         "stamp",
// 			Type:           "info",
// 			Proto:          "nats",
// 			OrganizationID: helpers.ParseID(eventData.OrganizationID),
// 		})

// 	})

// 	if err == nil {
// 		log.Println("stamp nat subscribed")
// 	}
// }
