package helpers

import (
	"context"

	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/state"
	"gorm.io/gorm"
)

func GetCurrentOrganizationWithChildOrgs(db *gorm.DB, ctx context.Context) []string {
	var org_id_list []string
	var fetchChildOrgs func(parentID string)

	fetchChildOrgs = func(parentID string) {
		var childIDs []string
		db.Model(&entities.Organization{}).Where(query.BuildQuery(query.WhereParentID), parentID).Pluck("id", &childIDs)
		org_id_list = append(org_id_list, childIDs...)
		for _, childID := range childIDs {
			fetchChildOrgs(childID)
		}
	}

	if state.CurrentAdminOrganization(ctx).String() == "00000000-0000-0000-0000-000000000000" {
		fetchChildOrgs(state.CurrentUserOrganization(ctx).String())
		org_id_list = append(org_id_list, state.CurrentUserOrganization(ctx).String())
	} else {
		fetchChildOrgs(state.CurrentAdminOrganization(ctx).String())
		org_id_list = append(org_id_list, state.CurrentAdminOrganization(ctx).String())
	}

	return org_id_list
}

func GetOrganizationWithChildOrgs(orgID string, db *gorm.DB, ctx context.Context) []string {
	var org_id_list []string
	var fetchChildOrgs func(parentID string)
	fetchChildOrgs = func(parentID string) {
		var childIDs []string
		db.Model(&entities.Organization{}).Where(query.BuildQuery(query.WhereParentID), parentID).Pluck("id", &childIDs)
		org_id_list = append(org_id_list, childIDs...)
		for _, childID := range childIDs {
			fetchChildOrgs(childID)
		}
	}
	fetchChildOrgs(orgID)
	org_id_list = append(org_id_list, orgID)
	return org_id_list
}
