package helpers

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

func DefaultPagination(ctx *gin.Context, defaults ...int) (int, int) {
	page, _ := strconv.Atoi(ctx.Query("page"))
	perPage, _ := strconv.Atoi(ctx.Query("per_page"))
	if page == 0 {
		if len(defaults) > 0 {
			page = defaults[0]
		} else {
			page = 1
		}
	}
	if perPage == 0 {
		if len(defaults) > 1 {
			perPage = defaults[1]
		} else {
			perPage = 10
		}
	}
	return page, perPage
}
