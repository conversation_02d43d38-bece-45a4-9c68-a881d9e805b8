package helpers

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

const (
	defaultPage    = 1
	defaultPerPage = 10
	maxPage        = 99999
	maxPerPage     = 100
)

func ParsePagination(c *gin.Context) (page int, perPage int) {
	page, err := strconv.Atoi(c.Query("page"))
	if err != nil || page < 1 {
		page = defaultPage
	} else if page > maxPage {
		page = maxPage
	}

	perPage, err = strconv.Atoi(c.Query("per_page"))
	if err != nil || perPage < 1 {
		perPage = defaultPerPage
	} else if perPage > maxPerPage {
		perPage = maxPerPage
	}

	return page, perPage
}
