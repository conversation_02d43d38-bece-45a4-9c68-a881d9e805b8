package helpers

import (
	"strconv"
	"time"
)

func BoolPtr(b bool) *bool {
	return &b
}

func IntPtr(i int) *int {
	return &i
}

func IntPtrTo32(i *int) *int32 {
	if i == nil {
		return nil
	}
	i32 := int32(*i)
	return &i32
}

func IntPtrTo64(i *int) *int64 {
	if i == nil {
		return nil
	}
	i64 := int64(*i)
	return &i64
}

func StringPtr(s string) *string {
	return &s
}

func TimePtr(t time.Time) *time.Time {
	return &t
}

func BoolRtp(b *bool) bool {
	if b == nil {
		return false
	}
	return *b
}

func Int64Rtp(i *int64) int64 {
	if i == nil {
		return 0
	}
	return *i
}

func Int64RtrToInt(i *int64) *int {
	if i == nil {
		return nil
	}
	i32 := int(*i)
	return &i32
}

func Uint64Rtp(i *uint64) uint64 {
	if i == nil {
		return 0
	}
	return *i
}

func Uint64RtrToInt(i *uint64) *uint {
	if i == nil {
		return nil
	}
	ui32 := uint(*i)
	return &ui32
}

func Float64RtrToString(i *float64) string {
	if i == nil {
		return ""
	}
	float := strconv.FormatFloat(*i, 'f', -1, 64)
	return float
}

func StringRtp(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

func Int64ToString(i int64) string {
	return strconv.FormatInt(i, 10)
}
