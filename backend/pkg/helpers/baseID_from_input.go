package helpers

import (
	"encoding/json"
	"errors"

	"github.com/gorules/zen-go"
)

func GetBaseIDsFromInput(request zen.NodeRequest) (organizationID, simulationID, userID string, m map[string]interface{}, err error) {
	var input_map map[string]interface{}

	if err := json.Unmarshal(request.Input, &input_map); err != nil {
		return "", "", "", input_map, err
	}

	org_id, ok := input_map["_organization_id"].(string)
	if !ok {
		return "", "", "", input_map, errors.New("_organization_id not found or not string")
	}

	user_id, ok := input_map["_user_id"].(string)
	if !ok {
		return "", "", "", input_map, errors.New("_user_id not found or not string")
	}

	sim_id, ok := input_map["_simulate_id"].(string)
	if !ok {
		sim_id = ""
	}

	return org_id, sim_id, user_id, input_map, nil
}
