package helpers

import (
	"context"

	"go.opentelemetry.io/otel/trace"
)

func PushToTrace(t trace.Tracer, ctx context.Context, msg, spanName string) context.Context {
	ctx, span := t.Start(ctx, spanName)
	//  span, span.SpanContext().TraceID()
	// splitted := strings.Split(msg, ".")[0]
	// sentrySpan := sentry.StartSpan(ctx, msg, sentry.TransactionName(splitted))

	// sentry.ConfigureScope(func(scope *sentry.Scope) {
	// 	scope.SetUser(sentry.User{ID: state.CurrentAdminUser(ctx).String()})
	// })

	defer span.End()
	// defer sentrySpan.Finish()
	return ctx
}
