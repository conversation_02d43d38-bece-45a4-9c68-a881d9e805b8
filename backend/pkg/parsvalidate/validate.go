package parsvalidate

import (
	"reflect"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/tr"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"

	en_translations "github.com/go-playground/validator/v10/translations/en"
	tr_translations "github.com/go-playground/validator/v10/translations/tr"
)

func ValidateAndTranslate(ctx *gin.Context, payload interface{}) (string, map[string]string, error) {
	validate := validator.New()

	en := en.New()
	tr := tr.New()
	uni := ut.New(en, tr, en)

	locale := ctx.GetHeader("Accept-Language")
	trans, found := uni.FindTranslator(locale)
	if !found {
		trans, _ = uni.GetTranslator("en")
	}

	switch locale {
	case "tr":
		tr_translations.RegisterDefaultTranslations(validate, trans)
		justStringWithSpaceAndTurkishCharsCheck(validate, trans, "{0} sadece boşluk ve Türkçe karakterler içerebilir")
	case "en":
		en_translations.RegisterDefaultTranslations(validate, trans)
		justStringWithSpaceAndTurkishCharsCheck(validate, trans, "{0} can only contain space and Turkish characters")
	default:
		en_translations.RegisterDefaultTranslations(validate, trans)
		justStringWithSpaceAndTurkishCharsCheck(validate, trans, "{0} can only contain space and Turkish characters")
	}

	validate.RegisterTagNameFunc(func(field reflect.StructField) string {
		name := strings.SplitN(field.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}

		return name
	})

	if err := validate.Struct(payload); err != nil {
		if _, ok := err.(*validator.InvalidValidationError); ok {
			return "", nil, err
		}

		validationErrors, ok := err.(validator.ValidationErrors)
		if ok {
			fieldErrors := make(map[string]string, len(validationErrors))
			var fieldErrorsArr []string
			for _, field := range validationErrors {
				fieldErrors[field.Field()] = strings.ReplaceAll(field.Translate(trans), field.Field(), "")
				fieldErrorsArr = append(fieldErrorsArr, field.Translate(trans))
			}
			return strings.Join(fieldErrorsArr, ", "), fieldErrors, nil
		}
		return "", nil, nil
	}
	return "", nil, nil
}

func justStringWithSpaceAndTurkishCharsCheck(validate *validator.Validate, trans ut.Translator, translatedText string) error {
	var justStringWithSpaceAndTurkish validator.Func = func(fl validator.FieldLevel) bool {
		return regexp.MustCompile("^[a-zA-ZçÇğĞıİöÖşŞüÜ ]+$").MatchString(fl.Field().String())
	}
	validate.RegisterValidation("just_string_with_space_and_turkish", justStringWithSpaceAndTurkish, true)
	validate.RegisterTranslation("just_string_with_space_and_turkish", trans, func(ut ut.Translator) (err error) {
		if err = ut.Add("just_string_with_space_and_turkish", translatedText, true); err != nil {
			return
		}
		return
	}, func(ut ut.Translator, fe validator.FieldError) string {

		t, err := ut.T("just_string_with_space_and_turkish", fe.Field())
		if err != nil {
			return fe.(error).Error()
		}

		return t
	})

	return nil
}
