package validator

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type Validator struct {
	Errors      []string          `json:",omitempty"`
	FieldErrors map[string]string `json:",omitempty"`
}

func (v Validator) HasErrors() bool {
	return len(v.Errors) != 0 || len(v.FieldErrors) != 0
}

func (v *Validator) AddError(message string) {
	if v.Errors == nil {
		v.Errors = []string{}
	}

	v.Errors = append(v.Errors, message)
}

func (v *Validator) AddFieldError(key, message string) {
	if v.FieldErrors == nil {
		v.FieldErrors = map[string]string{}
	}

	if _, exists := v.FieldErrors[key]; !exists {
		v.FieldErrors[key] = message
	}
}

func (v *Validator) Check(ok bool, message string) {
	if !ok {
		v.AddError(message)
	}
}

func (v *Validator) CheckField(ok bool, key, message string) {
	if !ok {
		v.AddFieldError(key, message)
	}
}

func (v *Validator) Add(ok bool, key, message string) *Validator {
	if !ok {
		v.AddFieldError(key, message)
	}
	return v
}

func (v *Validator) Run(c *gin.Context) bool {
	if v.HasErrors() {
		c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Validation error", "errors": v.FieldErrors})
		return true
	}
	return false
}

func New() *Validator {
	return &Validator{}
}
