package validator

import (
	"net/url"
	"regexp"
	"strings"
	"unicode/utf8"

	"golang.org/x/exp/constraints"
)

var (
	RgxEmail                                     = regexp.MustCompile("^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$")
	RgxPhoneInternational                        = regexp.MustCompile("^\\+[1-9]{1}[0-9]{3,14}$")
	RgxCreditCard                                = regexp.MustCompile("^[0-9]{4} [0-9]{4} [0-9]{4} [0-9]{4}$")
	RgxJustString                                = regexp.MustCompile("^[a-zA-Z]+$")
	RgxJustNumber                                = regexp.MustCompile("^[0-9]+$")
	RgxJustStringWithSpace                       = regexp.MustCompile("^[a-zA-Z ]+$")
	RgxJustStringWithSpaceAndTurkish             = regexp.MustCompile("^[a-zA-ZçÇğĞıİöÖşŞüÜ ]+$")
	RgxStringWithSpaceAndTurkishAndNumbersAndDot = regexp.MustCompile("^[a-zA-ZçÇğĞıİöÖşŞüÜ0-9. ]+$")
	RgxNumberWithDotsAndCommas                   = regexp.MustCompile("^[0-9.,]+$")
	RgxJustStringWithSpaceAndNumber              = regexp.MustCompile("^[a-zA-Z0-9 ]+$")
	RgxNoSpacePrefixSuffix                       = regexp.MustCompile(`^[\w\s]+(?:[\w\s]+$)?`)
)

func NotBlank(value string) bool {
	return strings.TrimSpace(value) != ""
}

func MinRunes(value string, n int) bool {
	return utf8.RuneCountInString(value) >= n
}

func MaxRunes(value string, n int) bool {
	return utf8.RuneCountInString(value) <= n
}

func Between[T constraints.Ordered](value, min, max T) bool {
	return value >= min && value <= max
}

func Matches(value string, rx *regexp.Regexp) bool {
	return rx.MatchString(value)
}

func Equal[T comparable](value, other T) bool {
	return value == other
}

func In[T comparable](value T, safelist ...T) bool {
	for i := range safelist {
		if value == safelist[i] {
			return true
		}
	}
	return false
}

func AllIn[T comparable](values []T, safelist ...T) bool {
	for i := range values {
		if !In(values[i], safelist...) {
			return false
		}
	}
	return true
}

func NotIn[T comparable](value T, blocklist ...T) bool {
	for i := range blocklist {
		if value == blocklist[i] {
			return false
		}
	}
	return true
}

func NoDuplicates[T comparable](values []T) bool {
	uniqueValues := make(map[T]bool)

	for _, value := range values {
		uniqueValues[value] = true
	}

	return len(values) == len(uniqueValues)
}

func IsEmail(value string) bool {
	if len(value) > 254 {
		return false
	}

	return RgxEmail.MatchString(value)
}

func IsURL(value string) bool {
	u, err := url.ParseRequestURI(value)
	if err != nil {
		return false
	}

	return u.Scheme != "" && u.Host != ""
}
