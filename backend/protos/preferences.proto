syntax = "proto3";
package preferences;
option go_package = "./grpc";

service PreferencesService {
    rpc AddDSN(DSNAddDTO) returns (DSNResponseDTO);
    rpc GetDSN(DSNEmptyDTO) returns (DSNGetResponseDTO);
    rpc DeleteDSN(DSNDeleteDTO) returns (DSNResponseDTO);
}

message DSNEmptyDTO {}

message DSNAddDTO {
    string key = 1;
    string dsn = 2;
    int64 db_engine = 3;
}

message DSNGetDTO {
    string id = 1;
    string created_at = 2;
    string key = 3;
    string dsn = 4;
    string last_dsn_check_at = 7;
    int64 db_engine = 5;
    int64 last_dsn_check_status = 6;
}

message DSNGetResponseDTO {
    repeated DSNGetDTO dsns = 1;
}

message DSNResponseDTO {
    string message = 1;
}

message DSNDeleteDTO {
    string id = 1;
}