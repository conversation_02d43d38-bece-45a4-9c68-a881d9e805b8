syntax = "proto3";
package com.tapsilat.ParsAuth.v1;
option go_package = "./grpc/pars_auth/v1";

service ParsAuthService {
  rpc Login(AuthLoginDTO) returns (AuthResponseDTO);
  rpc Register(AuthRegisterDTO) returns (AuthResponseDTO);
  rpc TokenRefresh(AuthEmptyDTO) returns (AuthResponseDTO);
  rpc HealthCheck(AuthEmptyDTO) returns (AuthEmptyDTO);
}

message AuthLoginDTO {
  string email = 1;
  string password = 2;
}

message AuthRegisterDTO {
  string name = 1;
  string email = 2;
  string password = 3;
}

message AuthResponseDTO {
  string token = 1;
}

message AuthEmptyDTO {}
