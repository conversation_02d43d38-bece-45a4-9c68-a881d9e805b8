syntax = "proto3";
package com.tapsilat.ParsNode.v1;
option go_package = "./grpc/pars_node/v1";

service ParsNodeService {
  rpc DeleteFavoritedNode(NodeDeleteFavoriteDTO) returns (NodeResponseDTO);
  rpc CreateFavoritedNode(Node) returns (NodeResponseDTO);
  rpc GetFavoritedNodes(NodeGetRequestDTO) returns (NodePaginatedData);
}

message NodeDeleteFavoriteDTO {
  string id = 1;
}

message NodeGetRequestDTO {
  int64 page = 1;
  int64 per_page = 2;
}

message NodePaginatedData {
  int64 page = 1;
  int64 per_page = 2;
  int64 total = 3;
  int64 total_pages = 4;
  repeated Node rows = 5;
}
message NodeResponseDTO {
  string message = 1;
}

message Node {
  string label = 1;
  string name = 2;
  string id = 3;
  NodePosition position = 4;
  string type = 5;
  oneof content {
    Content object = 6;
    string string = 7;
  };
}

message NodePosition {
  optional int64 x = 1;
  optional int64 y = 2;
}

message Content {
  string hitPolicy = 1;
  repeated InputOutput inputs = 2;
  repeated InputOutput outputs = 3;
  repeated Rule rules = 4;
  repeated Statement statements = 5;
  repeated Expression expressions = 6;
}


message InputOutput {
  string id = 1;
  string name = 2;
  string type = 3;
  string field = 4;
}

message Rule {
  map<string, string> rule = 1;
}

message Statement {
  string id = 1;
  string condition = 2;
}
message Expression {
  string id = 1;
  string key = 2;
  string value = 3;
}