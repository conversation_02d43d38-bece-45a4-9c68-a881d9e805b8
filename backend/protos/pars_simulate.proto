syntax = "proto3";
package com.tapsilat.ParsSimulate.v1;
option go_package = "./grpc/pars_simulate/v1";

service ParsSimulateService {
  rpc Simulate(SimulateDTO) returns (ResponseSimulate);
  rpc GetSimulations(SimulatePageRequest) returns (SimulatePaginatedData);
  rpc GetSimulationTokenBySimulateID(SimulateGet) returns (SimulateGeneralResponse);
  rpc GetSimulationsShort(SimulatePageRequest) returns (SimulatePaginatedData);
  rpc UpdateOrCreateSimulate(SimulateDTO) returns (SimulateGeneralResponse);
  rpc DeleteSimulate(SimulateDelete) returns (SimulateGeneralResponse);
  rpc GetSimulationRules(SimulatePageRequest) returns (SimulatePaginatedData);
  rpc SimulateRule(SimulateGet) returns (SimulateResponse);
  rpc SimByID(SimulateByID) returns (ResponseSimulate);
}

message SimulateDTO {
  string id = 1;
  string name = 2;
  bytes context = 3;
  SimulateRequestContent content = 4;
  bool is_new = 5;
  string default_request = 6;
}

message ResponseSimulate {
  string performance = 1;
  bytes result = 2;
  bytes trace = 3;
}

message Edge {
  string id = 1;
  string sourceId = 2;
  string type = 3;
  string targetId = 4;
  string source_handle = 5;
}
message Node {
  string label = 1;
  string name = 2;
  string id = 3;
  NodePosition position = 4;
  string type = 5;
  oneof content {
    Content object = 6;
    string string = 7;
  };
}

message NodePosition {
  optional int64 x = 1;
  optional int64 y = 2;
}

message Content {
  string hitPolicy = 1;
  repeated InputOutput inputs = 2;
  repeated InputOutput outputs = 3;
  repeated Rule rules = 4;
  repeated Statement statements = 5;
  repeated Expression expressions = 6;
}

message Rule {
  map<string, string> rule = 1;
}
message Statement {
  string id = 1;
  string condition = 2;
}
message Expression {
  string id = 1;
  string key = 2;
  string value = 3;
}

message InputOutput {
  string id = 1;
  string name = 2;
  string type = 3;
  string field = 4;
}

message SimulateOutput {
  string id = 1;
  string performance = 2;
  bytes result = 3;
  bytes trace = 4;
  string simulate_id = 5;
}

message SimulateResponse {
  string id = 1;
  string created_at = 2;
  string name = 3;
  repeated Node nodes = 4;
  repeated Edge edges = 5;
  bytes context = 6;
  SimulateOutput simulate_output = 7;
  string organization_id = 8;
  string organization_name = 9;
  string admin_id = 10;
  string version_id = 11;
  string version_name = 12;
  string access_token = 13;
}

message SimulatePaginatedData {
  int64 page = 1;
  int64 per_page = 2;
  int64 total = 3;
  int64 total_pages = 4;
  repeated SimulateResponse rows = 5;
}

message SimulateDelete {
  string id = 1;
}

message SimulateGet {
  string id = 1;
}

message SimulateByID {
  string token = 1;
  bytes context = 2;
  string domain = 3;
}

message SimulatePageRequest {
  int64 page = 1;
  int64 per_page = 2;
  string version_id = 3;
}

message SimulateGeneralResponse {
  string message = 1;
}

message SimulateRequestContent {
  repeated Node nodes = 1;
  repeated Edge edges = 2;
}