syntax = "proto3";
package log;
option go_package = "./grpc";

service LogService {
  rpc GetLog(LogRequestDTO) returns (LogDTO);
  rpc GetLogs(LogPageRequestDTO) returns (LogPaginatedData);
  rpc GetSimulateLog(LogSimulateRequestDTO) returns (LogPaginatedData);
  rpc GetSingleSimulateLog(LogSingleSimulateLog) returns (LogSimulateLog);
}

message LogRequestDTO {
  string id = 1;
}

message LogDTO {
  string id = 1;
  string title = 2;
  string entity = 3;
  string entity_id = 4;
  string type = 5;
  string ip = 6;
  string proto = 7;
  string user = 8;
  string admin = 9;
  string message = 10;
  string organization = 11;
  string created_at = 12;
}

message LogListItem {
  string id = 1;
  string message = 2;
  string type = 3;
  string proto = 4;
  string created_at = 5;
  string simulate_id = 6;
  string simulate_name = 7;
  string organization_id = 8;
}

message LogPaginatedData {
  int64 page = 1;
  int64 per_page = 2;
  int64 total = 3;
  int64 total_pages = 4;
  repeated LogListItem rows = 5;
}

message LogPageRequestDTO {
  int64 page = 1;
  int64 per_page = 2;
  string entity = 3;
  int64 entityID = 4;
}

message LogSimulateRequestDTO {
  int64 page = 1;
  int64 per_page = 2;
  string simulate_id = 3;
  string application = 4;
}

message LogSingleSimulateLog {
  string log_id = 1;
}

message LogSimulateLog {
  string id = 1;
  string simulate_id = 2;
  string simulate_name = 3;
  string organization_id = 4;
  string organization_name = 5;
  string type = 6;
  string proto = 7;
  string created_at = 8;
  string message = 9;
  bytes request = 10;
  bytes response = 11;
}