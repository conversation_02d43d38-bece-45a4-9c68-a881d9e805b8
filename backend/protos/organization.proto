syntax = "proto3";
package organization;
option go_package = "./grpc";

// import "user.proto";

service OrganizationService {
    rpc Get(OrganizationEmptyDTO) returns (Organization);
    rpc Create(CreateOrganization) returns (Organization);
    rpc ChangeName(OrganizationChangeName) returns (OrganizationEmptyDTO);
    // rpc AddUser(AddUserToOrganization) returns (user.UserDTO);
    rpc RemoveUser(RemoveUserFromOrganization) returns (OrganizationEmptyDTO);
}

message OrganizationEmptyDTO {}

message Organization {
    string id = 1;
    string created_at= 2;
    string name = 3;
    int64 simulate_count = 4;
    // repeated user.UserDTO users = 6;
}

message OrganizationChangeName {
    string id = 1;
    string name = 2;
}

message AddUserToOrganization {
    string user_id = 1;
    string organization_id= 2;
}

message RemoveUserFromOrganization {
    string user_id = 1;
    string organization_id= 2;
}

message CreateOrganization {
    string name = 1;
}
