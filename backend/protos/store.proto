syntax = "proto3";
package guru.pars.store.v1;
option go_package = "./grpc/store/v1";

service StoreService {
    rpc Insert (StoreInsertRequest) returns (StoreResult);
    rpc Update (StoreUpdateRequest) returns (StoreResult);
    rpc Delete (StoreDeleteRequest) returns (StoreResult);
    rpc Select (StoreConditionRequest) returns (StoreResult);
    rpc Upsert (StoreUpsertRequest) returns (StoreResult);
    rpc DbCreate (StoreDbCreateRequest) returns (StoreResponse);
    rpc DbDrop (StoreDbDropRequest) returns (StoreResponse);
    rpc DbList (StoreDbListRequest) returns (StoreResponse);
    rpc TableCreate (StoreTableCreateRequest) returns (StoreResponse);
    rpc TableDrop (StoreTableDropRequest) returns (StoreResponse);
    rpc TableList (StoreTableListRequest) returns (StoreResponse);
    rpc ColumnCreate (StoreColumnCreateRequest) returns (StoreResponse);
    rpc ColumnDrop (StoreColumnDropRequest) returns (StoreResponse);
    rpc ColumnList (StoreColumnListRequest) returns (StoreColumnListResponse);
}

message StoreConditionRequest {
    string db = 1;
    string table = 2;
    repeated Kv kv = 3;
    int64 limit = 4;
    int64 offset = 5;
    string order_by = 6;
    string group_by = 7;
    string raw_query = 8;
}

message Kv {
    string k = 1;
    string v = 2;
}

message StoreRow {
    repeated Kv kv = 1;
}

message StoreResult {
    bool success = 1;
    string message = 2;
    int64 affected_rows = 3;
    repeated StoreRow store_rows = 4;
}

message StoreInsertRequest {
    string db = 1;
    string table = 2;
    repeated Kv kv = 3;
}

message StoreUpdateRequest {
    string db = 1;
    string table =  2;
    repeated Kv kv = 3;
}

message StoreUpsertRequest {
    string db = 1;
    string table = 2;
    repeated Kv kv = 3;
}

message StoreDeleteRequest {
    string db =1;
    string table = 2;
    repeated Kv kv = 3;
    bool solft_delete = 4;
}

message StoreDbCreateRequest {
    string db = 1;
    string table = 2;
    repeated Kv kv = 3;
}

message StoreDbDropRequest {
    string db = 1;
}



message StoreTableCreateRequest {
    string db = 1;
    string table = 2;
    repeated Kv kv = 3;
}

message StoreTableDropRequest {
    string db = 1;
    string table = 2;
}


message StoreDbListRequest {
    string db = 1;
}

message StoreTableListRequest {
    string db = 1;
}

message StoreTableListResponse {
    repeated string tables = 1;
}

message StoreResponse {
    bool success = 1;
    string message = 2;
}

message StoreColumnCreateRequest {
    string db = 1;
    string table = 2;
    string column = 3;
}

message StoreColumnDropRequest {
    string db = 1;
    string table = 2;
    string column = 3;
}

message StoreColumnListRequest {
    string db = 1;
    string table = 2;
}


message StoreColumnListResponse {
    repeated string columns = 1;
}
