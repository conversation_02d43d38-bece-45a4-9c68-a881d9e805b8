syntax = "proto3";
package user;
option go_package = "./grpc";

service UserService {
    rpc Get(UserEmptyDTO) returns (UserDTO);
    rpc LeaveOrganization(UserEmptyDTO) returns (UserEmptyDTO);
    rpc ChangeOrganizationAllow(ChangeOrganizationAllowDTO) returns (UserEmptyDTO);
}

message UserEmptyDTO{}

message UserDTO {
    string id = 1;
    string name = 2;
    string email = 3;
    string organization_id = 4;
    string organization_name = 5;
    bool allow_organization_change = 6;
}

message ChangeOrganizationAllowDTO {
    bool allow_organization_change = 1;
}

