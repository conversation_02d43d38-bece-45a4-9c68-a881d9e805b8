// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: protos/organization.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OrganizationEmptyDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OrganizationEmptyDTO) Reset() {
	*x = OrganizationEmptyDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_organization_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrganizationEmptyDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrganizationEmptyDTO) ProtoMessage() {}

func (x *OrganizationEmptyDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_organization_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrganizationEmptyDTO.ProtoReflect.Descriptor instead.
func (*OrganizationEmptyDTO) Descriptor() ([]byte, []int) {
	return file_protos_organization_proto_rawDescGZIP(), []int{0}
}

type Organization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt     string `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	SimulateCount int64  `protobuf:"varint,4,opt,name=simulate_count,json=simulateCount,proto3" json:"simulate_count,omitempty"` // repeated user.UserDTO users = 6;
}

func (x *Organization) Reset() {
	*x = Organization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_organization_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Organization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Organization) ProtoMessage() {}

func (x *Organization) ProtoReflect() protoreflect.Message {
	mi := &file_protos_organization_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Organization.ProtoReflect.Descriptor instead.
func (*Organization) Descriptor() ([]byte, []int) {
	return file_protos_organization_proto_rawDescGZIP(), []int{1}
}

func (x *Organization) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Organization) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Organization) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Organization) GetSimulateCount() int64 {
	if x != nil {
		return x.SimulateCount
	}
	return 0
}

type OrganizationChangeName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *OrganizationChangeName) Reset() {
	*x = OrganizationChangeName{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_organization_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrganizationChangeName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrganizationChangeName) ProtoMessage() {}

func (x *OrganizationChangeName) ProtoReflect() protoreflect.Message {
	mi := &file_protos_organization_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrganizationChangeName.ProtoReflect.Descriptor instead.
func (*OrganizationChangeName) Descriptor() ([]byte, []int) {
	return file_protos_organization_proto_rawDescGZIP(), []int{2}
}

func (x *OrganizationChangeName) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OrganizationChangeName) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type AddUserToOrganization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId         string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrganizationId string `protobuf:"bytes,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
}

func (x *AddUserToOrganization) Reset() {
	*x = AddUserToOrganization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_organization_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddUserToOrganization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserToOrganization) ProtoMessage() {}

func (x *AddUserToOrganization) ProtoReflect() protoreflect.Message {
	mi := &file_protos_organization_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserToOrganization.ProtoReflect.Descriptor instead.
func (*AddUserToOrganization) Descriptor() ([]byte, []int) {
	return file_protos_organization_proto_rawDescGZIP(), []int{3}
}

func (x *AddUserToOrganization) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AddUserToOrganization) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type RemoveUserFromOrganization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId         string `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrganizationId string `protobuf:"bytes,2,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
}

func (x *RemoveUserFromOrganization) Reset() {
	*x = RemoveUserFromOrganization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_organization_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveUserFromOrganization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveUserFromOrganization) ProtoMessage() {}

func (x *RemoveUserFromOrganization) ProtoReflect() protoreflect.Message {
	mi := &file_protos_organization_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveUserFromOrganization.ProtoReflect.Descriptor instead.
func (*RemoveUserFromOrganization) Descriptor() ([]byte, []int) {
	return file_protos_organization_proto_rawDescGZIP(), []int{4}
}

func (x *RemoveUserFromOrganization) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RemoveUserFromOrganization) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type CreateOrganization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CreateOrganization) Reset() {
	*x = CreateOrganization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_organization_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOrganization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrganization) ProtoMessage() {}

func (x *CreateOrganization) ProtoReflect() protoreflect.Message {
	mi := &file_protos_organization_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrganization.ProtoReflect.Descriptor instead.
func (*CreateOrganization) Descriptor() ([]byte, []int) {
	return file_protos_organization_proto_rawDescGZIP(), []int{5}
}

func (x *CreateOrganization) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_protos_organization_proto protoreflect.FileDescriptor

var file_protos_organization_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x16, 0x0a, 0x14, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x54,
	0x4f, 0x22, 0x78, 0x0a, 0x0c, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3c, 0x0a, 0x16, 0x4f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x59, 0x0a, 0x15, 0x41, 0x64, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x54, 0x6f, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x22, 0x5e, 0x0a, 0x1a, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x22, 0x28, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x32, 0xd8,
	0x02, 0x0a, 0x13, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x45, 0x0a, 0x03, 0x47, 0x65, 0x74, 0x12, 0x22, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x54,
	0x4f, 0x1a, 0x1a, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a,
	0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x20, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x1a, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x0a, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x24, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x22, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x54, 0x4f, 0x12, 0x5a, 0x0a,
	0x0a, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x28, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x72, 0x6f, 0x6d, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x22, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x54, 0x4f, 0x42, 0x08, 0x5a, 0x06, 0x2e, 0x2f, 0x67,
	0x72, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_organization_proto_rawDescOnce sync.Once
	file_protos_organization_proto_rawDescData = file_protos_organization_proto_rawDesc
)

func file_protos_organization_proto_rawDescGZIP() []byte {
	file_protos_organization_proto_rawDescOnce.Do(func() {
		file_protos_organization_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_organization_proto_rawDescData)
	})
	return file_protos_organization_proto_rawDescData
}

var file_protos_organization_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_protos_organization_proto_goTypes = []any{
	(*OrganizationEmptyDTO)(nil),       // 0: organization.OrganizationEmptyDTO
	(*Organization)(nil),               // 1: organization.Organization
	(*OrganizationChangeName)(nil),     // 2: organization.OrganizationChangeName
	(*AddUserToOrganization)(nil),      // 3: organization.AddUserToOrganization
	(*RemoveUserFromOrganization)(nil), // 4: organization.RemoveUserFromOrganization
	(*CreateOrganization)(nil),         // 5: organization.CreateOrganization
}
var file_protos_organization_proto_depIdxs = []int32{
	0, // 0: organization.OrganizationService.Get:input_type -> organization.OrganizationEmptyDTO
	5, // 1: organization.OrganizationService.Create:input_type -> organization.CreateOrganization
	2, // 2: organization.OrganizationService.ChangeName:input_type -> organization.OrganizationChangeName
	4, // 3: organization.OrganizationService.RemoveUser:input_type -> organization.RemoveUserFromOrganization
	1, // 4: organization.OrganizationService.Get:output_type -> organization.Organization
	1, // 5: organization.OrganizationService.Create:output_type -> organization.Organization
	0, // 6: organization.OrganizationService.ChangeName:output_type -> organization.OrganizationEmptyDTO
	0, // 7: organization.OrganizationService.RemoveUser:output_type -> organization.OrganizationEmptyDTO
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_protos_organization_proto_init() }
func file_protos_organization_proto_init() {
	if File_protos_organization_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_organization_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*OrganizationEmptyDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_organization_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Organization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_organization_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*OrganizationChangeName); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_organization_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*AddUserToOrganization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_organization_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*RemoveUserFromOrganization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_organization_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*CreateOrganization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_organization_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_organization_proto_goTypes,
		DependencyIndexes: file_protos_organization_proto_depIdxs,
		MessageInfos:      file_protos_organization_proto_msgTypes,
	}.Build()
	File_protos_organization_proto = out.File
	file_protos_organization_proto_rawDesc = nil
	file_protos_organization_proto_goTypes = nil
	file_protos_organization_proto_depIdxs = nil
}
