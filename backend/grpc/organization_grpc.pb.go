// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: protos/organization.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	OrganizationService_Get_FullMethodName        = "/organization.OrganizationService/Get"
	OrganizationService_Create_FullMethodName     = "/organization.OrganizationService/Create"
	OrganizationService_ChangeName_FullMethodName = "/organization.OrganizationService/ChangeName"
	OrganizationService_RemoveUser_FullMethodName = "/organization.OrganizationService/RemoveUser"
)

// OrganizationServiceClient is the client API for OrganizationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OrganizationServiceClient interface {
	Get(ctx context.Context, in *OrganizationEmptyDTO, opts ...grpc.CallOption) (*Organization, error)
	Create(ctx context.Context, in *CreateOrganization, opts ...grpc.CallOption) (*Organization, error)
	ChangeName(ctx context.Context, in *OrganizationChangeName, opts ...grpc.CallOption) (*OrganizationEmptyDTO, error)
	// rpc AddUser(AddUserToOrganization) returns (user.UserDTO);
	RemoveUser(ctx context.Context, in *RemoveUserFromOrganization, opts ...grpc.CallOption) (*OrganizationEmptyDTO, error)
}

type organizationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrganizationServiceClient(cc grpc.ClientConnInterface) OrganizationServiceClient {
	return &organizationServiceClient{cc}
}

func (c *organizationServiceClient) Get(ctx context.Context, in *OrganizationEmptyDTO, opts ...grpc.CallOption) (*Organization, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Organization)
	err := c.cc.Invoke(ctx, OrganizationService_Get_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) Create(ctx context.Context, in *CreateOrganization, opts ...grpc.CallOption) (*Organization, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Organization)
	err := c.cc.Invoke(ctx, OrganizationService_Create_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) ChangeName(ctx context.Context, in *OrganizationChangeName, opts ...grpc.CallOption) (*OrganizationEmptyDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrganizationEmptyDTO)
	err := c.cc.Invoke(ctx, OrganizationService_ChangeName_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *organizationServiceClient) RemoveUser(ctx context.Context, in *RemoveUserFromOrganization, opts ...grpc.CallOption) (*OrganizationEmptyDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrganizationEmptyDTO)
	err := c.cc.Invoke(ctx, OrganizationService_RemoveUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrganizationServiceServer is the server API for OrganizationService service.
// All implementations must embed UnimplementedOrganizationServiceServer
// for forward compatibility.
type OrganizationServiceServer interface {
	Get(context.Context, *OrganizationEmptyDTO) (*Organization, error)
	Create(context.Context, *CreateOrganization) (*Organization, error)
	ChangeName(context.Context, *OrganizationChangeName) (*OrganizationEmptyDTO, error)
	// rpc AddUser(AddUserToOrganization) returns (user.UserDTO);
	RemoveUser(context.Context, *RemoveUserFromOrganization) (*OrganizationEmptyDTO, error)
	mustEmbedUnimplementedOrganizationServiceServer()
}

// UnimplementedOrganizationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOrganizationServiceServer struct{}

func (UnimplementedOrganizationServiceServer) Get(context.Context, *OrganizationEmptyDTO) (*Organization, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Get not implemented")
}
func (UnimplementedOrganizationServiceServer) Create(context.Context, *CreateOrganization) (*Organization, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedOrganizationServiceServer) ChangeName(context.Context, *OrganizationChangeName) (*OrganizationEmptyDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeName not implemented")
}
func (UnimplementedOrganizationServiceServer) RemoveUser(context.Context, *RemoveUserFromOrganization) (*OrganizationEmptyDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveUser not implemented")
}
func (UnimplementedOrganizationServiceServer) mustEmbedUnimplementedOrganizationServiceServer() {}
func (UnimplementedOrganizationServiceServer) testEmbeddedByValue()                             {}

// UnsafeOrganizationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrganizationServiceServer will
// result in compilation errors.
type UnsafeOrganizationServiceServer interface {
	mustEmbedUnimplementedOrganizationServiceServer()
}

func RegisterOrganizationServiceServer(s grpc.ServiceRegistrar, srv OrganizationServiceServer) {
	// If the following call pancis, it indicates UnimplementedOrganizationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OrganizationService_ServiceDesc, srv)
}

func _OrganizationService_Get_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrganizationEmptyDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).Get(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_Get_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).Get(ctx, req.(*OrganizationEmptyDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrganization)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_Create_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).Create(ctx, req.(*CreateOrganization))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_ChangeName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrganizationChangeName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).ChangeName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_ChangeName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).ChangeName(ctx, req.(*OrganizationChangeName))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrganizationService_RemoveUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveUserFromOrganization)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrganizationServiceServer).RemoveUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrganizationService_RemoveUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrganizationServiceServer).RemoveUser(ctx, req.(*RemoveUserFromOrganization))
	}
	return interceptor(ctx, in, info, handler)
}

// OrganizationService_ServiceDesc is the grpc.ServiceDesc for OrganizationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrganizationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "organization.OrganizationService",
	HandlerType: (*OrganizationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Get",
			Handler:    _OrganizationService_Get_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _OrganizationService_Create_Handler,
		},
		{
			MethodName: "ChangeName",
			Handler:    _OrganizationService_ChangeName_Handler,
		},
		{
			MethodName: "RemoveUser",
			Handler:    _OrganizationService_RemoveUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/organization.proto",
}
