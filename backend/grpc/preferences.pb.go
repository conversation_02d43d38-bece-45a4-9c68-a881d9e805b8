// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: protos/preferences.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DSNEmptyDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DSNEmptyDTO) Reset() {
	*x = DSNEmptyDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_preferences_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DSNEmptyDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DSNEmptyDTO) ProtoMessage() {}

func (x *DSNEmptyDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_preferences_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DSNEmptyDTO.ProtoReflect.Descriptor instead.
func (*DSNEmptyDTO) Descriptor() ([]byte, []int) {
	return file_protos_preferences_proto_rawDescGZIP(), []int{0}
}

type DSNAddDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Key      string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Dsn      string `protobuf:"bytes,2,opt,name=dsn,proto3" json:"dsn,omitempty"`
	DbEngine int64  `protobuf:"varint,3,opt,name=db_engine,json=dbEngine,proto3" json:"db_engine,omitempty"`
}

func (x *DSNAddDTO) Reset() {
	*x = DSNAddDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_preferences_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DSNAddDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DSNAddDTO) ProtoMessage() {}

func (x *DSNAddDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_preferences_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DSNAddDTO.ProtoReflect.Descriptor instead.
func (*DSNAddDTO) Descriptor() ([]byte, []int) {
	return file_protos_preferences_proto_rawDescGZIP(), []int{1}
}

func (x *DSNAddDTO) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *DSNAddDTO) GetDsn() string {
	if x != nil {
		return x.Dsn
	}
	return ""
}

func (x *DSNAddDTO) GetDbEngine() int64 {
	if x != nil {
		return x.DbEngine
	}
	return 0
}

type DSNGetDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                 string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt          string `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Key                string `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	Dsn                string `protobuf:"bytes,4,opt,name=dsn,proto3" json:"dsn,omitempty"`
	LastDsnCheckAt     string `protobuf:"bytes,7,opt,name=last_dsn_check_at,json=lastDsnCheckAt,proto3" json:"last_dsn_check_at,omitempty"`
	DbEngine           int64  `protobuf:"varint,5,opt,name=db_engine,json=dbEngine,proto3" json:"db_engine,omitempty"`
	LastDsnCheckStatus int64  `protobuf:"varint,6,opt,name=last_dsn_check_status,json=lastDsnCheckStatus,proto3" json:"last_dsn_check_status,omitempty"`
}

func (x *DSNGetDTO) Reset() {
	*x = DSNGetDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_preferences_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DSNGetDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DSNGetDTO) ProtoMessage() {}

func (x *DSNGetDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_preferences_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DSNGetDTO.ProtoReflect.Descriptor instead.
func (*DSNGetDTO) Descriptor() ([]byte, []int) {
	return file_protos_preferences_proto_rawDescGZIP(), []int{2}
}

func (x *DSNGetDTO) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DSNGetDTO) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *DSNGetDTO) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *DSNGetDTO) GetDsn() string {
	if x != nil {
		return x.Dsn
	}
	return ""
}

func (x *DSNGetDTO) GetLastDsnCheckAt() string {
	if x != nil {
		return x.LastDsnCheckAt
	}
	return ""
}

func (x *DSNGetDTO) GetDbEngine() int64 {
	if x != nil {
		return x.DbEngine
	}
	return 0
}

func (x *DSNGetDTO) GetLastDsnCheckStatus() int64 {
	if x != nil {
		return x.LastDsnCheckStatus
	}
	return 0
}

type DSNGetResponseDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dsns []*DSNGetDTO `protobuf:"bytes,1,rep,name=dsns,proto3" json:"dsns,omitempty"`
}

func (x *DSNGetResponseDTO) Reset() {
	*x = DSNGetResponseDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_preferences_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DSNGetResponseDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DSNGetResponseDTO) ProtoMessage() {}

func (x *DSNGetResponseDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_preferences_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DSNGetResponseDTO.ProtoReflect.Descriptor instead.
func (*DSNGetResponseDTO) Descriptor() ([]byte, []int) {
	return file_protos_preferences_proto_rawDescGZIP(), []int{3}
}

func (x *DSNGetResponseDTO) GetDsns() []*DSNGetDTO {
	if x != nil {
		return x.Dsns
	}
	return nil
}

type DSNResponseDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *DSNResponseDTO) Reset() {
	*x = DSNResponseDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_preferences_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DSNResponseDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DSNResponseDTO) ProtoMessage() {}

func (x *DSNResponseDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_preferences_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DSNResponseDTO.ProtoReflect.Descriptor instead.
func (*DSNResponseDTO) Descriptor() ([]byte, []int) {
	return file_protos_preferences_proto_rawDescGZIP(), []int{4}
}

func (x *DSNResponseDTO) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DSNDeleteDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DSNDeleteDTO) Reset() {
	*x = DSNDeleteDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_preferences_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DSNDeleteDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DSNDeleteDTO) ProtoMessage() {}

func (x *DSNDeleteDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_preferences_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DSNDeleteDTO.ProtoReflect.Descriptor instead.
func (*DSNDeleteDTO) Descriptor() ([]byte, []int) {
	return file_protos_preferences_proto_rawDescGZIP(), []int{5}
}

func (x *DSNDeleteDTO) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

var File_protos_preferences_proto protoreflect.FileDescriptor

var file_protos_preferences_proto_rawDesc = []byte{
	0x0a, 0x18, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x22, 0x0d, 0x0a, 0x0b, 0x44, 0x53, 0x4e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x44, 0x54, 0x4f, 0x22, 0x4c, 0x0a, 0x09, 0x44, 0x53, 0x4e, 0x41, 0x64, 0x64,
	0x44, 0x54, 0x4f, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x73, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x64, 0x73, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x62, 0x5f, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x62, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x22, 0xd9, 0x01, 0x0a, 0x09, 0x44, 0x53, 0x4e, 0x47, 0x65, 0x74, 0x44,
	0x54, 0x4f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x73, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x64, 0x73, 0x6e, 0x12, 0x29, 0x0a, 0x11, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x73,
	0x6e, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x44, 0x73, 0x6e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x74,
	0x12, 0x1b, 0x0a, 0x09, 0x64, 0x62, 0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x62, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x31, 0x0a,
	0x15, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x73, 0x6e, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x6c, 0x61,
	0x73, 0x74, 0x44, 0x73, 0x6e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x3f, 0x0a, 0x11, 0x44, 0x53, 0x4e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x2a, 0x0a, 0x04, 0x64, 0x73, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x73, 0x2e, 0x44, 0x53, 0x4e, 0x47, 0x65, 0x74, 0x44, 0x54, 0x4f, 0x52, 0x04, 0x64, 0x73, 0x6e,
	0x73, 0x22, 0x2a, 0x0a, 0x0e, 0x44, 0x53, 0x4e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x44, 0x54, 0x4f, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x1e, 0x0a,
	0x0c, 0x44, 0x53, 0x4e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x32, 0xdc, 0x01,
	0x0a, 0x12, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x3d, 0x0a, 0x06, 0x41, 0x64, 0x64, 0x44, 0x53, 0x4e, 0x12, 0x16,
	0x2e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x2e, 0x44, 0x53, 0x4e,
	0x41, 0x64, 0x64, 0x44, 0x54, 0x4f, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x2e, 0x44, 0x53, 0x4e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x44, 0x54, 0x4f, 0x12, 0x42, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x44, 0x53, 0x4e, 0x12, 0x18, 0x2e,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x2e, 0x44, 0x53, 0x4e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x44, 0x54, 0x4f, 0x1a, 0x1e, 0x2e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x2e, 0x44, 0x53, 0x4e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x43, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x44, 0x53, 0x4e, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x73, 0x2e, 0x44, 0x53, 0x4e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x54, 0x4f, 0x1a,
	0x1b, 0x2e, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x2e, 0x44, 0x53,
	0x4e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f, 0x42, 0x08, 0x5a, 0x06,
	0x2e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_preferences_proto_rawDescOnce sync.Once
	file_protos_preferences_proto_rawDescData = file_protos_preferences_proto_rawDesc
)

func file_protos_preferences_proto_rawDescGZIP() []byte {
	file_protos_preferences_proto_rawDescOnce.Do(func() {
		file_protos_preferences_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_preferences_proto_rawDescData)
	})
	return file_protos_preferences_proto_rawDescData
}

var file_protos_preferences_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_protos_preferences_proto_goTypes = []any{
	(*DSNEmptyDTO)(nil),       // 0: preferences.DSNEmptyDTO
	(*DSNAddDTO)(nil),         // 1: preferences.DSNAddDTO
	(*DSNGetDTO)(nil),         // 2: preferences.DSNGetDTO
	(*DSNGetResponseDTO)(nil), // 3: preferences.DSNGetResponseDTO
	(*DSNResponseDTO)(nil),    // 4: preferences.DSNResponseDTO
	(*DSNDeleteDTO)(nil),      // 5: preferences.DSNDeleteDTO
}
var file_protos_preferences_proto_depIdxs = []int32{
	2, // 0: preferences.DSNGetResponseDTO.dsns:type_name -> preferences.DSNGetDTO
	1, // 1: preferences.PreferencesService.AddDSN:input_type -> preferences.DSNAddDTO
	0, // 2: preferences.PreferencesService.GetDSN:input_type -> preferences.DSNEmptyDTO
	5, // 3: preferences.PreferencesService.DeleteDSN:input_type -> preferences.DSNDeleteDTO
	4, // 4: preferences.PreferencesService.AddDSN:output_type -> preferences.DSNResponseDTO
	3, // 5: preferences.PreferencesService.GetDSN:output_type -> preferences.DSNGetResponseDTO
	4, // 6: preferences.PreferencesService.DeleteDSN:output_type -> preferences.DSNResponseDTO
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_protos_preferences_proto_init() }
func file_protos_preferences_proto_init() {
	if File_protos_preferences_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_preferences_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*DSNEmptyDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_preferences_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*DSNAddDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_preferences_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*DSNGetDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_preferences_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*DSNGetResponseDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_preferences_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*DSNResponseDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_preferences_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DSNDeleteDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_preferences_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_preferences_proto_goTypes,
		DependencyIndexes: file_protos_preferences_proto_depIdxs,
		MessageInfos:      file_protos_preferences_proto_msgTypes,
	}.Build()
	File_protos_preferences_proto = out.File
	file_protos_preferences_proto_rawDesc = nil
	file_protos_preferences_proto_goTypes = nil
	file_protos_preferences_proto_depIdxs = nil
}
