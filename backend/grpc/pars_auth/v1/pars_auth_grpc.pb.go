// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: protos/pars_auth.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ParsAuthService_Login_FullMethodName        = "/com.tapsilat.ParsAuth.v1.ParsAuthService/Login"
	ParsAuthService_Register_FullMethodName     = "/com.tapsilat.ParsAuth.v1.ParsAuthService/Register"
	ParsAuthService_TokenRefresh_FullMethodName = "/com.tapsilat.ParsAuth.v1.ParsAuthService/TokenRefresh"
	ParsAuthService_HealthCheck_FullMethodName  = "/com.tapsilat.ParsAuth.v1.ParsAuthService/HealthCheck"
)

// ParsAuthServiceClient is the client API for ParsAuthService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ParsAuthServiceClient interface {
	Login(ctx context.Context, in *AuthLoginDTO, opts ...grpc.CallOption) (*AuthResponseDTO, error)
	Register(ctx context.Context, in *AuthRegisterDTO, opts ...grpc.CallOption) (*AuthResponseDTO, error)
	TokenRefresh(ctx context.Context, in *AuthEmptyDTO, opts ...grpc.CallOption) (*AuthResponseDTO, error)
	HealthCheck(ctx context.Context, in *AuthEmptyDTO, opts ...grpc.CallOption) (*AuthEmptyDTO, error)
}

type parsAuthServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewParsAuthServiceClient(cc grpc.ClientConnInterface) ParsAuthServiceClient {
	return &parsAuthServiceClient{cc}
}

func (c *parsAuthServiceClient) Login(ctx context.Context, in *AuthLoginDTO, opts ...grpc.CallOption) (*AuthResponseDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AuthResponseDTO)
	err := c.cc.Invoke(ctx, ParsAuthService_Login_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsAuthServiceClient) Register(ctx context.Context, in *AuthRegisterDTO, opts ...grpc.CallOption) (*AuthResponseDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AuthResponseDTO)
	err := c.cc.Invoke(ctx, ParsAuthService_Register_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsAuthServiceClient) TokenRefresh(ctx context.Context, in *AuthEmptyDTO, opts ...grpc.CallOption) (*AuthResponseDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AuthResponseDTO)
	err := c.cc.Invoke(ctx, ParsAuthService_TokenRefresh_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsAuthServiceClient) HealthCheck(ctx context.Context, in *AuthEmptyDTO, opts ...grpc.CallOption) (*AuthEmptyDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AuthEmptyDTO)
	err := c.cc.Invoke(ctx, ParsAuthService_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ParsAuthServiceServer is the server API for ParsAuthService service.
// All implementations must embed UnimplementedParsAuthServiceServer
// for forward compatibility.
type ParsAuthServiceServer interface {
	Login(context.Context, *AuthLoginDTO) (*AuthResponseDTO, error)
	Register(context.Context, *AuthRegisterDTO) (*AuthResponseDTO, error)
	TokenRefresh(context.Context, *AuthEmptyDTO) (*AuthResponseDTO, error)
	HealthCheck(context.Context, *AuthEmptyDTO) (*AuthEmptyDTO, error)
	mustEmbedUnimplementedParsAuthServiceServer()
}

// UnimplementedParsAuthServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedParsAuthServiceServer struct{}

func (UnimplementedParsAuthServiceServer) Login(context.Context, *AuthLoginDTO) (*AuthResponseDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedParsAuthServiceServer) Register(context.Context, *AuthRegisterDTO) (*AuthResponseDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Register not implemented")
}
func (UnimplementedParsAuthServiceServer) TokenRefresh(context.Context, *AuthEmptyDTO) (*AuthResponseDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TokenRefresh not implemented")
}
func (UnimplementedParsAuthServiceServer) HealthCheck(context.Context, *AuthEmptyDTO) (*AuthEmptyDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedParsAuthServiceServer) mustEmbedUnimplementedParsAuthServiceServer() {}
func (UnimplementedParsAuthServiceServer) testEmbeddedByValue()                         {}

// UnsafeParsAuthServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ParsAuthServiceServer will
// result in compilation errors.
type UnsafeParsAuthServiceServer interface {
	mustEmbedUnimplementedParsAuthServiceServer()
}

func RegisterParsAuthServiceServer(s grpc.ServiceRegistrar, srv ParsAuthServiceServer) {
	// If the following call pancis, it indicates UnimplementedParsAuthServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ParsAuthService_ServiceDesc, srv)
}

func _ParsAuthService_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthLoginDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsAuthServiceServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsAuthService_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsAuthServiceServer).Login(ctx, req.(*AuthLoginDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsAuthService_Register_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthRegisterDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsAuthServiceServer).Register(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsAuthService_Register_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsAuthServiceServer).Register(ctx, req.(*AuthRegisterDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsAuthService_TokenRefresh_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthEmptyDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsAuthServiceServer).TokenRefresh(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsAuthService_TokenRefresh_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsAuthServiceServer).TokenRefresh(ctx, req.(*AuthEmptyDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsAuthService_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthEmptyDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsAuthServiceServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsAuthService_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsAuthServiceServer).HealthCheck(ctx, req.(*AuthEmptyDTO))
	}
	return interceptor(ctx, in, info, handler)
}

// ParsAuthService_ServiceDesc is the grpc.ServiceDesc for ParsAuthService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ParsAuthService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.tapsilat.ParsAuth.v1.ParsAuthService",
	HandlerType: (*ParsAuthServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Login",
			Handler:    _ParsAuthService_Login_Handler,
		},
		{
			MethodName: "Register",
			Handler:    _ParsAuthService_Register_Handler,
		},
		{
			MethodName: "TokenRefresh",
			Handler:    _ParsAuthService_TokenRefresh_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _ParsAuthService_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/pars_auth.proto",
}
