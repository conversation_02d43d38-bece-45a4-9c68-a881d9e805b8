// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: protos/pars_auth.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthLoginDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email    string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *AuthLoginDTO) Reset() {
	*x = AuthLoginDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_auth_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthLoginDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthLoginDTO) ProtoMessage() {}

func (x *AuthLoginDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_auth_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthLoginDTO.ProtoReflect.Descriptor instead.
func (*AuthLoginDTO) Descriptor() ([]byte, []int) {
	return file_protos_pars_auth_proto_rawDescGZIP(), []int{0}
}

func (x *AuthLoginDTO) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AuthLoginDTO) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type AuthRegisterDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Email    string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *AuthRegisterDTO) Reset() {
	*x = AuthRegisterDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_auth_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthRegisterDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthRegisterDTO) ProtoMessage() {}

func (x *AuthRegisterDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_auth_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthRegisterDTO.ProtoReflect.Descriptor instead.
func (*AuthRegisterDTO) Descriptor() ([]byte, []int) {
	return file_protos_pars_auth_proto_rawDescGZIP(), []int{1}
}

func (x *AuthRegisterDTO) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AuthRegisterDTO) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *AuthRegisterDTO) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type AuthResponseDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *AuthResponseDTO) Reset() {
	*x = AuthResponseDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_auth_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthResponseDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthResponseDTO) ProtoMessage() {}

func (x *AuthResponseDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_auth_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthResponseDTO.ProtoReflect.Descriptor instead.
func (*AuthResponseDTO) Descriptor() ([]byte, []int) {
	return file_protos_pars_auth_proto_rawDescGZIP(), []int{2}
}

func (x *AuthResponseDTO) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type AuthEmptyDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AuthEmptyDTO) Reset() {
	*x = AuthEmptyDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_auth_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthEmptyDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthEmptyDTO) ProtoMessage() {}

func (x *AuthEmptyDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_auth_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthEmptyDTO.ProtoReflect.Descriptor instead.
func (*AuthEmptyDTO) Descriptor() ([]byte, []int) {
	return file_protos_pars_auth_proto_rawDescGZIP(), []int{3}
}

var File_protos_pars_auth_proto protoreflect.FileDescriptor

var file_protos_pars_auth_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x70, 0x61, 0x72, 0x73, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61,
	0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x41, 0x75, 0x74, 0x68, 0x2e,
	0x76, 0x31, 0x22, 0x40, 0x0a, 0x0c, 0x41, 0x75, 0x74, 0x68, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x44,
	0x54, 0x4f, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x22, 0x57, 0x0a, 0x0f, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x44, 0x54, 0x4f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x27, 0x0a,
	0x0f, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x0e, 0x0a, 0x0c, 0x41, 0x75, 0x74, 0x68, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x44, 0x54, 0x4f, 0x32, 0x91, 0x03, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x73, 0x41,
	0x75, 0x74, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5a, 0x0a, 0x05, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c,
	0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x41, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x44, 0x54, 0x4f, 0x1a, 0x29, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x41,
	0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x60, 0x0a, 0x08, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74,
	0x65, 0x72, 0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61,
	0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x41, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x44, 0x54, 0x4f, 0x1a, 0x29, 0x2e,
	0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72,
	0x73, 0x41, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x61, 0x0a, 0x0c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74,
	0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x41, 0x75, 0x74, 0x68,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x54, 0x4f,
	0x1a, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e,
	0x50, 0x61, 0x72, 0x73, 0x41, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x5d, 0x0a, 0x0b, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x41, 0x75,
	0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44,
	0x54, 0x4f, 0x1a, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61,
	0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x41, 0x75, 0x74, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75,
	0x74, 0x68, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x54, 0x4f, 0x42, 0x15, 0x5a, 0x13, 0x2e, 0x2f,
	0x67, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x72, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x76,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_pars_auth_proto_rawDescOnce sync.Once
	file_protos_pars_auth_proto_rawDescData = file_protos_pars_auth_proto_rawDesc
)

func file_protos_pars_auth_proto_rawDescGZIP() []byte {
	file_protos_pars_auth_proto_rawDescOnce.Do(func() {
		file_protos_pars_auth_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_pars_auth_proto_rawDescData)
	})
	return file_protos_pars_auth_proto_rawDescData
}

var file_protos_pars_auth_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_protos_pars_auth_proto_goTypes = []any{
	(*AuthLoginDTO)(nil),    // 0: com.tapsilat.ParsAuth.v1.AuthLoginDTO
	(*AuthRegisterDTO)(nil), // 1: com.tapsilat.ParsAuth.v1.AuthRegisterDTO
	(*AuthResponseDTO)(nil), // 2: com.tapsilat.ParsAuth.v1.AuthResponseDTO
	(*AuthEmptyDTO)(nil),    // 3: com.tapsilat.ParsAuth.v1.AuthEmptyDTO
}
var file_protos_pars_auth_proto_depIdxs = []int32{
	0, // 0: com.tapsilat.ParsAuth.v1.ParsAuthService.Login:input_type -> com.tapsilat.ParsAuth.v1.AuthLoginDTO
	1, // 1: com.tapsilat.ParsAuth.v1.ParsAuthService.Register:input_type -> com.tapsilat.ParsAuth.v1.AuthRegisterDTO
	3, // 2: com.tapsilat.ParsAuth.v1.ParsAuthService.TokenRefresh:input_type -> com.tapsilat.ParsAuth.v1.AuthEmptyDTO
	3, // 3: com.tapsilat.ParsAuth.v1.ParsAuthService.HealthCheck:input_type -> com.tapsilat.ParsAuth.v1.AuthEmptyDTO
	2, // 4: com.tapsilat.ParsAuth.v1.ParsAuthService.Login:output_type -> com.tapsilat.ParsAuth.v1.AuthResponseDTO
	2, // 5: com.tapsilat.ParsAuth.v1.ParsAuthService.Register:output_type -> com.tapsilat.ParsAuth.v1.AuthResponseDTO
	2, // 6: com.tapsilat.ParsAuth.v1.ParsAuthService.TokenRefresh:output_type -> com.tapsilat.ParsAuth.v1.AuthResponseDTO
	3, // 7: com.tapsilat.ParsAuth.v1.ParsAuthService.HealthCheck:output_type -> com.tapsilat.ParsAuth.v1.AuthEmptyDTO
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_protos_pars_auth_proto_init() }
func file_protos_pars_auth_proto_init() {
	if File_protos_pars_auth_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_pars_auth_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*AuthLoginDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_auth_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*AuthRegisterDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_auth_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*AuthResponseDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_auth_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*AuthEmptyDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_pars_auth_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_pars_auth_proto_goTypes,
		DependencyIndexes: file_protos_pars_auth_proto_depIdxs,
		MessageInfos:      file_protos_pars_auth_proto_msgTypes,
	}.Build()
	File_protos_pars_auth_proto = out.File
	file_protos_pars_auth_proto_rawDesc = nil
	file_protos_pars_auth_proto_goTypes = nil
	file_protos_pars_auth_proto_depIdxs = nil
}
