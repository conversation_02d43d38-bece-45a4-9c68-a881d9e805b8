// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: protos/log.proto

package grpc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LogRequestDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *LogRequestDTO) Reset() {
	*x = LogRequestDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_log_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogRequestDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogRequestDTO) ProtoMessage() {}

func (x *LogRequestDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_log_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogRequestDTO.ProtoReflect.Descriptor instead.
func (*LogRequestDTO) Descriptor() ([]byte, []int) {
	return file_protos_log_proto_rawDescGZIP(), []int{0}
}

func (x *LogRequestDTO) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type LogDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title        string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Entity       string `protobuf:"bytes,3,opt,name=entity,proto3" json:"entity,omitempty"`
	EntityId     string `protobuf:"bytes,4,opt,name=entity_id,json=entityId,proto3" json:"entity_id,omitempty"`
	Type         string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	Ip           string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`
	Proto        string `protobuf:"bytes,7,opt,name=proto,proto3" json:"proto,omitempty"`
	User         string `protobuf:"bytes,8,opt,name=user,proto3" json:"user,omitempty"`
	Admin        string `protobuf:"bytes,9,opt,name=admin,proto3" json:"admin,omitempty"`
	Message      string `protobuf:"bytes,10,opt,name=message,proto3" json:"message,omitempty"`
	Organization string `protobuf:"bytes,11,opt,name=organization,proto3" json:"organization,omitempty"`
	CreatedAt    string `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *LogDTO) Reset() {
	*x = LogDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_log_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogDTO) ProtoMessage() {}

func (x *LogDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_log_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogDTO.ProtoReflect.Descriptor instead.
func (*LogDTO) Descriptor() ([]byte, []int) {
	return file_protos_log_proto_rawDescGZIP(), []int{1}
}

func (x *LogDTO) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LogDTO) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *LogDTO) GetEntity() string {
	if x != nil {
		return x.Entity
	}
	return ""
}

func (x *LogDTO) GetEntityId() string {
	if x != nil {
		return x.EntityId
	}
	return ""
}

func (x *LogDTO) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *LogDTO) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *LogDTO) GetProto() string {
	if x != nil {
		return x.Proto
	}
	return ""
}

func (x *LogDTO) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *LogDTO) GetAdmin() string {
	if x != nil {
		return x.Admin
	}
	return ""
}

func (x *LogDTO) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LogDTO) GetOrganization() string {
	if x != nil {
		return x.Organization
	}
	return ""
}

func (x *LogDTO) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

type LogListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Message        string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Type           string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Proto          string `protobuf:"bytes,4,opt,name=proto,proto3" json:"proto,omitempty"`
	CreatedAt      string `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	SimulateId     string `protobuf:"bytes,6,opt,name=simulate_id,json=simulateId,proto3" json:"simulate_id,omitempty"`
	SimulateName   string `protobuf:"bytes,7,opt,name=simulate_name,json=simulateName,proto3" json:"simulate_name,omitempty"`
	OrganizationId string `protobuf:"bytes,8,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
}

func (x *LogListItem) Reset() {
	*x = LogListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_log_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogListItem) ProtoMessage() {}

func (x *LogListItem) ProtoReflect() protoreflect.Message {
	mi := &file_protos_log_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogListItem.ProtoReflect.Descriptor instead.
func (*LogListItem) Descriptor() ([]byte, []int) {
	return file_protos_log_proto_rawDescGZIP(), []int{2}
}

func (x *LogListItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LogListItem) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LogListItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *LogListItem) GetProto() string {
	if x != nil {
		return x.Proto
	}
	return ""
}

func (x *LogListItem) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *LogListItem) GetSimulateId() string {
	if x != nil {
		return x.SimulateId
	}
	return ""
}

func (x *LogListItem) GetSimulateName() string {
	if x != nil {
		return x.SimulateName
	}
	return ""
}

func (x *LogListItem) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

type LogPaginatedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int64          `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PerPage    int64          `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	Total      int64          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	TotalPages int64          `protobuf:"varint,4,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	Rows       []*LogListItem `protobuf:"bytes,5,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *LogPaginatedData) Reset() {
	*x = LogPaginatedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_log_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogPaginatedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogPaginatedData) ProtoMessage() {}

func (x *LogPaginatedData) ProtoReflect() protoreflect.Message {
	mi := &file_protos_log_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogPaginatedData.ProtoReflect.Descriptor instead.
func (*LogPaginatedData) Descriptor() ([]byte, []int) {
	return file_protos_log_proto_rawDescGZIP(), []int{3}
}

func (x *LogPaginatedData) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *LogPaginatedData) GetPerPage() int64 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *LogPaginatedData) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *LogPaginatedData) GetTotalPages() int64 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

func (x *LogPaginatedData) GetRows() []*LogListItem {
	if x != nil {
		return x.Rows
	}
	return nil
}

type LogPageRequestDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PerPage  int64  `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	Entity   string `protobuf:"bytes,3,opt,name=entity,proto3" json:"entity,omitempty"`
	EntityID int64  `protobuf:"varint,4,opt,name=entityID,proto3" json:"entityID,omitempty"`
}

func (x *LogPageRequestDTO) Reset() {
	*x = LogPageRequestDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_log_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogPageRequestDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogPageRequestDTO) ProtoMessage() {}

func (x *LogPageRequestDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_log_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogPageRequestDTO.ProtoReflect.Descriptor instead.
func (*LogPageRequestDTO) Descriptor() ([]byte, []int) {
	return file_protos_log_proto_rawDescGZIP(), []int{4}
}

func (x *LogPageRequestDTO) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *LogPageRequestDTO) GetPerPage() int64 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *LogPageRequestDTO) GetEntity() string {
	if x != nil {
		return x.Entity
	}
	return ""
}

func (x *LogPageRequestDTO) GetEntityID() int64 {
	if x != nil {
		return x.EntityID
	}
	return 0
}

type LogSimulateRequestDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page        int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PerPage     int64  `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	SimulateId  string `protobuf:"bytes,3,opt,name=simulate_id,json=simulateId,proto3" json:"simulate_id,omitempty"`
	Application string `protobuf:"bytes,4,opt,name=application,proto3" json:"application,omitempty"`
}

func (x *LogSimulateRequestDTO) Reset() {
	*x = LogSimulateRequestDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_log_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogSimulateRequestDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogSimulateRequestDTO) ProtoMessage() {}

func (x *LogSimulateRequestDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_log_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogSimulateRequestDTO.ProtoReflect.Descriptor instead.
func (*LogSimulateRequestDTO) Descriptor() ([]byte, []int) {
	return file_protos_log_proto_rawDescGZIP(), []int{5}
}

func (x *LogSimulateRequestDTO) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *LogSimulateRequestDTO) GetPerPage() int64 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *LogSimulateRequestDTO) GetSimulateId() string {
	if x != nil {
		return x.SimulateId
	}
	return ""
}

func (x *LogSimulateRequestDTO) GetApplication() string {
	if x != nil {
		return x.Application
	}
	return ""
}

type LogSingleSimulateLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LogId string `protobuf:"bytes,1,opt,name=log_id,json=logId,proto3" json:"log_id,omitempty"`
}

func (x *LogSingleSimulateLog) Reset() {
	*x = LogSingleSimulateLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_log_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogSingleSimulateLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogSingleSimulateLog) ProtoMessage() {}

func (x *LogSingleSimulateLog) ProtoReflect() protoreflect.Message {
	mi := &file_protos_log_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogSingleSimulateLog.ProtoReflect.Descriptor instead.
func (*LogSingleSimulateLog) Descriptor() ([]byte, []int) {
	return file_protos_log_proto_rawDescGZIP(), []int{6}
}

func (x *LogSingleSimulateLog) GetLogId() string {
	if x != nil {
		return x.LogId
	}
	return ""
}

type LogSimulateLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SimulateId       string `protobuf:"bytes,2,opt,name=simulate_id,json=simulateId,proto3" json:"simulate_id,omitempty"`
	SimulateName     string `protobuf:"bytes,3,opt,name=simulate_name,json=simulateName,proto3" json:"simulate_name,omitempty"`
	OrganizationId   string `protobuf:"bytes,4,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	OrganizationName string `protobuf:"bytes,5,opt,name=organization_name,json=organizationName,proto3" json:"organization_name,omitempty"`
	Type             string `protobuf:"bytes,6,opt,name=type,proto3" json:"type,omitempty"`
	Proto            string `protobuf:"bytes,7,opt,name=proto,proto3" json:"proto,omitempty"`
	CreatedAt        string `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Message          string `protobuf:"bytes,9,opt,name=message,proto3" json:"message,omitempty"`
	Request          []byte `protobuf:"bytes,10,opt,name=request,proto3" json:"request,omitempty"`
	Response         []byte `protobuf:"bytes,11,opt,name=response,proto3" json:"response,omitempty"`
}

func (x *LogSimulateLog) Reset() {
	*x = LogSimulateLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_log_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogSimulateLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogSimulateLog) ProtoMessage() {}

func (x *LogSimulateLog) ProtoReflect() protoreflect.Message {
	mi := &file_protos_log_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogSimulateLog.ProtoReflect.Descriptor instead.
func (*LogSimulateLog) Descriptor() ([]byte, []int) {
	return file_protos_log_proto_rawDescGZIP(), []int{7}
}

func (x *LogSimulateLog) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LogSimulateLog) GetSimulateId() string {
	if x != nil {
		return x.SimulateId
	}
	return ""
}

func (x *LogSimulateLog) GetSimulateName() string {
	if x != nil {
		return x.SimulateName
	}
	return ""
}

func (x *LogSimulateLog) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *LogSimulateLog) GetOrganizationName() string {
	if x != nil {
		return x.OrganizationName
	}
	return ""
}

func (x *LogSimulateLog) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *LogSimulateLog) GetProto() string {
	if x != nil {
		return x.Proto
	}
	return ""
}

func (x *LogSimulateLog) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *LogSimulateLog) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LogSimulateLog) GetRequest() []byte {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *LogSimulateLog) GetResponse() []byte {
	if x != nil {
		return x.Response
	}
	return nil
}

var File_protos_log_proto protoreflect.FileDescriptor

var file_protos_log_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x6c, 0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x03, 0x6c, 0x6f, 0x67, 0x22, 0x1f, 0x0a, 0x0d, 0x4c, 0x6f, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x54, 0x4f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xa4, 0x02, 0x0a, 0x06, 0x4c, 0x6f, 0x67,
	0x44, 0x54, 0x4f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0xef, 0x01, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0x9e, 0x01, 0x0a, 0x10, 0x4c, 0x6f, 0x67, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65,
	0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x65,
	0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x04,
	0x72, 0x6f, 0x77, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6c, 0x6f, 0x67,
	0x2e, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x72, 0x6f,
	0x77, 0x73, 0x22, 0x76, 0x0a, 0x11, 0x4c, 0x6f, 0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x54, 0x4f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70,
	0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70,
	0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1a,
	0x0a, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x44, 0x22, 0x89, 0x01, 0x0a, 0x15, 0x4c,
	0x6f, 0x67, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x44, 0x54, 0x4f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50,
	0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x2d, 0x0a, 0x14, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x15,
	0x0a, 0x06, 0x6c, 0x6f, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6c, 0x6f, 0x67, 0x49, 0x64, 0x22, 0xd5, 0x02, 0x0a, 0x0e, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72, 0x67, 0x61, 0x6e,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x32, 0xfe, 0x01,
	0x0a, 0x0a, 0x4c, 0x6f, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x06,
	0x47, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x12, 0x12, 0x2e, 0x6c, 0x6f, 0x67, 0x2e, 0x4c, 0x6f, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x54, 0x4f, 0x1a, 0x0b, 0x2e, 0x6c, 0x6f, 0x67,
	0x2e, 0x4c, 0x6f, 0x67, 0x44, 0x54, 0x4f, 0x12, 0x38, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x67, 0x73, 0x12, 0x16, 0x2e, 0x6c, 0x6f, 0x67, 0x2e, 0x4c, 0x6f, 0x67, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x54, 0x4f, 0x1a, 0x15, 0x2e, 0x6c, 0x6f, 0x67,
	0x2e, 0x4c, 0x6f, 0x67, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x43, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x67, 0x12, 0x1a, 0x2e, 0x6c, 0x6f, 0x67, 0x2e, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x54, 0x4f, 0x1a,
	0x15, 0x2e, 0x6c, 0x6f, 0x67, 0x2e, 0x4c, 0x6f, 0x67, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e,
	0x67, 0x6c, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x19,
	0x2e, 0x6c, 0x6f, 0x67, 0x2e, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x1a, 0x13, 0x2e, 0x6c, 0x6f, 0x67, 0x2e,
	0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x42, 0x08,
	0x5a, 0x06, 0x2e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_log_proto_rawDescOnce sync.Once
	file_protos_log_proto_rawDescData = file_protos_log_proto_rawDesc
)

func file_protos_log_proto_rawDescGZIP() []byte {
	file_protos_log_proto_rawDescOnce.Do(func() {
		file_protos_log_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_log_proto_rawDescData)
	})
	return file_protos_log_proto_rawDescData
}

var file_protos_log_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_protos_log_proto_goTypes = []any{
	(*LogRequestDTO)(nil),         // 0: log.LogRequestDTO
	(*LogDTO)(nil),                // 1: log.LogDTO
	(*LogListItem)(nil),           // 2: log.LogListItem
	(*LogPaginatedData)(nil),      // 3: log.LogPaginatedData
	(*LogPageRequestDTO)(nil),     // 4: log.LogPageRequestDTO
	(*LogSimulateRequestDTO)(nil), // 5: log.LogSimulateRequestDTO
	(*LogSingleSimulateLog)(nil),  // 6: log.LogSingleSimulateLog
	(*LogSimulateLog)(nil),        // 7: log.LogSimulateLog
}
var file_protos_log_proto_depIdxs = []int32{
	2, // 0: log.LogPaginatedData.rows:type_name -> log.LogListItem
	0, // 1: log.LogService.GetLog:input_type -> log.LogRequestDTO
	4, // 2: log.LogService.GetLogs:input_type -> log.LogPageRequestDTO
	5, // 3: log.LogService.GetSimulateLog:input_type -> log.LogSimulateRequestDTO
	6, // 4: log.LogService.GetSingleSimulateLog:input_type -> log.LogSingleSimulateLog
	1, // 5: log.LogService.GetLog:output_type -> log.LogDTO
	3, // 6: log.LogService.GetLogs:output_type -> log.LogPaginatedData
	3, // 7: log.LogService.GetSimulateLog:output_type -> log.LogPaginatedData
	7, // 8: log.LogService.GetSingleSimulateLog:output_type -> log.LogSimulateLog
	5, // [5:9] is the sub-list for method output_type
	1, // [1:5] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_protos_log_proto_init() }
func file_protos_log_proto_init() {
	if File_protos_log_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_log_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*LogRequestDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_log_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*LogDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_log_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*LogListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_log_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*LogPaginatedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_log_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*LogPageRequestDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_log_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*LogSimulateRequestDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_log_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*LogSingleSimulateLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_log_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*LogSimulateLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_log_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_log_proto_goTypes,
		DependencyIndexes: file_protos_log_proto_depIdxs,
		MessageInfos:      file_protos_log_proto_msgTypes,
	}.Build()
	File_protos_log_proto = out.File
	file_protos_log_proto_rawDesc = nil
	file_protos_log_proto_goTypes = nil
	file_protos_log_proto_depIdxs = nil
}
