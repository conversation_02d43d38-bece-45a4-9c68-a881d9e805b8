// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: protos/pars_control.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RequestForParsControlAuthorization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *RequestForParsControlAuthorization) Reset() {
	*x = RequestForParsControlAuthorization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_control_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestForParsControlAuthorization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestForParsControlAuthorization) ProtoMessage() {}

func (x *RequestForParsControlAuthorization) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_control_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestForParsControlAuthorization.ProtoReflect.Descriptor instead.
func (*RequestForParsControlAuthorization) Descriptor() ([]byte, []int) {
	return file_protos_pars_control_proto_rawDescGZIP(), []int{0}
}

func (x *RequestForParsControlAuthorization) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type ResponseForParsControlAuthorization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *ResponseForParsControlAuthorization) Reset() {
	*x = ResponseForParsControlAuthorization{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_control_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResponseForParsControlAuthorization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseForParsControlAuthorization) ProtoMessage() {}

func (x *ResponseForParsControlAuthorization) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_control_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseForParsControlAuthorization.ProtoReflect.Descriptor instead.
func (*ResponseForParsControlAuthorization) Descriptor() ([]byte, []int) {
	return file_protos_pars_control_proto_rawDescGZIP(), []int{1}
}

func (x *ResponseForParsControlAuthorization) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_protos_pars_control_proto protoreflect.FileDescriptor

var file_protos_pars_control_proto_rawDesc = []byte{
	0x0a, 0x19, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x70, 0x61, 0x72, 0x73, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x63, 0x6f, 0x6d,
	0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x22, 0x3a, 0x0a, 0x22, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x3f, 0x0a, 0x23, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x46, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0xb4, 0x01, 0x0a, 0x12, 0x50, 0x61, 0x72, 0x73, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9d, 0x01, 0x0a,
	0x18, 0x50, 0x61, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46,
	0x6f, 0x72, 0x50, 0x61, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x40, 0x2e, 0x63, 0x6f, 0x6d,
	0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x43, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x46, 0x6f, 0x72, 0x50, 0x61, 0x72, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x18, 0x5a, 0x16,
	0x2e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x72, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_pars_control_proto_rawDescOnce sync.Once
	file_protos_pars_control_proto_rawDescData = file_protos_pars_control_proto_rawDesc
)

func file_protos_pars_control_proto_rawDescGZIP() []byte {
	file_protos_pars_control_proto_rawDescOnce.Do(func() {
		file_protos_pars_control_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_pars_control_proto_rawDescData)
	})
	return file_protos_pars_control_proto_rawDescData
}

var file_protos_pars_control_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_protos_pars_control_proto_goTypes = []any{
	(*RequestForParsControlAuthorization)(nil),  // 0: com.tapsilat.ParsControl.v1.RequestForParsControlAuthorization
	(*ResponseForParsControlAuthorization)(nil), // 1: com.tapsilat.ParsControl.v1.ResponseForParsControlAuthorization
}
var file_protos_pars_control_proto_depIdxs = []int32{
	0, // 0: com.tapsilat.ParsControl.v1.ParsControlService.ParsControlAuthorization:input_type -> com.tapsilat.ParsControl.v1.RequestForParsControlAuthorization
	1, // 1: com.tapsilat.ParsControl.v1.ParsControlService.ParsControlAuthorization:output_type -> com.tapsilat.ParsControl.v1.ResponseForParsControlAuthorization
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_protos_pars_control_proto_init() }
func file_protos_pars_control_proto_init() {
	if File_protos_pars_control_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_pars_control_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*RequestForParsControlAuthorization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_control_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ResponseForParsControlAuthorization); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_pars_control_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_pars_control_proto_goTypes,
		DependencyIndexes: file_protos_pars_control_proto_depIdxs,
		MessageInfos:      file_protos_pars_control_proto_msgTypes,
	}.Build()
	File_protos_pars_control_proto = out.File
	file_protos_pars_control_proto_rawDesc = nil
	file_protos_pars_control_proto_goTypes = nil
	file_protos_pars_control_proto_depIdxs = nil
}
