// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: protos/pars_control.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ParsControlService_ParsControlAuthorization_FullMethodName = "/com.tapsilat.ParsControl.v1.ParsControlService/ParsControlAuthorization"
)

// ParsControlServiceClient is the client API for ParsControlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ParsControlServiceClient interface {
	ParsControlAuthorization(ctx context.Context, in *RequestForParsControlAuthorization, opts ...grpc.CallOption) (*ResponseForParsControlAuthorization, error)
}

type parsControlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewParsControlServiceClient(cc grpc.ClientConnInterface) ParsControlServiceClient {
	return &parsControlServiceClient{cc}
}

func (c *parsControlServiceClient) ParsControlAuthorization(ctx context.Context, in *RequestForParsControlAuthorization, opts ...grpc.CallOption) (*ResponseForParsControlAuthorization, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResponseForParsControlAuthorization)
	err := c.cc.Invoke(ctx, ParsControlService_ParsControlAuthorization_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ParsControlServiceServer is the server API for ParsControlService service.
// All implementations must embed UnimplementedParsControlServiceServer
// for forward compatibility.
type ParsControlServiceServer interface {
	ParsControlAuthorization(context.Context, *RequestForParsControlAuthorization) (*ResponseForParsControlAuthorization, error)
	mustEmbedUnimplementedParsControlServiceServer()
}

// UnimplementedParsControlServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedParsControlServiceServer struct{}

func (UnimplementedParsControlServiceServer) ParsControlAuthorization(context.Context, *RequestForParsControlAuthorization) (*ResponseForParsControlAuthorization, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ParsControlAuthorization not implemented")
}
func (UnimplementedParsControlServiceServer) mustEmbedUnimplementedParsControlServiceServer() {}
func (UnimplementedParsControlServiceServer) testEmbeddedByValue()                            {}

// UnsafeParsControlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ParsControlServiceServer will
// result in compilation errors.
type UnsafeParsControlServiceServer interface {
	mustEmbedUnimplementedParsControlServiceServer()
}

func RegisterParsControlServiceServer(s grpc.ServiceRegistrar, srv ParsControlServiceServer) {
	// If the following call pancis, it indicates UnimplementedParsControlServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ParsControlService_ServiceDesc, srv)
}

func _ParsControlService_ParsControlAuthorization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestForParsControlAuthorization)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsControlServiceServer).ParsControlAuthorization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsControlService_ParsControlAuthorization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsControlServiceServer).ParsControlAuthorization(ctx, req.(*RequestForParsControlAuthorization))
	}
	return interceptor(ctx, in, info, handler)
}

// ParsControlService_ServiceDesc is the grpc.ServiceDesc for ParsControlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ParsControlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.tapsilat.ParsControl.v1.ParsControlService",
	HandlerType: (*ParsControlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ParsControlAuthorization",
			Handler:    _ParsControlService_ParsControlAuthorization_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/pars_control.proto",
}
