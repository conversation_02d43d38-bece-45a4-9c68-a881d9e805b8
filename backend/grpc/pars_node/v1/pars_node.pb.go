// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: protos/pars_node.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NodeDeleteFavoriteDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *NodeDeleteFavoriteDTO) Reset() {
	*x = NodeDeleteFavoriteDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeDeleteFavoriteDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeDeleteFavoriteDTO) ProtoMessage() {}

func (x *NodeDeleteFavoriteDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeDeleteFavoriteDTO.ProtoReflect.Descriptor instead.
func (*NodeDeleteFavoriteDTO) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{0}
}

func (x *NodeDeleteFavoriteDTO) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type NodeGetRequestDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page    int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PerPage int64 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
}

func (x *NodeGetRequestDTO) Reset() {
	*x = NodeGetRequestDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeGetRequestDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeGetRequestDTO) ProtoMessage() {}

func (x *NodeGetRequestDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeGetRequestDTO.ProtoReflect.Descriptor instead.
func (*NodeGetRequestDTO) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{1}
}

func (x *NodeGetRequestDTO) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *NodeGetRequestDTO) GetPerPage() int64 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

type NodePaginatedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int64   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PerPage    int64   `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	Total      int64   `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	TotalPages int64   `protobuf:"varint,4,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	Rows       []*Node `protobuf:"bytes,5,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *NodePaginatedData) Reset() {
	*x = NodePaginatedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodePaginatedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodePaginatedData) ProtoMessage() {}

func (x *NodePaginatedData) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodePaginatedData.ProtoReflect.Descriptor instead.
func (*NodePaginatedData) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{2}
}

func (x *NodePaginatedData) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *NodePaginatedData) GetPerPage() int64 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *NodePaginatedData) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *NodePaginatedData) GetTotalPages() int64 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

func (x *NodePaginatedData) GetRows() []*Node {
	if x != nil {
		return x.Rows
	}
	return nil
}

type NodeResponseDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *NodeResponseDTO) Reset() {
	*x = NodeResponseDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeResponseDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeResponseDTO) ProtoMessage() {}

func (x *NodeResponseDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeResponseDTO.ProtoReflect.Descriptor instead.
func (*NodeResponseDTO) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{3}
}

func (x *NodeResponseDTO) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label    string        `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Name     string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Id       string        `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Position *NodePosition `protobuf:"bytes,4,opt,name=position,proto3" json:"position,omitempty"`
	Type     string        `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	// Types that are assignable to Content:
	//
	//	*Node_Object
	//	*Node_String_
	Content isNode_Content `protobuf_oneof:"content"`
}

func (x *Node) Reset() {
	*x = Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{4}
}

func (x *Node) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Node) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Node) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Node) GetPosition() *NodePosition {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *Node) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (m *Node) GetContent() isNode_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (x *Node) GetObject() *Content {
	if x, ok := x.GetContent().(*Node_Object); ok {
		return x.Object
	}
	return nil
}

func (x *Node) GetString_() string {
	if x, ok := x.GetContent().(*Node_String_); ok {
		return x.String_
	}
	return ""
}

type isNode_Content interface {
	isNode_Content()
}

type Node_Object struct {
	Object *Content `protobuf:"bytes,6,opt,name=object,proto3,oneof"`
}

type Node_String_ struct {
	String_ string `protobuf:"bytes,7,opt,name=string,proto3,oneof"`
}

func (*Node_Object) isNode_Content() {}

func (*Node_String_) isNode_Content() {}

type NodePosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X *int64 `protobuf:"varint,1,opt,name=x,proto3,oneof" json:"x,omitempty"`
	Y *int64 `protobuf:"varint,2,opt,name=y,proto3,oneof" json:"y,omitempty"`
}

func (x *NodePosition) Reset() {
	*x = NodePosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodePosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodePosition) ProtoMessage() {}

func (x *NodePosition) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodePosition.ProtoReflect.Descriptor instead.
func (*NodePosition) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{5}
}

func (x *NodePosition) GetX() int64 {
	if x != nil && x.X != nil {
		return *x.X
	}
	return 0
}

func (x *NodePosition) GetY() int64 {
	if x != nil && x.Y != nil {
		return *x.Y
	}
	return 0
}

type Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HitPolicy   string         `protobuf:"bytes,1,opt,name=hitPolicy,proto3" json:"hitPolicy,omitempty"`
	Inputs      []*InputOutput `protobuf:"bytes,2,rep,name=inputs,proto3" json:"inputs,omitempty"`
	Outputs     []*InputOutput `protobuf:"bytes,3,rep,name=outputs,proto3" json:"outputs,omitempty"`
	Rules       []*Rule        `protobuf:"bytes,4,rep,name=rules,proto3" json:"rules,omitempty"`
	Statements  []*Statement   `protobuf:"bytes,5,rep,name=statements,proto3" json:"statements,omitempty"`
	Expressions []*Expression  `protobuf:"bytes,6,rep,name=expressions,proto3" json:"expressions,omitempty"`
}

func (x *Content) Reset() {
	*x = Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Content) ProtoMessage() {}

func (x *Content) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Content.ProtoReflect.Descriptor instead.
func (*Content) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{6}
}

func (x *Content) GetHitPolicy() string {
	if x != nil {
		return x.HitPolicy
	}
	return ""
}

func (x *Content) GetInputs() []*InputOutput {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *Content) GetOutputs() []*InputOutput {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *Content) GetRules() []*Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *Content) GetStatements() []*Statement {
	if x != nil {
		return x.Statements
	}
	return nil
}

func (x *Content) GetExpressions() []*Expression {
	if x != nil {
		return x.Expressions
	}
	return nil
}

type InputOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type  string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Field string `protobuf:"bytes,4,opt,name=field,proto3" json:"field,omitempty"`
}

func (x *InputOutput) Reset() {
	*x = InputOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputOutput) ProtoMessage() {}

func (x *InputOutput) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputOutput.ProtoReflect.Descriptor instead.
func (*InputOutput) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{7}
}

func (x *InputOutput) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InputOutput) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InputOutput) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *InputOutput) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

type Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rule map[string]string `protobuf:"bytes,1,rep,name=rule,proto3" json:"rule,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Rule) Reset() {
	*x = Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rule) ProtoMessage() {}

func (x *Rule) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rule.ProtoReflect.Descriptor instead.
func (*Rule) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{8}
}

func (x *Rule) GetRule() map[string]string {
	if x != nil {
		return x.Rule
	}
	return nil
}

type Statement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Condition string `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition,omitempty"`
}

func (x *Statement) Reset() {
	*x = Statement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Statement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Statement) ProtoMessage() {}

func (x *Statement) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Statement.ProtoReflect.Descriptor instead.
func (*Statement) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{9}
}

func (x *Statement) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Statement) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

type Expression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Key   string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Expression) Reset() {
	*x = Expression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_node_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expression) ProtoMessage() {}

func (x *Expression) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_node_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expression.ProtoReflect.Descriptor instead.
func (*Expression) Descriptor() ([]byte, []int) {
	return file_protos_pars_node_proto_rawDescGZIP(), []int{10}
}

func (x *Expression) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Expression) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Expression) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_protos_pars_node_proto protoreflect.FileDescriptor

var file_protos_pars_node_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x70, 0x61, 0x72, 0x73, 0x5f, 0x6e, 0x6f,
	0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61,
	0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e,
	0x76, 0x31, 0x22, 0x27, 0x0a, 0x15, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x42, 0x0a, 0x11, 0x4e,
	0x6f, 0x64, 0x65, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x54, 0x4f,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x22,
	0xad, 0x01, 0x0a, 0x11, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72,
	0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x65, 0x72,
	0x50, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x04, 0x72,
	0x6f, 0x77, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x22,
	0x2b, 0x0a, 0x0f, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44,
	0x54, 0x4f, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xfa, 0x01, 0x0a,
	0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x42, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74,
	0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61,
	0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x06, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x09,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x40, 0x0a, 0x0c, 0x4e, 0x6f, 0x64,
	0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x11, 0x0a, 0x01, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x01, 0x78, 0x88, 0x01, 0x01, 0x12, 0x11, 0x0a, 0x01,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x01, 0x79, 0x88, 0x01, 0x01, 0x42,
	0x04, 0x0a, 0x02, 0x5f, 0x78, 0x42, 0x04, 0x0a, 0x02, 0x5f, 0x79, 0x22, 0xea, 0x02, 0x0a, 0x07,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x69, 0x74, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x3d, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73,
	0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x06, 0x69, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73,
	0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x07, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x73, 0x12, 0x34, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69,
	0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50,
	0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x46, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73,
	0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x78, 0x70,
	0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x5b, 0x0a, 0x0b, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x22, 0x7d, 0x0a, 0x04, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x3c, 0x0a,
	0x04, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e,
	0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x52,
	0x75, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x39, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x44, 0x0a, 0x0a, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xd5, 0x02, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f,
	0x64, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x13, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x64, 0x4e, 0x6f, 0x64, 0x65,
	0x12, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e,
	0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x44, 0x54,
	0x4f, 0x1a, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74,
	0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x60, 0x0a, 0x13,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x64, 0x4e,
	0x6f, 0x64, 0x65, 0x12, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c,
	0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x1a, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c,
	0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x6d,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x46, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x64, 0x4e, 0x6f,
	0x64, 0x65, 0x73, 0x12, 0x2b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c,
	0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x54, 0x4f,
	0x1a, 0x2b, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e,
	0x50, 0x61, 0x72, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x42, 0x15, 0x5a,
	0x13, 0x2e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x72, 0x73, 0x5f, 0x6e, 0x6f, 0x64,
	0x65, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_pars_node_proto_rawDescOnce sync.Once
	file_protos_pars_node_proto_rawDescData = file_protos_pars_node_proto_rawDesc
)

func file_protos_pars_node_proto_rawDescGZIP() []byte {
	file_protos_pars_node_proto_rawDescOnce.Do(func() {
		file_protos_pars_node_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_pars_node_proto_rawDescData)
	})
	return file_protos_pars_node_proto_rawDescData
}

var file_protos_pars_node_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_protos_pars_node_proto_goTypes = []any{
	(*NodeDeleteFavoriteDTO)(nil), // 0: com.tapsilat.ParsNode.v1.NodeDeleteFavoriteDTO
	(*NodeGetRequestDTO)(nil),     // 1: com.tapsilat.ParsNode.v1.NodeGetRequestDTO
	(*NodePaginatedData)(nil),     // 2: com.tapsilat.ParsNode.v1.NodePaginatedData
	(*NodeResponseDTO)(nil),       // 3: com.tapsilat.ParsNode.v1.NodeResponseDTO
	(*Node)(nil),                  // 4: com.tapsilat.ParsNode.v1.Node
	(*NodePosition)(nil),          // 5: com.tapsilat.ParsNode.v1.NodePosition
	(*Content)(nil),               // 6: com.tapsilat.ParsNode.v1.Content
	(*InputOutput)(nil),           // 7: com.tapsilat.ParsNode.v1.InputOutput
	(*Rule)(nil),                  // 8: com.tapsilat.ParsNode.v1.Rule
	(*Statement)(nil),             // 9: com.tapsilat.ParsNode.v1.Statement
	(*Expression)(nil),            // 10: com.tapsilat.ParsNode.v1.Expression
	nil,                           // 11: com.tapsilat.ParsNode.v1.Rule.RuleEntry
}
var file_protos_pars_node_proto_depIdxs = []int32{
	4,  // 0: com.tapsilat.ParsNode.v1.NodePaginatedData.rows:type_name -> com.tapsilat.ParsNode.v1.Node
	5,  // 1: com.tapsilat.ParsNode.v1.Node.position:type_name -> com.tapsilat.ParsNode.v1.NodePosition
	6,  // 2: com.tapsilat.ParsNode.v1.Node.object:type_name -> com.tapsilat.ParsNode.v1.Content
	7,  // 3: com.tapsilat.ParsNode.v1.Content.inputs:type_name -> com.tapsilat.ParsNode.v1.InputOutput
	7,  // 4: com.tapsilat.ParsNode.v1.Content.outputs:type_name -> com.tapsilat.ParsNode.v1.InputOutput
	8,  // 5: com.tapsilat.ParsNode.v1.Content.rules:type_name -> com.tapsilat.ParsNode.v1.Rule
	9,  // 6: com.tapsilat.ParsNode.v1.Content.statements:type_name -> com.tapsilat.ParsNode.v1.Statement
	10, // 7: com.tapsilat.ParsNode.v1.Content.expressions:type_name -> com.tapsilat.ParsNode.v1.Expression
	11, // 8: com.tapsilat.ParsNode.v1.Rule.rule:type_name -> com.tapsilat.ParsNode.v1.Rule.RuleEntry
	0,  // 9: com.tapsilat.ParsNode.v1.ParsNodeService.DeleteFavoritedNode:input_type -> com.tapsilat.ParsNode.v1.NodeDeleteFavoriteDTO
	4,  // 10: com.tapsilat.ParsNode.v1.ParsNodeService.CreateFavoritedNode:input_type -> com.tapsilat.ParsNode.v1.Node
	1,  // 11: com.tapsilat.ParsNode.v1.ParsNodeService.GetFavoritedNodes:input_type -> com.tapsilat.ParsNode.v1.NodeGetRequestDTO
	3,  // 12: com.tapsilat.ParsNode.v1.ParsNodeService.DeleteFavoritedNode:output_type -> com.tapsilat.ParsNode.v1.NodeResponseDTO
	3,  // 13: com.tapsilat.ParsNode.v1.ParsNodeService.CreateFavoritedNode:output_type -> com.tapsilat.ParsNode.v1.NodeResponseDTO
	2,  // 14: com.tapsilat.ParsNode.v1.ParsNodeService.GetFavoritedNodes:output_type -> com.tapsilat.ParsNode.v1.NodePaginatedData
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_protos_pars_node_proto_init() }
func file_protos_pars_node_proto_init() {
	if File_protos_pars_node_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_pars_node_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*NodeDeleteFavoriteDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*NodeGetRequestDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*NodePaginatedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*NodeResponseDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*NodePosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*InputOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*Statement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_node_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*Expression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_protos_pars_node_proto_msgTypes[4].OneofWrappers = []any{
		(*Node_Object)(nil),
		(*Node_String_)(nil),
	}
	file_protos_pars_node_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_pars_node_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_pars_node_proto_goTypes,
		DependencyIndexes: file_protos_pars_node_proto_depIdxs,
		MessageInfos:      file_protos_pars_node_proto_msgTypes,
	}.Build()
	File_protos_pars_node_proto = out.File
	file_protos_pars_node_proto_rawDesc = nil
	file_protos_pars_node_proto_goTypes = nil
	file_protos_pars_node_proto_depIdxs = nil
}
