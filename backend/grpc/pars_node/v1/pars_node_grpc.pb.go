// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: protos/pars_node.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ParsNodeService_DeleteFavoritedNode_FullMethodName = "/com.tapsilat.ParsNode.v1.ParsNodeService/DeleteFavoritedNode"
	ParsNodeService_CreateFavoritedNode_FullMethodName = "/com.tapsilat.ParsNode.v1.ParsNodeService/CreateFavoritedNode"
	ParsNodeService_GetFavoritedNodes_FullMethodName   = "/com.tapsilat.ParsNode.v1.ParsNodeService/GetFavoritedNodes"
)

// ParsNodeServiceClient is the client API for ParsNodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ParsNodeServiceClient interface {
	DeleteFavoritedNode(ctx context.Context, in *NodeDeleteFavoriteDTO, opts ...grpc.CallOption) (*NodeResponseDTO, error)
	CreateFavoritedNode(ctx context.Context, in *Node, opts ...grpc.CallOption) (*NodeResponseDTO, error)
	GetFavoritedNodes(ctx context.Context, in *NodeGetRequestDTO, opts ...grpc.CallOption) (*NodePaginatedData, error)
}

type parsNodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewParsNodeServiceClient(cc grpc.ClientConnInterface) ParsNodeServiceClient {
	return &parsNodeServiceClient{cc}
}

func (c *parsNodeServiceClient) DeleteFavoritedNode(ctx context.Context, in *NodeDeleteFavoriteDTO, opts ...grpc.CallOption) (*NodeResponseDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeResponseDTO)
	err := c.cc.Invoke(ctx, ParsNodeService_DeleteFavoritedNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsNodeServiceClient) CreateFavoritedNode(ctx context.Context, in *Node, opts ...grpc.CallOption) (*NodeResponseDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeResponseDTO)
	err := c.cc.Invoke(ctx, ParsNodeService_CreateFavoritedNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsNodeServiceClient) GetFavoritedNodes(ctx context.Context, in *NodeGetRequestDTO, opts ...grpc.CallOption) (*NodePaginatedData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodePaginatedData)
	err := c.cc.Invoke(ctx, ParsNodeService_GetFavoritedNodes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ParsNodeServiceServer is the server API for ParsNodeService service.
// All implementations must embed UnimplementedParsNodeServiceServer
// for forward compatibility.
type ParsNodeServiceServer interface {
	DeleteFavoritedNode(context.Context, *NodeDeleteFavoriteDTO) (*NodeResponseDTO, error)
	CreateFavoritedNode(context.Context, *Node) (*NodeResponseDTO, error)
	GetFavoritedNodes(context.Context, *NodeGetRequestDTO) (*NodePaginatedData, error)
	mustEmbedUnimplementedParsNodeServiceServer()
}

// UnimplementedParsNodeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedParsNodeServiceServer struct{}

func (UnimplementedParsNodeServiceServer) DeleteFavoritedNode(context.Context, *NodeDeleteFavoriteDTO) (*NodeResponseDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFavoritedNode not implemented")
}
func (UnimplementedParsNodeServiceServer) CreateFavoritedNode(context.Context, *Node) (*NodeResponseDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFavoritedNode not implemented")
}
func (UnimplementedParsNodeServiceServer) GetFavoritedNodes(context.Context, *NodeGetRequestDTO) (*NodePaginatedData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFavoritedNodes not implemented")
}
func (UnimplementedParsNodeServiceServer) mustEmbedUnimplementedParsNodeServiceServer() {}
func (UnimplementedParsNodeServiceServer) testEmbeddedByValue()                         {}

// UnsafeParsNodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ParsNodeServiceServer will
// result in compilation errors.
type UnsafeParsNodeServiceServer interface {
	mustEmbedUnimplementedParsNodeServiceServer()
}

func RegisterParsNodeServiceServer(s grpc.ServiceRegistrar, srv ParsNodeServiceServer) {
	// If the following call pancis, it indicates UnimplementedParsNodeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ParsNodeService_ServiceDesc, srv)
}

func _ParsNodeService_DeleteFavoritedNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NodeDeleteFavoriteDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsNodeServiceServer).DeleteFavoritedNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsNodeService_DeleteFavoritedNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsNodeServiceServer).DeleteFavoritedNode(ctx, req.(*NodeDeleteFavoriteDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsNodeService_CreateFavoritedNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Node)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsNodeServiceServer).CreateFavoritedNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsNodeService_CreateFavoritedNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsNodeServiceServer).CreateFavoritedNode(ctx, req.(*Node))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsNodeService_GetFavoritedNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NodeGetRequestDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsNodeServiceServer).GetFavoritedNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsNodeService_GetFavoritedNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsNodeServiceServer).GetFavoritedNodes(ctx, req.(*NodeGetRequestDTO))
	}
	return interceptor(ctx, in, info, handler)
}

// ParsNodeService_ServiceDesc is the grpc.ServiceDesc for ParsNodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ParsNodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.tapsilat.ParsNode.v1.ParsNodeService",
	HandlerType: (*ParsNodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DeleteFavoritedNode",
			Handler:    _ParsNodeService_DeleteFavoritedNode_Handler,
		},
		{
			MethodName: "CreateFavoritedNode",
			Handler:    _ParsNodeService_CreateFavoritedNode_Handler,
		},
		{
			MethodName: "GetFavoritedNodes",
			Handler:    _ParsNodeService_GetFavoritedNodes_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/pars_node.proto",
}
