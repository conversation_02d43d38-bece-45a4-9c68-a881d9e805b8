// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: protos/pars_simulate.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SimulateDTO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name           string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Context        []byte                  `protobuf:"bytes,3,opt,name=context,proto3" json:"context,omitempty"`
	Content        *SimulateRequestContent `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	IsNew          bool                    `protobuf:"varint,5,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	DefaultRequest string                  `protobuf:"bytes,6,opt,name=default_request,json=defaultRequest,proto3" json:"default_request,omitempty"`
}

func (x *SimulateDTO) Reset() {
	*x = SimulateDTO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateDTO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateDTO) ProtoMessage() {}

func (x *SimulateDTO) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateDTO.ProtoReflect.Descriptor instead.
func (*SimulateDTO) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{0}
}

func (x *SimulateDTO) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SimulateDTO) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SimulateDTO) GetContext() []byte {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *SimulateDTO) GetContent() *SimulateRequestContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *SimulateDTO) GetIsNew() bool {
	if x != nil {
		return x.IsNew
	}
	return false
}

func (x *SimulateDTO) GetDefaultRequest() string {
	if x != nil {
		return x.DefaultRequest
	}
	return ""
}

type ResponseSimulate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Performance string `protobuf:"bytes,1,opt,name=performance,proto3" json:"performance,omitempty"`
	Result      []byte `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	Trace       []byte `protobuf:"bytes,3,opt,name=trace,proto3" json:"trace,omitempty"`
}

func (x *ResponseSimulate) Reset() {
	*x = ResponseSimulate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResponseSimulate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseSimulate) ProtoMessage() {}

func (x *ResponseSimulate) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseSimulate.ProtoReflect.Descriptor instead.
func (*ResponseSimulate) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{1}
}

func (x *ResponseSimulate) GetPerformance() string {
	if x != nil {
		return x.Performance
	}
	return ""
}

func (x *ResponseSimulate) GetResult() []byte {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *ResponseSimulate) GetTrace() []byte {
	if x != nil {
		return x.Trace
	}
	return nil
}

type Edge struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SourceId     string `protobuf:"bytes,2,opt,name=sourceId,proto3" json:"sourceId,omitempty"`
	Type         string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	TargetId     string `protobuf:"bytes,4,opt,name=targetId,proto3" json:"targetId,omitempty"`
	SourceHandle string `protobuf:"bytes,5,opt,name=source_handle,json=sourceHandle,proto3" json:"source_handle,omitempty"`
}

func (x *Edge) Reset() {
	*x = Edge{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Edge) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Edge) ProtoMessage() {}

func (x *Edge) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Edge.ProtoReflect.Descriptor instead.
func (*Edge) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{2}
}

func (x *Edge) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Edge) GetSourceId() string {
	if x != nil {
		return x.SourceId
	}
	return ""
}

func (x *Edge) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Edge) GetTargetId() string {
	if x != nil {
		return x.TargetId
	}
	return ""
}

func (x *Edge) GetSourceHandle() string {
	if x != nil {
		return x.SourceHandle
	}
	return ""
}

type Node struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Label    string        `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	Name     string        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Id       string        `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Position *NodePosition `protobuf:"bytes,4,opt,name=position,proto3" json:"position,omitempty"`
	Type     string        `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	// Types that are assignable to Content:
	//
	//	*Node_Object
	//	*Node_String_
	Content isNode_Content `protobuf_oneof:"content"`
}

func (x *Node) Reset() {
	*x = Node{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{3}
}

func (x *Node) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *Node) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Node) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Node) GetPosition() *NodePosition {
	if x != nil {
		return x.Position
	}
	return nil
}

func (x *Node) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (m *Node) GetContent() isNode_Content {
	if m != nil {
		return m.Content
	}
	return nil
}

func (x *Node) GetObject() *Content {
	if x, ok := x.GetContent().(*Node_Object); ok {
		return x.Object
	}
	return nil
}

func (x *Node) GetString_() string {
	if x, ok := x.GetContent().(*Node_String_); ok {
		return x.String_
	}
	return ""
}

type isNode_Content interface {
	isNode_Content()
}

type Node_Object struct {
	Object *Content `protobuf:"bytes,6,opt,name=object,proto3,oneof"`
}

type Node_String_ struct {
	String_ string `protobuf:"bytes,7,opt,name=string,proto3,oneof"`
}

func (*Node_Object) isNode_Content() {}

func (*Node_String_) isNode_Content() {}

type NodePosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X *int64 `protobuf:"varint,1,opt,name=x,proto3,oneof" json:"x,omitempty"`
	Y *int64 `protobuf:"varint,2,opt,name=y,proto3,oneof" json:"y,omitempty"`
}

func (x *NodePosition) Reset() {
	*x = NodePosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodePosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodePosition) ProtoMessage() {}

func (x *NodePosition) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodePosition.ProtoReflect.Descriptor instead.
func (*NodePosition) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{4}
}

func (x *NodePosition) GetX() int64 {
	if x != nil && x.X != nil {
		return *x.X
	}
	return 0
}

func (x *NodePosition) GetY() int64 {
	if x != nil && x.Y != nil {
		return *x.Y
	}
	return 0
}

type Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HitPolicy   string         `protobuf:"bytes,1,opt,name=hitPolicy,proto3" json:"hitPolicy,omitempty"`
	Inputs      []*InputOutput `protobuf:"bytes,2,rep,name=inputs,proto3" json:"inputs,omitempty"`
	Outputs     []*InputOutput `protobuf:"bytes,3,rep,name=outputs,proto3" json:"outputs,omitempty"`
	Rules       []*Rule        `protobuf:"bytes,4,rep,name=rules,proto3" json:"rules,omitempty"`
	Statements  []*Statement   `protobuf:"bytes,5,rep,name=statements,proto3" json:"statements,omitempty"`
	Expressions []*Expression  `protobuf:"bytes,6,rep,name=expressions,proto3" json:"expressions,omitempty"`
}

func (x *Content) Reset() {
	*x = Content{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Content) ProtoMessage() {}

func (x *Content) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Content.ProtoReflect.Descriptor instead.
func (*Content) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{5}
}

func (x *Content) GetHitPolicy() string {
	if x != nil {
		return x.HitPolicy
	}
	return ""
}

func (x *Content) GetInputs() []*InputOutput {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *Content) GetOutputs() []*InputOutput {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *Content) GetRules() []*Rule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *Content) GetStatements() []*Statement {
	if x != nil {
		return x.Statements
	}
	return nil
}

func (x *Content) GetExpressions() []*Expression {
	if x != nil {
		return x.Expressions
	}
	return nil
}

type Rule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rule map[string]string `protobuf:"bytes,1,rep,name=rule,proto3" json:"rule,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Rule) Reset() {
	*x = Rule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Rule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rule) ProtoMessage() {}

func (x *Rule) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rule.ProtoReflect.Descriptor instead.
func (*Rule) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{6}
}

func (x *Rule) GetRule() map[string]string {
	if x != nil {
		return x.Rule
	}
	return nil
}

type Statement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Condition string `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition,omitempty"`
}

func (x *Statement) Reset() {
	*x = Statement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Statement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Statement) ProtoMessage() {}

func (x *Statement) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Statement.ProtoReflect.Descriptor instead.
func (*Statement) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{7}
}

func (x *Statement) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Statement) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

type Expression struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Key   string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Value string `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Expression) Reset() {
	*x = Expression{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expression) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expression) ProtoMessage() {}

func (x *Expression) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expression.ProtoReflect.Descriptor instead.
func (*Expression) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{8}
}

func (x *Expression) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Expression) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Expression) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type InputOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name  string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type  string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Field string `protobuf:"bytes,4,opt,name=field,proto3" json:"field,omitempty"`
}

func (x *InputOutput) Reset() {
	*x = InputOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InputOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InputOutput) ProtoMessage() {}

func (x *InputOutput) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InputOutput.ProtoReflect.Descriptor instead.
func (*InputOutput) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{9}
}

func (x *InputOutput) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *InputOutput) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InputOutput) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *InputOutput) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

type SimulateOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Performance string `protobuf:"bytes,2,opt,name=performance,proto3" json:"performance,omitempty"`
	Result      []byte `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`
	Trace       []byte `protobuf:"bytes,4,opt,name=trace,proto3" json:"trace,omitempty"`
	SimulateId  string `protobuf:"bytes,5,opt,name=simulate_id,json=simulateId,proto3" json:"simulate_id,omitempty"`
}

func (x *SimulateOutput) Reset() {
	*x = SimulateOutput{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateOutput) ProtoMessage() {}

func (x *SimulateOutput) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateOutput.ProtoReflect.Descriptor instead.
func (*SimulateOutput) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{10}
}

func (x *SimulateOutput) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SimulateOutput) GetPerformance() string {
	if x != nil {
		return x.Performance
	}
	return ""
}

func (x *SimulateOutput) GetResult() []byte {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SimulateOutput) GetTrace() []byte {
	if x != nil {
		return x.Trace
	}
	return nil
}

func (x *SimulateOutput) GetSimulateId() string {
	if x != nil {
		return x.SimulateId
	}
	return ""
}

type SimulateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string          `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatedAt        string          `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Name             string          `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Nodes            []*Node         `protobuf:"bytes,4,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Edges            []*Edge         `protobuf:"bytes,5,rep,name=edges,proto3" json:"edges,omitempty"`
	Context          []byte          `protobuf:"bytes,6,opt,name=context,proto3" json:"context,omitempty"`
	SimulateOutput   *SimulateOutput `protobuf:"bytes,7,opt,name=simulate_output,json=simulateOutput,proto3" json:"simulate_output,omitempty"`
	OrganizationId   string          `protobuf:"bytes,8,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	OrganizationName string          `protobuf:"bytes,9,opt,name=organization_name,json=organizationName,proto3" json:"organization_name,omitempty"`
	AdminId          string          `protobuf:"bytes,10,opt,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`
	VersionId        string          `protobuf:"bytes,11,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
	VersionName      string          `protobuf:"bytes,12,opt,name=version_name,json=versionName,proto3" json:"version_name,omitempty"`
	AccessToken      string          `protobuf:"bytes,13,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
}

func (x *SimulateResponse) Reset() {
	*x = SimulateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateResponse) ProtoMessage() {}

func (x *SimulateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateResponse.ProtoReflect.Descriptor instead.
func (*SimulateResponse) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{11}
}

func (x *SimulateResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SimulateResponse) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *SimulateResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SimulateResponse) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *SimulateResponse) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

func (x *SimulateResponse) GetContext() []byte {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *SimulateResponse) GetSimulateOutput() *SimulateOutput {
	if x != nil {
		return x.SimulateOutput
	}
	return nil
}

func (x *SimulateResponse) GetOrganizationId() string {
	if x != nil {
		return x.OrganizationId
	}
	return ""
}

func (x *SimulateResponse) GetOrganizationName() string {
	if x != nil {
		return x.OrganizationName
	}
	return ""
}

func (x *SimulateResponse) GetAdminId() string {
	if x != nil {
		return x.AdminId
	}
	return ""
}

func (x *SimulateResponse) GetVersionId() string {
	if x != nil {
		return x.VersionId
	}
	return ""
}

func (x *SimulateResponse) GetVersionName() string {
	if x != nil {
		return x.VersionName
	}
	return ""
}

func (x *SimulateResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

type SimulatePaginatedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       int64               `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PerPage    int64               `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	Total      int64               `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	TotalPages int64               `protobuf:"varint,4,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	Rows       []*SimulateResponse `protobuf:"bytes,5,rep,name=rows,proto3" json:"rows,omitempty"`
}

func (x *SimulatePaginatedData) Reset() {
	*x = SimulatePaginatedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulatePaginatedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulatePaginatedData) ProtoMessage() {}

func (x *SimulatePaginatedData) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulatePaginatedData.ProtoReflect.Descriptor instead.
func (*SimulatePaginatedData) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{12}
}

func (x *SimulatePaginatedData) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SimulatePaginatedData) GetPerPage() int64 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *SimulatePaginatedData) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *SimulatePaginatedData) GetTotalPages() int64 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

func (x *SimulatePaginatedData) GetRows() []*SimulateResponse {
	if x != nil {
		return x.Rows
	}
	return nil
}

type SimulateDelete struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SimulateDelete) Reset() {
	*x = SimulateDelete{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateDelete) ProtoMessage() {}

func (x *SimulateDelete) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateDelete.ProtoReflect.Descriptor instead.
func (*SimulateDelete) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{13}
}

func (x *SimulateDelete) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SimulateGet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *SimulateGet) Reset() {
	*x = SimulateGet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateGet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateGet) ProtoMessage() {}

func (x *SimulateGet) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateGet.ProtoReflect.Descriptor instead.
func (*SimulateGet) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{14}
}

func (x *SimulateGet) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SimulateByID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token   string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	Context []byte `protobuf:"bytes,2,opt,name=context,proto3" json:"context,omitempty"`
	Domain  string `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
}

func (x *SimulateByID) Reset() {
	*x = SimulateByID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateByID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateByID) ProtoMessage() {}

func (x *SimulateByID) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateByID.ProtoReflect.Descriptor instead.
func (*SimulateByID) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{15}
}

func (x *SimulateByID) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SimulateByID) GetContext() []byte {
	if x != nil {
		return x.Context
	}
	return nil
}

func (x *SimulateByID) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type SimulatePageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      int64  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PerPage   int64  `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	VersionId string `protobuf:"bytes,3,opt,name=version_id,json=versionId,proto3" json:"version_id,omitempty"`
}

func (x *SimulatePageRequest) Reset() {
	*x = SimulatePageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulatePageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulatePageRequest) ProtoMessage() {}

func (x *SimulatePageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulatePageRequest.ProtoReflect.Descriptor instead.
func (*SimulatePageRequest) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{16}
}

func (x *SimulatePageRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SimulatePageRequest) GetPerPage() int64 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *SimulatePageRequest) GetVersionId() string {
	if x != nil {
		return x.VersionId
	}
	return ""
}

type SimulateGeneralResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *SimulateGeneralResponse) Reset() {
	*x = SimulateGeneralResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateGeneralResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateGeneralResponse) ProtoMessage() {}

func (x *SimulateGeneralResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateGeneralResponse.ProtoReflect.Descriptor instead.
func (*SimulateGeneralResponse) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{17}
}

func (x *SimulateGeneralResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SimulateRequestContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nodes []*Node `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	Edges []*Edge `protobuf:"bytes,2,rep,name=edges,proto3" json:"edges,omitempty"`
}

func (x *SimulateRequestContent) Reset() {
	*x = SimulateRequestContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_pars_simulate_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimulateRequestContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimulateRequestContent) ProtoMessage() {}

func (x *SimulateRequestContent) ProtoReflect() protoreflect.Message {
	mi := &file_protos_pars_simulate_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimulateRequestContent.ProtoReflect.Descriptor instead.
func (*SimulateRequestContent) Descriptor() ([]byte, []int) {
	return file_protos_pars_simulate_proto_rawDescGZIP(), []int{18}
}

func (x *SimulateRequestContent) GetNodes() []*Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *SimulateRequestContent) GetEdges() []*Edge {
	if x != nil {
		return x.Edges
	}
	return nil
}

var File_protos_pars_simulate_proto protoreflect.FileDescriptor

var file_protos_pars_simulate_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x70, 0x61, 0x72, 0x73, 0x5f, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x63, 0x6f,
	0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x22, 0xdb, 0x01, 0x0a, 0x0b, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x54, 0x4f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x4e, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x6e,
	0x65, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x4e, 0x65, 0x77, 0x12,
	0x27, 0x0a, 0x0f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x62, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x22, 0x87, 0x01, 0x0a,
	0x04, 0x45, 0x64, 0x67, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x68, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x22, 0x82, 0x02, 0x0a, 0x04, 0x4e, 0x6f, 0x64, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x46, 0x0a, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73,
	0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x06,
	0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x18, 0x0a, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x42, 0x09, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x40, 0x0a, 0x0c, 0x4e,
	0x6f, 0x64, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x11, 0x0a, 0x01, 0x78,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x01, 0x78, 0x88, 0x01, 0x01, 0x12, 0x11,
	0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x01, 0x79, 0x88, 0x01,
	0x01, 0x42, 0x04, 0x0a, 0x02, 0x5f, 0x78, 0x42, 0x04, 0x0a, 0x02, 0x5f, 0x79, 0x22, 0xfe, 0x02,
	0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x69, 0x74,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x68, 0x69,
	0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x41, 0x0a, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61,
	0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x52, 0x06, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x43, 0x0a, 0x07, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x73, 0x12,
	0x38, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61,
	0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72,
	0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x12, 0x4a, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61,
	0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x81,
	0x01, 0x0a, 0x04, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73,
	0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x04, 0x72, 0x75, 0x6c, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x52, 0x75, 0x6c,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x39, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x44, 0x0a,
	0x0a, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x5b, 0x0a, 0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x22, 0x91, 0x01, 0x0a, 0x0e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x72, 0x61, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x49, 0x64, 0x22, 0x90, 0x04, 0x0a, 0x10, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x05,
	0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73,
	0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x55, 0x0a, 0x0f, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c,
	0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x52, 0x0e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xc1, 0x01, 0x0a, 0x15, 0x53, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73,
	0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x22, 0x20, 0x0a, 0x0e, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1d, 0x0a,
	0x0b, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x56, 0x0a, 0x0c,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x79, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x22, 0x63, 0x0a, 0x13, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x70, 0x65, 0x72, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x33, 0x0a, 0x17, 0x53, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x8c,
	0x01, 0x0a, 0x16, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x05, 0x6e, 0x6f, 0x64,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74,
	0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x05, 0x6e, 0x6f,
	0x64, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61,
	0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x64, 0x67, 0x65, 0x52, 0x05, 0x65, 0x64, 0x67, 0x65, 0x73, 0x32, 0xbd, 0x08,
	0x0a, 0x13, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x65, 0x0a, 0x08, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74,
	0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x54, 0x4f, 0x1a, 0x2e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x78, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x31,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61,
	0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x33, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74,
	0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x82, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x79, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x44, 0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e,
	0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x47, 0x65, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69,
	0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7d, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53, 0x68, 0x6f,
	0x72, 0x74, 0x12, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61,
	0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73,
	0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x7a, 0x0a, 0x16, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69,
	0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x44, 0x54, 0x4f, 0x1a,
	0x35, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50,
	0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x75, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74,
	0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x1a, 0x35, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70,
	0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c,
	0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70,
	0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x69, 0x0a, 0x0c, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x29, 0x2e, 0x63, 0x6f,
	0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53,
	0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x47, 0x65, 0x74, 0x1a, 0x2e, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70,
	0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x07, 0x53, 0x69, 0x6d, 0x42, 0x79, 0x49,
	0x44, 0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74,
	0x2e, 0x50, 0x61, 0x72, 0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x79, 0x49, 0x44, 0x1a, 0x2e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2e, 0x74, 0x61, 0x70, 0x73, 0x69, 0x6c, 0x61, 0x74, 0x2e, 0x50, 0x61, 0x72,
	0x73, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x42, 0x19, 0x5a,
	0x17, 0x2e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x72, 0x73, 0x5f, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_pars_simulate_proto_rawDescOnce sync.Once
	file_protos_pars_simulate_proto_rawDescData = file_protos_pars_simulate_proto_rawDesc
)

func file_protos_pars_simulate_proto_rawDescGZIP() []byte {
	file_protos_pars_simulate_proto_rawDescOnce.Do(func() {
		file_protos_pars_simulate_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_pars_simulate_proto_rawDescData)
	})
	return file_protos_pars_simulate_proto_rawDescData
}

var file_protos_pars_simulate_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_protos_pars_simulate_proto_goTypes = []any{
	(*SimulateDTO)(nil),             // 0: com.tapsilat.ParsSimulate.v1.SimulateDTO
	(*ResponseSimulate)(nil),        // 1: com.tapsilat.ParsSimulate.v1.ResponseSimulate
	(*Edge)(nil),                    // 2: com.tapsilat.ParsSimulate.v1.Edge
	(*Node)(nil),                    // 3: com.tapsilat.ParsSimulate.v1.Node
	(*NodePosition)(nil),            // 4: com.tapsilat.ParsSimulate.v1.NodePosition
	(*Content)(nil),                 // 5: com.tapsilat.ParsSimulate.v1.Content
	(*Rule)(nil),                    // 6: com.tapsilat.ParsSimulate.v1.Rule
	(*Statement)(nil),               // 7: com.tapsilat.ParsSimulate.v1.Statement
	(*Expression)(nil),              // 8: com.tapsilat.ParsSimulate.v1.Expression
	(*InputOutput)(nil),             // 9: com.tapsilat.ParsSimulate.v1.InputOutput
	(*SimulateOutput)(nil),          // 10: com.tapsilat.ParsSimulate.v1.SimulateOutput
	(*SimulateResponse)(nil),        // 11: com.tapsilat.ParsSimulate.v1.SimulateResponse
	(*SimulatePaginatedData)(nil),   // 12: com.tapsilat.ParsSimulate.v1.SimulatePaginatedData
	(*SimulateDelete)(nil),          // 13: com.tapsilat.ParsSimulate.v1.SimulateDelete
	(*SimulateGet)(nil),             // 14: com.tapsilat.ParsSimulate.v1.SimulateGet
	(*SimulateByID)(nil),            // 15: com.tapsilat.ParsSimulate.v1.SimulateByID
	(*SimulatePageRequest)(nil),     // 16: com.tapsilat.ParsSimulate.v1.SimulatePageRequest
	(*SimulateGeneralResponse)(nil), // 17: com.tapsilat.ParsSimulate.v1.SimulateGeneralResponse
	(*SimulateRequestContent)(nil),  // 18: com.tapsilat.ParsSimulate.v1.SimulateRequestContent
	nil,                             // 19: com.tapsilat.ParsSimulate.v1.Rule.RuleEntry
}
var file_protos_pars_simulate_proto_depIdxs = []int32{
	18, // 0: com.tapsilat.ParsSimulate.v1.SimulateDTO.content:type_name -> com.tapsilat.ParsSimulate.v1.SimulateRequestContent
	4,  // 1: com.tapsilat.ParsSimulate.v1.Node.position:type_name -> com.tapsilat.ParsSimulate.v1.NodePosition
	5,  // 2: com.tapsilat.ParsSimulate.v1.Node.object:type_name -> com.tapsilat.ParsSimulate.v1.Content
	9,  // 3: com.tapsilat.ParsSimulate.v1.Content.inputs:type_name -> com.tapsilat.ParsSimulate.v1.InputOutput
	9,  // 4: com.tapsilat.ParsSimulate.v1.Content.outputs:type_name -> com.tapsilat.ParsSimulate.v1.InputOutput
	6,  // 5: com.tapsilat.ParsSimulate.v1.Content.rules:type_name -> com.tapsilat.ParsSimulate.v1.Rule
	7,  // 6: com.tapsilat.ParsSimulate.v1.Content.statements:type_name -> com.tapsilat.ParsSimulate.v1.Statement
	8,  // 7: com.tapsilat.ParsSimulate.v1.Content.expressions:type_name -> com.tapsilat.ParsSimulate.v1.Expression
	19, // 8: com.tapsilat.ParsSimulate.v1.Rule.rule:type_name -> com.tapsilat.ParsSimulate.v1.Rule.RuleEntry
	3,  // 9: com.tapsilat.ParsSimulate.v1.SimulateResponse.nodes:type_name -> com.tapsilat.ParsSimulate.v1.Node
	2,  // 10: com.tapsilat.ParsSimulate.v1.SimulateResponse.edges:type_name -> com.tapsilat.ParsSimulate.v1.Edge
	10, // 11: com.tapsilat.ParsSimulate.v1.SimulateResponse.simulate_output:type_name -> com.tapsilat.ParsSimulate.v1.SimulateOutput
	11, // 12: com.tapsilat.ParsSimulate.v1.SimulatePaginatedData.rows:type_name -> com.tapsilat.ParsSimulate.v1.SimulateResponse
	3,  // 13: com.tapsilat.ParsSimulate.v1.SimulateRequestContent.nodes:type_name -> com.tapsilat.ParsSimulate.v1.Node
	2,  // 14: com.tapsilat.ParsSimulate.v1.SimulateRequestContent.edges:type_name -> com.tapsilat.ParsSimulate.v1.Edge
	0,  // 15: com.tapsilat.ParsSimulate.v1.ParsSimulateService.Simulate:input_type -> com.tapsilat.ParsSimulate.v1.SimulateDTO
	16, // 16: com.tapsilat.ParsSimulate.v1.ParsSimulateService.GetSimulations:input_type -> com.tapsilat.ParsSimulate.v1.SimulatePageRequest
	14, // 17: com.tapsilat.ParsSimulate.v1.ParsSimulateService.GetSimulationTokenBySimulateID:input_type -> com.tapsilat.ParsSimulate.v1.SimulateGet
	16, // 18: com.tapsilat.ParsSimulate.v1.ParsSimulateService.GetSimulationsShort:input_type -> com.tapsilat.ParsSimulate.v1.SimulatePageRequest
	0,  // 19: com.tapsilat.ParsSimulate.v1.ParsSimulateService.UpdateOrCreateSimulate:input_type -> com.tapsilat.ParsSimulate.v1.SimulateDTO
	13, // 20: com.tapsilat.ParsSimulate.v1.ParsSimulateService.DeleteSimulate:input_type -> com.tapsilat.ParsSimulate.v1.SimulateDelete
	16, // 21: com.tapsilat.ParsSimulate.v1.ParsSimulateService.GetSimulationRules:input_type -> com.tapsilat.ParsSimulate.v1.SimulatePageRequest
	14, // 22: com.tapsilat.ParsSimulate.v1.ParsSimulateService.SimulateRule:input_type -> com.tapsilat.ParsSimulate.v1.SimulateGet
	15, // 23: com.tapsilat.ParsSimulate.v1.ParsSimulateService.SimByID:input_type -> com.tapsilat.ParsSimulate.v1.SimulateByID
	1,  // 24: com.tapsilat.ParsSimulate.v1.ParsSimulateService.Simulate:output_type -> com.tapsilat.ParsSimulate.v1.ResponseSimulate
	12, // 25: com.tapsilat.ParsSimulate.v1.ParsSimulateService.GetSimulations:output_type -> com.tapsilat.ParsSimulate.v1.SimulatePaginatedData
	17, // 26: com.tapsilat.ParsSimulate.v1.ParsSimulateService.GetSimulationTokenBySimulateID:output_type -> com.tapsilat.ParsSimulate.v1.SimulateGeneralResponse
	12, // 27: com.tapsilat.ParsSimulate.v1.ParsSimulateService.GetSimulationsShort:output_type -> com.tapsilat.ParsSimulate.v1.SimulatePaginatedData
	17, // 28: com.tapsilat.ParsSimulate.v1.ParsSimulateService.UpdateOrCreateSimulate:output_type -> com.tapsilat.ParsSimulate.v1.SimulateGeneralResponse
	17, // 29: com.tapsilat.ParsSimulate.v1.ParsSimulateService.DeleteSimulate:output_type -> com.tapsilat.ParsSimulate.v1.SimulateGeneralResponse
	12, // 30: com.tapsilat.ParsSimulate.v1.ParsSimulateService.GetSimulationRules:output_type -> com.tapsilat.ParsSimulate.v1.SimulatePaginatedData
	11, // 31: com.tapsilat.ParsSimulate.v1.ParsSimulateService.SimulateRule:output_type -> com.tapsilat.ParsSimulate.v1.SimulateResponse
	1,  // 32: com.tapsilat.ParsSimulate.v1.ParsSimulateService.SimByID:output_type -> com.tapsilat.ParsSimulate.v1.ResponseSimulate
	24, // [24:33] is the sub-list for method output_type
	15, // [15:24] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_protos_pars_simulate_proto_init() }
func file_protos_pars_simulate_proto_init() {
	if File_protos_pars_simulate_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_pars_simulate_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*SimulateDTO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*ResponseSimulate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*Edge); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*Node); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*NodePosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*Content); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*Rule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*Statement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Expression); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*InputOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*SimulateOutput); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*SimulateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*SimulatePaginatedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*SimulateDelete); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*SimulateGet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*SimulateByID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*SimulatePageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*SimulateGeneralResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_pars_simulate_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*SimulateRequestContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_protos_pars_simulate_proto_msgTypes[3].OneofWrappers = []any{
		(*Node_Object)(nil),
		(*Node_String_)(nil),
	}
	file_protos_pars_simulate_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_pars_simulate_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_pars_simulate_proto_goTypes,
		DependencyIndexes: file_protos_pars_simulate_proto_depIdxs,
		MessageInfos:      file_protos_pars_simulate_proto_msgTypes,
	}.Build()
	File_protos_pars_simulate_proto = out.File
	file_protos_pars_simulate_proto_rawDesc = nil
	file_protos_pars_simulate_proto_goTypes = nil
	file_protos_pars_simulate_proto_depIdxs = nil
}
