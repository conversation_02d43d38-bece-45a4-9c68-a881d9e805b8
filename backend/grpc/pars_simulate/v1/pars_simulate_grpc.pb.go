// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: protos/pars_simulate.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ParsSimulateService_Simulate_FullMethodName                       = "/com.tapsilat.ParsSimulate.v1.ParsSimulateService/Simulate"
	ParsSimulateService_GetSimulations_FullMethodName                 = "/com.tapsilat.ParsSimulate.v1.ParsSimulateService/GetSimulations"
	ParsSimulateService_GetSimulationTokenBySimulateID_FullMethodName = "/com.tapsilat.ParsSimulate.v1.ParsSimulateService/GetSimulationTokenBySimulateID"
	ParsSimulateService_GetSimulationsShort_FullMethodName            = "/com.tapsilat.ParsSimulate.v1.ParsSimulateService/GetSimulationsShort"
	ParsSimulateService_UpdateOrCreateSimulate_FullMethodName         = "/com.tapsilat.ParsSimulate.v1.ParsSimulateService/UpdateOrCreateSimulate"
	ParsSimulateService_DeleteSimulate_FullMethodName                 = "/com.tapsilat.ParsSimulate.v1.ParsSimulateService/DeleteSimulate"
	ParsSimulateService_GetSimulationRules_FullMethodName             = "/com.tapsilat.ParsSimulate.v1.ParsSimulateService/GetSimulationRules"
	ParsSimulateService_SimulateRule_FullMethodName                   = "/com.tapsilat.ParsSimulate.v1.ParsSimulateService/SimulateRule"
	ParsSimulateService_SimByID_FullMethodName                        = "/com.tapsilat.ParsSimulate.v1.ParsSimulateService/SimByID"
)

// ParsSimulateServiceClient is the client API for ParsSimulateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ParsSimulateServiceClient interface {
	Simulate(ctx context.Context, in *SimulateDTO, opts ...grpc.CallOption) (*ResponseSimulate, error)
	GetSimulations(ctx context.Context, in *SimulatePageRequest, opts ...grpc.CallOption) (*SimulatePaginatedData, error)
	GetSimulationTokenBySimulateID(ctx context.Context, in *SimulateGet, opts ...grpc.CallOption) (*SimulateGeneralResponse, error)
	GetSimulationsShort(ctx context.Context, in *SimulatePageRequest, opts ...grpc.CallOption) (*SimulatePaginatedData, error)
	UpdateOrCreateSimulate(ctx context.Context, in *SimulateDTO, opts ...grpc.CallOption) (*SimulateGeneralResponse, error)
	DeleteSimulate(ctx context.Context, in *SimulateDelete, opts ...grpc.CallOption) (*SimulateGeneralResponse, error)
	GetSimulationRules(ctx context.Context, in *SimulatePageRequest, opts ...grpc.CallOption) (*SimulatePaginatedData, error)
	SimulateRule(ctx context.Context, in *SimulateGet, opts ...grpc.CallOption) (*SimulateResponse, error)
	SimByID(ctx context.Context, in *SimulateByID, opts ...grpc.CallOption) (*ResponseSimulate, error)
}

type parsSimulateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewParsSimulateServiceClient(cc grpc.ClientConnInterface) ParsSimulateServiceClient {
	return &parsSimulateServiceClient{cc}
}

func (c *parsSimulateServiceClient) Simulate(ctx context.Context, in *SimulateDTO, opts ...grpc.CallOption) (*ResponseSimulate, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResponseSimulate)
	err := c.cc.Invoke(ctx, ParsSimulateService_Simulate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsSimulateServiceClient) GetSimulations(ctx context.Context, in *SimulatePageRequest, opts ...grpc.CallOption) (*SimulatePaginatedData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimulatePaginatedData)
	err := c.cc.Invoke(ctx, ParsSimulateService_GetSimulations_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsSimulateServiceClient) GetSimulationTokenBySimulateID(ctx context.Context, in *SimulateGet, opts ...grpc.CallOption) (*SimulateGeneralResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimulateGeneralResponse)
	err := c.cc.Invoke(ctx, ParsSimulateService_GetSimulationTokenBySimulateID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsSimulateServiceClient) GetSimulationsShort(ctx context.Context, in *SimulatePageRequest, opts ...grpc.CallOption) (*SimulatePaginatedData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimulatePaginatedData)
	err := c.cc.Invoke(ctx, ParsSimulateService_GetSimulationsShort_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsSimulateServiceClient) UpdateOrCreateSimulate(ctx context.Context, in *SimulateDTO, opts ...grpc.CallOption) (*SimulateGeneralResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimulateGeneralResponse)
	err := c.cc.Invoke(ctx, ParsSimulateService_UpdateOrCreateSimulate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsSimulateServiceClient) DeleteSimulate(ctx context.Context, in *SimulateDelete, opts ...grpc.CallOption) (*SimulateGeneralResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimulateGeneralResponse)
	err := c.cc.Invoke(ctx, ParsSimulateService_DeleteSimulate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsSimulateServiceClient) GetSimulationRules(ctx context.Context, in *SimulatePageRequest, opts ...grpc.CallOption) (*SimulatePaginatedData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimulatePaginatedData)
	err := c.cc.Invoke(ctx, ParsSimulateService_GetSimulationRules_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsSimulateServiceClient) SimulateRule(ctx context.Context, in *SimulateGet, opts ...grpc.CallOption) (*SimulateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimulateResponse)
	err := c.cc.Invoke(ctx, ParsSimulateService_SimulateRule_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *parsSimulateServiceClient) SimByID(ctx context.Context, in *SimulateByID, opts ...grpc.CallOption) (*ResponseSimulate, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResponseSimulate)
	err := c.cc.Invoke(ctx, ParsSimulateService_SimByID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ParsSimulateServiceServer is the server API for ParsSimulateService service.
// All implementations must embed UnimplementedParsSimulateServiceServer
// for forward compatibility.
type ParsSimulateServiceServer interface {
	Simulate(context.Context, *SimulateDTO) (*ResponseSimulate, error)
	GetSimulations(context.Context, *SimulatePageRequest) (*SimulatePaginatedData, error)
	GetSimulationTokenBySimulateID(context.Context, *SimulateGet) (*SimulateGeneralResponse, error)
	GetSimulationsShort(context.Context, *SimulatePageRequest) (*SimulatePaginatedData, error)
	UpdateOrCreateSimulate(context.Context, *SimulateDTO) (*SimulateGeneralResponse, error)
	DeleteSimulate(context.Context, *SimulateDelete) (*SimulateGeneralResponse, error)
	GetSimulationRules(context.Context, *SimulatePageRequest) (*SimulatePaginatedData, error)
	SimulateRule(context.Context, *SimulateGet) (*SimulateResponse, error)
	SimByID(context.Context, *SimulateByID) (*ResponseSimulate, error)
	mustEmbedUnimplementedParsSimulateServiceServer()
}

// UnimplementedParsSimulateServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedParsSimulateServiceServer struct{}

func (UnimplementedParsSimulateServiceServer) Simulate(context.Context, *SimulateDTO) (*ResponseSimulate, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Simulate not implemented")
}
func (UnimplementedParsSimulateServiceServer) GetSimulations(context.Context, *SimulatePageRequest) (*SimulatePaginatedData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimulations not implemented")
}
func (UnimplementedParsSimulateServiceServer) GetSimulationTokenBySimulateID(context.Context, *SimulateGet) (*SimulateGeneralResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimulationTokenBySimulateID not implemented")
}
func (UnimplementedParsSimulateServiceServer) GetSimulationsShort(context.Context, *SimulatePageRequest) (*SimulatePaginatedData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimulationsShort not implemented")
}
func (UnimplementedParsSimulateServiceServer) UpdateOrCreateSimulate(context.Context, *SimulateDTO) (*SimulateGeneralResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOrCreateSimulate not implemented")
}
func (UnimplementedParsSimulateServiceServer) DeleteSimulate(context.Context, *SimulateDelete) (*SimulateGeneralResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSimulate not implemented")
}
func (UnimplementedParsSimulateServiceServer) GetSimulationRules(context.Context, *SimulatePageRequest) (*SimulatePaginatedData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimulationRules not implemented")
}
func (UnimplementedParsSimulateServiceServer) SimulateRule(context.Context, *SimulateGet) (*SimulateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SimulateRule not implemented")
}
func (UnimplementedParsSimulateServiceServer) SimByID(context.Context, *SimulateByID) (*ResponseSimulate, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SimByID not implemented")
}
func (UnimplementedParsSimulateServiceServer) mustEmbedUnimplementedParsSimulateServiceServer() {}
func (UnimplementedParsSimulateServiceServer) testEmbeddedByValue()                             {}

// UnsafeParsSimulateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ParsSimulateServiceServer will
// result in compilation errors.
type UnsafeParsSimulateServiceServer interface {
	mustEmbedUnimplementedParsSimulateServiceServer()
}

func RegisterParsSimulateServiceServer(s grpc.ServiceRegistrar, srv ParsSimulateServiceServer) {
	// If the following call pancis, it indicates UnimplementedParsSimulateServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ParsSimulateService_ServiceDesc, srv)
}

func _ParsSimulateService_Simulate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsSimulateServiceServer).Simulate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsSimulateService_Simulate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsSimulateServiceServer).Simulate(ctx, req.(*SimulateDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsSimulateService_GetSimulations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulatePageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsSimulateServiceServer).GetSimulations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsSimulateService_GetSimulations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsSimulateServiceServer).GetSimulations(ctx, req.(*SimulatePageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsSimulateService_GetSimulationTokenBySimulateID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateGet)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsSimulateServiceServer).GetSimulationTokenBySimulateID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsSimulateService_GetSimulationTokenBySimulateID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsSimulateServiceServer).GetSimulationTokenBySimulateID(ctx, req.(*SimulateGet))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsSimulateService_GetSimulationsShort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulatePageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsSimulateServiceServer).GetSimulationsShort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsSimulateService_GetSimulationsShort_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsSimulateServiceServer).GetSimulationsShort(ctx, req.(*SimulatePageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsSimulateService_UpdateOrCreateSimulate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsSimulateServiceServer).UpdateOrCreateSimulate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsSimulateService_UpdateOrCreateSimulate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsSimulateServiceServer).UpdateOrCreateSimulate(ctx, req.(*SimulateDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsSimulateService_DeleteSimulate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateDelete)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsSimulateServiceServer).DeleteSimulate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsSimulateService_DeleteSimulate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsSimulateServiceServer).DeleteSimulate(ctx, req.(*SimulateDelete))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsSimulateService_GetSimulationRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulatePageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsSimulateServiceServer).GetSimulationRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsSimulateService_GetSimulationRules_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsSimulateServiceServer).GetSimulationRules(ctx, req.(*SimulatePageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsSimulateService_SimulateRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateGet)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsSimulateServiceServer).SimulateRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsSimulateService_SimulateRule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsSimulateServiceServer).SimulateRule(ctx, req.(*SimulateGet))
	}
	return interceptor(ctx, in, info, handler)
}

func _ParsSimulateService_SimByID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SimulateByID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ParsSimulateServiceServer).SimByID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ParsSimulateService_SimByID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ParsSimulateServiceServer).SimByID(ctx, req.(*SimulateByID))
	}
	return interceptor(ctx, in, info, handler)
}

// ParsSimulateService_ServiceDesc is the grpc.ServiceDesc for ParsSimulateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ParsSimulateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.tapsilat.ParsSimulate.v1.ParsSimulateService",
	HandlerType: (*ParsSimulateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Simulate",
			Handler:    _ParsSimulateService_Simulate_Handler,
		},
		{
			MethodName: "GetSimulations",
			Handler:    _ParsSimulateService_GetSimulations_Handler,
		},
		{
			MethodName: "GetSimulationTokenBySimulateID",
			Handler:    _ParsSimulateService_GetSimulationTokenBySimulateID_Handler,
		},
		{
			MethodName: "GetSimulationsShort",
			Handler:    _ParsSimulateService_GetSimulationsShort_Handler,
		},
		{
			MethodName: "UpdateOrCreateSimulate",
			Handler:    _ParsSimulateService_UpdateOrCreateSimulate_Handler,
		},
		{
			MethodName: "DeleteSimulate",
			Handler:    _ParsSimulateService_DeleteSimulate_Handler,
		},
		{
			MethodName: "GetSimulationRules",
			Handler:    _ParsSimulateService_GetSimulationRules_Handler,
		},
		{
			MethodName: "SimulateRule",
			Handler:    _ParsSimulateService_SimulateRule_Handler,
		},
		{
			MethodName: "SimByID",
			Handler:    _ParsSimulateService_SimByID_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/pars_simulate.proto",
}
