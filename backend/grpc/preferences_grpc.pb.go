// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: protos/preferences.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PreferencesService_AddDSN_FullMethodName    = "/preferences.PreferencesService/AddDSN"
	PreferencesService_GetDSN_FullMethodName    = "/preferences.PreferencesService/GetDSN"
	PreferencesService_DeleteDSN_FullMethodName = "/preferences.PreferencesService/DeleteDSN"
)

// PreferencesServiceClient is the client API for PreferencesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PreferencesServiceClient interface {
	AddDSN(ctx context.Context, in *DSNAddDTO, opts ...grpc.CallOption) (*DSNResponseDTO, error)
	GetDSN(ctx context.Context, in *DSNEmptyDTO, opts ...grpc.CallOption) (*DSNGetResponseDTO, error)
	DeleteDSN(ctx context.Context, in *DSNDeleteDTO, opts ...grpc.CallOption) (*DSNResponseDTO, error)
}

type preferencesServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPreferencesServiceClient(cc grpc.ClientConnInterface) PreferencesServiceClient {
	return &preferencesServiceClient{cc}
}

func (c *preferencesServiceClient) AddDSN(ctx context.Context, in *DSNAddDTO, opts ...grpc.CallOption) (*DSNResponseDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DSNResponseDTO)
	err := c.cc.Invoke(ctx, PreferencesService_AddDSN_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preferencesServiceClient) GetDSN(ctx context.Context, in *DSNEmptyDTO, opts ...grpc.CallOption) (*DSNGetResponseDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DSNGetResponseDTO)
	err := c.cc.Invoke(ctx, PreferencesService_GetDSN_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *preferencesServiceClient) DeleteDSN(ctx context.Context, in *DSNDeleteDTO, opts ...grpc.CallOption) (*DSNResponseDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DSNResponseDTO)
	err := c.cc.Invoke(ctx, PreferencesService_DeleteDSN_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PreferencesServiceServer is the server API for PreferencesService service.
// All implementations must embed UnimplementedPreferencesServiceServer
// for forward compatibility.
type PreferencesServiceServer interface {
	AddDSN(context.Context, *DSNAddDTO) (*DSNResponseDTO, error)
	GetDSN(context.Context, *DSNEmptyDTO) (*DSNGetResponseDTO, error)
	DeleteDSN(context.Context, *DSNDeleteDTO) (*DSNResponseDTO, error)
	mustEmbedUnimplementedPreferencesServiceServer()
}

// UnimplementedPreferencesServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPreferencesServiceServer struct{}

func (UnimplementedPreferencesServiceServer) AddDSN(context.Context, *DSNAddDTO) (*DSNResponseDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddDSN not implemented")
}
func (UnimplementedPreferencesServiceServer) GetDSN(context.Context, *DSNEmptyDTO) (*DSNGetResponseDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDSN not implemented")
}
func (UnimplementedPreferencesServiceServer) DeleteDSN(context.Context, *DSNDeleteDTO) (*DSNResponseDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDSN not implemented")
}
func (UnimplementedPreferencesServiceServer) mustEmbedUnimplementedPreferencesServiceServer() {}
func (UnimplementedPreferencesServiceServer) testEmbeddedByValue()                            {}

// UnsafePreferencesServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PreferencesServiceServer will
// result in compilation errors.
type UnsafePreferencesServiceServer interface {
	mustEmbedUnimplementedPreferencesServiceServer()
}

func RegisterPreferencesServiceServer(s grpc.ServiceRegistrar, srv PreferencesServiceServer) {
	// If the following call pancis, it indicates UnimplementedPreferencesServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PreferencesService_ServiceDesc, srv)
}

func _PreferencesService_AddDSN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DSNAddDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreferencesServiceServer).AddDSN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreferencesService_AddDSN_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreferencesServiceServer).AddDSN(ctx, req.(*DSNAddDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreferencesService_GetDSN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DSNEmptyDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreferencesServiceServer).GetDSN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreferencesService_GetDSN_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreferencesServiceServer).GetDSN(ctx, req.(*DSNEmptyDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _PreferencesService_DeleteDSN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DSNDeleteDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PreferencesServiceServer).DeleteDSN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PreferencesService_DeleteDSN_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PreferencesServiceServer).DeleteDSN(ctx, req.(*DSNDeleteDTO))
	}
	return interceptor(ctx, in, info, handler)
}

// PreferencesService_ServiceDesc is the grpc.ServiceDesc for PreferencesService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PreferencesService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "preferences.PreferencesService",
	HandlerType: (*PreferencesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddDSN",
			Handler:    _PreferencesService_AddDSN_Handler,
		},
		{
			MethodName: "GetDSN",
			Handler:    _PreferencesService_GetDSN_Handler,
		},
		{
			MethodName: "DeleteDSN",
			Handler:    _PreferencesService_DeleteDSN_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/preferences.proto",
}
