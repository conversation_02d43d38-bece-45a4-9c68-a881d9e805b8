// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: protos/log.proto

package grpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	LogService_GetLog_FullMethodName               = "/log.LogService/GetLog"
	LogService_GetLogs_FullMethodName              = "/log.LogService/GetLogs"
	LogService_GetSimulateLog_FullMethodName       = "/log.LogService/GetSimulateLog"
	LogService_GetSingleSimulateLog_FullMethodName = "/log.LogService/GetSingleSimulateLog"
)

// LogServiceClient is the client API for LogService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LogServiceClient interface {
	GetLog(ctx context.Context, in *LogRequestDTO, opts ...grpc.CallOption) (*LogDTO, error)
	GetLogs(ctx context.Context, in *LogPageRequestDTO, opts ...grpc.CallOption) (*LogPaginatedData, error)
	GetSimulateLog(ctx context.Context, in *LogSimulateRequestDTO, opts ...grpc.CallOption) (*LogPaginatedData, error)
	GetSingleSimulateLog(ctx context.Context, in *LogSingleSimulateLog, opts ...grpc.CallOption) (*LogSimulateLog, error)
}

type logServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLogServiceClient(cc grpc.ClientConnInterface) LogServiceClient {
	return &logServiceClient{cc}
}

func (c *logServiceClient) GetLog(ctx context.Context, in *LogRequestDTO, opts ...grpc.CallOption) (*LogDTO, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogDTO)
	err := c.cc.Invoke(ctx, LogService_GetLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *logServiceClient) GetLogs(ctx context.Context, in *LogPageRequestDTO, opts ...grpc.CallOption) (*LogPaginatedData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogPaginatedData)
	err := c.cc.Invoke(ctx, LogService_GetLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *logServiceClient) GetSimulateLog(ctx context.Context, in *LogSimulateRequestDTO, opts ...grpc.CallOption) (*LogPaginatedData, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogPaginatedData)
	err := c.cc.Invoke(ctx, LogService_GetSimulateLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *logServiceClient) GetSingleSimulateLog(ctx context.Context, in *LogSingleSimulateLog, opts ...grpc.CallOption) (*LogSimulateLog, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogSimulateLog)
	err := c.cc.Invoke(ctx, LogService_GetSingleSimulateLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LogServiceServer is the server API for LogService service.
// All implementations must embed UnimplementedLogServiceServer
// for forward compatibility.
type LogServiceServer interface {
	GetLog(context.Context, *LogRequestDTO) (*LogDTO, error)
	GetLogs(context.Context, *LogPageRequestDTO) (*LogPaginatedData, error)
	GetSimulateLog(context.Context, *LogSimulateRequestDTO) (*LogPaginatedData, error)
	GetSingleSimulateLog(context.Context, *LogSingleSimulateLog) (*LogSimulateLog, error)
	mustEmbedUnimplementedLogServiceServer()
}

// UnimplementedLogServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLogServiceServer struct{}

func (UnimplementedLogServiceServer) GetLog(context.Context, *LogRequestDTO) (*LogDTO, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLog not implemented")
}
func (UnimplementedLogServiceServer) GetLogs(context.Context, *LogPageRequestDTO) (*LogPaginatedData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLogs not implemented")
}
func (UnimplementedLogServiceServer) GetSimulateLog(context.Context, *LogSimulateRequestDTO) (*LogPaginatedData, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimulateLog not implemented")
}
func (UnimplementedLogServiceServer) GetSingleSimulateLog(context.Context, *LogSingleSimulateLog) (*LogSimulateLog, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSingleSimulateLog not implemented")
}
func (UnimplementedLogServiceServer) mustEmbedUnimplementedLogServiceServer() {}
func (UnimplementedLogServiceServer) testEmbeddedByValue()                    {}

// UnsafeLogServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LogServiceServer will
// result in compilation errors.
type UnsafeLogServiceServer interface {
	mustEmbedUnimplementedLogServiceServer()
}

func RegisterLogServiceServer(s grpc.ServiceRegistrar, srv LogServiceServer) {
	// If the following call pancis, it indicates UnimplementedLogServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LogService_ServiceDesc, srv)
}

func _LogService_GetLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogRequestDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LogServiceServer).GetLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LogService_GetLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LogServiceServer).GetLog(ctx, req.(*LogRequestDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _LogService_GetLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogPageRequestDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LogServiceServer).GetLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LogService_GetLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LogServiceServer).GetLogs(ctx, req.(*LogPageRequestDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _LogService_GetSimulateLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogSimulateRequestDTO)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LogServiceServer).GetSimulateLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LogService_GetSimulateLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LogServiceServer).GetSimulateLog(ctx, req.(*LogSimulateRequestDTO))
	}
	return interceptor(ctx, in, info, handler)
}

func _LogService_GetSingleSimulateLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogSingleSimulateLog)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LogServiceServer).GetSingleSimulateLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LogService_GetSingleSimulateLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LogServiceServer).GetSingleSimulateLog(ctx, req.(*LogSingleSimulateLog))
	}
	return interceptor(ctx, in, info, handler)
}

// LogService_ServiceDesc is the grpc.ServiceDesc for LogService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LogService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "log.LogService",
	HandlerType: (*LogServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLog",
			Handler:    _LogService_GetLog_Handler,
		},
		{
			MethodName: "GetLogs",
			Handler:    _LogService_GetLogs_Handler,
		},
		{
			MethodName: "GetSimulateLog",
			Handler:    _LogService_GetSimulateLog_Handler,
		},
		{
			MethodName: "GetSingleSimulateLog",
			Handler:    _LogService_GetSingleSimulateLog_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/log.proto",
}
