// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: protos/store.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StoreConditionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db       string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table    string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
	Kv       []*Kv  `protobuf:"bytes,3,rep,name=kv,proto3" json:"kv,omitempty"`
	Limit    int64  `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset   int64  `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	OrderBy  string `protobuf:"bytes,6,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	GroupBy  string `protobuf:"bytes,7,opt,name=group_by,json=groupBy,proto3" json:"group_by,omitempty"`
	RawQuery string `protobuf:"bytes,8,opt,name=raw_query,json=rawQuery,proto3" json:"raw_query,omitempty"`
}

func (x *StoreConditionRequest) Reset() {
	*x = StoreConditionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreConditionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreConditionRequest) ProtoMessage() {}

func (x *StoreConditionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreConditionRequest.ProtoReflect.Descriptor instead.
func (*StoreConditionRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{0}
}

func (x *StoreConditionRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreConditionRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *StoreConditionRequest) GetKv() []*Kv {
	if x != nil {
		return x.Kv
	}
	return nil
}

func (x *StoreConditionRequest) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *StoreConditionRequest) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *StoreConditionRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *StoreConditionRequest) GetGroupBy() string {
	if x != nil {
		return x.GroupBy
	}
	return ""
}

func (x *StoreConditionRequest) GetRawQuery() string {
	if x != nil {
		return x.RawQuery
	}
	return ""
}

type Kv struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	K string `protobuf:"bytes,1,opt,name=k,proto3" json:"k,omitempty"`
	V string `protobuf:"bytes,2,opt,name=v,proto3" json:"v,omitempty"`
}

func (x *Kv) Reset() {
	*x = Kv{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Kv) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Kv) ProtoMessage() {}

func (x *Kv) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Kv.ProtoReflect.Descriptor instead.
func (*Kv) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{1}
}

func (x *Kv) GetK() string {
	if x != nil {
		return x.K
	}
	return ""
}

func (x *Kv) GetV() string {
	if x != nil {
		return x.V
	}
	return ""
}

type StoreRow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kv []*Kv `protobuf:"bytes,1,rep,name=kv,proto3" json:"kv,omitempty"`
}

func (x *StoreRow) Reset() {
	*x = StoreRow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreRow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreRow) ProtoMessage() {}

func (x *StoreRow) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreRow.ProtoReflect.Descriptor instead.
func (*StoreRow) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{2}
}

func (x *StoreRow) GetKv() []*Kv {
	if x != nil {
		return x.Kv
	}
	return nil
}

type StoreResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success      bool        `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message      string      `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	AffectedRows int64       `protobuf:"varint,3,opt,name=affected_rows,json=affectedRows,proto3" json:"affected_rows,omitempty"`
	StoreRows    []*StoreRow `protobuf:"bytes,4,rep,name=store_rows,json=storeRows,proto3" json:"store_rows,omitempty"`
}

func (x *StoreResult) Reset() {
	*x = StoreResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreResult) ProtoMessage() {}

func (x *StoreResult) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreResult.ProtoReflect.Descriptor instead.
func (*StoreResult) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{3}
}

func (x *StoreResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StoreResult) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StoreResult) GetAffectedRows() int64 {
	if x != nil {
		return x.AffectedRows
	}
	return 0
}

func (x *StoreResult) GetStoreRows() []*StoreRow {
	if x != nil {
		return x.StoreRows
	}
	return nil
}

type StoreInsertRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db    string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
	Kv    []*Kv  `protobuf:"bytes,3,rep,name=kv,proto3" json:"kv,omitempty"`
}

func (x *StoreInsertRequest) Reset() {
	*x = StoreInsertRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreInsertRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreInsertRequest) ProtoMessage() {}

func (x *StoreInsertRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreInsertRequest.ProtoReflect.Descriptor instead.
func (*StoreInsertRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{4}
}

func (x *StoreInsertRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreInsertRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *StoreInsertRequest) GetKv() []*Kv {
	if x != nil {
		return x.Kv
	}
	return nil
}

type StoreUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db    string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
	Kv    []*Kv  `protobuf:"bytes,3,rep,name=kv,proto3" json:"kv,omitempty"`
}

func (x *StoreUpdateRequest) Reset() {
	*x = StoreUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreUpdateRequest) ProtoMessage() {}

func (x *StoreUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreUpdateRequest.ProtoReflect.Descriptor instead.
func (*StoreUpdateRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{5}
}

func (x *StoreUpdateRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreUpdateRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *StoreUpdateRequest) GetKv() []*Kv {
	if x != nil {
		return x.Kv
	}
	return nil
}

type StoreUpsertRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db    string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
	Kv    []*Kv  `protobuf:"bytes,3,rep,name=kv,proto3" json:"kv,omitempty"`
}

func (x *StoreUpsertRequest) Reset() {
	*x = StoreUpsertRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreUpsertRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreUpsertRequest) ProtoMessage() {}

func (x *StoreUpsertRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreUpsertRequest.ProtoReflect.Descriptor instead.
func (*StoreUpsertRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{6}
}

func (x *StoreUpsertRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreUpsertRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *StoreUpsertRequest) GetKv() []*Kv {
	if x != nil {
		return x.Kv
	}
	return nil
}

type StoreDeleteRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db          string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table       string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
	Kv          []*Kv  `protobuf:"bytes,3,rep,name=kv,proto3" json:"kv,omitempty"`
	SolftDelete bool   `protobuf:"varint,4,opt,name=solft_delete,json=solftDelete,proto3" json:"solft_delete,omitempty"`
}

func (x *StoreDeleteRequest) Reset() {
	*x = StoreDeleteRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreDeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreDeleteRequest) ProtoMessage() {}

func (x *StoreDeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreDeleteRequest.ProtoReflect.Descriptor instead.
func (*StoreDeleteRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{7}
}

func (x *StoreDeleteRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreDeleteRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *StoreDeleteRequest) GetKv() []*Kv {
	if x != nil {
		return x.Kv
	}
	return nil
}

func (x *StoreDeleteRequest) GetSolftDelete() bool {
	if x != nil {
		return x.SolftDelete
	}
	return false
}

type StoreDbCreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db    string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
	Kv    []*Kv  `protobuf:"bytes,3,rep,name=kv,proto3" json:"kv,omitempty"`
}

func (x *StoreDbCreateRequest) Reset() {
	*x = StoreDbCreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreDbCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreDbCreateRequest) ProtoMessage() {}

func (x *StoreDbCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreDbCreateRequest.ProtoReflect.Descriptor instead.
func (*StoreDbCreateRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{8}
}

func (x *StoreDbCreateRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreDbCreateRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *StoreDbCreateRequest) GetKv() []*Kv {
	if x != nil {
		return x.Kv
	}
	return nil
}

type StoreDbDropRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
}

func (x *StoreDbDropRequest) Reset() {
	*x = StoreDbDropRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreDbDropRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreDbDropRequest) ProtoMessage() {}

func (x *StoreDbDropRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreDbDropRequest.ProtoReflect.Descriptor instead.
func (*StoreDbDropRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{9}
}

func (x *StoreDbDropRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

type StoreTableCreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db    string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
	Kv    []*Kv  `protobuf:"bytes,3,rep,name=kv,proto3" json:"kv,omitempty"`
}

func (x *StoreTableCreateRequest) Reset() {
	*x = StoreTableCreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreTableCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreTableCreateRequest) ProtoMessage() {}

func (x *StoreTableCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreTableCreateRequest.ProtoReflect.Descriptor instead.
func (*StoreTableCreateRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{10}
}

func (x *StoreTableCreateRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreTableCreateRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *StoreTableCreateRequest) GetKv() []*Kv {
	if x != nil {
		return x.Kv
	}
	return nil
}

type StoreTableDropRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db    string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
}

func (x *StoreTableDropRequest) Reset() {
	*x = StoreTableDropRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreTableDropRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreTableDropRequest) ProtoMessage() {}

func (x *StoreTableDropRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreTableDropRequest.ProtoReflect.Descriptor instead.
func (*StoreTableDropRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{11}
}

func (x *StoreTableDropRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreTableDropRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

type StoreDbListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
}

func (x *StoreDbListRequest) Reset() {
	*x = StoreDbListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreDbListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreDbListRequest) ProtoMessage() {}

func (x *StoreDbListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreDbListRequest.ProtoReflect.Descriptor instead.
func (*StoreDbListRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{12}
}

func (x *StoreDbListRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

type StoreTableListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
}

func (x *StoreTableListRequest) Reset() {
	*x = StoreTableListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreTableListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreTableListRequest) ProtoMessage() {}

func (x *StoreTableListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreTableListRequest.ProtoReflect.Descriptor instead.
func (*StoreTableListRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{13}
}

func (x *StoreTableListRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

type StoreTableListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tables []string `protobuf:"bytes,1,rep,name=tables,proto3" json:"tables,omitempty"`
}

func (x *StoreTableListResponse) Reset() {
	*x = StoreTableListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreTableListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreTableListResponse) ProtoMessage() {}

func (x *StoreTableListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreTableListResponse.ProtoReflect.Descriptor instead.
func (*StoreTableListResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{14}
}

func (x *StoreTableListResponse) GetTables() []string {
	if x != nil {
		return x.Tables
	}
	return nil
}

type StoreResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *StoreResponse) Reset() {
	*x = StoreResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreResponse) ProtoMessage() {}

func (x *StoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreResponse.ProtoReflect.Descriptor instead.
func (*StoreResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{15}
}

func (x *StoreResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StoreResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type StoreColumnCreateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db     string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table  string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
	Column string `protobuf:"bytes,3,opt,name=column,proto3" json:"column,omitempty"`
}

func (x *StoreColumnCreateRequest) Reset() {
	*x = StoreColumnCreateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreColumnCreateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreColumnCreateRequest) ProtoMessage() {}

func (x *StoreColumnCreateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreColumnCreateRequest.ProtoReflect.Descriptor instead.
func (*StoreColumnCreateRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{16}
}

func (x *StoreColumnCreateRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreColumnCreateRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *StoreColumnCreateRequest) GetColumn() string {
	if x != nil {
		return x.Column
	}
	return ""
}

type StoreColumnDropRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db     string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table  string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
	Column string `protobuf:"bytes,3,opt,name=column,proto3" json:"column,omitempty"`
}

func (x *StoreColumnDropRequest) Reset() {
	*x = StoreColumnDropRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreColumnDropRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreColumnDropRequest) ProtoMessage() {}

func (x *StoreColumnDropRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreColumnDropRequest.ProtoReflect.Descriptor instead.
func (*StoreColumnDropRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{17}
}

func (x *StoreColumnDropRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreColumnDropRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

func (x *StoreColumnDropRequest) GetColumn() string {
	if x != nil {
		return x.Column
	}
	return ""
}

type StoreColumnListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Db    string `protobuf:"bytes,1,opt,name=db,proto3" json:"db,omitempty"`
	Table string `protobuf:"bytes,2,opt,name=table,proto3" json:"table,omitempty"`
}

func (x *StoreColumnListRequest) Reset() {
	*x = StoreColumnListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreColumnListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreColumnListRequest) ProtoMessage() {}

func (x *StoreColumnListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreColumnListRequest.ProtoReflect.Descriptor instead.
func (*StoreColumnListRequest) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{18}
}

func (x *StoreColumnListRequest) GetDb() string {
	if x != nil {
		return x.Db
	}
	return ""
}

func (x *StoreColumnListRequest) GetTable() string {
	if x != nil {
		return x.Table
	}
	return ""
}

type StoreColumnListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Columns []string `protobuf:"bytes,1,rep,name=columns,proto3" json:"columns,omitempty"`
}

func (x *StoreColumnListResponse) Reset() {
	*x = StoreColumnListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_protos_store_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreColumnListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreColumnListResponse) ProtoMessage() {}

func (x *StoreColumnListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_store_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreColumnListResponse.ProtoReflect.Descriptor instead.
func (*StoreColumnListResponse) Descriptor() ([]byte, []int) {
	return file_protos_store_proto_rawDescGZIP(), []int{19}
}

func (x *StoreColumnListResponse) GetColumns() []string {
	if x != nil {
		return x.Columns
	}
	return nil
}

var File_protos_store_proto protoreflect.FileDescriptor

var file_protos_store_proto_rawDesc = []byte{
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x73, 0x2f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x22, 0xe6, 0x01, 0x0a, 0x15, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x64, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x02, 0x6b, 0x76, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x76, 0x52, 0x02, 0x6b, 0x76,
	0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x61, 0x77, 0x5f, 0x71, 0x75, 0x65, 0x72,
	0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x61, 0x77, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x22, 0x20, 0x0a, 0x02, 0x4b, 0x76, 0x12, 0x0c, 0x0a, 0x01, 0x6b, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x01, 0x6b, 0x12, 0x0c, 0x0a, 0x01, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x01, 0x76, 0x22, 0x32, 0x0a, 0x08, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x6f, 0x77, 0x12,
	0x26, 0x0a, 0x02, 0x6b, 0x76, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x75,
	0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4b, 0x76, 0x52, 0x02, 0x6b, 0x76, 0x22, 0xa3, 0x01, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x52, 0x6f, 0x77, 0x73,
	0x12, 0x3b, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x72, 0x6f, 0x77, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52,
	0x6f, 0x77, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x6f, 0x77, 0x73, 0x22, 0x62, 0x0a,
	0x12, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x64, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x02, 0x6b, 0x76, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72,
	0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x76, 0x52, 0x02, 0x6b,
	0x76, 0x22, 0x62, 0x0a, 0x12, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a,
	0x02, 0x6b, 0x76, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x75, 0x72, 0x75,
	0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b,
	0x76, 0x52, 0x02, 0x6b, 0x76, 0x22, 0x62, 0x0a, 0x12, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64,
	0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x26, 0x0a, 0x02, 0x6b, 0x76, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4b, 0x76, 0x52, 0x02, 0x6b, 0x76, 0x22, 0x85, 0x01, 0x0a, 0x12, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x02, 0x6b, 0x76, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x76, 0x52, 0x02, 0x6b, 0x76, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x6f, 0x6c, 0x66, 0x74, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x6f, 0x6c, 0x66, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x22, 0x64, 0x0a, 0x14, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x44, 0x62, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x26, 0x0a, 0x02, 0x6b, 0x76, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x75,
	0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4b, 0x76, 0x52, 0x02, 0x6b, 0x76, 0x22, 0x24, 0x0a, 0x12, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x44, 0x62, 0x44, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x22, 0x67, 0x0a,
	0x17, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x26,
	0x0a, 0x02, 0x6b, 0x76, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x75, 0x72,
	0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4b, 0x76, 0x52, 0x02, 0x6b, 0x76, 0x22, 0x3d, 0x0a, 0x15, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x44, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x24, 0x0a, 0x12, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x44, 0x62,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64,
	0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x22, 0x27, 0x0a, 0x15, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x64, 0x62, 0x22, 0x30, 0x0a, 0x16, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x61, 0x62,
	0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x22, 0x43, 0x0a, 0x0d, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x58, 0x0a, 0x18, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x22, 0x56, 0x0a, 0x16, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x44, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x22, 0x3e, 0x0a,
	0x16, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x64, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x64, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x33, 0x0a,
	0x17, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6c, 0x75,
	0x6d, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6c, 0x75, 0x6d,
	0x6e, 0x73, 0x32, 0xed, 0x09, 0x0a, 0x0c, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x51, 0x0a, 0x06, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x12, 0x26, 0x2e,
	0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72,
	0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x26, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e,
	0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x06, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x12, 0x26, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x75,
	0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x54, 0x0a, 0x06,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x29, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61,
	0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x51, 0x0a, 0x06, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x12, 0x26, 0x2e, 0x67,
	0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x57, 0x0a, 0x08, 0x44, 0x62, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0x28, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x44, 0x62, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x67, 0x75,
	0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x53,
	0x0a, 0x06, 0x44, 0x62, 0x44, 0x72, 0x6f, 0x70, 0x12, 0x26, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e,
	0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x44, 0x62, 0x44, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x21, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x53, 0x0a, 0x06, 0x44, 0x62, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x2e,
	0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x44, 0x62, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72,
	0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x0b, 0x54, 0x61, 0x62, 0x6c,
	0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x2b, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70,
	0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59, 0x0a, 0x09, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x44, 0x72, 0x6f, 0x70, 0x12, 0x29, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x44, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x59, 0x0a, 0x09, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x29, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x67, 0x75, 0x72,
	0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5f, 0x0a,
	0x0c, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x2e,
	0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x67, 0x75,
	0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b,
	0x0a, 0x0a, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x44, 0x72, 0x6f, 0x70, 0x12, 0x2a, 0x2e, 0x67,
	0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x44, 0x72, 0x6f,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e,
	0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x0a, 0x43,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x2e, 0x67, 0x75, 0x72, 0x75,
	0x2e, 0x70, 0x61, 0x72, 0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x67, 0x75, 0x72, 0x75, 0x2e, 0x70, 0x61, 0x72,
	0x73, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x11, 0x5a, 0x0f, 0x2e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x2f, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_protos_store_proto_rawDescOnce sync.Once
	file_protos_store_proto_rawDescData = file_protos_store_proto_rawDesc
)

func file_protos_store_proto_rawDescGZIP() []byte {
	file_protos_store_proto_rawDescOnce.Do(func() {
		file_protos_store_proto_rawDescData = protoimpl.X.CompressGZIP(file_protos_store_proto_rawDescData)
	})
	return file_protos_store_proto_rawDescData
}

var file_protos_store_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_protos_store_proto_goTypes = []any{
	(*StoreConditionRequest)(nil),    // 0: guru.pars.store.v1.StoreConditionRequest
	(*Kv)(nil),                       // 1: guru.pars.store.v1.Kv
	(*StoreRow)(nil),                 // 2: guru.pars.store.v1.StoreRow
	(*StoreResult)(nil),              // 3: guru.pars.store.v1.StoreResult
	(*StoreInsertRequest)(nil),       // 4: guru.pars.store.v1.StoreInsertRequest
	(*StoreUpdateRequest)(nil),       // 5: guru.pars.store.v1.StoreUpdateRequest
	(*StoreUpsertRequest)(nil),       // 6: guru.pars.store.v1.StoreUpsertRequest
	(*StoreDeleteRequest)(nil),       // 7: guru.pars.store.v1.StoreDeleteRequest
	(*StoreDbCreateRequest)(nil),     // 8: guru.pars.store.v1.StoreDbCreateRequest
	(*StoreDbDropRequest)(nil),       // 9: guru.pars.store.v1.StoreDbDropRequest
	(*StoreTableCreateRequest)(nil),  // 10: guru.pars.store.v1.StoreTableCreateRequest
	(*StoreTableDropRequest)(nil),    // 11: guru.pars.store.v1.StoreTableDropRequest
	(*StoreDbListRequest)(nil),       // 12: guru.pars.store.v1.StoreDbListRequest
	(*StoreTableListRequest)(nil),    // 13: guru.pars.store.v1.StoreTableListRequest
	(*StoreTableListResponse)(nil),   // 14: guru.pars.store.v1.StoreTableListResponse
	(*StoreResponse)(nil),            // 15: guru.pars.store.v1.StoreResponse
	(*StoreColumnCreateRequest)(nil), // 16: guru.pars.store.v1.StoreColumnCreateRequest
	(*StoreColumnDropRequest)(nil),   // 17: guru.pars.store.v1.StoreColumnDropRequest
	(*StoreColumnListRequest)(nil),   // 18: guru.pars.store.v1.StoreColumnListRequest
	(*StoreColumnListResponse)(nil),  // 19: guru.pars.store.v1.StoreColumnListResponse
}
var file_protos_store_proto_depIdxs = []int32{
	1,  // 0: guru.pars.store.v1.StoreConditionRequest.kv:type_name -> guru.pars.store.v1.Kv
	1,  // 1: guru.pars.store.v1.StoreRow.kv:type_name -> guru.pars.store.v1.Kv
	2,  // 2: guru.pars.store.v1.StoreResult.store_rows:type_name -> guru.pars.store.v1.StoreRow
	1,  // 3: guru.pars.store.v1.StoreInsertRequest.kv:type_name -> guru.pars.store.v1.Kv
	1,  // 4: guru.pars.store.v1.StoreUpdateRequest.kv:type_name -> guru.pars.store.v1.Kv
	1,  // 5: guru.pars.store.v1.StoreUpsertRequest.kv:type_name -> guru.pars.store.v1.Kv
	1,  // 6: guru.pars.store.v1.StoreDeleteRequest.kv:type_name -> guru.pars.store.v1.Kv
	1,  // 7: guru.pars.store.v1.StoreDbCreateRequest.kv:type_name -> guru.pars.store.v1.Kv
	1,  // 8: guru.pars.store.v1.StoreTableCreateRequest.kv:type_name -> guru.pars.store.v1.Kv
	4,  // 9: guru.pars.store.v1.StoreService.Insert:input_type -> guru.pars.store.v1.StoreInsertRequest
	5,  // 10: guru.pars.store.v1.StoreService.Update:input_type -> guru.pars.store.v1.StoreUpdateRequest
	7,  // 11: guru.pars.store.v1.StoreService.Delete:input_type -> guru.pars.store.v1.StoreDeleteRequest
	0,  // 12: guru.pars.store.v1.StoreService.Select:input_type -> guru.pars.store.v1.StoreConditionRequest
	6,  // 13: guru.pars.store.v1.StoreService.Upsert:input_type -> guru.pars.store.v1.StoreUpsertRequest
	8,  // 14: guru.pars.store.v1.StoreService.DbCreate:input_type -> guru.pars.store.v1.StoreDbCreateRequest
	9,  // 15: guru.pars.store.v1.StoreService.DbDrop:input_type -> guru.pars.store.v1.StoreDbDropRequest
	12, // 16: guru.pars.store.v1.StoreService.DbList:input_type -> guru.pars.store.v1.StoreDbListRequest
	10, // 17: guru.pars.store.v1.StoreService.TableCreate:input_type -> guru.pars.store.v1.StoreTableCreateRequest
	11, // 18: guru.pars.store.v1.StoreService.TableDrop:input_type -> guru.pars.store.v1.StoreTableDropRequest
	13, // 19: guru.pars.store.v1.StoreService.TableList:input_type -> guru.pars.store.v1.StoreTableListRequest
	16, // 20: guru.pars.store.v1.StoreService.ColumnCreate:input_type -> guru.pars.store.v1.StoreColumnCreateRequest
	17, // 21: guru.pars.store.v1.StoreService.ColumnDrop:input_type -> guru.pars.store.v1.StoreColumnDropRequest
	18, // 22: guru.pars.store.v1.StoreService.ColumnList:input_type -> guru.pars.store.v1.StoreColumnListRequest
	3,  // 23: guru.pars.store.v1.StoreService.Insert:output_type -> guru.pars.store.v1.StoreResult
	3,  // 24: guru.pars.store.v1.StoreService.Update:output_type -> guru.pars.store.v1.StoreResult
	3,  // 25: guru.pars.store.v1.StoreService.Delete:output_type -> guru.pars.store.v1.StoreResult
	3,  // 26: guru.pars.store.v1.StoreService.Select:output_type -> guru.pars.store.v1.StoreResult
	3,  // 27: guru.pars.store.v1.StoreService.Upsert:output_type -> guru.pars.store.v1.StoreResult
	15, // 28: guru.pars.store.v1.StoreService.DbCreate:output_type -> guru.pars.store.v1.StoreResponse
	15, // 29: guru.pars.store.v1.StoreService.DbDrop:output_type -> guru.pars.store.v1.StoreResponse
	15, // 30: guru.pars.store.v1.StoreService.DbList:output_type -> guru.pars.store.v1.StoreResponse
	15, // 31: guru.pars.store.v1.StoreService.TableCreate:output_type -> guru.pars.store.v1.StoreResponse
	15, // 32: guru.pars.store.v1.StoreService.TableDrop:output_type -> guru.pars.store.v1.StoreResponse
	15, // 33: guru.pars.store.v1.StoreService.TableList:output_type -> guru.pars.store.v1.StoreResponse
	15, // 34: guru.pars.store.v1.StoreService.ColumnCreate:output_type -> guru.pars.store.v1.StoreResponse
	15, // 35: guru.pars.store.v1.StoreService.ColumnDrop:output_type -> guru.pars.store.v1.StoreResponse
	19, // 36: guru.pars.store.v1.StoreService.ColumnList:output_type -> guru.pars.store.v1.StoreColumnListResponse
	23, // [23:37] is the sub-list for method output_type
	9,  // [9:23] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_protos_store_proto_init() }
func file_protos_store_proto_init() {
	if File_protos_store_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_protos_store_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*StoreConditionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Kv); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*StoreRow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*StoreResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*StoreInsertRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*StoreUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*StoreUpsertRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*StoreDeleteRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*StoreDbCreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*StoreDbDropRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*StoreTableCreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*StoreTableDropRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*StoreDbListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*StoreTableListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*StoreTableListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*StoreResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*StoreColumnCreateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*StoreColumnDropRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*StoreColumnListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_protos_store_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*StoreColumnListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_protos_store_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_store_proto_goTypes,
		DependencyIndexes: file_protos_store_proto_depIdxs,
		MessageInfos:      file_protos_store_proto_msgTypes,
	}.Build()
	File_protos_store_proto = out.File
	file_protos_store_proto_rawDesc = nil
	file_protos_store_proto_goTypes = nil
	file_protos_store_proto_depIdxs = nil
}
