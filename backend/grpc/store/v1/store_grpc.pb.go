// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: protos/store.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	StoreService_Insert_FullMethodName       = "/guru.pars.store.v1.StoreService/Insert"
	StoreService_Update_FullMethodName       = "/guru.pars.store.v1.StoreService/Update"
	StoreService_Delete_FullMethodName       = "/guru.pars.store.v1.StoreService/Delete"
	StoreService_Select_FullMethodName       = "/guru.pars.store.v1.StoreService/Select"
	StoreService_Upsert_FullMethodName       = "/guru.pars.store.v1.StoreService/Upsert"
	StoreService_DbCreate_FullMethodName     = "/guru.pars.store.v1.StoreService/DbCreate"
	StoreService_DbDrop_FullMethodName       = "/guru.pars.store.v1.StoreService/DbDrop"
	StoreService_DbList_FullMethodName       = "/guru.pars.store.v1.StoreService/DbList"
	StoreService_TableCreate_FullMethodName  = "/guru.pars.store.v1.StoreService/TableCreate"
	StoreService_TableDrop_FullMethodName    = "/guru.pars.store.v1.StoreService/TableDrop"
	StoreService_TableList_FullMethodName    = "/guru.pars.store.v1.StoreService/TableList"
	StoreService_ColumnCreate_FullMethodName = "/guru.pars.store.v1.StoreService/ColumnCreate"
	StoreService_ColumnDrop_FullMethodName   = "/guru.pars.store.v1.StoreService/ColumnDrop"
	StoreService_ColumnList_FullMethodName   = "/guru.pars.store.v1.StoreService/ColumnList"
)

// StoreServiceClient is the client API for StoreService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StoreServiceClient interface {
	Insert(ctx context.Context, in *StoreInsertRequest, opts ...grpc.CallOption) (*StoreResult, error)
	Update(ctx context.Context, in *StoreUpdateRequest, opts ...grpc.CallOption) (*StoreResult, error)
	Delete(ctx context.Context, in *StoreDeleteRequest, opts ...grpc.CallOption) (*StoreResult, error)
	Select(ctx context.Context, in *StoreConditionRequest, opts ...grpc.CallOption) (*StoreResult, error)
	Upsert(ctx context.Context, in *StoreUpsertRequest, opts ...grpc.CallOption) (*StoreResult, error)
	DbCreate(ctx context.Context, in *StoreDbCreateRequest, opts ...grpc.CallOption) (*StoreResponse, error)
	DbDrop(ctx context.Context, in *StoreDbDropRequest, opts ...grpc.CallOption) (*StoreResponse, error)
	DbList(ctx context.Context, in *StoreDbListRequest, opts ...grpc.CallOption) (*StoreResponse, error)
	TableCreate(ctx context.Context, in *StoreTableCreateRequest, opts ...grpc.CallOption) (*StoreResponse, error)
	TableDrop(ctx context.Context, in *StoreTableDropRequest, opts ...grpc.CallOption) (*StoreResponse, error)
	TableList(ctx context.Context, in *StoreTableListRequest, opts ...grpc.CallOption) (*StoreResponse, error)
	ColumnCreate(ctx context.Context, in *StoreColumnCreateRequest, opts ...grpc.CallOption) (*StoreResponse, error)
	ColumnDrop(ctx context.Context, in *StoreColumnDropRequest, opts ...grpc.CallOption) (*StoreResponse, error)
	ColumnList(ctx context.Context, in *StoreColumnListRequest, opts ...grpc.CallOption) (*StoreColumnListResponse, error)
}

type storeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStoreServiceClient(cc grpc.ClientConnInterface) StoreServiceClient {
	return &storeServiceClient{cc}
}

func (c *storeServiceClient) Insert(ctx context.Context, in *StoreInsertRequest, opts ...grpc.CallOption) (*StoreResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResult)
	err := c.cc.Invoke(ctx, StoreService_Insert_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) Update(ctx context.Context, in *StoreUpdateRequest, opts ...grpc.CallOption) (*StoreResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResult)
	err := c.cc.Invoke(ctx, StoreService_Update_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) Delete(ctx context.Context, in *StoreDeleteRequest, opts ...grpc.CallOption) (*StoreResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResult)
	err := c.cc.Invoke(ctx, StoreService_Delete_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) Select(ctx context.Context, in *StoreConditionRequest, opts ...grpc.CallOption) (*StoreResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResult)
	err := c.cc.Invoke(ctx, StoreService_Select_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) Upsert(ctx context.Context, in *StoreUpsertRequest, opts ...grpc.CallOption) (*StoreResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResult)
	err := c.cc.Invoke(ctx, StoreService_Upsert_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) DbCreate(ctx context.Context, in *StoreDbCreateRequest, opts ...grpc.CallOption) (*StoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResponse)
	err := c.cc.Invoke(ctx, StoreService_DbCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) DbDrop(ctx context.Context, in *StoreDbDropRequest, opts ...grpc.CallOption) (*StoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResponse)
	err := c.cc.Invoke(ctx, StoreService_DbDrop_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) DbList(ctx context.Context, in *StoreDbListRequest, opts ...grpc.CallOption) (*StoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResponse)
	err := c.cc.Invoke(ctx, StoreService_DbList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) TableCreate(ctx context.Context, in *StoreTableCreateRequest, opts ...grpc.CallOption) (*StoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResponse)
	err := c.cc.Invoke(ctx, StoreService_TableCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) TableDrop(ctx context.Context, in *StoreTableDropRequest, opts ...grpc.CallOption) (*StoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResponse)
	err := c.cc.Invoke(ctx, StoreService_TableDrop_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) TableList(ctx context.Context, in *StoreTableListRequest, opts ...grpc.CallOption) (*StoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResponse)
	err := c.cc.Invoke(ctx, StoreService_TableList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) ColumnCreate(ctx context.Context, in *StoreColumnCreateRequest, opts ...grpc.CallOption) (*StoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResponse)
	err := c.cc.Invoke(ctx, StoreService_ColumnCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) ColumnDrop(ctx context.Context, in *StoreColumnDropRequest, opts ...grpc.CallOption) (*StoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreResponse)
	err := c.cc.Invoke(ctx, StoreService_ColumnDrop_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) ColumnList(ctx context.Context, in *StoreColumnListRequest, opts ...grpc.CallOption) (*StoreColumnListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StoreColumnListResponse)
	err := c.cc.Invoke(ctx, StoreService_ColumnList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StoreServiceServer is the server API for StoreService service.
// All implementations must embed UnimplementedStoreServiceServer
// for forward compatibility.
type StoreServiceServer interface {
	Insert(context.Context, *StoreInsertRequest) (*StoreResult, error)
	Update(context.Context, *StoreUpdateRequest) (*StoreResult, error)
	Delete(context.Context, *StoreDeleteRequest) (*StoreResult, error)
	Select(context.Context, *StoreConditionRequest) (*StoreResult, error)
	Upsert(context.Context, *StoreUpsertRequest) (*StoreResult, error)
	DbCreate(context.Context, *StoreDbCreateRequest) (*StoreResponse, error)
	DbDrop(context.Context, *StoreDbDropRequest) (*StoreResponse, error)
	DbList(context.Context, *StoreDbListRequest) (*StoreResponse, error)
	TableCreate(context.Context, *StoreTableCreateRequest) (*StoreResponse, error)
	TableDrop(context.Context, *StoreTableDropRequest) (*StoreResponse, error)
	TableList(context.Context, *StoreTableListRequest) (*StoreResponse, error)
	ColumnCreate(context.Context, *StoreColumnCreateRequest) (*StoreResponse, error)
	ColumnDrop(context.Context, *StoreColumnDropRequest) (*StoreResponse, error)
	ColumnList(context.Context, *StoreColumnListRequest) (*StoreColumnListResponse, error)
	mustEmbedUnimplementedStoreServiceServer()
}

// UnimplementedStoreServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedStoreServiceServer struct{}

func (UnimplementedStoreServiceServer) Insert(context.Context, *StoreInsertRequest) (*StoreResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Insert not implemented")
}
func (UnimplementedStoreServiceServer) Update(context.Context, *StoreUpdateRequest) (*StoreResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Update not implemented")
}
func (UnimplementedStoreServiceServer) Delete(context.Context, *StoreDeleteRequest) (*StoreResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedStoreServiceServer) Select(context.Context, *StoreConditionRequest) (*StoreResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Select not implemented")
}
func (UnimplementedStoreServiceServer) Upsert(context.Context, *StoreUpsertRequest) (*StoreResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Upsert not implemented")
}
func (UnimplementedStoreServiceServer) DbCreate(context.Context, *StoreDbCreateRequest) (*StoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DbCreate not implemented")
}
func (UnimplementedStoreServiceServer) DbDrop(context.Context, *StoreDbDropRequest) (*StoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DbDrop not implemented")
}
func (UnimplementedStoreServiceServer) DbList(context.Context, *StoreDbListRequest) (*StoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DbList not implemented")
}
func (UnimplementedStoreServiceServer) TableCreate(context.Context, *StoreTableCreateRequest) (*StoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TableCreate not implemented")
}
func (UnimplementedStoreServiceServer) TableDrop(context.Context, *StoreTableDropRequest) (*StoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TableDrop not implemented")
}
func (UnimplementedStoreServiceServer) TableList(context.Context, *StoreTableListRequest) (*StoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TableList not implemented")
}
func (UnimplementedStoreServiceServer) ColumnCreate(context.Context, *StoreColumnCreateRequest) (*StoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ColumnCreate not implemented")
}
func (UnimplementedStoreServiceServer) ColumnDrop(context.Context, *StoreColumnDropRequest) (*StoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ColumnDrop not implemented")
}
func (UnimplementedStoreServiceServer) ColumnList(context.Context, *StoreColumnListRequest) (*StoreColumnListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ColumnList not implemented")
}
func (UnimplementedStoreServiceServer) mustEmbedUnimplementedStoreServiceServer() {}
func (UnimplementedStoreServiceServer) testEmbeddedByValue()                      {}

// UnsafeStoreServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StoreServiceServer will
// result in compilation errors.
type UnsafeStoreServiceServer interface {
	mustEmbedUnimplementedStoreServiceServer()
}

func RegisterStoreServiceServer(s grpc.ServiceRegistrar, srv StoreServiceServer) {
	// If the following call pancis, it indicates UnimplementedStoreServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&StoreService_ServiceDesc, srv)
}

func _StoreService_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreInsertRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_Insert_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).Insert(ctx, req.(*StoreInsertRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_Update_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).Update(ctx, req.(*StoreUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreDeleteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).Delete(ctx, req.(*StoreDeleteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_Select_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreConditionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).Select(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_Select_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).Select(ctx, req.(*StoreConditionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_Upsert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreUpsertRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).Upsert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_Upsert_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).Upsert(ctx, req.(*StoreUpsertRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_DbCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreDbCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).DbCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_DbCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).DbCreate(ctx, req.(*StoreDbCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_DbDrop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreDbDropRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).DbDrop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_DbDrop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).DbDrop(ctx, req.(*StoreDbDropRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_DbList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreDbListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).DbList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_DbList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).DbList(ctx, req.(*StoreDbListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_TableCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreTableCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).TableCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_TableCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).TableCreate(ctx, req.(*StoreTableCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_TableDrop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreTableDropRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).TableDrop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_TableDrop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).TableDrop(ctx, req.(*StoreTableDropRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_TableList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreTableListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).TableList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_TableList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).TableList(ctx, req.(*StoreTableListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_ColumnCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreColumnCreateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).ColumnCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_ColumnCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).ColumnCreate(ctx, req.(*StoreColumnCreateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_ColumnDrop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreColumnDropRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).ColumnDrop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_ColumnDrop_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).ColumnDrop(ctx, req.(*StoreColumnDropRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_ColumnList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreColumnListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).ColumnList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_ColumnList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).ColumnList(ctx, req.(*StoreColumnListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StoreService_ServiceDesc is the grpc.ServiceDesc for StoreService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StoreService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "guru.pars.store.v1.StoreService",
	HandlerType: (*StoreServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _StoreService_Insert_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _StoreService_Update_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _StoreService_Delete_Handler,
		},
		{
			MethodName: "Select",
			Handler:    _StoreService_Select_Handler,
		},
		{
			MethodName: "Upsert",
			Handler:    _StoreService_Upsert_Handler,
		},
		{
			MethodName: "DbCreate",
			Handler:    _StoreService_DbCreate_Handler,
		},
		{
			MethodName: "DbDrop",
			Handler:    _StoreService_DbDrop_Handler,
		},
		{
			MethodName: "DbList",
			Handler:    _StoreService_DbList_Handler,
		},
		{
			MethodName: "TableCreate",
			Handler:    _StoreService_TableCreate_Handler,
		},
		{
			MethodName: "TableDrop",
			Handler:    _StoreService_TableDrop_Handler,
		},
		{
			MethodName: "TableList",
			Handler:    _StoreService_TableList_Handler,
		},
		{
			MethodName: "ColumnCreate",
			Handler:    _StoreService_ColumnCreate_Handler,
		},
		{
			MethodName: "ColumnDrop",
			Handler:    _StoreService_ColumnDrop_Handler,
		},
		{
			MethodName: "ColumnList",
			Handler:    _StoreService_ColumnList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/store.proto",
}
