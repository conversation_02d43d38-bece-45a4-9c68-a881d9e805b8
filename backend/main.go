package main

import (
	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/app/cmd"
	"github.com/parsguru/pars-vue/pkg/global"
)

// @title PARS vue
// @version 1.0
// @description PARS vue

// @contact.name API Support
// @contact.url https://github.com/parsguru/pars-vue/issues

// @host localhost:7000
// @BasePath /api/v1
// @schemes http

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
var (
	app_id = uuid.New().String()
)

func main() {
	global.SetAppID(app_id)
	cmd.Execute()
}
