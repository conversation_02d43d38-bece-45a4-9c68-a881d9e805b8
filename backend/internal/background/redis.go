package background

import (
	"context"
	"fmt"
	"log"
	"net"
	"time"

	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/global"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/redis/go-redis/v9"
)

func RedisAlive(host, port, password string, db int) {

	ticker := time.NewTicker(5 * time.Second)
	ctx := context.Background()

	for range ticker.C {

		cli := redis.NewClient(&redis.Options{
			Addr:     net.JoinHostPort(host, port),
			Password: password,
			DB:       db,
		})

		_, err := cli.Ping(ctx).Result()
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Red<PERSON> is not alive",
				Message:        fmt.Sprintf("Redis is not alive: %v", err),
				Type:           "error",
				Level:          entities.LogLevel5,
				OrganizationID: global.GetMainOrgID(),
			})
			log.Println("Red<PERSON> is not alive")
		}

		cli.Close()

	}
}
