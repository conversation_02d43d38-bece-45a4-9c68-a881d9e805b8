package background

import (
	"fmt"
	"log"
	"time"

	"github.com/parsguru/pars-vue/pkg/config"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	Alive bool
)

func DBAlive(host, port, user, password, dbname string) {

	timeTicker := time.NewTicker(15 * time.Second)

	for range timeTicker.C {
		dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s", host, port, user, password, dbname, config.ReadValue().Database.SslMode)
		db, err := gorm.Open(
			postgres.New(
				postgres.Config{
					DSN:                  dsn,
					PreferSimpleProtocol: true,
				},
			),
			&gorm.Config{
				TranslateError: true,
			},
		)
		if err != nil {
			Alive = false
		}

		sqldb, err := db.DB()
		if err != nil {
			Alive = false
		}

		err = sqldb.Ping()
		if err != nil {
			Alive = false
		} else {
			Alive = true
		}

		sqldb.Close()

		if !Alive {
			log.Println("Database Alive: ", Alive)
		}
	}
}
