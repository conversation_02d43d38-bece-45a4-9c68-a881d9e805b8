package cache

import (
	"time"

	"github.com/parsguru/pars-vue/pkg/redis"
)

const prefix = "cache:"

func Set(key string, value interface{}, expire time.Duration) error {
	return redis.Set(prefix+key, value, expire)
}

func SAdd(key string, value interface{}) error {
	return redis.SAdd(prefix+key, value)
}

func <PERSON><PERSON><PERSON>(key string, value interface{}, expire time.Duration) error {
	return redis.Set<PERSON><PERSON>(prefix+key, value, expire)
}

func <PERSON><PERSON><PERSON>(key string, value interface{}) error {
	return redis.Get<PERSON>son(prefix+key, value)
}

func Get(key string) (string, error) {
	return redis.Get(prefix + key)
}

func <PERSON>(key string) error {
	return redis.Del(prefix + key)
}

func FlushAll() error {
	// take all keys with prefix "cache:"
	keys, _ := redis.Keys(prefix + "*")

	for _, key := range keys {
		if err := redis.Del(key); err != nil {
			return err
		}
	}
	return nil
}

func Exists(key string) (bool, error) {
	return redis.Exists(prefix + key)
}

func SMembers(key string) ([]string, error) {
	return redis.SMembers(prefix + key)
}

func <PERSON>el(key string, value interface{}) error {
	return redis.SDelete(prefix+key, value)
}

func Keys(prefix string) ([]string, error) {
	return redis.Keys(prefix)
}
