app_name: pars-vue
host:
port: 4000
grpc_port: 50055
env: development
base_url: http://localhost:7000
front_base_url: http://localhost:3001
jwt_secret: secret
jwt_expire: 24 # is hour
jwt_issuer: pars-vue
totp_issuer: pars-vue
admin_email: <EMAIL>
admin_name: Admin
admin_password: 123456
second_admin_email: <EMAIL>
second_admin_name: Admin2
license_key: YvTuIeFR6J36p9skcQcxd4fSUfjxbR03OrqvohV7yvt_D7bMPEibfVQjTWZRaqjF-6g2i4XrAQg82xeFOmoe
hash_salt: s3SFR6J3s4lYM3%TuIeA3^J3

database:
  host: pars-vue-db
  port: 5432
  name: pars-vue
  user: pars-vue
  password: pars-vue

redis:
  host: pars-vue-redis
  port: 6379
  password: pars-vue
  db: 0

allow_methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS

allow_headers:
  - Content-Type
  - Authorization
  - X-CSRF-Token
  - data-api-key
  - x-mono-auth
  - X-HASH
  
allow_origins:
    - http://localhost:7000
    - http://localhost:9000
    - http://localhost:4040

nats:
    url: nats://pars-vue-nats:4222


