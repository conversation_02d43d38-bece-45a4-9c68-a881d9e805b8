package cmd

import (
	"github.com/parsguru/pars-vue/internal/background"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/infrastructure/database"
	"github.com/parsguru/pars-vue/pkg/infrastructure/server"
	"github.com/parsguru/pars-vue/pkg/nat"
	"github.com/parsguru/pars-vue/pkg/redis"
	"github.com/parsguru/pars-vue/pkg/scheduler"
)

func Execute() {
	database.InitDB(
		config.ReadValue().Database.Host, config.ReadValue().Database.Port,
		config.ReadValue().Database.User, config.ReadValue().Database.Password,
		config.ReadValue().Database.Name,
	)

	redis.InitRedis(
		config.ReadValue().Redis.Host,
		config.ReadValue().Redis.Port,
		config.ReadValue().Redis.Password,
		config.ReadValue().Redis.DB,
	)

	go background.RedisAlive(
		config.ReadValue().Redis.Host,
		config.ReadValue().Redis.Port,
		config.ReadValue().Redis.Password,
		config.ReadValue().Redis.DB,
	)

	go background.RedisAlive(
		config.ReadValue().Redis.Host,
		config.ReadValue().Redis.Port,
		config.ReadValue().Redis.Password,
		config.ReadValue().Redis.DB,
	)

	nat.InitNats()
	nat.InitEventerNats()

	scheduler.StartScheduler(database.ClientDB())

	go func() {
		server.LaunchGRPCServer(
			config.ReadValue().GrpcPort,
		)
	}()

	server.LaunchHttpServer(
		config.ReadValue().Host,
		config.ReadValue().Port,
		config.ReadValue().AppName,
		config.ReadValue().AllowMethods,
		config.ReadValue().AllowOrigins,
		config.ReadValue().AllowHeaders,
	)

}
