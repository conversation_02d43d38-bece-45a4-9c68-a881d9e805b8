package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/domains/store"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"go.opentelemetry.io/otel/trace"
)

func StoreRoutes(r *gin.RouterGroup, s store.Service, tracer trace.Tracer) {
	r = r.Group("/store")

	r.POST("/create", middleware.Authorized(), StoreCreate(s, tracer))
	r.POST("/update", middleware.Authorized(), StoreUpdate(s, tracer))
	r.POST("/delete", middleware.Authorized(), StoreDelete(s, tracer))
	r.POST("/select", middleware.Authorized(), StoreSelect(s, tracer))
	r.POST("/upsert", middleware.Authorized(), StoreUpsert(s, tracer))
	r.POST("/db/create", middleware.Authorized(), StoreDbCreate(s, tracer))
	r.POST("/db/drop", middleware.Authorized(), StoreDbDrop(s, tracer))
	r.GET("/db/list", middleware.Authorized(), StoreDbList(s, tracer))
	r.POST("/table/create", middleware.Authorized(), StoreTableCreate(s, tracer))
	r.POST("/table/drop", middleware.Authorized(), StoreTableDrop(s, tracer))
	r.GET("/table/list", middleware.Authorized(), StoreTableList(s, tracer))
	r.POST("/column/create", middleware.Authorized(), StoreColumnCreate(s, tracer))
	r.POST("/column/drop", middleware.Authorized(), StoreColumnDrop(s, tracer))
	r.GET("/column/list", middleware.Authorized(), StoreColumnList(s, tracer))
}

func StoreCreate(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreInsertRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.Create(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreUpdate(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreUpdateRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.Update(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreDelete(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreDeleteRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.Delete(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreSelect(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreConditionRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.Select(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreUpsert(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreUpsertRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.Upsert(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreDbCreate(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreDbCreateRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.DbCreate(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreDbDrop(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreDbDropRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.DbDrop(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreDbList(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreDbListRequest
		if err := c.ShouldBindQuery(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.DbList(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreTableCreate(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreTableCreateRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.TableCreate(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreTableDrop(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreTableDropRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.TableDrop(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreTableList(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreTableListRequest
		if err := c.ShouldBindQuery(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.TableList(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreColumnCreate(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreColumnCreateRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.ColumnCreate(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreColumnDrop(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreColumnDropRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.ColumnDrop(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}

func StoreColumnList(s store.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.StoreColumnListRequest
		if err := c.ShouldBindQuery(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		result, err := s.ColumnList(&payload, c, tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, result)
	}
}
