package routes

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/activity"
	"github.com/parsguru/pars-vue/pkg/domains/organization"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/validator"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

func OrganizationRoutes(r *gin.RouterGroup, s organization.Service, tracer trace.Tracer) {
	r.POST("/organizations", middleware.Authorized(), middleware.RoleCheck("organization", true, "create"), createOrganization(s, tracer))
	r.GET("/organizations", middleware.Authorized(), middleware.RoleCheck("organization", true, "read"), getOrganizations(s, tracer))
	r.GET("/organizations/:id", middleware.Authorized(), middleware.RoleCheck("organization", true, "read"), getOrganization(s, tracer))
	r.PATCH("/organizations/:id", middleware.Authorized(), middleware.RoleCheck("organization", true, "update"), middleware.HashCheck(), updateOrganization(s, tracer))
	r.DELETE("/organizations/:id", middleware.Authorized(), middleware.RoleCheck("organization", true, "delete"), middleware.HashCheck(), deleteOrganization(s, tracer))
	r.GET("/organizations/:id/detail", middleware.Authorized(), middleware.RoleCheck("organization", true, "read"), getOrganizationDetail(s, tracer))

}

// @ID				createOrganization
// @Summary		Organization create
// @Description	Organization create
// @Tags			Organization Endpoints
// @Param			payload	body	dtos.OrganizationCreateDto	true	"Organization Info"
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Failure		500	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Router			/organizations [post]
func createOrganization(s organization.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.OrganizationCreateDto
		if err := c.ShouldBindJSON(&req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization create binding error",
				Message:        "Error while binding payload : " + err.Error(),
				Type:           "error",
				Entity:         "organization",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		v := validator.New()

		v.Add(req.Organization.Name != "", "organization.name", "Name is required")
		v.Add(validator.Matches(req.Organization.Name, validator.RgxJustStringWithSpaceAndTurkish), "organization.name", "Name is invalid")

		v.Add(req.Admin.Email != "", "admin.email", "Email is required")
		v.Add(validator.Matches(req.Admin.Email, validator.RgxEmail), "admin.email", "Email is invalid")

		v.Add(req.Admin.FirstName != "", "admin.first_name", "First Name is required")
		v.Add(validator.Matches(req.Admin.FirstName, validator.RgxJustStringWithSpaceAndTurkish), "admin.first_name", "First Name is invalid")

		v.Add(req.Admin.LastName != "", "admin.last_name", "Last Name is required")
		v.Add(validator.Matches(req.Admin.LastName, validator.RgxJustStringWithSpaceAndTurkish), "admin.last_name", "Last Name is invalid")

		if v.Run(c) {
			return
		}

		if err := s.Create(c, tracer, &req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization create error",
				Message:        "Error while creating organization : " + err.Error(),
				Type:           "error",
				Entity:         "organization",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Organization created",
			Message:        "Success : organization created",
			Type:           "info",
			Entity:         "organization",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		activity.NewActivity("create", "organization", c.ClientIP(), c, tracer)
		c.JSON(http.StatusCreated, gin.H{"message": "Organization created"})
	}
}

// @ID				listOrganizations
// @Summary		Organization list
// @Description	Organization list
// @Tags			Organization Endpoints
// @Accept			json
// @Produce		json
// @Param			page		query		int	false	"Page"
// @Param			per_page	query		int	false	"Per Page"
// @Success		200			{object}	dtos.PaginatedData
// @Failure		400			{object}	dtos.ErrorMessage
// @Failure		500			{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Router			/organizations [get]
func getOrganizations(s organization.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, perPage := helpers.ParsePagination(c)

		orgName := c.Query("name")
		orgid := c.Query("id")

		organizations, err := s.List(c, tracer, page, perPage, orgid, orgName)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization list error",
				Message:        "Error while listing organizations : " + err.Error(),
				Type:           "error",
				Entity:         "organization",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Organization list",
			Message:        "Success : organization list",
			Type:           "info",
			Entity:         "organization",
			Proto:          "http",
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("list", "organization", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, organizations)
	}
}

// @ID				getOrganization
// @Summary		Organization Get
// @Description	Organization Get
// @Tags			Organization Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	entities.Organization
// @Failure		400	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			id	path	string	true	"Organization  ID"
// @Router			/organizations/{id} [get]
func getOrganization(s organization.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		organization, err := s.Get(c, tracer, c.Param("id"))
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization get error",
				Message:        "Error while getting organization : " + err.Error(),
				Type:           "error",
				Entity:         "organization",
				Proto:          "http",
				EntityID:       helpers.ParseID(c.Param("id")),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Organization get",
			Message:        "Success : organization get",
			Type:           "info",
			Entity:         "organization",
			Proto:          "http",
			EntityID:       helpers.ParseID(c.Param("id")),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("get", "organization", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, organization)
	}
}

// @ID				updateOrganization
// @Summary		Organization Update
// @Description	Organization Update
// @Tags			Organization Endpoints
// @Param			payload	body	dtos.OrganizationUpdateDto	true	"Organization Info"
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Failure		500	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			id	path	string	true	"Organization  ID"
// @Router			/organizations/{id} [patch]
func updateOrganization(s organization.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.OrganizationUpdateDto
		if err := c.ShouldBindJSON(&payload); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization update error",
				Message:        "Error while updating organization : " + err.Error(),
				Type:           "error",
				Entity:         "organization",
				Proto:          "http",
				EntityID:       helpers.ParseID(c.Param("id")),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		if err := s.Update(c.Param("id"), &payload, c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization update error",
				Message:        "Error while updating organization : " + err.Error(),
				Type:           "error",
				Entity:         "organization",
				Proto:          "http",
				EntityID:       helpers.ParseID(c.Param("id")),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Organization update",
			Message:        "Success : organization update",
			Type:           "info",
			Entity:         "organization",
			Proto:          "http",
			EntityID:       helpers.ParseID(c.Param("id")),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("update", "organization", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, gin.H{"message": "Organization updated"})
	}
}

// @ID				deleteOrganization
// @Summary		Organization Delete
// @Description	Organization Delete
// @Tags			Organization Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Failure		500	{object}	dtos.ErrorMessage
// @Param			id	path		string	true	"Organization  ID"
// @Security		BearerAuth
// @Router			/organizations/{id} [delete]
func deleteOrganization(s organization.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := s.Delete(c, tracer, c.Param("id")); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization delete error",
				Message:        "Error while deleting organization : " + err.Error(),
				Type:           "error",
				Entity:         "organization",
				Proto:          "http",
				EntityID:       helpers.ParseID(c.Param("id")),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Organization delete",
			Message:        "Success : organization delete",
			Type:           "info",
			Entity:         "organization",
			Proto:          "http",
			EntityID:       helpers.ParseID(c.Param("id")),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("delete", "organization", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, gin.H{"message": "Organization deleted"})
	}
}

// @ID				getOrganizationDetail
// @Summary		Organization Detail Get
// @Description	Organization Detail Get
// @Tags			Organization Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.OrganizationDetailDto
// @Failure		400	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			id	path	string	true	"Organization  ID"
// @Router			/organizations/{id}/detail [get]
func getOrganizationDetail(s organization.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		organization, err := s.Detail(c.Param("id"), c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Organization detail get error",
				Message:        "Error while getting organization detail : " + err.Error(),
				Type:           "error",
				Entity:         "organization",
				Proto:          "http",
				EntityID:       helpers.ParseID(c.Param("id")),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			if errors.Is(err, gorm.ErrRecordNotFound) {
				c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"error": "organization_not_found"})
				return
			}
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Organization detail get",
			Message:        "Success : organization detail get",
			Type:           "info",
			Entity:         "organization",
			Proto:          "http",
			EntityID:       helpers.ParseID(c.Param("id")),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("get", "organization", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, organization)
	}
}
