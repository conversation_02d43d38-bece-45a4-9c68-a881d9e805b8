package routes

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/activity"
	"github.com/parsguru/pars-vue/pkg/domains/role"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/parsvalidate"
	"github.com/parsguru/pars-vue/pkg/state"

	"go.opentelemetry.io/otel/trace"
)

func RoleRoutes(r *gin.RouterGroup, s role.Service, tracer trace.Tracer) {
	r.GET("/roles", middleware.Authorized(), middleware.RoleCheck("role", true, "read"), roleList(s, tracer))
	r.POST("/roles", middleware.Authorized(), middleware.RoleCheck("role", true, "create"), roleCreate(s, tracer))
	r.GET("/roles/:id", middleware.Authorized(), middleware.RoleCheck("role", true, "read"), roleGet(s, tracer))
	r.PATCH("/roles/:id", middleware.Authorized(), middleware.RoleCheck("role", true, "update"), middleware.HashCheck(), roleUpdate(s, tracer))
	r.DELETE("/roles/:id", middleware.Authorized(), middleware.RoleCheck("role", true, "delete"), middleware.HashCheck(), roleDelete(s, tracer))
	r.POST("/roles/export", middleware.Authorized(), middleware.RoleCheck("role", true, "read"), roleExport(s, tracer))
	r.POST("/roles/import", middleware.Authorized(), middleware.RoleCheck("role", true, "create"), roleImport(s, tracer))
}

func logRole(c *gin.Context, logType, title, message string, entityId uuid.UUID) {
	monolog.CreateLog(&entities.Log{
		Title:          title,
		Message:        message,
		Type:           logType,
		Entity:         "role",
		Proto:          "http",
		EntityID:       entityId,
		Ip:             state.CurrentIP(c),
		AdminID:        state.CurrentAdminUser(c),
		OrganizationID: state.CurrentAdminOrganization(c),
	})
}

// @ID				listRoles
// @Summary		Role List
// @Description	Role List
// @Tags			Role Endpoints
// @Accept			json
// @Produce		json
// @Param			page			query		int		false	"Page"
// @Param			per_page		query		int		false	"Per Page"
// @Param			organization_id	query		string	false	"Organization ID"
// @Success		200				{object}	dtos.PaginatedData
// @Failure		400				{object}	dtos.ErrorMessage
// @Failure		500				{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Router			/roles [get]
func roleList(s role.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, perPage := helpers.ParsePagination(c)
		org_id := c.Query("organization_id")
		roles, err := s.List(page, perPage, org_id, c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Role List Error",
				Message:        "Error while listing roles",
				Type:           "error",
				Entity:         "role",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Role List",
			Message:        "Success:  roles listed",
			Type:           "info",
			Entity:         "role",
			Proto:          "http",
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("list", "role", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, roles)
	}
}

// @ID				createRole
// @Summary		Role create
// @Description	Role create
// @Tags			Role Endpoints
// @Param			payload	body	dtos.RoleCreateDto	true	"Role Info"
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Failure		500	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Router			/roles [post]
func roleCreate(s role.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload *dtos.RoleCreateDto
		if err := c.ShouldBindJSON(&payload); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Role Create Bind Error",
				Message:        "Error while binding payload : " + err.Error(),
				Type:           "error",
				Entity:         "role",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		payload.Name = strings.TrimSpace(payload.Name)
		fieldErrorsString, fieldErrorsMap, err := parsvalidate.ValidateAndTranslate(c, payload)
		if err != nil {
			logRole(c, "error", "Role create: Validation System Error", err.Error(), uuid.Nil)
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "general.internal_error"})
			return
		}
		if fieldErrorsMap != nil {
			logRole(c, "info", "Role create: Invalid Field Errors", fieldErrorsString, uuid.Nil)
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Validation error", "errors": fieldErrorsMap})
			return
		}

		if err := s.Create(payload, c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Role Create Error",
				Message:        "Error while creating role : " + err.Error(),
				Type:           "error",
				Entity:         "role",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Role Create",
			Message:        "Success:  role created",
			Type:           "info",
			Entity:         "role",
			Proto:          "http",
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("create", "role", c.ClientIP(), c, tracer)

		c.JSON(http.StatusCreated, gin.H{"message": "Role created"})
	}
}

// @ID				getRole
// @Summary		Role Get
// @Description	Role Get
// @Tags			Role Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.RoleDto
// @Failure		400	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			id	path	string	true	"Role  ID"
// @Router			/roles/{id} [get]
func roleGet(s role.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, err := s.Get(c.Param("id"), c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Role Get Error",
				Message:        "Error while getting role : " + err.Error(),
				Type:           "error",
				Entity:         "role",
				Proto:          "http",
				EntityID:       helpers.ParseID(c.Param("id")),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Role Get",
			Message:        "Success: role retrieved",
			Type:           "info",
			Entity:         "role",
			Proto:          "http",
			EntityID:       helpers.ParseID(c.Param("id")),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("get", "role", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, role)
	}
}

// @ID				updateRole
// @Summary		Role Update
// @Description	Role Update
// @Tags			Role Endpoints
// @Param			payload	body	dtos.RoleUpdateDto	true	"Role Info"
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Failure		500	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			id	path	string	true	"Role  ID"
// @Router			/roles/{id} [patch]
func roleUpdate(s role.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload *dtos.RoleUpdateDto
		if err := c.ShouldBindJSON(&payload); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Role Update Bind Error",
				Message:        "Error while binding payload : " + err.Error(),
				Type:           "error",
				Entity:         "role",
				Proto:          "http",
				EntityID:       helpers.ParseID(c.Param("id")),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		payload.Name = strings.TrimSpace(payload.Name)
		fieldErrorsString, fieldErrorsMap, err := parsvalidate.ValidateAndTranslate(c, payload)
		if err != nil {
			logRole(c, "error", "Role update: Validation System Error", err.Error(), helpers.ParseID(c.Param("id")))
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "general.internal_error"})
			return
		}
		if fieldErrorsMap != nil {
			logRole(c, "info", "Role update: Invalid Field Errors", fieldErrorsString, helpers.ParseID(c.Param("id")))
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Validation error", "errors": fieldErrorsMap})
			return
		}

		if err := s.Update(c.Param("id"), payload, c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Role Update Error",
				Message:        "Error while updating role : " + err.Error(),
				Type:           "error",
				Entity:         "role",
				Proto:          "http",
				EntityID:       helpers.ParseID(c.Param("id")),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Role Update",
			Message:        "Success: role updated",
			Type:           "info",
			Entity:         "role",
			Proto:          "http",
			EntityID:       helpers.ParseID(c.Param("id")),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("update", "role", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, gin.H{"message": "Role updated"})
	}
}

// @ID				deleteRole
// @Summary		Role Delete
// @Description	Role Delete
// @Tags			Role Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Failure		500	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			id	path	string	true	"Role  ID"
// @Router			/roles/{id} [delete]
func roleDelete(s role.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := s.Delete(c.Param("id"), c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Role Delete Error",
				Message:        "Error while deleting role : " + err.Error(),
				Type:           "error",
				Entity:         "role",
				Proto:          "http",
				EntityID:       helpers.ParseID(c.Param("id")),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Role Delete",
			Message:        "Success: role deleted",
			Type:           "info",
			Entity:         "role",
			Proto:          "http",
			EntityID:       helpers.ParseID(c.Param("id")),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("delete", "role", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, gin.H{})
	}
}

// @ID				exportRoles
// @Summary		Role Export
// @Description	Role Export
// @Tags			Role Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Failure		500	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Router			/roles/export [post]
func roleExport(s role.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload *dtos.RoleExportDto
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		roles, permissions, err := s.Export(payload.OrganizationID, c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Role Export Error",
				Message:        "Error while exporting roles : " + err.Error(),
				Type:           "error",
				Entity:         "role",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})

			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Role Export",
			Message:        "Success: roles exported",
			Type:           "info",
			Entity:         "role",
			Proto:          "http",
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("export", "role", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, gin.H{"roles": roles, "permissions": permissions})
	}
}

// @ID				importRoles
// @Summary		Role Import
// @Description	Role Import
// @Tags			Role Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Failure		500	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			payload	body	dtos.RoleImportDto	true	"Role Info"
// @Router			/roles/import [post]
func roleImport(s role.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload *dtos.RoleImportDto
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		if err := s.Import(payload, c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Role Import Error",
				Message:        "Error while importing roles : " + err.Error(),
				Type:           "error",
				Entity:         "role",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})

			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Role Import",
			Message:        "Success: roles imported",
			Type:           "info",
			Entity:         "role",
			Proto:          "http",
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("import", "role", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, gin.H{"message": "Roles imported"})
	}
}
