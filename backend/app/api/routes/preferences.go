package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/domains/preferences"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
)

func PreferencesRoutes(r *gin.RouterGroup, s preferences.Service, tracer trace.Tracer) {
	r = r.Group("/preferences")

	r.POST("/dsn", middleware.Authorized(), addDSN(s, tracer))
	r.GET("/dsn", middleware.Authorized(), getDSN(s, tracer))
	r.DELETE("/dsn/:id", middleware.Authorized(), deleteDSN(s, tracer))
}

// @Summary     Add DSN
// @Description Adds a new DSN configuration for db node.
// @Tags        Preferences
// @Accept      json
// @Produce     json
// @Param       payload body     dtos.RequestForAddDSN true "Request body to add DSN"
// @Success     201     {string}  "success" "DSN successfully added"
// @Failure     400     {object} dtos.ErrorMessage   "Invalid input data"
// @Failure     500     {object} dtos.ErrorMessage   "Internal server error"
// @Security    BearerAuth
// @Router      /preferences/dsn [post]
func addDSN(s preferences.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.RequestForAddDSN
		if err := c.Bind(&req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "addDSN",
				Entity:         "preferences",
				Message:        "Error: " + utils.Validate(err),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		if err := s.AddDSN(req, c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "addDSN",
				Entity:         "preferences",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(201, "success")
	}
}

// @Summary     Get DSN
// @Description Get DSN
// @Tags        Preferences
// @Accept      json
// @Produce     json
// @Success     200     {object}  []dtos.ResponseForDSN   "The DSN configuration"
// @Failure     500     {object} dtos.ErrorMessage  "Internal server error"
// @Security    BearerAuth
// @Router      /preferences/dsn [get]
func getDSN(s preferences.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		resp, err := s.GetDSN(c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "addDSN",
				Entity:         "preferences",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(200, resp)
	}
}

// @Summary     Delete DSN
// @Description Delete a DSN by ID
// @Tags        Preferences
// @Param       id   path    string  true  "The ID of the DSN to delete"
// @Accept      json
// @Produce     json
// @Success     201     {string}  "success"    "DSN successfully deleted"
// @Failure     400     {object}  dtos.ErrorMessage  "Bad request - invalid DSN ID"
// @Failure     500     {object}  dtos.ErrorMessage  "Internal server error"
// @Security    BearerAuth
// @Router      /preferences/dsn/{id} [delete]
func deleteDSN(s preferences.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		if err := s.DeleteDSN(id, c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "deleteDSN",
				Entity:         "preferences",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(201, "success")
	}
}
