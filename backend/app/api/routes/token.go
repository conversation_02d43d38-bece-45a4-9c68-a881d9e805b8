package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/activity"
	"github.com/parsguru/pars-vue/pkg/domains/token"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
)

func TokenRoutes(r *gin.RouterGroup, s token.Service, tracer trace.Tracer) {
	t := r.Group("/access-token")

	t.GET("", middleware.Authorized(), middleware.RoleCheck("organization", true, "read"), getAllTokens(s, tracer))
	t.POST("", middleware.Authorized(), generateToken(s, tracer))
	t.DELETE("/:id", middleware.Authorized(), deleteToken(s, tracer))
}

// @Summary     Generate Token
// @Description Generate a new token with the provided details.
// @Tags        Application Token
// @Accept      json
// @Produce     json
// @Param       payload body     dtos.RequestForGenerateAppToken true "Token generation details"
// @Success     200     {object} dtos.SuccessMessage "Organization created successfully"
// @Failure     400     {object} dtos.ErrorMessage   "Invalid input data"
// @Failure     500     {object} dtos.ErrorMessage   "Internal server error"
// @Security    BearerAuth
// @Router      /access-token [post]
func generateToken(s token.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.RequestForGenerateAppToken
		if err := c.ShouldBindJSON(&payload); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Generate Token JSON Binding Error",
				Entity:         "token",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		token, err := s.GenerateToken(c, payload)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Generate Token Error",
				Entity:         "token",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Generate Token Success",
			Entity:         "token",
			Message:        "Success: " + payload.Name + " created",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		activity.NewActivity("generateApplicationToken", "token", c.ClientIP(), c, tracer)

		c.JSON(200, gin.H{
			"token": token,
		})
	}
}

// @Summary     Delete Token
// @Description Delete token by ID
// @Tags        Application Token
// @Accept      json
// @Produce     json
// @Param       id      path     string true "Token ID"
// @Success     200     {object} dtos.SuccessMessage "Token deleted successfully"
// @Failure     400     {object} dtos.ErrorMessage   "Invalid input data"
// @Failure     500     {object} dtos.ErrorMessage   "Internal server error"
// @Security    BearerAuth
// @Router      /access-token/{id} [delete]
func deleteToken(s token.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		if err := s.DeleteToken(c, id); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Delete Token Error",
				Entity:         "token",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Delete Token Success",
			Entity:         "token",
			Message:        "Success: " + id + " deleted",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		activity.NewActivity("deleteApplicationToken", "token", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, gin.H{"message": "token deleted successfully"})
	}
}

// @Summary     Get All Application Token
// @Description Get All Application Token
// @Tags        Application Token
// @Accept      json
// @Produce     json
// @Success   	200 {object} dtos.SuccessMessage "Tokens fetched successfully"
// @Failure     500     {object} dtos.ErrorMessage "Internal server error"
// @Security    BearerAuth
// @Router      /access-token [get]
func getAllTokens(s token.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		resp, err := s.GetAllTokens(c, page, perPage)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Get All Tokens Error",
				Entity:         "token",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Get All Tokens Success",
			Entity:         "token",
			Message:        "Success: tokens fetched",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		activity.NewActivity("getAllApplicationTokens", "token", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, resp)
	}
}
