package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/activity"
	"github.com/parsguru/pars-vue/pkg/domains/retry"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
)

func RetryRoutes(r *gin.RouterGroup, s retry.Service, tracer trace.Tracer) {
	r = r.Group("/retry")

	r.POST("", middleware.Authorized(), retryByID(s, tracer))
}

// @Summary Retry a simulate by ID
// @Description Retry a simulate by ID
// @Tags Retry
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.SuccessMessage "Successfully created"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /retry/{id} [post]
func retryByID(s retry.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.RequestForRetry
		if err := c.Bind(&req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "retryByID",
				Entity:         "retry",
				Message:        "Error: " + utils.Validate(err),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		if err := s.Retry(c, req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "retryByID",
				Entity:         "retry",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.JSON(http.StatusBadRequest, gin.H{
				"error": err.Error(),
			})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "retryByID",
			Entity:         "retry",
			Message:        "Success: retry a simulate by ID",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		activity.NewActivity("retry", "retry", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, gin.H{"message": "Successfully created"})
	}
}
