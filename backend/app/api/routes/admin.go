package routes

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/activity"
	"github.com/parsguru/pars-vue/pkg/domains/admin"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/parsvalidate"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
)

func AdminRoutes(r *gin.RouterGroup, s admin.Service, tracer trace.Tracer) {
	r.POST("/admins", middleware.Authorized(), middleware.RoleCheck("admin", true, "create"), adminCreate(s, tracer))
	r.GET("/admins", middleware.Authorized(), middleware.RoleCheck("admin", true, "read"), adminList(s, tracer))
	r.PATCH("/admins/:id", middleware.Authorized(), middleware.RoleCheck("admin", true, "update"), middleware.HashCheck(), adminUpdate(s, tracer))
	r.DELETE("/admins/:id", middleware.Authorized(), middleware.RoleCheck("admin", true, "delete"), middleware.HashCheck(), adminDelete(s, tracer))
	r.GET("/admins/:id", middleware.Authorized(), middleware.RoleCheck("admin", true, "read"), adminGet(s, tracer))
	r.GET("/admins/:id/details", middleware.Authorized(), middleware.RoleCheck("admin", true, "read"), adminDetails(s, tracer))
	r.GET("/admins/:id/force-logout", middleware.Authorized(), middleware.RoleCheck("admin", true, "read"), adminforcelogout(s, tracer))

}

// @ID				createAdmin
// @Summary		Admin User Create
// @Description	Admin User Create
// @Tags			Admin Endpoints
// @Param			payload	body	dtos.AdminCreateDto	true	"Admin Info"
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Router			/admins [post]
func adminCreate(s admin.Service, tracer trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		var payload dtos.AdminCreateDto
		if err := c.ShouldBindJSON(&payload); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Create binding error",
				Message:        "Error while binding payload: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		payload.FirstName = strings.TrimSpace(payload.FirstName)
		payload.LastName = strings.TrimSpace(payload.LastName)
		fieldErrorsString, fieldErrorsMap, err := parsvalidate.ValidateAndTranslate(c, payload)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin create: Validation System Error",
				Message:        "Error while validate and translate payload : " + err.Error(),
				Type:           "error",
				Entity:         "organization",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "general.internal_error"})
			return
		}
		if fieldErrorsMap != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin create: Invalid Field Errors",
				Message:        "Error Invalid Field Errors : " + fieldErrorsString,
				Type:           "info",
				Entity:         "admin",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Validation error", "errors": fieldErrorsMap})
			return
		}

		if err := s.Create(&payload, c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Create error",
				Message:        "Error while creating admin: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Admin Create",
			Message:        "Success : Admin created",
			Type:           "info",
			Proto:          "http",
			Entity:         "admin",
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("create", "admin", c.ClientIP(), c, tracer)

		c.JSON(http.StatusCreated, gin.H{"message": "Admin created"})
	}
}

// @ID				listAdmins
// @Summary		Admin User List
// @Description	Admin User List
// @Tags			Admin Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.PaginatedData
// @Failure		400	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Router			/admins [get]
func adminList(s admin.Service, tracer trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, perPage := helpers.ParsePagination(c)
		org_id := c.Query("organization_id")
		users, err := s.List(page, perPage, org_id, c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin List error",
				Message:        "Error while listing admins: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Admin List",
			Message:        "Success : Admins listed",
			Type:           "info",
			Proto:          "http",
			Entity:         "admin",
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("list", "admin", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, users)
	}
}

// @ID				updateAdmin
// @Summary		Admin User Update
// @Description	Admin User Update
// @Tags			Admin Endpoints
// @Param			payload	body	dtos.AdminUpdateDto	true	"Admin User Info"
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			id	path	string	true	"Admin User  ID"
// @Router			/admins/{id} [patch]
func adminUpdate(s admin.Service, tracer trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Update: Invalid ID Param",
				Message:        "Error invalid id param: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Invalid ID Param"})
			return
		}
		var payload dtos.AdminUpdateDto
		if err := c.ShouldBindJSON(&payload); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Update binding error",
				Message:        "Error while binding update payload: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				EntityID:       id,
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})

			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		payload.FirstName = strings.TrimSpace(payload.FirstName)
		payload.LastName = strings.TrimSpace(payload.LastName)
		fieldErrorsString, fieldErrorsMap, err := parsvalidate.ValidateAndTranslate(c, payload)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin update: Validation System Error",
				Message:        "Error while validate and translate payload : " + err.Error(),
				Type:           "error",
				Entity:         "admin",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "general.internal_error"})
			return
		}
		if fieldErrorsMap != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin create: Invalid Field Errors",
				Message:        "Error Invalid Field Errors : " + fieldErrorsString,
				Type:           "info",
				Entity:         "admin",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Validation error", "errors": fieldErrorsMap})
			return
		}

		if err := s.Update(id.String(), &payload, c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Update error",
				Message:        "Error while updating admin data: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				EntityID:       id,
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Admin Update",
			Message:        "Success : Admin updated",
			Type:           "info",
			Proto:          "http",
			Entity:         "admin",
			EntityID:       id,
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("update", "admin", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, gin.H{"message": "User updated"})
	}
}

// @ID				deleteAdmin
// @Summary		Admin User Delete
// @Description	Admin User Delete
// @Tags			Admin Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.SuccessMessage
// @Failure		400	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			id	path	string	true	"Admin ID"
// @Router			/admins/{id} [delete]
func adminDelete(s admin.Service, tracer trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Delete: Invalid ID Param",
				Message:        "Error invalid id param: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Invalid ID Param"})
			return
		}
		if err := s.Delete(id.String(), c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Delete error",
				Message:        "Error while deleting admin data: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				EntityID:       id,
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Admin Delete",
			Message:        "Success : Admin deleted",
			Type:           "info",
			Proto:          "http",
			Entity:         "admin",
			EntityID:       id,
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("delete", "admin", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, gin.H{"message": "User deleted"})
	}
}

// @ID				getAdmin
// @Summary		Admin User Get
// @Description	Admin User Get
// @Tags			Admin Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.AdminGetDTO
// @Failure		400	{object}	dtos.ErrorMessage
// @Security		BearerAuth
// @Param			id	path	string	true	"Admin User  ID"
// @Router			/admins/{id} [get]
func adminGet(s admin.Service, tracer trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Get: Invalid ID Param",
				Message:        "Error invalid id param: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Invalid ID Param"})
			return
		}
		user, err := s.GetByID(id.String(), c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Get error",
				Message:        "Error while getting admin data: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				EntityID:       id,
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})

			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Admin Get",
			Message:        "Success : Admin data retrieved",
			Type:           "info",
			Proto:          "http",
			Entity:         "admin",
			EntityID:       id,
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("get", "admin", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, user)
	}
}

func adminDetails(s admin.Service, tracer trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Details: Invalid ID Param",
				Message:        "Error invalid id param: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Invalid ID Param"})
			return
		}

		admin, err := s.Details(id.String(), c, tracer)

		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Details error",
				Message:        "Error while getting admin details: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				EntityID:       id,
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})

			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Admin Details",
			Message:        "Success : Admin details retrieved",
			Type:           "info",
			Proto:          "http",
			Entity:         "admin",
			EntityID:       id,
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("details", "admin", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, admin)
	}
}

func adminforcelogout(s admin.Service, tracer trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Force Logout: Invalid ID Param",
				Message:        "Error invalid id param: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "Invalid ID Param"})
			return
		}

		if err := s.ForceLogout(id.String(), c, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Admin Force Logout error",
				Message:        "Error while force logout admin: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Entity:         "admin",
				EntityID:       id,
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})

			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Admin Force Logout",
			Message:        "Success : Admin force logout",
			Type:           "info",
			Proto:          "http",
			Entity:         "admin",
			EntityID:       id,
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
			Ip:             state.CurrentIP(c),
		})

		activity.NewActivity("force-logout", "admin", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, gin.H{"message": "Admin force logout"})
	}
}
