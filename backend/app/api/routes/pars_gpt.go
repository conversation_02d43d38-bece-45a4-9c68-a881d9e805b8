package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/domains/parsgpt"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
)

func ParsGptRoutes(r *gin.RouterGroup, s parsgpt.Service, tracer trace.Tracer) {
	r = r.Group("/pars/gpt")

	r.POST("", middleware.Authorized(), createSimulate(s, tracer))
	r.POST("/rate", middleware.Authorized(), rateSimulate(s, tracer))
	r.GET("/:id", middleware.Authorized(), getSimulateByID(s, tracer))
	r.GET("", middleware.Authorized(), getAllSimulates(s, tracer))

}

// @Summary Create a simulate with PARS gpt
// @Description Create a simulate with PARS gpt
// @Tags PARS gpt
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.SuccessMessage "Successfully created"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /pars/gpt [post]
func createSimulate(s parsgpt.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateSimulateWithParsAI
		if err := c.Bind(&req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Create Simulate with PARS gpt",
				Entity:         "pars_gpt",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		resp, err := s.CreateSimulate(c, req)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Create Simulate with PARS gpt",
				Entity:         "pars_gpt",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Create Simulate with PARS gpt",
			Entity:         "pars_gpt",
			Message:        "Success: created simulate",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Get simulate
// @Description  Get simulate
// @Tags PARS gpt
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.SuccessMessage "Successfully created"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /pars/gpt/:id [get]
func getSimulateByID(s parsgpt.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var resp dtos.ResponseForParsGptResult
		pars_gpt_result, err := s.GetSimulateByID(c, c.Param("id"))
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Get Simulate By ID",
				Entity:         "pars_gpt",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Get Simulate By ID",
			Entity:         "pars_gpt",
			Message:        "Success: fetched simulate data",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		resp.ID = pars_gpt_result.ID.String()
		resp.JsonResult = pars_gpt_result.JSONResult

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Get all simulates
// @Description  Get all simulates
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.SuccessMessage "Successfully created"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /pars/gpt [get]
func getAllSimulates(s parsgpt.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		resp, err := s.GetAllSimulates(c, page, perPage)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "getAllSimulates",
				Entity:         "pars_gpt",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "getAllSimulates",
			Entity:         "pars_gpt",
			Message:        "Success: fetched all simulates",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Rate simulate
// @Description Rate simulate
// @Tags PARS gpt
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.SuccessMessage "Successfully rated"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /pars/gpt/rate [post]
func rateSimulate(s parsgpt.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.RequestForParsGptRate
		if err := c.Bind(&req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Rate Simulate",
				Entity:         "pars_gpt",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		if err := s.RateSimulate(c, req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Rate Simulate",
				Entity:         "pars_gpt",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Rate Simulate",
			Entity:         "pars_gpt",
			Message:        "Success: simulation rated",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		c.JSON(http.StatusOK, "success")
	}
}
