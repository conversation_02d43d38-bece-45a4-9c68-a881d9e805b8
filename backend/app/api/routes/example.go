package routes

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func ExampleRoutes(r *gin.RouterGroup) {
	r.GET("/example", getEx())
	r.POST("/example", postEx())
}

type RequestForPostEx struct {
	Name string `json:"name"`
}

func getEx() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "brms get request successful",
		})
	}
}

func postEx() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req RequestForPostEx
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{
			"message": fmt.Sprintf("brms post request successful, name: %s", req.Name),
		})
	}
}
