package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/domains/auth"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"go.opentelemetry.io/otel/trace"
)

func AuthRoutes(r *gin.RouterGroup, s auth.Service, tracer trace.Tracer) {
	r.POST("/auth/identify", identifyAuth(s, tracer))
	r.POST("/auth/authenticate", authenticate(s, tracer))
	r.POST("/auth/verify", verifyAuth(s, tracer))
}

// @ID				identifyAuth
// @Summary		Identify user
// @Description	Identify user
// @Tags			Auth Endpointsß
// @Accept			json
// @Produce		json
// @Param			payload	body		dtos.AuthV2IdentifyRequest	true	"payload"
// @Success		200		{object}	dtos.SuccessMessage
// @Failure		400		{object}	dtos.ErrorMessage
// @Failure		500		{object}	dtos.ErrorMessage
// @Router			/auth/identify [post]
func identifyAuth(s auth.Service, trace trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		var payload dtos.AuthV2IdentifyRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		if err := s.Check(c, trace, payload.Email); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, "success")
	}
}

// @ID				authenticate
// @Summary		Authenticate user
// @Description	Authenticate user
// @Tags			Auth Endpoints
// @Accept			json
// @Produce		json
// @Param			payload	body		dtos.AuthV2AuthenticateRequest	true	"payload"
// @Success		200		{object}	dtos.AuthV2AuthenticateResponse
// @Failure		400		{object}	dtos.ErrorMessage
// @Failure		500		{object}	dtos.ErrorMessage
// @Router			/auth/authenticate [post]
func authenticate(s auth.Service, trace trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		var payload dtos.AuthV2AuthenticateRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		token, tfa, pin, err := s.Authenticate(c, trace, payload.Email, payload.Password)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK,
			dtos.AuthV2AuthenticateResponse{
				Token: token,
				Tfa:   tfa,
				Pin:   pin,
			},
		)
	}
}

func verifyAuth(s auth.Service, trace trace.Tracer) func(c *gin.Context) {
	return func(c *gin.Context) {
		var payload dtos.AuthV2VerifyAuthRequest
		if err := c.ShouldBindJSON(&payload); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		user, err := s.VerifyToken(payload.Token, c, trace)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, dtos.AuthV2VerifyAuthResponse{
			User:   user,
			Expire: user.Expire,
		})
	}
}
