package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/activity"
	"github.com/parsguru/pars-vue/pkg/domains/scheduled"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
)

func ScheduledRoutes(r *gin.RouterGroup, s scheduled.Service, tracer trace.Tracer) {
	r = r.Group("/scheduled")

	r.POST("", middleware.Authorized(), createScheduledWork(s, tracer))
	r.DELETE("/:id", middleware.Authorized(), deleteScheduledWork(s, tracer))
	r.GET("", middleware.Authorized(), getScheduledWorks(s, tracer))
	r.GET("/:id", middleware.Authorized(), getSingleScheduledWork(s, tracer))

	r.GET("/:id/logs", middleware.Authorized(), getScheduledWorkLogs(s, tracer))
}

// @Summary Create a scheduled work
// @Description Create a scheduled work
// @Tags Scheduled
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.SuccessMessage "Successfully created"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /scheduled [post]
func createScheduledWork(s scheduled.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.RequestForCreateScheduledWork
		if err := c.Bind(&req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Create Scheduled Work Bind Error",
				Entity:         "scheduled",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		if err := s.CreateScheduledWork(c, req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Create Scheduled Work Error",
				Entity:         "scheduled",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Create Scheduled Work Success",
			Entity:         "scheduled",
			Message:        "Success: scheduled work created",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		c.JSON(http.StatusOK, "success")
	}
}

// @Summary Get scheduled works
// @Description Get scheduled works
// @Tags Scheduled
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.SuccessMessage "Successfully created"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /scheduled [get]
func getScheduledWorks(s scheduled.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		resp, err := s.GetScheduledWorks(c, page, perPage)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Get Scheduled Works Error",
				Entity:         "scheduled",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Get Scheduled Works Success",
			Entity:         "scheduled",
			Message:        "Success: list of scheduled works",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		activity.NewActivity("getScheduledWorks", "scheduled", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Get a scheduled work
// @Description Get a scheduled work
// @Tags Scheduled
// @Accept  json
// @Produce  json
// @Param id path string true "Scheduled Work ID"
// @Success 200 {object} dtos.ResponseForScheduledWork "Successfully created"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /scheduled/{id} [get]
func getSingleScheduledWork(s scheduled.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		resp, err := s.GetSingleScheduledWork(c, id)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Get Single Scheduled Work Error",
				Entity:         "scheduled",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Get Single Scheduled Work Success",
			Entity:         "scheduled",
			Message:        "Success: single scheduled work",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// get scheduled work logs
// @Summary Get scheduled work logs
// @Description Get scheduled work logs
// @Tags Scheduled
// @Accept  json
// @Produce  json
// @Param id path string true "Scheduled Work ID"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /scheduled/{id}/logs [get]
func getScheduledWorkLogs(s scheduled.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		resp, err := s.GetAllScheduledWorkLogs(c, page, perPage, id)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Get Scheduled Work Logs Error",
				Entity:         "scheduled",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Get Scheduled Work Logs Success",
			Entity:         "scheduled",
			Message:        "Success: scheduled work logs",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Delete a scheduled work
// @Description Delete a scheduled work
// @Tags Scheduled
// @Accept  json
// @Produce  json
// @Param id path string true "Scheduled Work ID"
// @Success 200 {object} dtos.SuccessMessage "Successfully created"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /scheduled/{id} [delete]
func deleteScheduledWork(s scheduled.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		if err := s.DeleteScheduledWork(c, id); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Delete Scheduled Work Error",
				Entity:         "scheduled",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Delete Scheduled Work Success",
			Entity:         "scheduled",
			Message:        "Success: scheduled work deleted",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		c.JSON(http.StatusOK, "success")
	}
}
