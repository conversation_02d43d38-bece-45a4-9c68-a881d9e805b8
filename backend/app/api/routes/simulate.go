package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/activity"
	simulatePkg "github.com/parsguru/pars-vue/pkg/domains/simulate"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"

	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
)

func SimulateRoutes(r *gin.RouterGroup, s simulatePkg.Service, tracer trace.Tracer) {
	r = r.Group("/simulate")

	r.POST("", middleware.Authorized(), simulate(s, tracer))
	r.POST("/by-id", middleware.Authorized(), simByID(s, tracer))
	r.GET("", middleware.Authorized(), getSimulations(s, tracer))
	r.PUT("", middleware.Authorized(), updateOrCreateSimulate(s, tracer))
	r.PUT("/name/:id", middleware.Authorized(), updateSimulateName(s, tracer))
	r.DELETE("/:simulate_id", middleware.Authorized(), deleteSimulate(s, tracer))
	r.GET("/rules", middleware.Authorized(), getSimulationRules(s, tracer))
	r.GET("/:simulate_id", middleware.Authorized(), simulateRule(s, tracer))
	r.GET("/detail/:simulate_id", middleware.Authorized(), simulateDetail(s, tracer))

	r.POST("/default-request", middleware.Authorized(), addDefaultRequest(s, tracer))
	r.GET("/default-request", middleware.Authorized(), getDefaultRequest(s, tracer))
	r.DELETE("/default-request/:id", middleware.Authorized(), deleteDefaultRequest(s, tracer))
}

// @Summary     Delete Simulate Data
// @Description Delete Simulate Data by Simulate ID
// @Tags        Simulate
// @Param       simulate_id  path    string  true  "The ID of the simulate data to delete"
// @Accept      json
// @Produce     json
// @Success     200     {object}  dtos.SuccessMessage  "Success: Simulate data deleted"
// @Failure     400     {object}  dtos.ErrorMessage     "Bad request - invalid simulate ID"
// @Failure     500     {object}  dtos.ErrorMessage     "Internal server error"
// @Security    BearerAuth
// @Router      /simulate/{simulate_id} [delete]
func deleteSimulate(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		err := s.DeleteSimulate(c.Param("simulate_id"), c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Simulate Error",
				Entity:         "simulate",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Simulate",
			Entity:         "simulate",
			Message:        "Success: deleted simulate",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("simulate", "simulate datas deleted", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, gin.H{"message": "ok"})
	}
}

// @Summary     Get Simulations List
// @Description Fetches a list of simulations, with pagination and optional filtering by version_id.
// @Tags        Simulate
// @Param       page        query   int    false  "Page number for pagination"   default(1)
// @Param       per_page    query   int    false  "Number of simulations per page"   default(10)
// @Param       version_id  query   string false  "Filter simulations by version ID"
// @Accept      json
// @Produce     json
// @Success     200     {object}  dtos.PaginatedData  "List of simulations"
// @Failure     400     {object}  dtos.ErrorMessage   "Bad request - Invalid query parameters"
// @Failure     500     {object}  dtos.ErrorMessage   "Internal server error"
// @Security    BearerAuth
// @Router      /simulate/ [get]
func getSimulations(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		version_id := c.Query("version_id")
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		res, err := s.GetSimulations(page, perPage, version_id, c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Simulations",
				Entity:         "simulate",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return

		}

		monolog.CreateLog(&entities.Log{
			Title:   "Simulations",
			Entity:  "simulate",
			Message: "Success: List of simulate rules and outputs",
			Type:    "info",
			Proto:   "http",

			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("list", "simulate", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, res)
	}
}

// @Summary     Get Simulation Rules List
// @Description Get Simulation Rules List with pagination.
// @Tags        Simulate
// @Param       page        query   int    false  "Page number for pagination"   default(1)
// @Param       per_page    query   int    false  "Number of simulation rules per page"   default(10)
// @Accept      json
// @Produce     json
// @Success     200     {object}  dtos.PaginatedData  "List of simulation rules"
// @Failure     400     {object}  dtos.ErrorMessage   "Bad request - Invalid query parameters"
// @Failure     500     {object}  dtos.ErrorMessage   "Internal server error"
// @Security    BearerAuth
// @Router      /simulate/rules [get]
func getSimulationRules(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		res, err := s.GetSimulationRules(page, perPage, c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Simulate Rules",
				Entity:         "simulate",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return

		}
		monolog.CreateLog(&entities.Log{
			Title:   "Simulate Rules",
			Entity:  "simulate",
			Message: "Success: List of simulate rules",
			Type:    "info",
			Proto:   "http",

			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("list", "simulate", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, res)

	}
}

// @Summary     Update or Create Simulate Data
// @Description Updates an existing simulation or creates a new one based on the provided payload.
// @Tags        Simulate
// @Param       simulate   body    dtos.Simulate  true  "Simulate data to be updated or created"
// @Accept      json
// @Produce     json
// @Success     200     {object}  dtos.SuccessMessage "ID of the updated or created simulation"
// @Failure     400     {object}  dtos.ErrorMessage "Bad request - Invalid input data"
// @Failure     500     {object}  dtos.ErrorMessage "Internal server error"
// @Security    BearerAuth
// @Router      /simulate/ [put]
func updateOrCreateSimulate(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.Simulate
		if err := c.Bind(&payload); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "UpdateSimulate Payload",
				Entity:         "simulate",
				Message:        "Error: " + utils.Validate(err),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		id, err := s.UpdateOrCreateSimulate(&payload, c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "UpdateSimulate Error",
				Entity:         "simulate",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "UpdateSimulate",
			Entity:         "simulate",
			Message:        "Success: update the simulate data",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("simulate", "simulate", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, gin.H{"id": id})
	}
}

// @Summary     Update Simulate Nme
// @Description Update Simulate Nme
// @Tags        Simulate
// @Param       simulate   body    dtos.Simulate  true  "Simulate data to be updated or created"
// @Accept      json
// @Produce     json
// @Success     200     {object}  dtos.SuccessMessage "
// @Failure     400     {object}  dtos.ErrorMessage "Bad request - Invalid input data"
// @Failure     500     {object}  dtos.ErrorMessage "Internal server error"
// @Security    BearerAuth
// @Router      /simulate/name/{id} [put]
func updateSimulateName(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var payload dtos.RequestForUpdateSimulateName
		payload.SimulateID = c.Param("id")
		if err := c.Bind(&payload); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "updateSimulateName Error",
				Entity:         "simulate",
				Message:        "Error: " + utils.Validate(err),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		if err := s.UpdateSimulateName(c, payload, tracer); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "updateSimulateName Error",
				Entity:         "simulate",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "updateSimulateName",
			Entity:         "simulate",
			Message:        "Success: update the simulate name",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("simulate", "simulate", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, "success")
	}
}

// @Summary     Simulate Data
// @Description Executes the simulation based on the provided payload data.
// @Tags        Simulate
// @Param       simulate   body    dtos.Simulate  true  "Data to simulate"
// @Accept      json
// @Produce     json
// @Success     200     {object}  ResponseSimulateForSwagger "Simulation result data"
// @Failure     400     {object}  dtos.ErrorMessage "Bad request - Invalid input data"
// @Failure     500     {object}  dtos.ErrorMessage "Internal server error"
// @Security    BearerAuth
// @Router      /simulate [post]
func simulate(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {

		var payload dtos.Simulate
		if err := c.Bind(&payload); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Simulate Payload Bind Error",
				Entity:         "simulate",
				Message:        "Error: " + utils.Validate(err),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		res, err := s.Simulate(&payload, c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Simulate Error",
				Entity:         "simulate",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Simulate Success",
			Entity:         "simulate",
			Message:        "Success: simulate has been executed",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("simulate", "simulate", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, res)
	}
}

// @Summary     Simulate Rule
// @Description Simulate Rule by Simulate ID
// @Tags        Simulate
// @Param       simulate_id   path    string  true  "Simulate ID"
// @Accept      json
// @Produce     json
// @Success     200     {object}  SimulateResponseForSwagger "Simulation result data"
// @Failure     400     {object}  dtos.ErrorMessage "Bad request - Invalid simulate ID"
// @Failure     500     {object}  dtos.ErrorMessage "Internal server error"
// @Security    BearerAuth
// @Router      /simulate/{simulate_id} [get]
func simulateRule(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		res, err := s.SimulateRule(c.Param("simulate_id"), c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Simulate Error",
				Entity:         "simulate",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Simulate",
			Entity:         "simulate",
			Message:        "Success: simulate the datas",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("simulate", "simulate datas", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, res)
	}
}

// @Summary     Simulate Detail by ID
// @Description Simulate Detail by ID
// @Tags        Simulate
// @Param       simulate_id   path    string  true  "Simulate ID"
// @Accept      json
// @Produce     json
// @Success     200     {object}  SimulateResponseForSwagger "Simulation detail data"
// @Failure     400     {object}  dtos.ErrorMessage "Bad request - Invalid simulate ID"
// @Failure     500     {object}  dtos.ErrorMessage "Internal server error"
// @Security    BearerAuth
// @Router      /simulate/detail/{simulate_id} [get]
func simulateDetail(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		res, err := s.SimulateDetail(c, c.Param("simulate_id"), tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "simulateDetail Error",
				Entity:         "simulate",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Simulate",
			Entity:         "simulate",
			Message:        "Success: simulate the datas",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("simulate", "simulate datas", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, res)
	}
}

// @Summary Add a Default Request
// @Description This endpoint is used to add a default request for simulation.
// @Tags Default Request
// @Accept  json
// @Produce  json
// @Param request body dtos.RequestForDefaultRequest true "Default Request Payload"
// @Success 200 {string} string "success"
// @Failure     400     {object}  dtos.ErrorMessage "Bad request - Invalid simulate ID"
// @Failure     500     {object}  dtos.ErrorMessage "Internal server error"
// @Router /simulate/default-request/add [post]
func addDefaultRequest(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.RequestForDefaultRequest
		if err := c.Bind(&req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Default Request Payload",
				Entity:         "default_request",
				Message:        "Error: " + utils.Validate(err),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}
		if err := s.AddDefaultRequest(c, tracer, req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Default Request Payload",
				Entity:         "default_request",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Simulate Payload",
			Entity:         "default_request",
			Message:        "Success: Added default request",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("default_request", "default_request", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, "success")
	}
}

// @Summary Get Default Request
// @Description Get Default Request
// @Tags Default Request
// @Accept  json
// @Produce  json
// @Param request body dtos.RequestForGetDefaultRequest true "Default Request Criteria"
// @Success 200 {object} dtos.DefaultRequestForSwagger "Successfully retrieved the default request data"
// @Failure 400 {object} dtos.ErrorMessage "Bad request - Invalid parameters"
// @Router /simulate/default-request [get]
func getDefaultRequest(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		simulate_id := c.Query("simulate_id")
		def_req_type := c.Query("type")
		resp, err := s.GetDefaultRequest(c, tracer, simulate_id, def_req_type)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Default Request",
				Entity:         "default_request",
				Message:        "Error: " + utils.Validate(err),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Simulate Payload",
			Entity:         "default_request",
			Message:        "Success: single default request data",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("default_request", "default_request", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Delete Default Request
// @Description Delete Default Request by ID
// @Tags Default Request
// @Accept  json
// @Produce  json
// @Param id path string true "ID of the default request to be deleted"
// @Success 200 {string} dtos.SuccessMessage "successfully deleted"
// @Failure 400 {object} dtos.ErrorMessage "Bad request - Invalid request ID"
// @Router /simulate/default-request/{id} [delete]
func deleteDefaultRequest(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		err := s.DeleteDefaultRequest(c, tracer, id)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Default Request",
				Entity:         "default_request",
				Message:        "Error: " + utils.Validate(err),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Simulate Payload",
			Entity:         "default_request",
			Message:        "Success: single default request data",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("default_request", "default_request", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, "successfully deleted")
	}
}

// @Summary Simulate by id
// @Description Simulate by id
// @Tags Simulate
// @Accept  json
// @Produce  json
// @Success 200 {object} dtos.RequestForSimByID "sim by id request"
// @Failure 400 {object} dtos.ErrorMessage "Bad request - Invalid parameters"
// @Router /simulate/by-id [post]
func simByID(s simulatePkg.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.RequestForSimByID
		if err := c.Bind(&req); err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "simByID Payload",
				Entity:         "simulate",
				Message:        "Error: " + utils.Validate(err),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": utils.Validate(err)})
			return
		}

		res, err := s.SimByID(c, req, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Simulate Error",
				Entity:         "simulate",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Simulate",
			Entity:         "simulate",
			Message:        "Success: simulate the data",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		activity.NewActivity("simByID", "simulate", c.ClientIP(), c, tracer)

		c.JSON(http.StatusOK, res)
	}
}
