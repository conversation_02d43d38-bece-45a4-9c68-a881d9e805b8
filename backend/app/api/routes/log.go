package routes

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/parsguru/pars-vue/pkg/activity"
	"github.com/parsguru/pars-vue/pkg/domains/log"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
)

func LogRoutes(r *gin.RouterGroup, s log.Service, tracer trace.Tracer) {
	r = r.Group("/log")

	r.GET("/", middleware.Authorized(), middleware.RoleCheck("log", true, "read"), getLogs(s, tracer))
	r.GET("/:id", middleware.Authorized(), middleware.RoleCheck("log", true, "read"), getLog(s, tracer))

	r.GET("/simulate/:simulate_id", middleware.Authorized(), getSimulateLog(s, tracer))
	r.GET("/simulate/single/:log_id", middleware.Authorized(), getSimulateLogSingle(s, tracer))

	r.GET("/retried/:retried_log_id", middleware.Authorized(), getRetriedLog(s, tracer))
}

// @Summary List logs with pagination
// @Description List logs with pagination
// @Tags Logs
// @Accept  json
// @Produce  json
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Number of logs per page" default(10)
// @Param entity query string false "Entity name"
// @Param entity_id query string false "Entity ID"
// @Success 200 {array} dtos.PaginatedData "List of logs"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /logs/ [get]
func getLogs(s log.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}
		entity := c.Query("entity")
		entity_id := c.Query("entity_id")

		logs, err := s.List(page, perPage, uuid.New(), entity, entity_id, c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Log List",
				Entity:         "log",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "Log List",
			Entity:         "log",
			Message:        "Success: List of logs",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("list", "log", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, logs)
	}
}

// @Summary Get a log by ID
// @Description Get a log by ID
// @Tags Logs
// @Accept  json
// @Produce  json
// @Param id path string true "Log ID"
// @Success 200 {object} dtos.LogDTO "Log details"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /logs/{id} [get]
func getLog(s log.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		log, err := s.Read(c.Param("id"), c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Log Get error",
				Entity:         "log",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             c.ClientIP(),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		monolog.CreateLog(&entities.Log{
			Title:          "Log Get",
			Entity:         "log",
			Message:        "Success: Log get by id",
			Type:           "info",
			Proto:          "http",
			EntityID:       helpers.ParseID(c.Param("id")),
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})
		activity.NewActivity("read", "log", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, log)
	}
}

// @Summary Get simulate logs with pagination
// @Description Get simulate logs with pagination, use simulate ID
// @Tags Logs
// @Accept  json
// @Produce  json
// @Param simulate_id path string true "Simulate ID"
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Number of logs per page" default(10)
// @Success 200 {array} dtos.PaginatedData "List of simulate logs"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /logs/simulate/{simulate_id} [get]
func getSimulateLog(s log.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.Query("page"))
		perPage, _ := strconv.Atoi(c.Query("per_page"))
		if page == 0 {
			page = 1
		}
		if perPage == 0 {
			perPage = 10
		}

		var (
			simulate_id  string = c.Param("simulate_id")
			log_type     string = c.Query("type")
			protocol     string = c.Query("protocol")
			date         string = c.Query("date")
			status       string = c.Query("status")
			scheduled_id string = c.Query("scheduled_id")
		)

		resp, err := s.GetSimulateLogs(page, perPage, simulate_id, scheduled_id, log_type, protocol, date, status, c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Simulate Log List",
				Entity:         "log",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		activity.NewActivity("list", "simulate log", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, resp)
	}
}

// @Summary Get a single simulate log by simulate ID and log ID
// @Description Get a single simulate log by simulate ID and log ID
// @Tags Logs
// @Accept  json
// @Produce  json
// @Param simulate_id path string true "Simulate ID"
// @Param log_id path string true "Log ID"
// @Success 200 {object} dtos.SimulateLogForSwagger "Simulate log details"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Router /logs/simulate/single/{log_id} [get]
func getSimulateLogSingle(s log.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		resp, err := s.GetSingleSimulateLog(c, tracer, c.Param("log_id"))
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "Simulate Log Get Single",
				Entity:         "log",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		activity.NewActivity("read", "simulate log", c.ClientIP(), c, tracer)
		c.JSON(http.StatusOK, resp)
	}
}

func getRetriedLog(s log.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		retried_log_id := c.Param("retried_log_id")
		resp, err := s.GetRetriedLog(c, retried_log_id, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "getRetriedLog",
				Entity:         "log",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}
