package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/domains/dash"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
)

func DashRoutes(r *gin.RouterGroup, s dash.Service, tracer trace.Tracer) {
	r = r.Group("/dash")

	r.GET("", middleware.Authorized(), getDashData(s, tracer))
}

// @Summary     Get Dashboard Data
// @Description Get dashboard data for current organization.
// @Tags        Dashboard
// @Accept      json
// @Produce     json
// @Success     200 {object} dtos.ResponseForDashboard "Success dashboard data"
// @Failure     500 {object} dtos.ErrorMessage "Internal server error"
// @Security    BearerAuth
// @Router      /dash [get]
func getDashData(s dash.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		resp, err := s.Get(c, tracer)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "GetDashData Error",
				Entity:         "dash",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}
