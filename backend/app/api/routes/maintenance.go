package routes

import (
	"net/http"

	"github.com/parsguru/pars-vue/pkg/domains/maintenance"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"

	"github.com/gin-gonic/gin"

	"go.opentelemetry.io/otel/trace"
)

func MaintenanceRoutes(r *gin.RouterGroup, s maintenance.Service, tracer trace.Tracer) {
	r.POST("/maintenance", middleware.Authorized(), middleware.RoleCheck("maintenance", true, "create"), maintenanceHandler(s, tracer))
	r.GET("/maintenance", getMaintenanceStatus(s, tracer))
}

// @ID				switchMaintenanceMode
// @Summary		Switch maintenance mode
// @Description	Switch maintenance mode
// @Tags			Maintenance Endpoints
// @Accept			json
// @Produce		json
// @Param			payload	body		dtos.MaintenanceRequest	true	"payload"
// @Success		200		{object}	dtos.SuccessMessage
// @Failure		400		{object}	dtos.ErrorMessage
// @Failure		500		{object}	dtos.ErrorMessage
// @Router			/maintenance [post]
func maintenanceHandler(s maintenance.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req dtos.MaintenanceRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, err.Error())
			return
		}

		if req.Enabled {
			if err := s.Enable(c, tracer); err != nil {
				c.JSON(http.StatusInternalServerError, err.Error())
				return
			}
			monolog.CreateLog(&entities.Log{
				Title:          "Maintenance Mode Enabled",
				Message:        "Success: Maintenance mode enabled",
				Type:           "info",
				Entity:         "maintenance",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})
			c.JSON(http.StatusOK, gin.H{"message": "maintenance mode enabled"})
		} else {
			if err := s.Disable(c, tracer); err != nil {
				c.JSON(http.StatusInternalServerError, err.Error())
				return
			}

			monolog.CreateLog(&entities.Log{
				Title:          "Maintenance Mode Disabled",
				Message:        "Success: Maintenance mode disabled",
				Type:           "info",
				Entity:         "maintenance",
				Proto:          "http",
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
				Ip:             state.CurrentIP(c),
			})

			c.JSON(http.StatusOK, gin.H{"message": "maintenance mode disabled"})
		}

	}
}

// @ID				getMaintenanceStatus
// @Summary		Get maintenance status
// @Description	Get maintenance status
// @Tags			Maintenance Endpoints
// @Accept			json
// @Produce		json
// @Success		200	{object}	dtos.MaintenanceStatusResponse
// @Failure		400	{object}	dtos.ErrorMessage
// @Failure		500	{object}	dtos.ErrorMessage
// @Router			/maintenance [get]
func getMaintenanceStatus(s maintenance.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		status, err := s.GetStatus(c.Request.Context(), tracer)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, dtos.MaintenanceStatusResponse{
			Enabled: status,
		})
	}
}
