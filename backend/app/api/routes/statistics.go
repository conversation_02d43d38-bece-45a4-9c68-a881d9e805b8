package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/parsguru/pars-vue/pkg/domains/statistics"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/infrastructure/middleware"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"go.opentelemetry.io/otel/trace"
)

func StatisticsRoutes(r *gin.RouterGroup, s statistics.Service, tracer trace.Tracer) {
	r = r.Group("/statistics")

	r.GET("/simulate/:simulate_id", middleware.Authorized(), getSimulateStatistics(s, tracer))
}

// @Summary     Get Simulate Statistics
// @Description Get simulate statistics for current organization.
// @Tags        Statistics
// @Accept      json
// @Produce     json
// @Success     200 {object} dtos.ResponseForSimulateStatistics "Success simulate statistics"
// @Failure     500 {object} dtos.ErrorMessage "Internal server error"
// @Security    BearerAuth
// @Router      /statistics/simulate/:simulate_id [get]
func getSimulateStatistics(s statistics.Service, tracer trace.Tracer) gin.HandlerFunc {
	return func(c *gin.Context) {
		simulate_id := c.Param("simulate_id")
		resp, err := s.GetSimulateStatistics(c, simulate_id)
		if err != nil {
			monolog.CreateLog(&entities.Log{
				Title:          "GetSimulateStatistics Error",
				Entity:         "statistics",
				Message:        "Error: " + err.Error(),
				Type:           "error",
				Proto:          "http",
				Ip:             state.CurrentIP(c),
				AdminID:        state.CurrentAdminUser(c),
				OrganizationID: state.CurrentAdminOrganization(c),
			})
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		monolog.CreateLog(&entities.Log{
			Title:          "GetSimulateStatistics",
			Entity:         "statistics",
			Message:        "Success: simulate statistics",
			Type:           "info",
			Proto:          "http",
			Ip:             state.CurrentIP(c),
			AdminID:        state.CurrentAdminUser(c),
			OrganizationID: state.CurrentAdminOrganization(c),
		})

		c.JSON(http.StatusOK, resp)
	}
}
