package grpcroutes

import (
	"context"
	"errors"

	pb "github.com/parsguru/pars-vue/grpc"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"

	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type PreferencesServer struct {
	pb.PreferencesServiceServer
	Tracer trace.Tracer
	DB     *gorm.DB
}

func (s PreferencesServer) AddDSN(ctx context.Context, req *pb.DSNAddDTO) (*pb.DSNResponseDTO, error) {
	var (
		db_node_setting         entities.DBNodeSetting
		control_db_node_setting []entities.DBNodeSetting
		resp                    pb.DSNResponseDTO
	)

	s.DB.WithContext(ctx).
		Where(query.WhereOrganizationID, state.CurrentUserOrganization(ctx)).
		Find(&control_db_node_setting)

	if len(control_db_node_setting) >= 3 {
		monolog.CreateLog(&entities.Log{
			Title:          "AddDSN",
			Entity:         "DBNodeSetting",
			Message:        "Error: you can add up to 3 keys",
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &resp, errors.New("you can add up to 3 keys")
	}

	for _, v := range control_db_node_setting {
		if v.Key == req.GetKey() {
			monolog.CreateLog(&entities.Log{
				Title:          "AddDSN",
				Entity:         "DBNodeSetting",
				Message:        "Error: key already exists",
				Type:           "error",
				Proto:          "grpc",
				Ip:             state.CurrentGRPCIP(ctx),
				AdminID:        state.CurrentUser(ctx),
				OrganizationID: state.CurrentUserOrganization(ctx),
			})
			return &resp, errors.New("key already exists")
		}
	}

	db_node_setting.DSN = req.GetDsn()
	db_node_setting.Key = req.Key
	db_node_setting.DBEngine = uint(req.GetDbEngine())
	db_node_setting.OrganizationID = state.CurrentUserOrganization(ctx)
	db_node_setting.AdminID = state.CurrentUser(ctx)

	if err := s.DB.WithContext(ctx).
		Create(&db_node_setting).Error; err != nil {
		return &resp, err
	}

	monolog.CreateLog(&entities.Log{
		Title:          "AddDSN",
		Entity:         "DBNodeSetting",
		Message:        "Success: " + req.GetKey() + " DSN added",
		Type:           "info",
		Proto:          "grpc",
		Ip:             state.CurrentGRPCIP(ctx),
		AdminID:        state.CurrentUser(ctx),
		OrganizationID: state.CurrentUserOrganization(ctx),
	})

	resp.Message = "DSN added successfully"
	return &resp, nil
}

func (s PreferencesServer) GetDSN(ctx context.Context, req *pb.DSNEmptyDTO) (*pb.DSNGetResponseDTO, error) {
	var (
		db_node_settings []entities.DBNodeSetting
		dsns             []*pb.DSNGetDTO
		resp             pb.DSNGetResponseDTO
	)

	s.DB.WithContext(ctx).
		Where(query.WhereOrganizationID, state.CurrentUserOrganization(ctx)).
		Find(&db_node_settings)

	for _, v := range db_node_settings {
		dsns = append(dsns, &pb.DSNGetDTO{
			Id:        v.ID.String(),
			CreatedAt: v.CreatedAt.Format("2006-01-02 15:04:05"),
			Key:       v.Key,
			Dsn:       v.DSN,
			DbEngine:  int64(v.DBEngine),
		})
	}

	monolog.CreateLog(&entities.Log{
		Title:          "GetDSN",
		Entity:         "DBNodeSetting",
		Message:        "Success: DSNs fetched",
		Type:           "info",
		Proto:          "grpc",
		Ip:             state.CurrentGRPCIP(ctx),
		AdminID:        state.CurrentUser(ctx),
		OrganizationID: state.CurrentUserOrganization(ctx),
	})

	resp.Dsns = dsns
	return &resp, nil
}

func (s PreferencesServer) DeleteDSN(ctx context.Context, req *pb.DSNDeleteDTO) (*pb.DSNResponseDTO, error) {
	var (
		db_node_setting entities.DBNodeSetting
		resp            pb.DSNResponseDTO
	)

	if err := s.DB.WithContext(ctx).
		Where("organization_id = ? AND id = ?", state.CurrentUserOrganization(ctx), req.GetId()).
		First(&db_node_setting).Error; err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "DeleteDSN",
			Entity:         "DBNodeSetting",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &resp, err
	}

	if err := s.DB.WithContext(ctx).
		Delete(&db_node_setting).Error; err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "DeleteDSN",
			Entity:         "DBNodeSetting",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &resp, err
	}

	monolog.CreateLog(&entities.Log{
		Title:          "DeleteDSN",
		Entity:         "DBNodeSetting",
		Message:        "Success: " + req.GetId() + "DSN deleted",
		Type:           "info",
		Proto:          "grpc",
		Ip:             state.CurrentGRPCIP(ctx),
		AdminID:        state.CurrentUser(ctx),
		OrganizationID: state.CurrentUserOrganization(ctx),
	})

	resp.Message = "DSN deleted successfully"
	return &resp, nil
}
