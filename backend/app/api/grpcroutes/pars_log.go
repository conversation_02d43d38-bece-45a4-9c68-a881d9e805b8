package grpcroutes

import (
	"context"
	"math"

	"github.com/google/uuid"
	pb "github.com/parsguru/pars-vue/grpc"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type ParsLogServer struct {
	pb.LogServiceServer
	Tracer trace.Tracer
	DB     *gorm.DB
}

func (s ParsLogServer) GetLog(ctx context.Context, req *pb.LogRequestDTO) (*pb.LogDTO, error) {
	ctx = helpers.PushToTrace(s.Tracer, ctx, "log.GetLog", "log.GetLog")
	var log entities.Log
	var err error
	if err = s.DB.WithContext(ctx).Where(query.BuildQuery(query.WhereID), req.Id).First(&log).Error; err != nil {
		return nil, err
	}

	logdto := pb.LogDTO{
		Id:        log.ID.String(),
		Title:     log.Title,
		Entity:    log.Entity,
		EntityId:  log.EntityID.String(),
		Type:      log.Type,
		Ip:        log.Ip,
		Proto:     log.Proto,
		Message:   log.Message,
		CreatedAt: log.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	if log.AdminID != uuid.Nil {
		var admin entities.User
		if err := s.DB.WithContext(ctx).Where(query.BuildQuery(query.WhereID), log.AdminID).First(&admin).Error; err != nil {
			return nil, err
		}
		logdto.Admin = admin.Name

	} else {
		logdto.Admin = "-"
	}
	defer func(err error) {
		log := &entities.Log{
			Title:          "GetLog",
			Entity:         "logs",
			Proto:          "grpc",
			Ip:             state.CurrentIP(ctx),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		}
		if err != nil {
			log.Message = "Error: " + utils.Validate(err)
			log.Type = "error"
			monolog.CreateLog(log)
		} else {
			log.Message = "Success"
			log.Type = "info"
			monolog.CreateLog(log)
		}
	}(err)
	return &logdto, nil
}

func (s ParsLogServer) GetLogs(ctx context.Context, req *pb.LogPageRequestDTO) (*pb.LogPaginatedData, error) {
	ctx = helpers.PushToTrace(s.Tracer, ctx, "log.GetLogs", "log.GetLogs")
	var count int64
	var logs []entities.Log
	var list []*pb.LogListItem
	condition := query.QueryBuilder{
		Keys: []query.QueryKey{
			{
				Key:    query.WhereEntity,
				Values: []interface{}{req.Entity},
				Skip:   req.Entity == "",
			},
			{
				Key:    query.WhereType,
				Values: []interface{}{"info"},
				Skip:   state.AmIAuthorized(ctx),
			},
		},
	}

	sql, data := condition.GetQueriesWithValues()

	tx := s.DB.WithContext(ctx).
		Where(sql, data...).
		Limit(int(req.PerPage)).
		Offset(int((req.Page - 1) * req.PerPage)).
		Order(query.OrderByCreatedAtDesc).
		Find(&logs)
	s.DB.WithContext(ctx).
		Model(&entities.Log{}).
		Where(sql, data...).
		Count(&count)

	if tx.Error != nil {
		return nil, tx.Error
	}

	for _, log := range logs {
		item := pb.LogListItem{
			Id:      log.ID.String(),
			Message: log.Message,
			Type:    log.Type,
			Proto:   log.Proto,
		}
		list = append(list, &item)
	}
	defer func(err error) {
		log := &entities.Log{
			Title:          "GetLogs",
			Entity:         "logs",
			Proto:          "grpc",
			Ip:             state.CurrentIP(ctx),
			AdminID:        state.CurrentAdminUser(ctx),
			OrganizationID: state.CurrentAdminOrganization(ctx),
		}
		if err != nil {
			log.Message = "Error: " + utils.Validate(err)
			log.Type = "error"
			monolog.CreateLog(log)
		} else {
			log.Message = "Success"
			log.Type = "info"
			monolog.CreateLog(log)
		}
	}(tx.Error)
	return &pb.LogPaginatedData{
		Page:       req.Page,
		PerPage:    req.PerPage,
		Total:      tx.RowsAffected,
		TotalPages: int64(math.Ceil(float64(count) / float64(req.PerPage))),
		Rows:       list,
	}, nil
}

func (s ParsLogServer) GetSimulateLog(ctx context.Context, req *pb.LogSimulateRequestDTO) (*pb.LogPaginatedData, error) {
	var (
		log_list         []*pb.LogListItem
		simulate_logs    []entities.SimulateLog
		current_simulate entities.Simulate
		count            int64
		resp             pb.LogPaginatedData
	)
	q := s.DB.WithContext(ctx).
		Where("simulate_id = ? AND organization_id = ?", req.GetSimulateId(), state.CurrentUserOrganization(ctx)).
		Where("application = ?", req.GetApplication()).
		Limit(int(req.GetPerPage())).
		Offset((int(req.GetPage()) - 1) * int(req.GetPerPage())).
		Order("created_at DESC").
		Find(&simulate_logs)

	if err := s.DB.WithContext(ctx).
		Where("id = ? AND organization_id = ?", req.GetSimulateId(), state.CurrentUserOrganization(ctx)).
		First(&current_simulate).Error; err != nil {
		return &resp, err
	}

	for _, v := range simulate_logs {
		s := &pb.LogListItem{
			Id:             v.ID.String(),
			Message:        v.Message,
			Type:           v.Type,
			Proto:          v.Proto,
			CreatedAt:      v.CreatedAt.Format("2006-01-02 15:04:05"),
			SimulateId:     v.SimulateID.String(),
			SimulateName:   current_simulate.Name,
			OrganizationId: v.OrganizationID.String(),
		}
		log_list = append(log_list, s)
	}
	if q.Error != nil {
		return &resp, q.Error
	}

	if err := s.DB.WithContext(ctx).
		Model(&entities.SimulateLog{}).
		Where("simulate_id = ? AND organization_id = ?", req.GetSimulateId(), state.CurrentUserOrganization(ctx)).
		Where("application = ?", req.GetApplication()).
		Count(&count).Error; err != nil {
		return &resp, err
	}

	resp.Page = req.GetPage()
	resp.PerPage = req.GetPerPage()
	resp.Total = count
	resp.TotalPages = int64(math.Ceil(float64(count) / float64(req.GetPerPage())))
	resp.Rows = log_list

	return &resp, nil
}

func (s ParsLogServer) GetSingleSimulateLog(ctx context.Context, req *pb.LogSingleSimulateLog) (*pb.LogSimulateLog, error) {
	var (
		resp         pb.LogSimulateLog
		simulate_log entities.SimulateLog
		current_org  entities.Organization
		current_sim  entities.Simulate
	)

	if err := s.DB.WithContext(ctx).
		Where("id = ?", state.CurrentUserOrganization(ctx)).
		First(&current_org).Error; err != nil {
		return &resp, err
	}

	if err := s.DB.WithContext(ctx).
		Where("organization_id = ?", current_org.ID).
		Where("id = ?", req.GetLogId()).
		First(&simulate_log).Error; err != nil {
		return &resp, err
	}

	if err := s.DB.WithContext(ctx).
		Where("id = ? AND organization_id = ?", simulate_log.SimulateID, current_org.ID).
		First(&current_sim).Error; err != nil {
		return &resp, err
	}

	resp.Id = simulate_log.ID.String()
	resp.SimulateId = current_sim.ID.String()
	resp.SimulateName = current_sim.Name
	resp.OrganizationId = current_org.ID.String()
	resp.OrganizationName = current_org.Name
	resp.Type = simulate_log.Type
	resp.Proto = simulate_log.Proto
	resp.CreatedAt = simulate_log.CreatedAt.Format("2006-01-02 15:04:05")
	resp.Message = simulate_log.Message
	resp.Request = []byte(simulate_log.Request)
	resp.Response = []byte(simulate_log.Response)

	return &resp, nil
}
