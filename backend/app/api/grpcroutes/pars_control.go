package grpcroutes

import (
	"context"

	pb "github.com/parsguru/pars-vue/grpc/pars_control/v1"
	"github.com/parsguru/pars-vue/pkg/helpers"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type ParsControlServer struct {
	pb.ParsControlServiceServer
	Tracer trace.Tracer
	DB     *gorm.DB
}

func (s ParsControlServer) ParsControlAuthorization(ctx context.Context, req *pb.RequestForParsControlAuthorization) (*pb.ResponseForParsControlAuthorization, error) {
	ctx = helpers.PushToTrace(s.Tracer, ctx, "pars_control.GetParsControl", "pars_control.GetParsControl")
	var resp pb.ResponseForParsControlAuthorization

	// jwt := utils.JwtWrapper{
	// 	SecretKey: config.ReadValue().JwtSecret,
	// 	Issuer:    config.ReadValue().JwtIssuer,
	// 	Expire:    config.ReadValue().JwtExpire,
	// }

	// _, err := jwt.ParseApplicationToken(req.GetToken())
	// if err != nil {
	// 	return &resp, err
	// }

	// var app_token_auth []entities.ApplicationTokenAuthorization
	// if err := s.DB.WithContext(ctx).
	// 	Model(&entities.ApplicationTokenAuthorization{}).Find(&app_token_auth).Error; err != nil {
	// 	return &resp, err
	// }
	// var (
	// 	control_boolean bool = false
	// 	control_int     int
	// )

	//-----> first we check token isn't manipulated or not
	// for _, x := range app_token_auth {
	// 	for _, y := range claims.Sections {
	// 		if y == req.GetSection() {
	// 			control_boolean = true
	// 		}
	// 		//-----> control for token
	// 		if x.Section == y {
	// 			control_int++
	// 		}
	// 	}
	// }

	// if control_int != len(app_token_auth) {
	// 	control_boolean = false
	// }

	// if !control_boolean {
	// 	return &resp, fmt.Errorf("not authorized")
	// }

	resp.Message = "authorized"

	return &resp, nil
}
