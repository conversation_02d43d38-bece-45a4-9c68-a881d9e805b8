package grpcroutes

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"github.com/gorules/zen-go"
	pb "github.com/parsguru/pars-vue/grpc/pars_simulate/v1"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/constants/query"
	"github.com/parsguru/pars-vue/pkg/domains/nodes"
	"github.com/parsguru/pars-vue/pkg/domains/simulate"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/monolog"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ParsSimulateServer struct {
	pb.ParsSimulateServiceServer
	Tracer trace.Tracer
	DB     *gorm.DB
}

func readTestFile(key string) ([]byte, error) {
	return []byte(key), nil
}

func (s ParsSimulateServer) Simulate(ctx context.Context, req *pb.SimulateDTO) (*pb.ResponseSimulate, error) {
	var (
		output       pb.ResponseSimulate
		evaluate     entities.Evaluations
		simulate_log entities.SimulateLog
	)

	evaluate.IsThereError = false

	defer func() {
		evaluate.ProtoType = "grpc"
		evaluate.OrganizationID = state.CurrentUserOrganization(ctx)
		evaluate.AdminID = state.CurrentUser(ctx)
		if req.GetId() != "" {
			evaluate.SimulateID = uuid.MustParse(req.GetId())
		}
		s.DB.WithContext(ctx).Create(&evaluate)

		if req.GetId() != "" {
			simulate_log.SimulateID = uuid.MustParse(req.GetId())
			simulate_log.OrganizationID = state.CurrentUserOrganization(ctx)
			simulate_log.AdminID = state.CurrentUser(ctx)
			simulate_log.Proto = "grpc"
		} else {
			return
		}
		s.DB.WithContext(ctx).Create(&simulate_log)
	}()

	graph, err := json.Marshal(req.GetContent())
	if err != nil {
		evaluate.IsThereError = true
		monolog.CreateLog(&entities.Log{
			Title:          "Simulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &output, err
	}

	simulate_log.Request = string(req.GetContext())

	if err := addUserOrgSimIDToContext(ctx, req); err != nil {
		evaluate.IsThereError = true
		monolog.CreateLog(&entities.Log{
			Title:          "Simulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &output, err
	}

	evaluate.Content = string(graph)
	evaluate.Context = string(req.GetContext())

	engine := zen.NewEngine(zen.EngineConfig{
		Loader:            readTestFile,
		CustomNodeHandler: nodes.CustomNodeHandler,
	})

	defer engine.Dispose()
	zenOutput, err := engine.EvaluateWithOpts(
		string(graph),
		req.GetContext(),
		zen.EvaluationOptions{Trace: true},
	)
	if err != nil {
		simulate_log.Type = "error"
		simulate_log.Message = err.Error()
		evaluate.IsThereError = true
		monolog.CreateLog(&entities.Log{
			Title:          "Simulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &output, err
	}

	output.Performance = zenOutput.Performance
	output.Trace = *zenOutput.Trace
	output.Result = zenOutput.Result

	var (
		resulJSONtbyte []byte
		resulJSONtmap  map[string]any
	)

	resulJSONtbyte, err = zenOutput.Result.MarshalJSON()
	if err != nil {
		simulate_log.Type = "error"
		simulate_log.Message = err.Error()
		simulate_log.Response = ""
		evaluate.IsThereError = true
		monolog.CreateLog(&entities.Log{
			Title:          "Simulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.ResponseSimulate{}, err
	}
	err = json.Unmarshal(resulJSONtbyte, &resulJSONtmap)
	if err != nil {
		evaluate.IsThereError = true
		monolog.CreateLog(&entities.Log{
			Title:          "Simulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.ResponseSimulate{}, err
	}
	simulate_log.Type = "success"
	simulate_log.Message = "Simulate is success"
	simulate_log.Response = string(resulJSONtbyte)

	evaluate.SimulateResult = resulJSONtmap
	output.Result = removeUnderlineValues(output.Result)

	return &output, err
}

type Content struct {
	Nodes []dtos.Node `json:"nodes"`
	Edges []dtos.Edge `json:"edges"`
}

func (s ParsSimulateServer) SimByID(ctx context.Context, req *pb.SimulateByID) (*pb.ResponseSimulate, error) {
	var (
		output       pb.ResponseSimulate
		evaluate     entities.Evaluations
		simulate_log entities.SimulateLog
	)

	evaluate.IsThereError = false

	// -----> parse simulate token start
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().JwtSecret,
		Issuer:    config.ReadValue().JwtIssuer,
	}

	if !jwt.ValidateSimulateToken(req.GetToken()) {
		return &output, errors.New("invalid token")
	}

	claims, err := jwt.ParseSimulateToken(req.GetToken())
	if err != nil {
		return &output, err
	}
	// parse simulate token end <-----

	defer func() {
		evaluate.ProtoType = "grpc"
		evaluate.OrganizationID = state.CurrentUserOrganization(ctx)
		evaluate.AdminID = state.CurrentUser(ctx)
		if claims.SimulateID != "" {
			evaluate.SimulateID = uuid.MustParse(claims.SimulateID)
		}
		s.DB.WithContext(ctx).Create(&evaluate)

		if claims.SimulateID != "" {
			simulate_log.SimulateID = uuid.MustParse(claims.SimulateID)
			simulate_log.OrganizationID = state.CurrentUserOrganization(ctx)
			simulate_log.AdminID = state.CurrentUser(ctx)
			simulate_log.Proto = "grpc"
			simulate_log.Source = "external"
			simulate_log.Domain = req.GetDomain()
		} else {
			return
		}
		s.DB.WithContext(ctx).Create(&simulate_log)
	}()

	var (
		current_simulate entities.Simulate
		m_node           []dtos.Node
		m_edge           []dtos.Edge
	)
	if err := s.DB.Where(query.WhereID, claims.SimulateID).
		Where(query.WhereOrganizationID, claims.OrganizationID).
		Preload(clause.Associations).
		First(&current_simulate).Error; err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "SimByID",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &output, err
	}

	for _, v := range current_simulate.Nodes {
		var s_node dtos.Node
		if v.Content != nil {
			s_node.Content = v.Content
		} else {
			s_node.Content = v.FunctionContent
		}
		s_node.ID = v.NodeID
		s_node.Name = v.Name
		s_node.Position = v.Position
		s_node.Type = v.Type

		m_node = append(m_node, s_node)
	}

	for _, v := range current_simulate.Edges {
		var s_edge dtos.Edge
		s_edge.ID = v.ID
		s_edge.SourceHandle = v.SourceHandle
		s_edge.SourceID = v.SourceID
		s_edge.TargetID = v.TargetID
		s_edge.Type = v.Type

		m_edge = append(m_edge, s_edge)
	}

	content_map := make(map[string]any)
	context_map := make(map[string]any)

	content_map["nodes"] = m_node
	content_map["edges"] = m_edge

	graph, err := json.Marshal(content_map)
	if err != nil {
		evaluate.IsThereError = true
		simulate_log.Message = err.Error()
		simulate_log.Type = "error"
		monolog.CreateLog(&entities.Log{
			Title:          "SimByID",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &output, err
	}

	simulate_log.Request = string(req.GetContext())
	if err := json.Unmarshal(req.GetContext(), &context_map); err != nil {
		simulate_log.Message = err.Error()
		simulate_log.Type = "error"
		evaluate.IsThereError = true
		monolog.CreateLog(&entities.Log{
			Title:          "SimByID",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &output, err
	}

	context_map["_simulate_id"] = claims.SimulateID
	context_map["_organization_id"] = claims.OrganizationID

	if err := addUserOrgSimIDToContext2(ctx, req, claims.SimulateID); err != nil {
		evaluate.IsThereError = true
		simulate_log.Message = err.Error()
		simulate_log.Type = "error"
		monolog.CreateLog(&entities.Log{
			Title:          "SimByID",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &output, err
	}

	evaluate.Content = string(graph)
	evaluate.Context = string(req.GetContext())

	engine := zen.NewEngine(zen.EngineConfig{
		Loader:            readTestFile,
		CustomNodeHandler: nodes.CustomNodeHandler,
	})

	defer engine.Dispose()
	zenOutput, err := engine.EvaluateWithOpts(
		string(graph),
		req.GetContext(),
		zen.EvaluationOptions{Trace: true},
	)
	if err != nil {
		simulate_log.Type = "error"
		simulate_log.Message = err.Error()
		evaluate.IsThereError = true
		monolog.CreateLog(&entities.Log{
			Title:          "SimByID",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &output, err
	}

	output.Performance = zenOutput.Performance
	output.Trace = *zenOutput.Trace
	output.Result = zenOutput.Result

	var (
		resulJSONtbyte []byte
		resulJSONtmap  map[string]any
	)

	zenOutput.Result = removeUnderlineValues(zenOutput.Result)

	resulJSONtbyte, err = zenOutput.Result.MarshalJSON()
	if err != nil {
		simulate_log.Type = "error"
		simulate_log.Message = err.Error()
		simulate_log.Response = ""
		evaluate.IsThereError = true
		monolog.CreateLog(&entities.Log{
			Title:          "SimByID",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.ResponseSimulate{}, err
	}
	err = json.Unmarshal(resulJSONtbyte, &resulJSONtmap)
	if err != nil {
		evaluate.IsThereError = true
		simulate_log.Message = err.Error()
		simulate_log.Type = "error"
		monolog.CreateLog(&entities.Log{
			Title:          "SimByID",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.ResponseSimulate{}, err
	}
	simulate_log.Type = "success"
	simulate_log.Message = "Simulate is success"
	simulate_log.Response = string(resulJSONtbyte)

	evaluate.SimulateResult = resulJSONtmap
	output.Result = removeUnderlineValues(output.Result)

	return &output, err
}

// -----> İf you send version_id as empty string, it will return all simulations.
func (s ParsSimulateServer) GetSimulations(ctx context.Context, req *pb.SimulatePageRequest) (*pb.SimulatePaginatedData, error) {
	preload := []string{"SimulateOutput", "Nodes", "Edges"}
	data, err := getSimulates(int(req.Page), int(req.PerPage), req.GetVersionId(), s.DB, preload, ctx)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "GetSimulations",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return data, err
	}

	monolog.CreateLog(&entities.Log{
		Title:          "GetSimulations",
		Entity:         "simulate",
		Message:        "Success: Simulations fetched",
		Type:           "info",
		Proto:          "grpc",
		Ip:             state.CurrentGRPCIP(ctx),
		AdminID:        state.CurrentUser(ctx),
		OrganizationID: state.CurrentUserOrganization(ctx),
	})

	return data, err
}

func (s ParsSimulateServer) GetSimulationTokenBySimulateID(ctx context.Context, req *pb.SimulateGet) (*pb.SimulateGeneralResponse, error) {
	var (
		output pb.SimulateGeneralResponse
		sim    entities.Simulate
	)

	if err := s.DB.Where("id=? AND organization_id=?", req.GetId(), state.CurrentUserOrganization(ctx)).
		First(&sim).Error; err != nil {
	}

	output.Message = sim.AccessToken

	return &output, nil
}

func (s ParsSimulateServer) GetSimulationsShort(ctx context.Context, req *pb.SimulatePageRequest) (*pb.SimulatePaginatedData, error) {
	preload := []string{}
	data, err := getSimulates(int(req.Page), int(req.PerPage), req.GetVersionId(), s.DB, preload, ctx)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "GetSimulationsShort",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return data, err
	}

	monolog.CreateLog(&entities.Log{
		Title:          "GetSimulationsShort",
		Entity:         "simulate",
		Message:        "Success: Simulations short fetched",
		Type:           "info",
		Proto:          "grpc",
		Ip:             state.CurrentGRPCIP(ctx),
		AdminID:        state.CurrentUser(ctx),
		OrganizationID: state.CurrentUserOrganization(ctx),
	})

	return data, err
}

func (s ParsSimulateServer) UpdateOrCreateSimulate(ctx context.Context, req *pb.SimulateDTO) (*pb.SimulateGeneralResponse, error) {
	var (
		output      pb.ResponseSimulate
		err         error
		simulate_id string
	)

	if err := addUserOrgSimIDToContext(ctx, req); err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "UpdateOrCreateSimulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.SimulateGeneralResponse{
			Message: err.Error(),
		}, err
	}

	graph, err := json.Marshal(req.GetContent())
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "UpdateOrCreateSimulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.SimulateGeneralResponse{
			Message: err.Error(),
		}, err
	}

	engine := zen.NewEngine(zen.EngineConfig{
		Loader:            readTestFile,
		CustomNodeHandler: nodes.CustomNodeHandler,
	})
	defer engine.Dispose()
	zenOutput, err := engine.EvaluateWithOpts(
		string(graph),
		req.GetContext(),
		zen.EvaluationOptions{Trace: true},
	)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "UpdateOrCreateSimulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.SimulateGeneralResponse{
			Message: err.Error(),
		}, err
	}

	output = pb.ResponseSimulate{
		Performance: zenOutput.Performance,
		Trace:       *zenOutput.Trace,
		Result:      zenOutput.Result,
	}

	var (
		nodeEntities  []*entities.Node
		edgeEntities  []*entities.Edge
		nodes         []dtos.Node
		edges         []dtos.Edge
		input_control int = 0
	)

	if err := utils.MapToStruct(req.Content.GetNodes(), &nodes); err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "UpdateOrCreateSimulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.SimulateGeneralResponse{
			Message: err.Error(),
		}, err
	}

	if err = utils.MapToStruct(req.Content.GetEdges(), &edges); err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "UpdateOrCreateSimulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.SimulateGeneralResponse{
			Message: err.Error(),
		}, err
	}

	for _, v := range nodes {
		if v.Type == "inputNode" || v.Type == "outputNode" {
			input_control++
		}
	}

	/*
	 * ERROR_2005: version name already exists
	 * ERROR_2004: there must be at least one input and one output node
	 * ERROR_2003: there must be at least one edge
	 */

	if input_control < 2 {
		monolog.CreateLog(&entities.Log{
			Title:          "UpdateOrCreateSimulate",
			Entity:         "simulate",
			Message:        "Error: there must be at least one input and one output node",
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.SimulateGeneralResponse{
			Message: "there must be at least one input and one output node",
		}, fmt.Errorf("ERROR_2004")
	}

	if len(edges) == 0 {
		monolog.CreateLog(&entities.Log{
			Title:          "UpdateOrCreateSimulate",
			Entity:         "simulate",
			Message:        "Error: there must be at least one edge",
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.SimulateGeneralResponse{
			Message: "there must be at least one edge",
		}, fmt.Errorf("ERROR_2003")
	}

	var previous_simulate entities.Simulate

	err = s.DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("id=? AND organization_id=?", req.GetId(), state.CurrentUserOrganization(ctx)).
			Preload(clause.Associations).
			First(&previous_simulate).Error; err != nil && err != gorm.ErrRecordNotFound {
			return err
		}

		var simulates_for_control []entities.Simulate
		if req.GetIsNew() {
			if err := tx.Where("version_id=? AND organization_id=?", previous_simulate.VersionID, state.CurrentUserOrganization(ctx)).
				Find(&simulates_for_control).Error; err != nil {
				return err
			}
			for _, v := range simulates_for_control {
				if v.Name == req.GetName() {
					return fmt.Errorf("ERROR_2005")
				}
			}
		}

		var (
			new_version_name   string
			new_version_number int
			version_id         uuid.UUID
			err_2              error
		)

		if req.GetIsNew() {
			// -----> if we do that first time
			if previous_simulate.VersionName != "" {
				version_id = previous_simulate.VersionID
				simulate.ResolveVersion(previous_simulate.VersionName)
				new_version_name, new_version_number, err_2 = simulate.GetNewVersion(previous_simulate.VersionName)
				if err_2 != nil {
					return err_2
				}
			} else {
				version_id = uuid.New()
				new_version_name, new_version_number, err_2 = simulate.GetNewVersion(previous_simulate.VersionName)
				if err_2 != nil {
					return err_2
				}
			}
		} else {
			// -----> if we do that not first time
			version_id = previous_simulate.VersionID
			new_version_name = previous_simulate.VersionName
			new_version_number = previous_simulate.VersionNumber
		}

		setted_simulate, err := setSimulate(req, ctx)
		if err != nil {
			return err
		}

		setted_simulate.VersionName = new_version_name
		setted_simulate.VersionNumber = new_version_number
		setted_simulate.VersionID = version_id

		if req.GetIsNew() {
			if err := tx.Model(&entities.Simulate{}).
				Create(&setted_simulate).Error; err != nil {
				return err
			}
		} else {
			if err := tx.Model(&entities.Simulate{}).Debug().
				Where("id=? AND organization_id=?", previous_simulate.ID, previous_simulate.OrganizationID).
				Updates(&setted_simulate).Error; err != nil {
				return err
			}
			setted_simulate.ID = previous_simulate.ID
		}

		simulate_id = setted_simulate.ID.String()

		if !req.GetIsNew() {
			if err := tx.Where("simulate_id", setted_simulate.ID).
				Where("version_id", setted_simulate.VersionID).
				Where("version_name", setted_simulate.VersionName).
				Where("organization_id", state.CurrentUserOrganization(ctx)).
				Unscoped().Delete(&nodeEntities).Error; err != nil {
				return err
			}

			if err := tx.Where("simulate_id", setted_simulate.ID).
				Where("version_id", setted_simulate.VersionID).
				Where("version_name", setted_simulate.VersionName).
				Where("organization_id", state.CurrentUserOrganization(ctx)).
				Unscoped().Delete(&edgeEntities).Error; err != nil {
				return err
			}
		}

		for _, n := range nodes {
			node, err := simulate.GetNode(n, setted_simulate.ID, version_id, setted_simulate.OrganizationID, setted_simulate.AdminID, new_version_name)
			if err != nil {
				return err
			}
			nodeEntities = append(nodeEntities, &node)
		}

		if len(nodeEntities) > 0 {
			if err := tx.Create(&nodeEntities).Error; err != nil {
				return err
			}
		}

		for _, e := range edges {
			edge := simulate.GetEdge(e, setted_simulate.ID, setted_simulate.VersionID, setted_simulate.OrganizationID, setted_simulate.AdminID, setted_simulate.VersionName, 2)
			edgeEntities = append(edgeEntities, &edge)
		}

		if len(edgeEntities) > 0 {
			if err := tx.Create(&edgeEntities).Error; err != nil {
				return err
			}
		}

		simulateOutput, err := setOutput(&output, setted_simulate)
		if err != nil {
			return err
		}

		if req.GetIsNew() {
			jwt := utils.JwtWrapper{
				SecretKey: config.ReadValue().JwtSecret,
				Issuer:    config.ReadValue().JwtIssuer,
			}

			token, _ := jwt.GenerateSimulateJWT(ctx, setted_simulate.ID.String(), setted_simulate.OrganizationID.String(), setted_simulate.AdminID.String(), version_id.String(), new_version_name, 999)

			setted_simulate.AccessToken = token

			if err := tx.Model(&entities.Simulate{}).
				Where("id", setted_simulate.ID).
				Updates(&setted_simulate).Error; err != nil {
				return err
			}
		}

		if rows := tx.Model(&entities.SimulateOutput{}).
			Where("simulate_id", req.GetId()).
			Updates(&simulateOutput).RowsAffected; rows == 0 {
			tx.Create(&simulateOutput)
			return err
		}
		return err
	})

	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "UpdateOrCreateSimulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.SimulateGeneralResponse{
			Message: err.Error(),
		}, err
	}

	if req.GetIsNew() && previous_simulate.VersionName != "" {
		jsonData, _ := json.Marshal(req.GetDefaultRequest())
		def_request_for_new_version := entities.DefaultRequest{
			Value:          string(jsonData),
			SimulateID:     uuid.MustParse(simulate_id),
			OrganizationID: state.CurrentUserOrganization(ctx),
			AdminID:        state.CurrentUser(ctx),
			Type:           2,
		}
		s.DB.WithContext(ctx).Model(&entities.DefaultRequest{}).
			Create(&def_request_for_new_version)
	}

	monolog.CreateLog(&entities.Log{
		Title:          "UpdateOrCreateSimulate",
		Entity:         "simulate",
		Message:        "Success: " + req.GetId() + " simulation created or updated",
		Type:           "info",
		Proto:          "grpc",
		Ip:             state.CurrentIP(ctx),
		AdminID:        state.CurrentAdminUser(ctx),
		OrganizationID: state.CurrentAdminOrganization(ctx),
	})

	return &pb.SimulateGeneralResponse{
		Message: simulate_id,
	}, nil
}

// -----> you also can delete version
func (s ParsSimulateServer) DeleteSimulate(ctx context.Context, req *pb.SimulateDelete) (*pb.SimulateGeneralResponse, error) {
	if err := s.DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.WithContext(ctx).
			Where("id=? AND organization_id=?", req.Id, state.CurrentUserOrganization(ctx)).
			Delete(&entities.Simulate{}).Error; err != nil {
			return err
		}

		query := tx.WithContext(ctx).
			Where("simulate_id=? AND organization_id=?", req.Id, state.CurrentUserOrganization(ctx))

		if err := query.Unscoped().Delete(&entities.Node{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.Edge{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.SimulateLog{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.DefaultRequest{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.SimulateOutput{}).Error; err != nil {
			return err
		}

		if err := query.Unscoped().Delete(&entities.Evaluations{}).Error; err != nil {
			return err
		}

		return nil
	}); err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "DeleteSimulate",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return &pb.SimulateGeneralResponse{
			Message: "error " + err.Error(),
		}, err
	}

	monolog.CreateLog(&entities.Log{
		Title:          "DeleteSimulate",
		Entity:         "simulate",
		Message:        "Success: " + req.Id + " simulation deleted",
		Type:           "info",
		Proto:          "grpc",
		Ip:             state.CurrentIP(ctx),
		AdminID:        state.CurrentAdminUser(ctx),
		OrganizationID: state.CurrentAdminOrganization(ctx),
	})

	return &pb.SimulateGeneralResponse{
		Message: "Simulation deleted successfully.",
	}, nil
}

func (s ParsSimulateServer) GetSimulationRules(ctx context.Context, req *pb.SimulatePageRequest) (*pb.SimulatePaginatedData, error) {
	preload := []string{"Nodes", "Edges"}
	data, err := getSimulates(int(req.Page), int(req.PerPage), "", s.DB, preload, ctx)
	if err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "GetSimulationRules",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return data, err
	}
	monolog.CreateLog(&entities.Log{
		Title:          "GetSimulationRules",
		Entity:         "simulate",
		Message:        "Success: Simulations fetched",
		Type:           "info",
		Proto:          "grpc",
		Ip:             state.CurrentGRPCIP(ctx),
		AdminID:        state.CurrentUser(ctx),
		OrganizationID: state.CurrentUserOrganization(ctx),
	})

	return data, err
}

func (s ParsSimulateServer) SimulateRule(ctx context.Context, req *pb.SimulateGet) (*pb.SimulateResponse, error) {
	var (
		simulate    entities.Simulate
		simulateDTO *pb.SimulateResponse
	)

	if err := s.DB.Where(query.WhereID, req.GetId()).
		Where(query.WhereOrganizationID, state.CurrentUserOrganization(ctx)).
		Preload(clause.Associations).
		First(&simulate).Error; err != nil {
		monolog.CreateLog(&entities.Log{
			Title:          "SimulateRule",
			Entity:         "simulate",
			Message:        "Error: " + err.Error(),
			Type:           "error",
			Proto:          "grpc",
			Ip:             state.CurrentGRPCIP(ctx),
			AdminID:        state.CurrentUser(ctx),
			OrganizationID: state.CurrentUserOrganization(ctx),
		})
		return simulateDTO, err
	}

	m_context, _ := json.Marshal(simulate.Context)
	m_result, _ := json.Marshal(simulate.SimulateOutput.Result)
	m_trace, _ := json.Marshal(simulate.SimulateOutput.Trace)

	simulateDTO = &pb.SimulateResponse{
		Id:             simulate.ID.String(),
		CreatedAt:      simulate.CreatedAt.Format("2006-01-02 15:04:05"),
		Name:           simulate.Name,
		Nodes:          []*pb.Node{},
		Edges:          []*pb.Edge{},
		Context:        m_context,
		OrganizationId: simulate.OrganizationID.String(),
		AdminId:        simulate.AdminID.String(),
		VersionId:      simulate.VersionID.String(),
		VersionName:    simulate.VersionName,
		AccessToken:    simulate.AccessToken,
	}

	simulateDTO.SimulateOutput = &pb.SimulateOutput{
		Id:          simulate.SimulateOutput.ID.String(),
		Performance: simulate.SimulateOutput.Performance,
		Result:      m_result,
		Trace:       m_trace,
		SimulateId:  simulate.SimulateOutput.SimulateID.String(),
	}

	for _, v := range simulate.Nodes {
		var term pb.Node
		if v.FunctionContent != "" {
			contentString := &pb.Node_String_{
				String_: v.FunctionContent,
			}
			term.Content = contentString
		} else if v.Content != nil {
			var object *pb.Content
			utils.MapToStruct(v.Content, &object)
			contentObject := &pb.Node_Object{
				Object: object,
			}
			term.Content = contentObject
		}
		for k := range v.Content {
			if k == "rules" {
				term.GetObject().Rules = []*pb.Rule{}
				for _, v := range v.Content["rules"].([]any) {
					rule := pb.Rule{}
					utils.MapToStruct(v, &rule.Rule)
					term.GetObject().Rules = append(term.GetObject().Rules, &rule)
				}
			}
		}
		term.Id = v.NodeID
		term.Name = v.Name

		xterm := int64(v.Position["x"])
		yterm := int64(v.Position["y"])

		term.Position = &pb.NodePosition{
			X: &xterm,
			Y: &yterm,
		}
		term.Type = v.Type
		simulateDTO.Nodes = append(simulateDTO.Nodes, &term)
	}
	for _, v := range simulate.Edges {
		var term pb.Edge
		term.Id = v.ID
		term.SourceHandle = v.SourceHandle
		term.SourceId = v.SourceID
		term.TargetId = v.TargetID
		term.Type = v.Type

		simulateDTO.Edges = append(simulateDTO.Edges, &term)
	}

	monolog.CreateLog(&entities.Log{
		Title:          "SimulateRule",
		Entity:         "simulate",
		Message:        "Success: " + req.GetId() + " simulation fetched",
		Type:           "info",
		Proto:          "grpc",
		Ip:             state.CurrentGRPCIP(ctx),
		AdminID:        state.CurrentUser(ctx),
		OrganizationID: state.CurrentUserOrganization(ctx),
	})

	return simulateDTO, nil
}

func getSimulates(page, perpage int, version_id string, db *gorm.DB, preload []string, ctx context.Context) (*pb.SimulatePaginatedData, error) {
	var (
		simulates   []entities.Simulate
		simulateDTO []*pb.SimulateResponse
		res         pb.SimulatePaginatedData
		total_count int64
	)

	var d = db
	var c = db

	if version_id == "" {
		d = d.Raw(`
			SELECT DISTINCT ON (version_id) * 
			FROM simulates 
			WHERE organization_id = ? AND deleted_at IS NULL 
			ORDER BY version_id, created_at DESC 
			LIMIT ? OFFSET ?
		`, state.CurrentUserOrganization(ctx), perpage, (page-1)*perpage)

		c = c.Raw(`
			SELECT COUNT(*) 
			FROM (
			SELECT DISTINCT ON (version_id) 1 
			FROM simulates 
			WHERE organization_id = ? AND deleted_at IS NULL
		) AS distinct_versions
		`, state.CurrentUserOrganization(ctx))
	} else {
		d = d.Order(query.OrderByCreatedAtDesc).
			Where(query.WhereOrganizationID, state.CurrentUserOrganization(ctx)).
			Where("version_id = ?", version_id).
			Limit(perpage).
			Offset((page - 1) * perpage)

		c = c.Model(&entities.Simulate{}).
			Where(query.WhereOrganizationID, state.CurrentUserOrganization(ctx)).
			Where("version_id = ?", version_id)
	}

	for _, v := range preload {
		d.Preload(v)
	}

	if err := d.Find(&simulates).Error; err != nil {
		return &pb.SimulatePaginatedData{
			Rows: []*pb.SimulateResponse{},
		}, err
	}

	if err := c.Count(&total_count).Error; err != nil {
		return &pb.SimulatePaginatedData{
			Rows: []*pb.SimulateResponse{},
		}, err
	}

	var org entities.Organization
	db.Where("id = ?", state.CurrentUserOrganization(ctx)).First(&org)

	for _, v := range simulates {
		var term pb.SimulateResponse
		var nodes []*pb.Node
		var edges []*pb.Edge
		term.Id = v.ID.String()
		term.Name = v.Name
		for _, e := range v.Nodes {
			var single_node pb.Node

			if e.FunctionContent != "" {
				contentString := &pb.Node_String_{
					String_: e.FunctionContent,
				}
				single_node.Content = contentString
			} else if e.Content != nil {
				var object *pb.Content
				utils.MapToStruct(e.Content, &object)
				contentObject := &pb.Node_Object{
					Object: object,
				}
				single_node.Content = contentObject
			}
			for k := range e.Content {
				if k == "rules" {
					single_node.GetObject().Rules = []*pb.Rule{}
					for _, v := range e.Content["rules"].([]any) {
						rule := pb.Rule{}
						utils.MapToStruct(v, &rule.Rule)
						single_node.GetObject().Rules = append(single_node.GetObject().Rules, &rule)
					}
				}
			}
			single_node.Id = e.NodeID
			single_node.Name = e.Name

			xterm := int64(e.Position["x"])
			yterm := int64(e.Position["y"])

			single_node.Position = &pb.NodePosition{
				X: &xterm,
				Y: &yterm,
			}
			single_node.Type = e.Type
			nodes = append(nodes, &single_node)
		}

		for _, v := range v.Edges {
			var term pb.Edge
			term.Id = v.ID
			term.SourceHandle = v.SourceHandle
			term.SourceId = v.SourceID
			term.TargetId = v.TargetID
			term.Type = v.Type

			edges = append(edges, &term)
		}
		m_context, _ := json.Marshal(v.Context)
		term.Nodes = nodes
		term.Edges = edges
		term.Context = m_context
		term.OrganizationId = v.OrganizationID.String()
		term.OrganizationName = org.Name
		term.CreatedAt = v.CreatedAt.Format("2006-01-02 15:04:05")
		term.VersionName = v.VersionName
		term.VersionId = v.VersionID.String()
		term.AccessToken = v.AccessToken

		simulateDTO = append(simulateDTO, &term)
	}

	res = pb.SimulatePaginatedData{
		Page:    int64(page),
		PerPage: int64(perpage),
		Total:   total_count,
		Rows:    simulateDTO,
	}

	return &res, nil
}
func setOutput(o *pb.ResponseSimulate, simulate entities.Simulate) (entities.SimulateOutput, error) {
	var output entities.SimulateOutput
	var err error

	output.SimulateID = simulate.ID
	output.Performance = o.Performance
	output.OrganizationID = simulate.OrganizationID
	output.AdminID = simulate.AdminID

	var traceJSONmap map[string]any
	var resulJSONtmap map[string]any

	err = json.Unmarshal(o.Trace, &traceJSONmap)
	if err != nil {
		return output, err
	}
	err = json.Unmarshal(o.Result, &resulJSONtmap)
	if err != nil {
		return output, err
	}
	output.Trace = traceJSONmap
	output.Result = resulJSONtmap

	return output, err
}

func getContext(payload *pb.SimulateDTO) (map[string]any, error) {
	var context map[string]any
	err := json.Unmarshal(payload.Context, &context)
	if err != nil {
		return context, err
	}
	return context, nil
}

func setSimulate(payload *pb.SimulateDTO, ctx context.Context) (entities.Simulate, error) {
	var simulate entities.Simulate

	payload.Context = removeUnderlineValues(payload.Context)
	context, err := getContext(payload)
	if err != nil {
		return simulate, err
	}
	simulate.Context = context
	if payload.Name != "" {
		simulate.Name = payload.GetName()
	} else {
		simulate.Name = uuid.NewString()
	}
	simulate.OrganizationID = state.CurrentUserOrganization(ctx)
	simulate.AdminID = state.CurrentUser(ctx)

	return simulate, nil
}
