package grpcroutes

import (
	"context"

	pb "github.com/parsguru/pars-vue/grpc/store/v1"
	"github.com/parsguru/pars-vue/pkg/domains/store"
	"github.com/parsguru/pars-vue/pkg/dtos"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type StoreServer struct {
	pb.StoreServiceServer
	Tracer  trace.Tracer
	DB      *gorm.DB
	service store.Service
}

func NewStoreServer(db *gorm.DB, tracer trace.Tracer, service store.Service) *StoreServer {
	return &StoreServer{
		DB:      db,
		Tracer:  tracer,
		service: service,
	}
}

func (s *StoreServer) Insert(ctx context.Context, req *pb.StoreInsertRequest) (*pb.StoreResult, error) {
	dto := &dtos.StoreInsertRequest{
		Db:    req.Db,
		Table: req.Table,
		Kv:    convertKvFromProto(req.Kv),
	}
	result, err := s.service.Create(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResult{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResult{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) Update(ctx context.Context, req *pb.StoreUpdateRequest) (*pb.StoreResult, error) {
	dto := &dtos.StoreUpdateRequest{
		Db:    req.Db,
		Table: req.Table,
		Kv:    convertKvFromProto(req.Kv),
	}
	result, err := s.service.Update(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResult{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResult{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) Delete(ctx context.Context, req *pb.StoreDeleteRequest) (*pb.StoreResult, error) {
	dto := &dtos.StoreDeleteRequest{
		Db:         req.Db,
		Table:      req.Table,
		Kv:         convertKvFromProto(req.Kv),
		SoftDelete: req.SolftDelete,
	}
	result, err := s.service.Delete(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResult{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResult{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) Select(ctx context.Context, req *pb.StoreConditionRequest) (*pb.StoreResult, error) {
	dto := &dtos.StoreConditionRequest{
		Db:       req.Db,
		Table:    req.Table,
		Kv:       convertKvFromProto(req.Kv),
		Limit:    req.Limit,
		Offset:   req.Offset,
		OrderBy:  req.OrderBy,
		GroupBy:  req.GroupBy,
		RawQuery: req.RawQuery,
	}
	result, err := s.service.Select(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResult{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return convertResultToProto(result), nil
}

func (s *StoreServer) Upsert(ctx context.Context, req *pb.StoreUpsertRequest) (*pb.StoreResult, error) {
	dto := &dtos.StoreUpsertRequest{
		Db:    req.Db,
		Table: req.Table,
		Kv:    convertKvFromProto(req.Kv),
	}
	result, err := s.service.Upsert(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResult{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResult{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) DbCreate(ctx context.Context, req *pb.StoreDbCreateRequest) (*pb.StoreResponse, error) {
	dto := &dtos.StoreDbCreateRequest{
		Db:    req.Db,
		Table: req.Table,
		Kv:    convertKvFromProto(req.Kv),
	}
	result, err := s.service.DbCreate(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResponse{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) DbDrop(ctx context.Context, req *pb.StoreDbDropRequest) (*pb.StoreResponse, error) {
	dto := &dtos.StoreDbDropRequest{
		Db: req.Db,
	}
	result, err := s.service.DbDrop(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResponse{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) DbList(ctx context.Context, req *pb.StoreDbListRequest) (*pb.StoreResponse, error) {
	dto := &dtos.StoreDbListRequest{
		Db: req.Db,
	}
	result, err := s.service.DbList(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResponse{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) TableCreate(ctx context.Context, req *pb.StoreTableCreateRequest) (*pb.StoreResponse, error) {
	dto := &dtos.StoreTableCreateRequest{
		Db:    req.Db,
		Table: req.Table,
		Kv:    convertKvFromProto(req.Kv),
	}
	result, err := s.service.TableCreate(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResponse{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) TableDrop(ctx context.Context, req *pb.StoreTableDropRequest) (*pb.StoreResponse, error) {
	dto := &dtos.StoreTableDropRequest{
		Db:    req.Db,
		Table: req.Table,
	}
	result, err := s.service.TableDrop(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResponse{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) TableList(ctx context.Context, req *pb.StoreTableListRequest) (*pb.StoreTableListResponse, error) {
	dto := &dtos.StoreTableListRequest{
		Db: req.Db,
	}
	result, err := s.service.TableList(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreTableListResponse{}, err
	}
	return &pb.StoreTableListResponse{
		Tables: result.Tables,
	}, nil
}

func (s *StoreServer) ColumnCreate(ctx context.Context, req *pb.StoreColumnCreateRequest) (*pb.StoreResponse, error) {
	dto := &dtos.StoreColumnCreateRequest{
		Db:    req.Db,
		Table: req.Table,
		Kv:    []dtos.Kv{{K: req.Column, V: ""}},
	}
	result, err := s.service.ColumnCreate(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResponse{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) ColumnDrop(ctx context.Context, req *pb.StoreColumnDropRequest) (*pb.StoreResponse, error) {
	dto := &dtos.StoreColumnDropRequest{
		Db:    req.Db,
		Table: req.Table,
		Kv:    []dtos.Kv{{K: req.Column, V: ""}},
	}
	result, err := s.service.ColumnDrop(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}
	return &pb.StoreResponse{
		Success: result.Success,
		Message: result.Message,
	}, nil
}

func (s *StoreServer) ColumnList(ctx context.Context, req *pb.StoreColumnListRequest) (*pb.StoreColumnListResponse, error) {
	dto := &dtos.StoreColumnListRequest{
		Db:    req.Db,
		Table: req.Table,
	}
	result, err := s.service.ColumnList(dto, nil, s.Tracer)
	if err != nil {
		return &pb.StoreColumnListResponse{}, err
	}
	return &pb.StoreColumnListResponse{
		Columns: result.Columns,
	}, nil
}

func convertKvFromProto(protoKvs []*pb.Kv) []dtos.Kv {
	kvs := make([]dtos.Kv, len(protoKvs))
	for i, kv := range protoKvs {
		kvs[i] = dtos.Kv{
			K: kv.K,
			V: kv.V,
		}
	}
	return kvs
}

func convertKvToProto(kvs []dtos.Kv) []*pb.Kv {
	protoKvs := make([]*pb.Kv, len(kvs))
	for i, kv := range kvs {
		protoKvs[i] = &pb.Kv{
			K: kv.K,
			V: kv.V,
		}
	}
	return protoKvs
}

func convertResultToProto(result *dtos.StoreResult) *pb.StoreResult {
	if result == nil {
		return &pb.StoreResult{
			Success: false,
			Message: "nil result",
		}
	}

	rows := make([]*pb.StoreRow, len(result.StoreRows))
	for i, row := range result.StoreRows {
		rows[i] = &pb.StoreRow{
			Kv: convertKvToProto(row.Kv),
		}
	}

	return &pb.StoreResult{
		Success:      result.Success,
		Message:      result.Message,
		AffectedRows: result.AffectedRows,
		StoreRows:    rows,
	}
}
