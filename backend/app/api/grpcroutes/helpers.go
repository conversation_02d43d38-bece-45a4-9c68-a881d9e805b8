package grpcroutes

import (
	"context"
	"encoding/json"

	pb "github.com/parsguru/pars-vue/grpc/pars_simulate/v1"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/state"
	"github.com/parsguru/pars-vue/pkg/utils"
	"google.golang.org/grpc/metadata"
)

func getClaim(ctx context.Context) (*utils.JwtClaim, error) {
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().JwtSecret,
		Issuer:    config.ReadValue().JwtIssuer,
	}
	var token string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		tokens := md.Get("authorization")
		if len(tokens) > 0 {
			token = tokens[0]
		}
	}
	claim, err := jwt.ParseAdminToken(token)
	return claim, err

}

func addUserOrgSimIDToContext(ctx context.Context, req *pb.SimulateDTO) error {
	var contextMap map[string]interface{}

	if req.GetContext() == nil {
		req.Context = []byte("{}")
	}
	if err := json.Unmarshal(req.GetContext(), &contextMap); err != nil {
		return err
	}
	contextMap["_user_id"] = state.CurrentUser(ctx).String()
	contextMap["_organization_id"] = state.CurrentUserOrganization(ctx).String()
	contextMap["_simulate_id"] = req.GetId()
	m_context, err := json.Marshal(contextMap)
	if err != nil {
		return err
	}
	req.Context = m_context
	return nil
}

func addUserOrgSimIDToContext2(ctx context.Context, req *pb.SimulateByID, simulate_id string) error {
	var contextMap map[string]interface{}

	if req.GetContext() == nil {
		req.Context = []byte("{}")
	}
	if err := json.Unmarshal(req.GetContext(), &contextMap); err != nil {
		return err
	}
	contextMap["_user_id"] = state.CurrentUser(ctx).String()
	contextMap["_organization_id"] = state.CurrentUserOrganization(ctx).String()
	contextMap["_simulate_id"] = simulate_id
	m_context, err := json.Marshal(contextMap)
	if err != nil {
		return err
	}
	req.Context = m_context
	return nil
}

func removeUnderlineValues(raw json.RawMessage) json.RawMessage {
	first := make(map[string]interface{})
	json.Unmarshal(raw, &first)
	second := make(map[string]interface{})
	for key, value := range first {
		if key == "_organization_id" || key == "_simulate_id" || key == "_user_id" || key == "_code" || key == "_sms_node_message" || key == "_mail_node_message" {
			continue
		} else {
			second[key] = value
		}
	}
	updatedJSON, _ := json.Marshal(second)
	return json.RawMessage(updatedJSON)
}
