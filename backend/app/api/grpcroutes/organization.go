package grpcroutes

import (
	"context"

	pb "github.com/parsguru/pars-vue/grpc"
	"github.com/parsguru/pars-vue/pkg/entities"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type OrganizationServer struct {
	pb.OrganizationServiceServer
	Tracer trace.Tracer
	DB     *gorm.DB
}

func (s *OrganizationServer) Get(ctx context.Context, req *pb.OrganizationEmptyDTO) (*pb.Organization, error) {
	var organizationEntity entities.Organization
	var userEntity entities.User

	claim, err := getClaim(ctx)
	if err != nil {
		return &pb.Organization{}, err
	}
	err = s.DB.Where("id = ?", claim.ID).First(&userEntity).Error
	if err != nil {
		return &pb.Organization{}, err
	}
	err = s.DB.Where("id = ?", userEntity.OrganizationID).Preload(clause.Associations).First(&organizationEntity).Error
	if err != nil {
		return &pb.Organization{}, err
	}

	// var OrganizationUsers []*pb.UserDTO
	// if organizationEntity.Users != nil {
	// 	for _, v := range organizationEntity.Users {
	// 		OrganizationUsers = append(OrganizationUsers, &pb.UserDTO{
	// 			AllowOrganizationChange: v.AllowOrganizationChange,
	// 			Email:                   v.Email,
	// 			Id:                      v.ID.String(),
	// 			Name:                    v.Name,
	// 			OrganizationId:          v.ID.String(),
	// 			OrganizationName:        v.Name,
	// 		})
	// 	}
	// }

	simulateCount := 0
	if organizationEntity.Simulate == nil {
		simulateCount = 0
	} else {
		simulateCount = len(organizationEntity.Simulate)
	}
	organization := &pb.Organization{
		Id:        organizationEntity.ID.String(),
		Name:      organizationEntity.Name,
		CreatedAt: organizationEntity.CreatedAt.String(),
		//Users:         OrganizationUsers,
		SimulateCount: int64(simulateCount),
	}
	return organization, err
}

func (s *OrganizationServer) Create(ctx context.Context, req *pb.CreateOrganization) (*pb.Organization, error) {
	var organizationEntity entities.Organization
	organizationEntity.Name = req.Name
	err := s.DB.Create(&organizationEntity).Error
	if err != nil {
		return &pb.Organization{}, err
	}

	claim, err := getClaim(ctx)
	if err != nil {
		return &pb.Organization{}, err
	}

	err = s.DB.Model(&entities.User{}).Where("id = ?", claim.ID).Update("organization_id", organizationEntity.ID).Error
	if err != nil {
		return &pb.Organization{}, err
	}

	var userEntity = &entities.User{}
	err = s.DB.Where("id = ?", claim.ID).
		First(&userEntity).
		Error

	if err != nil {
		return &pb.Organization{}, err
	}
	var userDTO []*pb.UserDTO
	userDTO = append(userDTO, &pb.UserDTO{
		Email:            userEntity.Email,
		Id:               userEntity.ID.String(),
		Name:             userEntity.Name,
		OrganizationId:   userEntity.OrganizationID.String(),
		OrganizationName: userEntity.Name,
	})

	simulateCount := 0
	if organizationEntity.Simulate == nil {
		simulateCount = 0
	} else {
		simulateCount = len(organizationEntity.Simulate)
	}
	return &pb.Organization{
		CreatedAt:     organizationEntity.CreatedAt.String(),
		Id:            organizationEntity.ID.String(),
		Name:          organizationEntity.Name,
		SimulateCount: int64(simulateCount),
		//Users:         userDTO,
	}, nil
}

func (s *OrganizationServer) ChangeName(ctx context.Context, req *pb.OrganizationChangeName) (*pb.OrganizationEmptyDTO, error) {
	var organizationEntity entities.Organization
	err := s.DB.Where("id = ?", req.Id).First(&organizationEntity).Error
	if err != nil {
		return &pb.OrganizationEmptyDTO{}, err
	}

	err = s.DB.Model(&entities.Organization{}).
		Where("id = ?", req.Id).
		Update("name", req.Name).
		Error
	if err != nil {
		return &pb.OrganizationEmptyDTO{}, err
	}

	return &pb.OrganizationEmptyDTO{}, nil
}
func (s *OrganizationServer) AddUser(ctx context.Context, req *pb.AddUserToOrganization) (*pb.UserDTO, error) {
	var userEntity entities.User
	var res pb.UserDTO

	err := s.DB.Where("id = ?", req.UserId).First(&userEntity).Error
	if err != nil {
		return &pb.UserDTO{}, err
	}

	err = s.DB.Model(&userEntity).
		Where("id = ?", req.UserId).
		Update("allow_organization_change", false).
		Update("organization_id", req.OrganizationId).
		Error
	if err != nil {
		return &pb.UserDTO{}, err
	}

	//res.AllowOrganizationChange = userEntity.AllowOrganizationChange
	res.Email = userEntity.Email
	res.Id = userEntity.ID.String()
	res.Name = userEntity.Name
	res.OrganizationId = userEntity.OrganizationID.String()
	res.OrganizationName = userEntity.Name

	return &res, nil
}
func (s *OrganizationServer) RemoveUser(ctx context.Context, req *pb.RemoveUserFromOrganization) (*pb.OrganizationEmptyDTO, error) {
	err := s.DB.Model(&entities.User{}).
		Where("id = ?", req.UserId).
		Where("organization_id = ?", req.OrganizationId).
		Update("organization_id", nil).
		Error
	if err != nil {
		return &pb.OrganizationEmptyDTO{}, err
	}
	return &pb.OrganizationEmptyDTO{}, nil
}
