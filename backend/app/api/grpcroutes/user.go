package grpcroutes

import (
	"context"

	"github.com/google/uuid"
	pb "github.com/parsguru/pars-vue/grpc"
	"github.com/parsguru/pars-vue/pkg/entities"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type UserServer struct {
	pb.UserServiceServer
	Tracer trace.Tracer
	DB     *gorm.DB
}

func (s *UserServer) Get(ctx context.Context, req *pb.UserEmptyDTO) (*pb.UserDTO, error) {
	var userDTO pb.UserDTO
	var user entities.User
	claim, err := getClaim(ctx)
	if err != nil {
		return &userDTO, err
	}
	err = s.DB.Where("id = ?", claim.ID).
		Preload(clause.Associations).
		First(&user).Error
	if err != nil {
		return &userDTO, nil
	}
	orgID := ""
	orgName := ""
	if user.OrganizationID != uuid.Nil {
		orgID = user.OrganizationID.String()
		orgName = user.Name
	}
	userDTO = pb.UserDTO{
		Id:               user.ID.String(),
		Email:            user.Email,
		Name:             user.Name,
		OrganizationId:   orgID,
		OrganizationName: orgName,
	}

	return &userDTO, nil
}

func (s *UserServer) LeaveOrganization(ctx context.Context, req *pb.UserEmptyDTO) (*pb.UserEmptyDTO, error) {
	claim, err := getClaim(ctx)
	if err != nil {
		return nil, err
	}
	err = s.DB.Model(&entities.User{}).
		Where("id", claim.ID).
		Update("organization_id", nil).Error
	if err != nil {
		return nil, err
	}

	return nil, nil
}
func (s *UserServer) ChangeOrganizationAllow(ctx context.Context, req *pb.ChangeOrganizationAllowDTO) (*pb.UserEmptyDTO, error) {
	claim, err := getClaim(ctx)
	if err != nil {
		return nil, err
	}

	err = s.DB.Model(&entities.User{}).
		Where("id = ?", claim.ID).
		Update("allow_organization_change", req.AllowOrganizationChange).
		Error
	if err != nil {
		return nil, err
	}
	return nil, nil
}
