package grpcroutes

import (
	"context"

	pb "github.com/parsguru/pars-vue/grpc/pars_auth/v1"
	"github.com/parsguru/pars-vue/pkg/config"
	"github.com/parsguru/pars-vue/pkg/entities"
	"github.com/parsguru/pars-vue/pkg/utils"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type ParsAuthServer struct {
	pb.ParsAuthServiceServer
	Tracer trace.Tracer
	DB     *gorm.DB
}

func (s ParsAuthServer) HealthCheck(ctx context.Context, req *pb.AuthEmptyDTO) (*pb.AuthEmptyDTO, error) {
	return &pb.AuthEmptyDTO{}, nil
}

func createToken(user entities.User, db *gorm.DB, ctx context.Context) (string, error) {
	jwt := utils.JwtWrapper{
		SecretKey: config.ReadValue().JwtSecret,
		Issuer:    config.ReadValue().JwtIssuer,
	}

	token, err := jwt.GenerateAdminJWT(user.ID.String(), user.OrganizationID.String(), user.Email, true)

	tokenEntity := entities.Token{
		// ID:     user.Token.ID,
		Token:  token,
		UserID: user.ID,
	}
	if err := db.Where(entities.Token{UserID: user.ID}).
		Delete(&entities.Token{}).Error; err != nil {
		return "", err
	}

	if err := db.
		Where(entities.Token{UserID: user.ID}).
		Save(&tokenEntity).Error; err != nil {
		return "", err
	}

	return token, err
}
