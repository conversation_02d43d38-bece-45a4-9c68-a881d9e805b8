const normalizeDate = (dateString) => {
  return new Date(dateString).toISOString().substring(0, 16)
}

const parseDate = (dateString) => {
  return new Date(dateString).toJSON()
}

const parseDateToLocale = (dateString) => {
  return new Date(dateString).toLocaleString("tr-TR")
}
const getOnlyDate = (dateString) => {
  const formattedDate = new Date(dateString);
  formattedDate.setUTCHours(0, 0, 0, 0);
  const result = formattedDate.toISOString().substring(0, 10);

  return result
}

export { normalizeDate, parseDate, parseDateToLocale, getOnlyDate }
