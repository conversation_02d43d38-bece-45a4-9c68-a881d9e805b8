import axios from "axios";
import { apiUrl } from "@/constant/config";
const securedAxios = axios.create({
    baseURL: apiUrl,
    withCredentials: false,
    headers: {
        "Content-Type": "application/json"
    }
});


const UnsecureAxios = axios.create({
    baseURL: apiUrl,
    withCredentials: false,
    headers: {
        "Content-Type": "application/json"
    }
});

//  token add to header for secured request
securedAxios.interceptors.request.use(config => {
    if (localStorage.getItem("hash_id")) {
        config.headers["X-HASH"] = localStorage.getItem("hash_id");
    }
    const method = config.method.toUpperCase();
    if (localStorage.getItem("ipa_token")) {
        if (method !== "OPTIONS") {
            config.headers = {
                ...config.headers,
                Authorization: "Bearer " + localStorage.getItem("ipa_token"),
            };
        }
    } else {
        if (method !== "OPTIONS") {
            config.headers = {
                ...config.headers,
                Authorization: "Bearer " + localStorage.getItem("token"),
            };
        }
    }
    return config;
});

// if response is unauthorized, redirect to login page and clear local storage
securedAxios.interceptors.response.use(null, error => {
    if (error.response && error.response.config && error.response.status === 401) {
        localStorage.removeItem("token");
        localStorage.removeItem("ipa_token");
        localStorage.removeItem("hash_id");
        localStorage.removeItem("user");
        document.location.href = "/auth";
        return;
    } else {
        return Promise.reject(error);
    }
});

export { securedAxios, UnsecureAxios };