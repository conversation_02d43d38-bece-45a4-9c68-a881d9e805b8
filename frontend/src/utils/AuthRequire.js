import { useAuthStore } from "@/store/submodules/auth";

export default (to, from, next) => {
    const authStore = useAuthStore();
    if (to.meta.auth) {
        if (authStore.isLoggedIn) {
            next();
        } else {
            next("/auth/identifier");
        }
    } else {
        if (authStore.isLoggedIn) {
            if(to.name.includes("auth") ){
                next("/dashboard");
            } else {
                next();
            }
        } else {
            next();
        }
    }
}