function BuildUrlQuery(url, paramsArray) {
    if (typeof url !== 'string' || !url.trim()) {
        throw new Error('Invalid URL. URL must be a non-empty string.');
    }

    if (!Array.isArray(paramsArray)) {
        throw new Error('Invalid parameters. Params must be an array of objects.');
    }

    const queryParams = new URLSearchParams();

    paramsArray.forEach((paramsObj) => {
        if (typeof paramsObj !== 'object' || paramsObj === null) {
            throw new Error('Invalid parameter object. Each element in paramsArray must be an object.');
        }

        for (const key in paramsObj) {
            if (Object.prototype.hasOwnProperty.call(paramsObj, key)) {
                const value = paramsObj[key];
                if (value !== undefined && value !== null && value !== '' && value !== "null" && value !== "undefined") {
                    queryParams.append(key, value.toString());
                } else {
                    queryParams.delete(key);
                }
            }
        }
    });

    if (queryParams.toString() === '') {
        return url;
    }

    const separator = url.includes('?') ? '&' : '?';
    const updatedUrl = `${url}${separator}${queryParams.toString()}`;

    return updatedUrl;
}


export { BuildUrlQuery }