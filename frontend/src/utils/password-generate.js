const passwordGenerate = (length = 12, allowedSymbols = "!\"#\$%&'\(\)\*\+,-\./:;<=>\?@\[\\\\\\]\^_`\{|\}~") => {
    const crypto = window.crypto || window.msCrypto;
    const allowed = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789" + allowedSymbols;
    let password = "";
    for (let i = 0; i < length; i++) {
        password += allowed.charAt(crypto.getRandomValues(new Uint32Array(1))[0] % allowed.length);
    }
    return password;
}

export {passwordGenerate};
