export const formatMoney = (amount, currency = "TRY") => {
    if (amount && currency) {
        if (currency === "TRY") {
            return new Intl.NumberFormat("tr-TR", {
                style: "currency",
                currency: currency,
                minimumFractionDigits: 2,
            }).format(amount);
        } else {
            return new Intl.NumberFormat(currency, {
                style: "currency",
                currency: currency,
                minimumFractionDigits: 2,
            }).format(amount);
        }
    } else {
        return new Intl.NumberFormat(currency, {
            style: "currency",
            currency: currency,
            minimumFractionDigits: 2,
        }).format(0);
    }
};
// Bu fonksiyon, bir nesnenin kopyasını oluşturur ve null veya undefined olan tüm özellikleri atlar.
// Bu, bir nesnenin içindeki tüm null veya undefined değerleri kaldırmak için kullanılabilir.

export function deepOmit(obj, destroys = [undefined, null]) {
    const result = Array.isArray(obj) ? [] : {};
    Object.keys(obj).forEach((key) => {
        const value = obj[key];
        if (!destroys.includes(value)) {
            // Eğer değer nesne veya dizi ise, yinelemeli olarak aynı işlemi uygular
            if (typeof value === "object") {
                result[key] = deepOmit(value);
            } else {
                result[key] = value;
            }
        }
    });
    return result;
}

export function checkIBANWithRegex(iban) {
    iban = iban?.replace(/\s/g, "");
    return iban.match(/^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$/);
}

export function parseIBAN(iban) {
    if (!checkIBANWithRegex(iban)) {
        return null;
    }
    iban = iban.replace(/\s/g, "");
    return {
        iban: iban,
        countryCode: iban.slice(0, 2),
        checkDigits: iban.slice(2, 4),
        bankCode: iban.slice(5, 9),
        accountNumber: iban.slice(11, 26),
        suffix: iban.slice(26)
    }
}

export function turkishToUniversal(str) {
    const letters = { İ: "i", I: "ı", Ğ: "ğ", Ü: "ü", Ş: "ş", Ö: "ö", Ç: "ç" };
    return str.replace(/[İIĞÜŞÖÇ]/g, (letter) => letters[letter] || letter);
}

export function getCurrencySymbol(currency) {
    return new Intl.NumberFormat(currency, {
        style: "currency",
        currency: currency,
        minimumFractionDigits: 2,
    }).formatToParts(0)[0].value;
}