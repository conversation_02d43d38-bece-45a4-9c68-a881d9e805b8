const VALIDATION_ERROR = "Validation error";

export const useFormErrorHandler = (form, onValidationError = null) => {
    const toast = inject('toast')
    const { t, te } = useI18n()

    return (err) => {
        if(!err || !err.response || !err.response.data) return;
        if (err.response.data.errors && err.response.data.error === VALIDATION_ERROR) {
            form.errors = err.response.data.errors;
            toast.error(t('form.validation_err'))
            if(onValidationError) onValidationError(form.errors);
            return;
        }
        if (te(`monocodes.${err.response.data.error}`)) {
            toast.error(t(`monocodes.${err.response.data.error}`))
            return;
        }
        toast.error(err.response.data.message || err.response.data.error || t('form.an_error_occurred'));
    }
}