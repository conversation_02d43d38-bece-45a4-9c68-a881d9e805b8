const isEmpty = (val) => val === null || val === undefined || val === '';


export const tableFiltersToObject = (filtersArr, defaults = {}) => {
    const result = {}
    filtersArr.forEach((filter) => {
        Object.keys(filter).forEach((key) => {
          if (key !== null && !isEmpty(filter[key])) {
            result[key] = filter[key];
          }
        });
    });
    if (defaults) {
        Object.keys(defaults).forEach((key) => {
          if (!result[key]) {
            result[key] = defaults[key];
          }
        });
    }
    return result;
}

export const tableFiltersToQuery = (filtersArr, defaults = {}) => {
    const url = new URLSearchParams();
    filtersArr.forEach((filter) => {
        Object.keys(filter).forEach((key) => {
          if (key !== null && !isEmpty(filter[key])) {
            url.append(key, filter[key]);
          }
        });
    });
    if (defaults) {
        Object.keys(defaults).forEach((key) => {
          if (!url.has(key)) {
            url.append(key, defaults[key]);
          }
        });
    }
    return url.toString();
}

export const tableFiltersToUrl = (filtersArr, defaults = {}, oldFiltersArr = []) => {
  const result = {}
  filtersArr.forEach((filter) => {
      Object.keys(filter).forEach((key) => {
        if (key !== null && !isEmpty(filter[key])) {
          result[key] = filter[key];
        }
      });
  });
  if (defaults) {
      Object.keys(defaults).forEach((key) => {
          if (!result[key]) {
            result[key] = defaults[key];
          }
      });
    }
  if (oldFiltersArr) {
    oldFiltersArr.forEach((filter) => {
      Object.keys(filter).forEach((key) => {
        if (key !== null && !isEmpty(filter[key])) {
          if (!result[key]) {
            result[key] = filter[key];
          }
        }
      });
    });
  }
  return result;
}

export const useListTable = (getter, fetcher, defaultFilters = {}) => {
    const route = useRoute();
    const router = useRouter();

    const items = computed(() => getter())

    const mergeDynamicFilters = (statics) => {
        return [
            ...statics,
            ('value' in defaultFilters ? defaultFilters.value : defaultFilters),
        ]
    }

    const filters = ref(Object.keys(route.query).map(q => ['page', 'per_page'].includes(q) ? null : { [q]: route.query[q] }).filter(Boolean));

    const loading = ref(true);

    const errorRes = reactive({
      msg: '',
      status: '',
    })

    const queries = reactive({
      page: route.query.page ? route.query.page : 1,
      per_page: route.query.per_page ? route.query.per_page : 10,
    });

    const onPageChange = (page) => {
        loading.value = true;
        queries.page = page;
        router.push({
          name: route.name,
          query: tableFiltersToUrl(filters.value, queries)
        });
        fetcher(queries, mergeDynamicFilters(filters.value))
        .then(() => {
            if (errorRes.msg !== '') {
              errorRes.msg = '';
            }
          }).catch((err) => {
            errorRes.msg = "Something went wrong";
          }).finally(() => {
            loading.value = false;
          })
    }

    const onPerPageChange = (perPage) => {
        loading.value = true;
        queries.per_page = perPage;
        router.push({
          name: route.name,
          query: tableFiltersToUrl(filters.value, queries)
        });
        fetcher(queries, mergeDynamicFilters(filters.value))
        .then(() => {
            if (errorRes.msg !== '') {
              errorRes.msg = '';
            }
          }).catch((err) => {
            errorRes.msg = "Something went wrong";
          }).finally(() => {
            loading.value = false;
          })
    }

    const onMount = () => {
        fetcher(queries, mergeDynamicFilters(filters.value))
        .then(() => {
            if (errorRes.msg !== '') {
              errorRes.msg = '';
            }
          }).catch((err) => {
            errorRes.msg = "Something went wrong";
          }).finally(() => {
            loading.value = false;
          })
    }

    const onFilterChange = (newFilters) => {
        loading.value = true;
        const oldFilters = filters.value;
        filters.value = newFilters
        router.push({
          name: route.name,
          query: tableFiltersToUrl(filters.value, queries, oldFilters)
        });
        fetcher(queries, mergeDynamicFilters(filters.value))
        .then(() => {
            if (errorRes.msg !== '') {
              errorRes.msg = '';
            }
          }).catch((err) => {
            errorRes.msg = "Something went wrong";
          }).finally(() => {
            loading.value = false;
          })
    }
    
    const refresh = (goToFirstPage = true) => {
        loading.value = true;
        if (goToFirstPage) {
          queries.page = 1;
        }
        fetcher(queries, mergeDynamicFilters(filters.value))
        .then(() => {
            if (errorRes.msg !== '') {
              errorRes.msg = '';
            }
          }).catch((err) => {
            errorRes.msg = "Something went wrong";
          }).finally(() => {
            loading.value = false;
          })
    }


    return {
        props: computed(() => ({
            data: items.value.rows,
            page: +queries.page,
            per_page: +queries.per_page,
            total: items.value.total,
            total_pages: items.value.total_pages,
            loading: loading.value,
            error: errorRes.msg,
        })),
        handlers: {
          "page-change": onPageChange,
          "per-page-change": onPerPageChange,
          "filter-change": onFilterChange,
        },
        onMount,
        refresh
    }
}