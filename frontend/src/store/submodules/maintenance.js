import { securedAxios } from '@/utils/axios';
import { defineStore } from 'pinia';

const path = '/maintenance'
export const useMaintenanceStore = defineStore('maintenance', {
    state: () => ({
        maintenance: false,
    }),
    actions: {
        async CheckMaintenance() {
            try {
                const { data } = await securedAxios.get(path)
                this.$patch({ maintenance: data.enabled });
            } catch (error) {
                throw error
            }
        },
        async SetMaintenance(form) {
            try {
                const { data } = await securedAxios.post(path, form)
                this.$patch({ maintenance: form.enabled });
            } catch (error) {
                throw error
            }
        },
    },
    getters: {
        getMaintenance: (state) => state.maintenance
    }
})