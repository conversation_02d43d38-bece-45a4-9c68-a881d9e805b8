import { defineStore } from 'pinia';
import { securedAxios, UnsecureAxios } from "@/utils/axios";
import { apiUrlV2 } from "@/constant/config";
import router from "@/router";

export const useAuthStore = defineStore('auth', {
    state: () => ({
        currentUser: localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user')) : {
            id: null,
            first_name: null,
            last_name: null,
            name: null,
            email: null,
            role: null,
            tfa: false,
            pin_login: false,
            token: null,
            organization: null,
            department: null,
            expire: null,
            ipu: null
        },
        ipa: localStorage.getItem('ipa') ? JSON.parse(localStorage.getItem('ipa')) : ({
            name: null,
            token: null,
        })
    }),
    actions: {
        async Identify(data) {
            try {
                const response = await UnsecureAxios.post("/auth/identify", data, {
                    baseURL: apiUrlV2,
                    withCredentials: false,
                    headers: {
                        "Content-Type": "application/json"
                    }
                })
                //router.push({ name: 'auth-password', query: { identiy: window.btoa(data.email) } })
                return response;
            } catch (error) {
                throw error;
            }
        },
        async Authentication(data) {
            try {
                const res = await UnsecureAxios.post("/auth/authenticate", data, {
                    baseURL: apiUrlV2,
                    withCredentials: false,
                    headers: {
                        "Content-Type": "application/json"
                    }
                })
                if (res.data.pin || res.data.tfa) {
                    router.push({ name: 'challenge', query: { identiy: window.btoa(data.email), available_challenge_pin: res.data.pin ? true : false, available_challenge_tfa: res.data.tfa ? true : false } })
                }

                if (res.data.token) {
                    const response = await UnsecureAxios.post('/auth/verify',
                        { token: res.data.token },
                        {
                            baseURL: apiUrlV2,
                            withCredentials: false,
                            headers: {
                                "Content-Type": "application/json"
                            }
                        }
                    )
                    localStorage.setItem('user', JSON.stringify({
                        id: response.data.user.id,
                        name: response.data.user.name,
                        first_name: response.data.user.first_name,
                        last_name: response.data.user.last_name,
                        email: response.data.user.email,
                        role: response.data.user.role,
                        tfa: response.data.user.tfa ?? false,
                        pin_login: response.data.user.pin_login ?? false,
                        organization: response.data.user.organization,
                        department: response.data.user.department,
                        token: res.data.token,
                        expire: response.data.expire,
                        ipu: response.data.user.ipu
                    }));
                    localStorage.setItem('token', res.data.token);
                    localStorage.setItem('locale', response.data.user.locale);
                    this.$patch({ currentUser: JSON.parse(localStorage.getItem('user')) });
                    router.push('/');

                }

            } catch (error) {
                throw error;
            }
        },
        async AuthPin(data) {
            try {
                const res = await UnsecureAxios.post("/auth/pin", data, {
                    baseURL: apiUrlV2,
                    withCredentials: false,
                    headers: {
                        "Content-Type": "application/json"
                    }
                })

                if (res.data.token) {
                    const response = await UnsecureAxios.post('/auth/verify',
                        { token: res.data.token },
                        {
                            baseURL: apiUrlV2,
                            withCredentials: false,
                            headers: {
                                "Content-Type": "application/json"
                            }
                        }
                    )
                    localStorage.setItem('user', JSON.stringify({
                        id: response.data.user.id,
                        name: response.data.user.name,
                        first_name: response.data.user.first_name,
                        last_name: response.data.user.last_name,
                        email: response.data.user.email,
                        role: response.data.user.role,
                        tfa: response.data.user.tfa ?? false,
                        pin_login: response.data.user.pin_login ?? false,
                        organization: response.data.user.organization,
                        department: response.data.user.department,
                        token: res.data.token,
                        expire: response.data.expire,
                        ipu: response.data.user.ipu
                    }));
                    localStorage.setItem('token', res.data.token);
                    localStorage.setItem('locale', response.data.user.locale);
                    this.$patch({ currentUser: JSON.parse(localStorage.getItem('user')) });

                    router.push('/');

                }

            } catch (error) {
                throw error;
            }
        },
        async AuthTFA(data) {
            try {
                const res = await UnsecureAxios.post("/auth/tfa", data, {
                    baseURL: apiUrlV2,
                    withCredentials: false,
                    headers: {
                        "Content-Type": "application/json"
                    }
                })

                if (res.data.token) {
                    const response = await UnsecureAxios.post('/auth/verify',
                        { token: res.data.token },
                        {
                            baseURL: apiUrlV2,
                            withCredentials: false,
                            headers: {
                                "Content-Type": "application/json"
                            }
                        }
                    )
                    localStorage.setItem('user', JSON.stringify({
                        id: response.data.user.id,
                        name: response.data.user.name,
                        first_name: response.data.user.first_name,
                        last_name: response.data.user.last_name,
                        email: response.data.user.email,
                        role: response.data.user.role,
                        tfa: response.data.user.tfa ?? false,
                        pin_login: response.data.user.pin_login ?? false,
                        organization: response.data.user.organization,
                        department: response.data.user.department,
                        token: res.data.token,
                        expire: response.data.expire,
                        ipu: response.data.user.ipu
                    }));
                    localStorage.setItem('token', res.data.token);
                    localStorage.setItem('locale', response.data.user.locale);
                    this.$patch({ currentUser: JSON.parse(localStorage.getItem('user')) });

                    router.push('/');

                }

            } catch (error) {
                throw error;
            }
        },
        async ForgotPassword(data) {
            try {
                const res = await UnsecureAxios.post("/auth/forgot-password", data, {
                    baseURL: apiUrlV2,
                    withCredentials: false,
                    headers: {
                        "Content-Type": "application/json"
                    }
                })
                router.push({ name: 'reset-password' })
                return res;
            } catch (error) {
                throw error;
            }
        },
        async ResetPassword(data) {
            try {
                const res = await UnsecureAxios.post("/auth/reset-password", data, {
                    baseURL: apiUrlV2,
                    withCredentials: false,
                    headers: {
                        "Content-Type": "application/json"
                    }
                })
                router.push({ name: 'auth-identifier' })
                return res;
            } catch (error) {
                throw error;
            }
        },
        async GetPin(data) {
            try {
                const res = await securedAxios.post("/auth/get-pin",
                    {
                        email: data.email
                    }
                    , {
                        baseURL: apiUrlV2,
                        withCredentials: false,
                        headers: {
                            "Content-Type": "application/json"
                        }
                    })
                return res;
            } catch (error) {
                throw error;
            }
        },
        SignOut(sessionDrop) {
            this.$patch({
                currentUser: {
                    id: null,
                    name: null,
                    first_name: null,
                    last_name: null,
                    email: null,
                    role: null,
                    tfa: false,
                    pin_login: false,
                    token: null,
                    organization: null,
                    department: null,
                    expire: null
                }
            });

            localStorage.removeItem('user');
            localStorage.removeItem('token');
            if (sessionDrop) {
                router.push({ name: 'auth-identifier', query: { sessionDrop: true } });
            } else {
                router.push({ name: 'auth-identifier' });
            }
        },
        async GetAuthConfig(tenant_id) {
            try {
                const { data } = await UnsecureAxios.get(`/auth/config${tenant_id ? `?tenant_id=${tenant_id}` : ''}`, {
                    baseURL: apiUrlV2,
                    withCredentials: false,
                    headers: {
                        "Content-Type": "application/json"
                    }
                })
                return data;
            } catch (error) {
                throw error;
            }
        },
        SetIPA(data) {
            localStorage.setItem('ipa_token', data.token);
            localStorage.setItem('ipa', JSON.stringify(data));
            this.$patch({ ipa: data });
        },
        ClearIPA() {
            localStorage.removeItem('ipa');
            localStorage.removeItem('ipa_token');
            this.$patch({ ipa: { name: null, token: null } });
        },
        UpdateOrganizationLogo(data) {
            let user = localStorage.getItem('user')
            user = JSON.parse(user);

            let organization = user.organization;
            organization.logo = data;

            user.organization = organization;

            localStorage.setItem('user', JSON.stringify(user));

            this.$patch({ currentUser: JSON.parse(localStorage.getItem('user')) });

        }
    },
    getters: {
        getIPAData: state => state.ipa,
        isIPAActive: state => state.ipa.name !== null,
        getCurrentUser: state => state.currentUser,
        getCurrentUserRole: state => state.currentUser?.role,
        ipuAvailable: state => state.currentUser?.ipu,
        isLoggedIn: state => state.currentUser?.id !== null && state.currentUser?.token !== null
    }
})