import { securedAxios } from '@/utils/axios';
import { BuildUrlQuery } from '@/utils/query-builder';
import { defineStore } from 'pinia';

const path = '/log'
export const useLogStore = defineStore('log', {
  state: () => ({
    items: {
      per_page: 10,
      page: 1,
      total: 0,
      total_pages: 0,
      rows: [],
    },
  }),
  actions: {
    async GetSimulateLogs(simulate_id, page, per_page, filters) {
      try {
        if (!filters) {
          filters = [];
        }

        let page_index = filters.findIndex(x => x.page)
        if (page_index > -1) {
          filters[page_index].page = page
        } else {
          filters.push({ page: page })
        }

        let per_page_index = filters.findIndex(x => x.per_page)
        if (per_page_index > -1) {
          filters[per_page_index].per_page = per_page
        } else {
          filters.push({ per_page: per_page })
        }

        let url = BuildUrlQuery(`${path}/simulate/${simulate_id}`, filters)

        const { data } = await securedAxios.get(url)
        this.$patch({ items: data })

        return data
      } catch (error) {
        throw error
      }
    },
    async GetSimulateLogSingle(log_id) {
      try {
        const { data } = await securedAxios.get(`${path}/simulate/single/${log_id}`)
        console.log("data : ", data)
        return data
      } catch (error) {
        throw error
      }
    },
  },
  getters: {
    getItems: (state) => state.items
  }
})