import { securedAxios } from '@/utils/axios';
import { BuildUrlQuery } from '@/utils/query-builder';
import { defineStore } from 'pinia';

const path = '/simulate'
export const useSimulateStore = defineStore('simulate', {
  state: () => ({
    items: {
      per_page: 10,
      page: 1,
      total: 0,
      total_pages: 0,
      rows: [],
    },
  }),
  actions: {
    async List(page, per_page, filters) {
      try {
        if (!filters) {
          filters = [];
        }

        let page_index = filters.findIndex(x => x.page)
        if (page_index > -1) {
          filters[page_index].page = page
        } else {
          filters.push({ page: page })
        }

        let per_page_index = filters.findIndex(x => x.per_page)
        if (per_page_index > -1) {
          filters[per_page_index].per_page = per_page
        } else {
          filters.push({ per_page: per_page })
        }

        let url = BuildUrlQuery(path, filters)

        const { data } = await securedAxios.get(url)
        this.$patch({ items: data })

        return data
      } catch (error) {
        throw error
      }
    },
    async Simulate(payload) {
      try {
        const { data } = await securedAxios.post(`${path}`, payload)
        return data
      } catch (error) {
        throw error
      }
    },
    async SimulateUpdateOrCreate(payload) {
      try {
        const { data } = await securedAxios.put(`${path}`, payload)
        return data
      } catch (error) {
        throw error
      }
    },
    async GetSimulate(simulate_id) {
      try {
        const { data } = await securedAxios.get(`${path}/${simulate_id}`)
        return data
      } catch (error) {
        throw error
      }
    },
    async GetSimulateDetail(simulate_id) {
      try {
        const { data } = await securedAxios.get(`${path}/detail/${simulate_id}`)
        return data
      } catch (error) {
        throw error
      }
    },
    async UpdateSimulateName(simulate_id, name) {
      try {
        const { data } = await securedAxios.put(`${path}/name/${simulate_id}`, { 
          name: name 
        });
        return data
      } catch (error) {
        throw error
      }
    },
    async DeleteSimulate(simulate_id) {
      try {
        const { data } = await securedAxios.delete(`${path}/${simulate_id}`)
        return data
      } catch (error) {
        throw error
      }
    },
    async GetDefaultRequest(simulate_id, type) {
      try {
        const { data } = await securedAxios.get(`${path}/default-request?simulate_id=${simulate_id}&type=${type}`)
        return data
      } catch (error) {
        throw error
      }
    },
    async AddDefaultRequest(payload) {
      try {
        const { data } = await securedAxios.post(`${path}/default-request`, payload)
        return data
      } catch (error) {
        throw error
      }
    },
    async DeleteDefaultRequest(id) {
      try {
        const { data } = await securedAxios.delete(`${path}/default-request/${id}`)
        return data
      } catch (error) {
        throw error
      }
    },
    async RetryToSimulate(payload) {
      try {
        const { data } = await securedAxios.post(`/retry`, payload)
        return data
      } catch (error) {
        throw error
      }
    },
  },
  getters: {
    getItems: (state) => state.items
  }
})