import { defaultLocale } from '@/constant/config';
import { i18n } from '@/plugins/i18n';
import { securedAxios } from '@/utils/axios';
import { defineStore } from 'pinia';

export const useUIStore = defineStore('ui', {
    state: () => ({
        settings: {
            locale: localStorage.locale ? localStorage.locale : defaultLocale,
        },
        theme: {},
    }),
    actions: {
        InitLocale() {
            this.$patch({ settings: { locale: localStorage.locale } });
            i18n.locale = localStorage.locale;
        },
        async ChangeLocale(locale) {
            try {
                const response = await securedAxios.patch("/profile/locale", { locale: locale });
                localStorage.setItem("locale", locale);
                this.$patch({ settings: { locale: locale } });
                i18n.locale = locale;
            } catch (error) {
                console.log(error);
            }
        },
        InitTheme() {
            const cachedTheme = localStorage.theme ? localStorage.theme : false;
            const userPrefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
            if (cachedTheme) {
                this.$patch({ theme: cachedTheme });
            } else if (userPrefersDark) {
                this.$patch({ theme: "dark" });
            } else {
                this.$patch({ theme: "light" });
            }
        },
        ToggleTheme() {
            switch (this.theme) {
                case "light":
                    this.$patch({ theme: "dark" });
                    break;
                default:
                    this.$patch({ theme: "light" });
                    break;
            }

            localStorage.setItem("theme", this.theme);
        },
    },
    getters: {
        getItems: (state) => state.items,
        getsettings: state => state.settings,
        getlocale: state => state.settings.locale,
        getTheme: state => state.theme,
    }
})