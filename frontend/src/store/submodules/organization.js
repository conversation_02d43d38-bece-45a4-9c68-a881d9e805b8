import { tableFiltersToObject } from '@/composables/table';
import { securedAxios } from '@/utils/axios';
import { BuildUrlQuery } from '@/utils/query-builder';
import { defineStore } from 'pinia';
import { useAuthStore } from './auth';

const path = '/organizations'
const authStore = useAuthStore()
export const useOrganizationStore = defineStore('organization', {
    state: () => ({
        items: {
            per_page: 10,
            page: 1,
            total: 0,
            total_pages: 0,
            rows: [],
        },
        submerchants: {
            per_page: 10,
            page: 1,
            total: 0,
            total_pages: 0,
            rows: [],
        },
        incomes: {
            per_page: 10,
            page: 1,
            total: 0,
            total_pages: 0,
            rows: [],
        },
        subOrganizations: [],
        organizationAdmins: [],
    }),
    actions: {
        async List(page, per_page, filters) {
            try {
                if (!filters) {
                    filters = [];
                }

                let page_index = filters.findIndex(x => x.page)
                if (page_index > -1) {
                    filters[page_index].page = page
                } else {
                    filters.push({ page: page })
                }

                let per_page_index = filters.findIndex(x => x.per_page)
                if (per_page_index > -1) {
                    filters[per_page_index].per_page = per_page
                } else {
                    filters.push({ per_page: per_page })
                }

                let url = BuildUrlQuery(path, filters)

                const { data } = await securedAxios.get(url)
                this.$patch({ items: data })

                return data
            } catch (error) {
                throw error
            }
        },
        async Create(payload) {
            try {
                const { data } = await securedAxios.post(`${path}`, payload)
                return data
            } catch (error) {
                throw error
            }
        },
        async Read(id) {
            try {
                const { data } = await securedAxios.get(`${path}/${id}`)
                return data
            } catch (error) {
                throw error
            }
        },
        async Detail(id) {
            try {
                const { data } = await securedAxios.get(`${path}/${id}/detail`)
                return data
            } catch (error) {
                throw error
            }
        },
        async Update(payload) {
            try {
                const { data } = await securedAxios.patch(`${path}/${payload.id}`, payload)
                authStore.UpdateOrganizationLogo(payload.logo)

                return data
            } catch (error) {
                throw error
            }
        },
        async Delete(id) {
            try {
                const { data } = await securedAxios.delete(`${path}/${id}`)
                this.$patch({ items: { rows: this.items.rows.filter((row) => row.id !== id) } });
                console.log("delete data: ", data)
                return data
            } catch (error) {
                throw error
            }
        },
        async GetSubmerchants(organization_id, page, per_page, filters) {
            try {
                if (!filters) {
                    filters = [];
                }

                let page_index = filters.findIndex(x => x.page)
                if (page_index > -1) {
                    filters[page_index].page = page
                } else {
                    filters.push({ page: page })
                }

                let per_page_index = filters.findIndex(x => x.per_page)
                if (per_page_index > -1) {
                    filters[per_page_index].per_page = per_page
                } else {
                    filters.push({ per_page: per_page })
                }

                let url = BuildUrlQuery(`submerchants/organization/${organization_id}`, filters)

                const { data } = await securedAxios.get(url)
                this.$patch({ submerchants: data })

                return data
            } catch (error) {
                throw error
            }
        },
        async UpdateScopes(payload) {
            try {
                const { data } = await securedAxios.patch(`${path}/${payload.id}/scopes`, payload);
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetLimits(id) {
            try {
                const { data } = await securedAxios.get(`${path}/${id}/limits`);
                return data;
            } catch (error) {
                throw error;
            }
        },
        async UpdateFlows(payload) {
            try {
                const { data } = await securedAxios.patch(`${path}/${payload.organization_id}/flows/${payload.id}`, payload);
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetSubOrganizations(id) {
            try {
                const { data } = await securedAxios.get(`${path}/${id}/sub-organizations`)

                this.$patch({ subOrganizations: data });
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetOrganizationAdmins(organization_id) {
            try {
                const { data } = await securedAxios.get(`/admins/organization/${organization_id}`);
                this.$patch({ organizationAdmins: data });
            } catch (error) {
                throw error;
            }
        },
        async GetUsers() {
            try {
                const { data } = await securedAxios.get(`${path}/users`);
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetFlows(organization_id) {
            try {
                const { data } = await securedAxios.get(`${path}/${organization_id}/flows`);
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetScopes() {
            try {
                const { data } = await securedAxios.get(`${path}/scopes`);
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetIncomes(pagi, filters) {
            try {
                const orgId = filters.find(x => x.id).id
                const filtersWithoutOrgId = filters.filter(x => x.id !== orgId)
                const params = tableFiltersToObject(filtersWithoutOrgId, pagi);
               const { data } = await securedAxios.get(`/organizations/${orgId}/incomes`, { params });
                data.rows = data.rows || [];
                this.$patch({ incomes: data });
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetOrganizationCurrency(organization_id){
            try {
                const { data } = await securedAxios.get(`${path}/${organization_id}/currency`);
                return data
            } catch (error) {
                throw error
            }
        },
        async GetCrawlerDomains(is_active, page, perPage, id) {
            try {
                const { data } = await securedAxios.get(`${path}/${id}/watcher?is_active=${is_active}&page=${page}&per_page=${perPage}`);
                return data
            } catch (error) {
                throw error
            }
        },
        async GetCrawlerDomain(id, cid) {
            try {
                const { data } = await securedAxios.get(`${path}/${id}/watcher/${cid}`);
                return data
            } catch (error) {
                throw error
            }
        },
        async AddDomain(url, cron_time, is_active) {
            try {
                const payload = {
                    url: url,
                    cron_time: cron_time+"h",
                    is_active: is_active
                };
        
                const { data } = await securedAxios.post(`${path}/watcher`, payload);
        
                return data;
            } catch (error) {
                throw error;
            }
        },
        async EditDomain(id, cron_time, is_active) {
            try {
                const payload = {
                    id: id,
                    cron_time: String(cron_time).replace(/h/g, '')+"h",
                    is_active: is_active
                };

                const { data } = await securedAxios.put(`${path}/watcher`, payload);
        
                return data;
            } catch (error) {
                throw error;
            }
        },
        async DeleteDomain(id) {
            try {
                const { data } = await securedAxios.delete(`${path}/${id}/watcher`);
                return data
            } catch (error) {
                throw error
            }
        },
        async GetWpCode(id, phone) {
            try {
                const payload = {
                    phone: phone,
                    id: id
                };
        
                const { data } = await securedAxios.post(`${path}/wp/login`, payload);
        
                return data;
            } catch (error) {
                throw error;
            }
        },
        async CheckDevice(id, reg_id, phone) {
            try {
                const payload = {
                    reg_id: reg_id,
                    id: id,
                    phone: phone
                };
        
                const { data } = await securedAxios.post(`${path}/wp/check`, payload);
        
                return data;
            } catch (error) {
                throw error;
            }
        },
        async SendMessage(reg_id, message, to) {
            try {
                const payload = {
                    reg_id: reg_id,
                    message: message,
                    to: to
                };
        
                const { data } = await securedAxios.post(`${path}/wp/send-message`, payload);
        
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetChats(reg_id) {
            try {
                const payload = {
                    reg_id: reg_id,
                };
        
                const { data } = await securedAxios.post(`${path}/wp/chats`, payload);
        
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetChatMessages(chat_id, reg_id) {
            try {
                const payload = {
                    reg_id: reg_id,
                    chat_id: chat_id,
                };
        
                const { data } = await securedAxios.post(`${path}/wp/messages`, payload);
        
                return data;
            } catch (error) {
                throw error;
            }
        },
        async WpLogout(reg_id, org_id) {
            try {
                const payload = {
                    reg_id: reg_id,
                    id: org_id,
                };
        
                const { data } = await securedAxios.post(`${path}/wp/logout`, payload);
        
                return data;
            } catch (error) {
                throw error;
            }
        },
        async TerminatePlan(organization_id) {
            try {
                const { data } = await securedAxios.get(`${path}/${organization_id}/terminate-plan`);
                return data;
            } catch (error) {
                throw error;
            }
        },
        async Terminate(organization_id) {
            try {
                const { data } = await securedAxios.delete(`${path}/${organization_id}/terminate`);
                return data;
            } catch (error) {
                throw error;
            }
        },
        async GetRegId(id) {
            try {        
                const { data } = await securedAxios.get(`${path}/wp/regid/${id}`);
                return data;
            } catch (error) {
                throw error;
            }
        },
        async IsActive(id) {
            try {        
                const { data } = await securedAxios.get(`${path}/wp/active/${id}`);
                return data;
            } catch (error) {
                throw error;
            }
        }
    },
    getters: {
        getItems: (state) => state.items,
        getIncomes: (state) => state.incomes,
        getSubmerchants: (state) => state.submerchants,
        getSubOrganizations: (state) => state.subOrganizations
    }
})