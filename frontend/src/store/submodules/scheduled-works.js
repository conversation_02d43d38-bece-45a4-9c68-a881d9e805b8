import { securedAxios } from '@/utils/axios';
import { BuildUrlQuery } from '@/utils/query-builder';
import { defineStore } from 'pinia';

const path = '/scheduled'
export const useScheduledWorksStore = defineStore('scheduled-works', {
  state: () => ({
    items: {
      per_page: 10,
      page: 1,
      total: 0,
      total_pages: 0,
      rows: [],
    },
  }),
  actions: {
    async List(page, per_page, filters) {
      try {
        if (!filters) {
          filters = [];
        }

        let page_index = filters.findIndex(x => x.page)
        if (page_index > -1) {
          filters[page_index].page = page
        } else {
          filters.push({ page: page })
        }

        let per_page_index = filters.findIndex(x => x.per_page)
        if (per_page_index > -1) {
          filters[per_page_index].per_page = per_page
        } else {
          filters.push({ per_page: per_page })
        }

        let url = BuildUrlQuery(path, filters)

        const { data } = await securedAxios.get(url)
        this.$patch({ items: data })

        return data
      } catch (error) {
        throw error
      }
    },
    async Read(id) {
      try {
        const { data } = await securedAxios.get(`${path}/${id}`)
        return data
      } catch (error) {
        throw error
      }
    },
    async Create(payload) {
      try {
          const { data } = await securedAxios.post(`${path}`, payload)
          return data
      } catch (error) {
          throw error
      }
    },
    async Delete(id) {
      try {
        const { data } = await securedAxios.delete(`${path}/${id}`)
        return data
      } catch (error) {
        throw error
      }
    },
  },
  getters: {
    getItems: (state) => state.items
  }
})