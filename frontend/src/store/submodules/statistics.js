import { defineStore } from 'pinia';
import { securedAxios } from '@/utils/axios';

const path = '/statistics';
export const useStatisticsStore = defineStore('statistics', {
    state: () => ({
        simulateStatistics: {}
    }),
    actions: {
        async fetchSimulateStatistics(id) {
            try {
                const res = await securedAxios.get(`${path}/simulate/${id}`);
                return res.data
            } catch (error) {
                throw error
            }
        }
    }
});
