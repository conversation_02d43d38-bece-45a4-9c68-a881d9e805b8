<template>
  <div>
    <h1 class="text-xl sm:text-2xl font-semibold text-gray-800 dark:text-neutral-200">
      {{ $t('auth.password_page.title') }}
    </h1>
    <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
      {{ $t('auth.password_page.description') }}
    </p>
  </div>


  <form @submit.prevent="Authenticate()">
    <div class="space-y-5">
      <div>
        <label for="email" class="block mb-2 text-sm font-medium text-gray-800 dark:text-white">
          {{ $t('auth.email') }}
        </label>

        <input type="email" id="email" :value="form.email" class="t-input" disabled>
      </div>

      <div>
        <div class="flex justify-between items-center mb-2">
          <label for="password" class="block text-sm font-medium text-gray-800 dark:text-white">
            {{ $t('auth.password') }}
          </label>

          <router-link :to="{ name: 'forgot-password' }"
            class="inline-flex items-center gap-x-1.5 text-xs text-gray-600 hover:text-gray-700 decoration-2 hover:underline focus:outline-none focus:underline dark:text-neutral-500 dark:hover:text-neutral-600">
            {{ $t('auth.forgot_password') }}
          </router-link>
        </div>

        <div class="flex gap-2">
          <input id="password" type="password" required v-model="form.password" autocomplete="off" autofocus
            class="t-input" placeholder="***********">

          <input type="text" v-model="form.tfa_code" required class="t-input" v-if="tfa_enabled === true"
            placeholder="TFA Code">
        </div>
      </div>

      <div class="flex gap-2" v-if="tfa_forced && tfa_img">
        <img v-if="tfa_img" :src="tfa_img" class="rounded-lg h-2/4 w-2/4" />
        <div class="flex flex-col justify-between gap-2 w-full">
          <span class="text-sm">
            {{ $t('auth.tfa_add_forced_title') }}
          </span>
          <span class="text-xs leading-4 text-gray-500 dark:text-gray-400">
            {{ $t('auth.tfa_add_forced_description') }}
          </span>
          <input type="text" v-model="form.tfa_code" required class="t-input" placeholder="TFA Code">
        </div>
      </div>
      <button type="submit"
        class="py-2.5 transition px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-semibold rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600">
        {{ $t('general.next') }}
      </button>
    </div>
  </form>
</template>

<script setup>
import { useAuthStore } from '@/store/submodules/auth';

const store = useAuthStore()
const processing = ref(false)
const route = useRoute()
const router = useRouter()
const toast = inject('toast')
const { t } = useI18n()


if (!route.query.identiy) {
  router.push({ name: 'auth-identifier' })
}

const tfa_forced = route.query.tfa_forced ? true : false
const tfa_img = route.query.tfa_img ? "data:image/jpg;base64,".concat(route.query.tfa_img) : null
const tfa_enabled = route.query.tfa_enabled ? true : false

const form = reactive({
  email: route.query.identiy ? atob(route.query.identiy) : '',
  password: null,
  ldap: false,
  tfa_code: null,
})

// prevent copy-paste of password
onMounted(() => {
  document.addEventListener('paste', (e) => {
    if (e.target.id === 'password') {
      e.preventDefault()
      toast.error(t('auth.errors.paste_not_allowed'))
    }
  })
})
onBeforeUnmount(() => {
  document.removeEventListener('paste', (e) => {
    if (e.target.id === 'password') {
      e.preventDefault()
      toast.error(t('auth.errors.paste_not_allowed'))
    }
  })
})


function Authenticate() {
  try {
    processing.value = true
    if (!form.password) {
      toast.error(t('auth.please_enter_password'))
      return
    }


    store.Authentication(form).catch((err) => {
      toast.error(t('auth.errors.check_data'))
    }).finally(() => {
      processing.value = false
    })
  } catch (error) {

    toast.error(error)
  } finally {
    processing.value = false
  }
}
</script>
