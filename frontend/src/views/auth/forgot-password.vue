<template>
  <div>
    <h1 class="text-xl sm:text-2xl font-semibold text-gray-800 dark:text-neutral-200">
      {{ $t('auth.forgot_password') }}
    </h1>
    <p class="mt-1 text-sm text-gray-500 dark:text-neutral-500">
      {{ $t('auth.forgot_password_description') }}
    </p>
  </div>


  <form @submit.prevent="ForgotPassword()">
    <div class="space-y-5">
      <div>
        <label for="email" class="block mb-2 text-sm font-medium text-gray-800 dark:text-white">
          Email
        </label>

        <input type="email" id="email" v-model="form.email" required
          class="py-2.5 px-3 block w-full border-gray-200 rounded-lg text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600"
          placeholder="<EMAIL>">
      </div>

      <button type="submit"
        class="py-2.5 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-semibold rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600">
        {{ $t('general.next') }}
      </button>
    </div>
  </form>
</template>

<script setup>
import { useAuthStore } from '@/store/submodules/auth';

const store = useAuthStore();
const processing = ref(false)
const toast = inject('toast')
const { t } = useI18n()


const form = reactive({
  email: null,
})

function ForgotPassword() {
  if (!form.email) {
    toast.error(t('auth.please_enter_email'))
    return
  }
  processing.value = true
  store.ForgotPassword(form).then(() => {
    toast.success(t('auth.forgot_password_success'))
    processing.value = false
  }).catch((err) => {
    toast.error(err.response.data.message)
    processing.value = false
  })
}
</script>