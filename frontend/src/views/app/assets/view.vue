<template>
  <div v-if="!loading" class="card">
    <div class="flex justify-center text-3xl">
      {{ $t("assets.view.asset_header") }}
    </div>

    <div>
      <div class="grid grid-cols-6 gap-5">
        <div class="col-span-6">
          <h1 class="text-lg font-semibold border-b border-gray-300/50 pb-1">
            {{ $t("assets.asset_information") }}
          </h1>
        </div>
        <div class="grid col-span-3">
          <label class="block text-lg font-bold">{{
            $t("assets.asset_number")
          }}</label>
          <div class="flex">
            {{ form.asset_number }}
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block text-lg font-bold">{{ $t("assets.name") }}</label>
          <div class="flex">
            {{ form.name }}
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block text-lg font-bold">{{
            $t("assets.asset_owner")
          }}</label>
          <div class="flex">

            <p>
              {{
                form.asset_owner == "" || AssetDataOwner.filter(
                  (e) => e.id == form.asset_owner
                ).length == 0
                ? `${$t("general.empty")}`
                : AssetDataOwner.filter(
                  (e) => e.id == form.asset_owner
                )[0].name
              }}
            </p>

          </div>
        </div>
        <div class="grid col-span-3">
          <label class="blockfont-lg font-bold">{{
            $t("assets.quantity")
          }}</label>
          <div class="flex">
            {{ form.quantity }}
          </div>
        </div>
        <div class="col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.detail_note")
          }}</label>
          <div class="flex">
            {{ form.detail_note }}
          </div>
        </div>
        <div class="col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.protect_manage_document")
          }}</label>
          <div class="flex">
            {{ form.protect_manage_document }}
          </div>
        </div>
        <div class="grid col-span-6">
          <label class="block font-lg font-bold">{{
            $t("assets.description")
          }}</label>
          <div class="flex">
            <p>
              {{ form.description == "" ? `${$t("general.empty")}` : form.description }}
            </p>

          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.main_group")
          }}</label>
          <div class="flex">
            <p>
              {{
                form.main_group == "" || MainGroup.filter(
                  (e) => e.id == form.main_group
                ).length == 0
                ? `${$t("general.empty")}`
                : MainGroup.filter(
                  (e) => e.id == form.main_group
                )[0].name
              }}
            </p>

          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.group")
          }}</label>
          <div class="flex">
            <p>
              {{
                form.group == "" || Group.filter(
                  (e) => e.id == form.group
                ).length == 0
                ? `${$t("general.empty")}`
                : Group.filter(
                  (e) => e.id == form.group
                )[0].name
              }}

            </p>
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.storage_place")
          }}</label>
          <div class="flex">
            <p>

              {{
                form.storage_place == "" || StoragePlace.filter(
                  (e) => e.id == form.storage_place
                ).length == 0
                ? `${$t("general.empty")}`
                : StoragePlace.filter(
                  (e) => e.id == form.storage_place
                )[0].name
              }}
            </p>
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.asset_location")
          }}</label>
          <div class="flex">
            <p>
              {{
                form.asset_location == "" || AssetLocation.filter(
                  (e) => e.id == form.asset_location
                ).length == 0
                ? `${$t("general.empty")}`
                : AssetLocation.filter(
                  (e) => e.id == form.asset_location
                )[0].name
              }}
            </p>
          </div>
        </div>
        <div class="col-span-6 pt-3">
          <h1 class="text-lg font-semibold border-b border-gray-300/50 pb-1">
            {{ $t("assets.asset_evaluation") }}
          </h1>
        </div>

        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.privacy_value")
          }}</label>
          <div class="flex">
            <p :class="rivacyValueClass">{{
              form.privacy_value == "" || Value.filter(
                (e) => e.id == form.privacy_value
              ).length == 0
              ? `${$t("general.empty")}`
              : Value.find((item) => item.id == form.privacy_value)
                .name

            }}</p>
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.integrity_value")
          }}</label>
          <div class="flex">
            <p :class="integrityValueClass">{{
              form.integrity_value == "" || Value.filter(
                (e) => e.id == form.integrity_value
              ).length == 0
              ? `${$t("general.empty")}`
              : Value.find((item) => item.id == form.integrity_value)
                .name

            }}</p>
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.accessibility_value")
          }}</label>
          <div class="flex">
            <p :class="accessibilityValueClass">{{

              form.accessibility_value == "" || Value.filter(
                (e) => e.id == form.accessibility_value
              ).length == 0
              ? `${$t("general.empty")}`
              : Value.find((item) => item.id == form.accessibility_value)
                .name

            }}</p>
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.privacy_score")
          }}</label>
          <div class="flex">
            <p :class="privacyClass">{{

              form.privacy_score == "" || Score.filter(
                (e) => e.id == form.privacy_score
              ).length == 0
              ? `${$t("general.empty")}`
              : Score.find((item) => item.id == form.privacy_score)
                .name

            }}</p>
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.integrity_score")
          }}</label>
          <div class="flex">
            <p :class="integrityClass">{{

              form.integrity_score == "" || Score.filter(
                (e) => e.id == form.integrity_score
              ).length == 0
              ? `${$t("general.empty")}`
              : Score.find((item) => item.id == form.integrity_score)
                .name



            }}</p>
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.accessibility_score")
          }}</label>
          <div class="flex">
            <p :class="accessibilityClass">{{

              form.accessibility_score == "" || Score.filter(
                (e) => e.id == form.accessibility_score
              ).length == 0
              ? `${$t("general.empty")}`
              : Score.find((item) => item.id == form.accessibility_score)
                .name

            }}</p>
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.view.wealth_value")
          }}</label>
          <div class="flex">
            <p :class="wealthClass">{{
              form.wealth_value <= 0 ? `${$t("general.empty")}` : form.wealth_value }}</p>
          </div>
        </div>
        <div class="col-span-6 pt-3">
          <h1 class="text-lg font-semibold border-b border-gray-300/50 pb-1">
            {{ $t("assets.history") }}
          </h1>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.created_by")
          }}</label>
          <div class="flex">
            {{ form.created_by }}
          </div>
        </div>

        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.created_at")
          }}</label>
          <div class="flex">
            {{ form.created_at }}
          </div>
        </div>

        <div v-if="!form.updated_by">
          <div class="flex">
            {{ $t("assets.view.no_update") }}
          </div>
        </div>
        <div v-else class="grid col-span-6">
          <div class="grid grid-cols-6 gap-5">
            <div class="grid col-span-3">
              <label class="block font-lg font-bold">{{
                $t("assets.updated_by")
              }}</label>

              <div class="flex">
                {{ form.updated_by }}
              </div>
            </div>
            <div class="grid col-span-3">
              <label class="block font-lg font-bold">{{
                $t("assets.updated_at")
              }}</label>
              <div class="flex">
                {{ form.updated_at }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, inject, reactive, ref } from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import { securedAxios } from "@/utils/axios";
import { parseDateToLocale } from '@/utils/datetime';
const store = useStore();
const router = useRouter();
const route = useRoute();
const toast = inject("toast");
const loading = ref(false);
const form = ref({
  name: "",
  asset_number: "",
  description: "",
  status: false,
  main_group: "",
  asset_owner: "",
  group: "",
  quantity: 1,
  storage_place: "",
  asset_location: "",
  privacy_value: "",
  integrity_value: "", //bütünlük
  accessibility_value: "", //erişilebilirlik
  privacy_score: "",
  integrity_score: "",
  accessibility_score: "",
  detail_note: "",
  protect_manage_document: "",
  wealth_value: "",
  created_at: "",
  created_by: "",
  updated_at: "",
  updated_by: "",
});

onMounted(() => {
  loading.value = true;
  securedAxios
    .get(`/assets/${route.params.id}`)
    .then((res) => {
      form.value.uuid = res.data.uuid;
      form.value.asset_number = res.data.asset_number;
      form.value.name = res.data.name;
      form.value.description = res.data.description;
      form.value.status = res.data.status;
      form.value.quantity = res.data.quantity;
      form.value.privacy_value = res.data.privacy_value;
      form.value.integrity_value = res.data.integrity_value;
      form.value.accessibility_value = res.data.accessibility_value;
      form.value.privacy_score = res.data.privacy_score;
      form.value.integrity_score = res.data.integrity_score;
      form.value.accessibility_score = res.data.accessibility_score;
      form.value.detail_note = res.data.detail_note;
      form.value.protect_manage_document = res.data.protect_manage_document;
      form.value.wealth_value = res.data.wealth_value;
      form.value.created_at = parseDateToLocale(res.data.created_at);
      form.value.updated_at = parseDateToLocale(res.data.updated_at);
      form.value.updated_by = res.data.updated_by;
      form.value.created_by = res.data.created_by;
      form.value.asset_owner = res.data.asset_owner
      form.value.main_group = res.data.main_group
      form.value.group = res.data.group
      form.value.storage_place = res.data.storage_place
      form.value.asset_location = res.data.asset_location
      loading.value = false;
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      loading.value = false;
    });
});

store
  .dispatch("AssetMainGroupList", { page: 1, per_page: 9999 })
  .catch((err) => {
    toast.error(err.response.data.error);
  });
store.dispatch("AssetGroupList", { page: 1, per_page: 9999 }).catch((err) => {
  toast.error(err.response.data.error);
});
store
  .dispatch("AssetStoragePlaceList", { page: 1, per_page: 9999 })
  .catch((err) => {
    toast.error(err.response.data.error);
  });
store.dispatch("AssetValueList", { page: 1, per_page: 9999 }).catch((err) => {
  toast.error(err.response.data.error);
});
store
  .dispatch("AssetDataOwnerList", { page: 1, per_page: 9999 })
  .catch((err) => {
    toast.error(err.response.data.error);
  });
store.dispatch("AssetLocationList", { page: 1, per_page: 9999 }).catch((err) => {
  toast.error(err.response.data.error);
});

const AssetLocation = computed(() => {
  return store.getters.assetLocationList.rows.map((item) => ({
    id: item.id,
    name: item.name,
  }));
});

const AssetDataOwner = computed(() => {
  return store.getters.assetDataOwnerList.rows.map((item) => ({
    id: item.id,
    name: item.name,
  }));
});

const MainGroup = computed(() => {
  return store.getters.assetMainGroupList.rows;
});

const Group = computed(() => {
  return store.getters.assetGroupList.rows;
});

const StoragePlace = computed(() => {
  return store.getters.assetStoragePlaceList.rows;
});
const Value = computed(() => {
  return store.getters.assetValueList.rows;
});
const Score = [
  { id: "1", name: "1" },
  { id: "2", name: "2" },
  { id: "3", name: "3" },
  { id: "4", name: "4" },
  { id: "5", name: "5" },
];

const ClassColor = (score) => {
  //1=green 2 3 4 5 6 7 8=red
  if (score == 1) {
    return "bg-green-500 px-2 rounded-md text-white";
  } else if (score == 2) {
    return "bg-yellow-500 px-2 rounded-md text-white";
  } else if (score == 3) {
    return "bg-red-500 px-2 rounded-md text-white";
  } else if (score == 4) {
    return "bg-red-700 px-2 rounded-md text-white";
  } else {
    return "bg-gray-500 px-2 rounded-md text-white";
  }
};
const rivacyValueClass = computed(() => {
  const priority = Value.value.find((item) => {
    if (item.id == form.value.privacy_value) {
      return item.name;
    }
  });
  if (priority) {
    return ClassColor(priority.asset_priority);
  }
});
const integrityValueClass = computed(() => {
  const priority = Value.value.find((item) => {
    if (item.id == form.value.integrity_value) {
      return item.name;
    }
  });
  if (priority) {
    return ClassColor(priority.asset_priority);
  }
});
const accessibilityValueClass = computed(() => {
  const priority = Value.value.find((item) => {
    if (item.id == form.value.accessibility_value) {
      return item.name;
    }
  });
  if (priority) {
    return ClassColor(priority.asset_priority);
  }
});

const integrityClass = computed(() => {
  return ClassColor(form.value.integrity_score);
});
const accessibilityClass = computed(() => {
  return ClassColor(form.value.accessibility_score);
});
const privacyClass = computed(() => {
  return ClassColor(form.value.privacy_score);
});
const wealthClass = computed(() => {
  return ClassColor(form.value.wealth_value);
});
</script>
