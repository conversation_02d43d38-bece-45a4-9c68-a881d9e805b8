<template>
  <Card>
    <div v-if="!errorRes.msg">
      <div class="flex justify-between items-center mb-2">
        <h1>{{ $t("page_titles.assets") }}</h1>
        <div class="flex justify-end items-center">

          <label v-if="showFilter" class="ml-2 text-sm font-sans">{{ showAdvencedFilters ?
      $t('general.close_advanced_filter') :
      $t('general.open_advanced_filter') }}</label>
          <Switch v-if="showFilter" class="items-center text-xs" v-model="showAdvencedFilters">
          </Switch>
          <div v-if="showFilters" class="w-px h-4 bg-gray-500 cursor-pointer" />
          <Button class="mb-2" color="primaryText" v-if="showFilter" @click="clearFilter">{{ $t("general.clear_filters") }}</Button>
          <Button class="mb-2" @click="openFilter"><box-icon color="white" name="filter" /></Button>

          <router-link class="bg-indigo-500 rounded-md px-4 py-2 text-gray-50" :to="{ name: 'assets-create' }">
            <Icon name="plus" />
          </router-link>
        </div>
      </div>
      <div v-if="showFilter" class="grid grid-cols-6 gap-5">
        <div class="col-span-6">
          <div class="grid grid-cols-6 gap-2">

            <div class="col-span-2">
              <label class="block text-sm font-medium">
                {{ $t("assets.asset_number") }}
              </label>
              <input type="text" class="input-text" v-model="queries.asset_number" @input="
      router.replace({
        query: {
          ...$route.query,
          asset_number: $event.target.value
            ? $event.target.value
            : undefined,
        },
      })
      " />
            </div>
            <div class="col-span-2">
              <label class="block text-sm font-medium">
                {{ $t("assets.name") }}
              </label>
              <input type="text" class="input-text" v-model="queries.name" @input="
      router.replace({
        query: {
          ...$route.query,
          name: $event.target.value
            ? $event.target.value
            : undefined,
        },
      })
      " />
            </div>
            <div class="col-span-2">
              <label class="block text-sm font-medium">
                {{ $t("assets.quantity") }}
              </label>
              <input type="text" class="input-text" v-model="queries.quantity" @input="
      router.replace({
        query: {
          ...$route.query,
          quantity: $event.target.value
            ? $event.target.value
            : undefined,
        },
      })
      " />
            </div>
            <div class="col-span-2">
              <label class="block text-sm font-medium">
                {{ $t("assets.protect_manage_document") }}
              </label>
              <input type="text" class="input-text" v-model="queries.protect_manage_document" @input="
      router.replace({
        query: {
          ...$route.query,
          protect_manage_document: $event.target.value
            ? $event.target.value
            : undefined,
        },
      })
      " />
            </div>
            <Selectbox class="col-span-2" v-model="queries.asset_owner" :label="$t('assets.asset_owner')"
              :options="AssetDataOwner" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          asset_owner: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />
            <Selectbox class="col-span-2" v-model="queries.asset_owner" :label="$t('assets.asset_owner')"
              :options="AssetDataOwner" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          asset_owner: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />
            <Selectbox class="col-span-2" v-model="queries.storage_place" :label="$t('assets.storage_place')"
              :options="StoragePlace" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          storage_place: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />

            <Selectbox class="col-span-2" v-model="queries.main_group" :label="$t('assets.main_group')"
              :options="MainGroup" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          main_group: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />

            <Selectbox class="col-span-2" v-model="queries.asset_location" :label="$t('assets.asset_location')"
              :options="AssetLocation" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          asset_location: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />

          </div>
        </div>
      </div>
      <div class="grid grid-cols-6 gap-5 mt-3" v-if="showAdvencedFilters">


        <Selectbox class="col-span-2" v-model="queries.privacy_value" :label="$t('assets.privacy_value')"
          :options="Value" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          privacy_value: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />
        <Selectbox class="col-span-2" v-model="queries.integrity_value" :label="$t('assets.integrity_value')"
          :options="Value" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          integrity_value: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />
        <Selectbox class="col-span-2" v-model="queries.accessibility_value" :label="$t('assets.accessibility_value')"
          :options="Value" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          accessibility_value: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />
        <Selectbox class="col-span-2" v-model="queries.privacy_score" :label="$t('assets.privacy_score')"
          :options="Score" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          privacy_score: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />
        <Selectbox class="col-span-2" v-model="queries.accessibility_score" :label="$t('assets.accessibility_score')"
          :options="Score" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          accessibility_score: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />
        <Selectbox class="col-span-2" v-model="queries.integrity_score" :label="$t('assets.integrity_score')"
          :options="Score" @update:model-value="
      $router.replace({
        query: {
          ...$route.query,
          integrity_score: $event
            ? $event
            : undefined,
        },
        force: true,
      })" />

      </div>
      <Card>
        <Table :columns="columns" :data="assets.list" :page="Number(queries.page)" :per_page="Number(queries.per_page)"
          :total_pages="assets.total_pages" @page-change="pageChange" @per-page-change="perPageChange" actionable>
          <template v-slot:default="action">
            <td class="table-rows flex gap-2 justify-center h-full">
              <router-link
                class="flex items-center justify-center w-8 h-8 font-bold text-white bg-blue-500 rounded hover:bg-blue-700"
                :to="{ name: 'assets-edit', params: { id: action.item.uuid } }">
                <Icon name="pencil" />
              </router-link>
              <router-link
                class="flex items-center justify-center w-8 h-8 font-bold text-white bg-blue-500 rounded hover:bg-blue-700"
                :to="{ name: 'assets-view', params: { id: action.item.uuid } }">
                <Icon name="eye" />
              </router-link>
              <button @click="
      deleteModal.id = action.item.uuid
    deleteModal.show = true;
    " class="flex items-center justify-center w-8 h-8 font-bold text-white bg-red-500 rounded hover:bg-red-700">
                <Icon name="trash" classes="w-4 h-4 " />

              </button>
              <div v-if="deleteModal.show">
                <DeleteModel :deleteModal="deleteModal" @delete="deleteAsset(action.item.uuid)" />
              </div>
            </td>
          </template>
        </Table>
      </Card>
    </div>
    <div class="max-w-max mx-auto" v-else>
      <div class="sm:flex items-center">
        <p class="text-4xl font-semibol text-indigo-600 sm:text-5xl">
          {{ errorRes.status }}
        </p>
        <div class="sm:ml-6">
          <div class="sm:border-l border-gray-300 dark:border-gray-700 sm:pl-6">
            <h1 class="text-xl text-gray-700 dark:text-gray-400 font-semibold">
              {{ errorRes.msg }}
            </h1>
          </div>
        </div>
      </div>
    </div>
  </Card>
</template>

<script setup>
import { computed, onBeforeMount, reactive } from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import Table from "@/components/table.vue";
import Switch from "@/components/switch.vue";
import Selectbox from "@/components/selectbox.vue";

const toast = inject("toast");
const { t } = useI18n();
const store = useStore();
const route = useRoute();
const router = useRouter();
const showFilter = ref(false);
const showAdvencedFilters = ref(false);

const errorRes = reactive({
  msg: "",
  status: "",
});

const columns = [
  {
    row: "asset_number",
    label: t("assets.asset_number"),
  },
];

const deleteModal = ref({
  show: false,
  id: null,
  processing: false,
});

const queries = reactive({
  page: route.query.page ? route.query.page : 1,
  per_page: route.query.per_page ? route.query.per_page : 10,
  asset_number: route.query.asset_number ? route.query.asset_number : "",
  name: route.query.name ? route.query.name : "",
  asset_owner: route.query.asset_owner ? route.query.asset_owner : "",
  status: route.query.status ? route.query.status : "",
  main_group: route.query.main_group ? route.query.main_group : "",
  group: route.query.group ? route.query.group : "",
  quantity: route.query.quantity ? route.query.quantity : "",
  storage_place: route.query.storage_place ? route.query.storage_place : "",
  asset_location: route.query.asset_location ? route.query.asset_location : "",
  detail_note: route.query.detail_note ? route.query.detail_note : "",
  protect_manage_document: route.query.protect_manage_document ? route.query.protect_manage_document : "",
  privacy_value: route.query.privacy_value ? route.query.privacy_value : "",
  integrity_value: route.query.integrity_value ? route.query.integrity_value : "",
  accessibility_value: route.query.accessibility_value ? route.query.accessibility_value : "",
  privacy_score: route.query.privacy_score ? route.query.privacy_score : "",
  integrity_score: route.query.integrity_score ? route.query.integrity_score : "",
  accessibility_score: route.query.accessibility_score ? route.query.accessibility_score : "",

});

function deleteAsset(uuid) {
  store.dispatch("deleteAsset", uuid).then((e) => {
    toast.success(t("general.sent_to_flow"));
  });
}

store
  .dispatch("getAssets", { ...queries })
  .catch((err) => {
    errorRes.msg = err.response.data.error;
    errorRes.status = err.response.status;
  });

function pageChange(pageNum) {
  router.push({
    name: "assets",
    query: {
      page: pageNum,
      per_page: queries.per_page,
    },
  });
  queries.page = pageNum;
  store
    .dispatch("getAssets", { ...queries })
    .catch((err) => {
      errorRes.msg = err.response.data.error;
      errorRes.status = err.response.status;
    });
}

function perPageChange(per_page) {
  router.push({
    name: "assets",
    query: {
      page: queries.page,
      per_page: per_page,
    },
  });
  queries.per_page = per_page;
  store
    .dispatch("getAssets", { ...queries })
    .catch((err) => {
      errorRes.msg = err.response.data.error;
      errorRes.status = err.response.status;
    });
}

watch(queries, () => {
  store
    .dispatch("getAssets", { ...queries })
    .catch((err) => {
      errorRes.msg = err.response.data.error;
      errorRes.status = err.response.status;
    });
});

const openFilter = () => {
  showFilter.value = !showFilter.value;
}

const clearFilter = () => {

  router.replace({
    ...route.query,
    asset_number: undefined,
    name: undefined,
    asset_owner: undefined,
    status: undefined,
    main_group: undefined,
    group: undefined,
    quantity: undefined,
    storage_place: undefined,
    asset_location: undefined,
    detail_note: undefined,
    protect_manage_document: undefined,
    privacy_value: undefined,
    integrity_value: undefined,
    accessibility_value: undefined,
    privacy_score: undefined,
    integrity_score: undefined,
    accessibility_score: undefined,
  })

  queries.asset_number = undefined
  queries.name = undefined
  queries.asset_owner = undefined
  queries.status = undefined
  queries.main_group = undefined
  queries.group = undefined
  queries.quantity = undefined
  queries.storage_place = undefined
  queries.asset_location = undefined
  queries.detail_note = undefined
  queries.protect_manage_document = undefined
  queries.privacy_value = undefined
  queries.integrity_value = undefined
  queries.accessibility_value = undefined
  queries.privacy_score = undefined
  queries.integrity_score = undefined
  queries.accessibility_score = undefined

  showFilter.value = false
  showAdvencedFilters.value = false


}
store
  .dispatch("AssetMainGroupList", { page: 1, per_page: 9999 })
  .catch((err) => {
    toast.error(err.response.data.error);
  });
store.dispatch("AssetGroupList", { page: 1, per_page: 9999 }).catch((err) => {
  toast.error(err.response.data.error);
});
store
  .dispatch("AssetStoragePlaceList", { page: 1, per_page: 9999 })
  .catch((err) => {
    toast.error(err.response.data.error);
  });
store.dispatch("AssetValueList", { page: 1, per_page: 9999 }).catch((err) => {
  toast.error(err.response.data.error);
});
store
  .dispatch("AssetDataOwnerList", { page: 1, per_page: 9999 })
  .catch((err) => {
    toast.error(err.response.data.error);
  });

store.dispatch("AssetLocationList", { page: 1, per_page: 9999 }).catch((err) => {
  toast.error(err.response.data.error);
});
const AssetLocation = computed(() => {
  return store.getters.assetLocationList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});

const AssetDataOwner = computed(() => {
  return store.getters.assetDataOwnerList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});

const MainGroup = computed(() => {
  return store.getters.assetMainGroupList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});

const Group = computed(() => {
  return store.getters.assetGroupList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});

const StoragePlace = computed(() => {
  return store.getters.assetStoragePlaceList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});
const Value = computed(() => {
  return store.getters.assetValueList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});
const Score = [
  //1 2
  { id: "1", name: "1" },
  { id: "2", name: "2" },
  { id: "3", name: "3" },
  { id: "4", name: "4" },
  { id: "5", name: "5" },
];
const assets = computed(() => {
  const list = store.getters.assets.rows;
  const total_pages = store.getters.assets.total_pages;
  const per_page = store.getters.assets.per_page;
  const page = store.getters.assets.page;
  return {
    list,
    total_pages,
    per_page,
    page,
  };
});
</script>
