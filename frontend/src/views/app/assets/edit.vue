<template>
  <div v-if="!loading" class="card mx-auto max-w-4xl">
    <form @submit.prevent="updateAsset">
      <div class="grid grid-cols-6 gap-2" :class="showContent ? 'blur-sm' : ''">
        <div class="col-span-6">
          <h1 class="text-lg font-semibold border-b border-gray-300/50 pb-1">
            {{ $t("assets.asset_information") }}
          </h1>
        </div>
        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.asset_number")
          }}</label>
          <input type="text" class="input-text" v-model="form.asset_number" />
        </div>

        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.main_group")
            }}</label>
            <editConstant @openContent="openContent" @click="contentData = MainGroup, editStore = 'EditAssetMainGroup'" />

          </div>
          <div class="">
            <Selectbox v-model="form.main_group" :options="MainGroup" searchable />
          </div>
        </div>

        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.group")
            }}</label>
            <editConstant @openContent="openContent" @click="contentData = Group, editStore = 'EditAssetGroup'" />
          </div>
          <div class="">
            <Selectbox v-model="form.group" :options="Group" searchable />
          </div>
        </div>
        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.name")
          }}</label>
          <input type="text" class="input-text" v-model="form.name" />
        </div>
        <div class="col-span-3">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.asset_owner")
            }}</label>
            <editConstant @openContent="openContent"
              @click="contentData = AssetDataOwner, editStore = 'EditAssetDataOwner'" />
          </div>
          <Selectbox v-model="form.asset_owner" :options="AssetDataOwner" searchable />
        </div>

        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.quantity")
          }}</label>
          <input type="number" class="input-text" v-model="form.quantity" />
        </div>
        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.detail_note")
          }}</label>
          <input type="text" class="input-text" v-model="form.detail_note" />
        </div>
        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.protect_manage_document")
          }}</label>
          <input type="text" class="input-text" v-model="form.protect_manage_document" />
        </div>
        <div class="col-span-6">
          <label class="block text-sm font-medium">{{
            $t("assets.description")
          }}</label>
          <textarea class="input-text" v-model="form.description"></textarea>
        </div>
        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.storage_place")
            }}</label>
            <editConstant @openContent="openContent"
              @click="contentData = StoragePlace, editStore = 'EditAssetStoragePlace'" />
          </div>
          <div class="">
            <Selectbox v-model="form.storage_place" :options="StoragePlace" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.asset_location")
            }}</label>
            <editConstant @openContent="openContent"
              @click="contentData = AssetLocation, editStore = 'EditAssetLocation'" />
          </div>
          <div class="">
            <Selectbox v-model="form.asset_location" :options="AssetLocation" searchable />
          </div>
        </div>


        <div class="col-span-6 pt-3">
          <h1 class="text-lg font-semibold border-b border-gray-300/50 pb-1">
            {{ $t("assets.asset_evaluation") }}
          </h1>
        </div>

        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{ $t("assets.privacy_value") }}
            </label>
            <editConstant @openContent="openContent" @click="contentData = Value, editStore = 'EditAssetValue'" />
          </div>
          <div class="">
            <Selectbox v-model="form.privacy_value" :options="Value" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{ $t("assets.integrity_value") }}
            </label>
            <editConstant @openContent="openContent" @click="contentData = Value, editStore = 'EditAssetValue'" />
          </div>
          <div class="">
            <Selectbox v-model="form.integrity_value" :options="Value" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{ $t("assets.accessibility_value") }}
            </label>
            <editConstant @openContent="openContent" @click="contentData = Value, editStore = 'EditAssetValue'" />
          </div>
          <div class="">
            <Selectbox v-model="form.accessibility_value" :options="Value" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <label class="block text-sm font-medium">{{ $t("assets.privacy_score") }}
          </label>
          <div class="">
            <Selectbox v-model="form.privacy_score" :options="Score" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <label class="block text-sm font-medium">{{ $t("assets.integrity_score") }}
          </label>
          <div class="">
            <Selectbox v-model="form.integrity_score" :options="Score" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <label class="block text-sm font-medium">{{ $t("assets.accessibility_score") }}
          </label>
          <div class="">
            <Selectbox v-model="form.accessibility_score" :options="Score" searchable />
          </div>
        </div>

        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.status")
          }}</label>
          <input type="checkbox" class="input-checkbox" v-model="form.status" />
        </div>
        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.status")
          }}</label>
          <div class="input-checkbox">
            {{ form.wealth_value }}
          </div>
        </div>
        <div class="col-span-6 pt-3">
          <h1 class="text-lg font-semibold border-b border-gray-300/50 pb-1">
            {{ $t("assets.history") }}
          </h1>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.created_at")
          }}</label>
          <div class="flex">
            {{ form.created_at }}
          </div>
        </div>
        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.updated_at")
          }}</label>
          <div class="flex">
            {{ form.updated_at }}
          </div>
        </div>

        <div class="grid col-span-3">
          <label class="block font-lg font-bold">{{
            $t("assets.updated_by")
          }}</label>
          <div class="flex" v-if="!form.updated_by">
            <div class="flex">
              {{ $t("assets.view.no_update") }}
            </div>
          </div>
          <div v-else class="flex">
            {{ form.updated_by }}
          </div>
        </div>
      </div>
      <div class="flex justify-center mt-5">
        <button type="submit" class="bg-indigo-600 px-4 py-2 rounded-md">
          {{ $t("general.create") }}
        </button>
      </div>
    </form>
    <Contetnt v-if="showContent" :showContent="showContent" @closeContent="closeTheContent" :contant="contentData"
      :editStore="editStore" />
  </div>
</template>

<script setup>
import { computed, inject, ref, watch } from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import { securedAxios } from "@/utils/axios";
import Selectbox from "@/components/selectbox.vue";
import Contetnt from "@/components/constanst/Constant.vue";
import editConstant from "@/components/constanst/editConstant.vue";
const store = useStore();
const router = useRouter();
const route = useRoute();
const toast = inject("toast");
const loading = ref(false);
const { t } = useI18n();
const contentData = ref([]);
const editStore = ref("");
const showContent = ref(false);
const form = reactive({
  name: "",
  asset_number: "",
  description: "",
  status: false,
  main_group: "",
  created_at: "",
  updated_at: "",
  updated_by: "",
  group: "",
  quantity: 1,
  storage_place: "",
  asset_location: "",
  asset_owner: "",
  privacy_value: "",
  integrity_value: "", //bütünlük
  accessibility_value: "", //erişilebilirlik
  privacy_score: "",
  integrity_score: "",
  accessibility_score: "",
  wealth_value: "",
  detail_note: "",
  protect_manage_document: "",
});

onMounted(() => {
  loading.value = true;
  securedAxios
    .get(`/assets/${route.params.id}`)
    .then((res) => {
      form.uuid = res.data.uuid;
      form.name = res.data.name;
      form.asset_number = res.data.asset_number;
      form.description = res.data.description;
      form.status = res.data.status;
      form.main_group = res.data.main_group;
      form.group = res.data.group;
      form.asset_owner = res.data.asset_owner;
      form.quantity = res.data.quantity;
      form.storage_place = res.data.storage_place;
      form.asset_location = res.data.asset_location;
      form.privacy_value = res.data.privacy_value;
      form.integrity_value = res.data.integrity_value;
      form.accessibility_value = res.data.accessibility_value;
      form.privacy_score = res.data.privacy_score;
      form.integrity_score = res.data.integrity_score;
      form.accessibility_score = res.data.accessibility_score;
      form.detail_note = res.data.detail_note;
      form.protect_manage_document = res.data.protect_manage_document;
      form.wealth_value = res.data.wealth_value;
      form.created_at = res.data.created_at;
      form.updated_at = res.data.updated_at;
      form.updated_by = res.data.updated_by;
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      loading.value = false;
    });
});

const FetchData = () => {
  store
    .dispatch("AssetMainGroupList", { page: 1, per_page: 9999 })
    .catch((err) => {
      toast.error(err.response.data.error);
    });
  store.dispatch("AssetGroupList", { page: 1, per_page: 9999 }).catch((err) => {
    toast.error(err.response.data.error);
  });
  store
    .dispatch("AssetStoragePlaceList", { page: 1, per_page: 9999 })
    .catch((err) => {
      toast.error(err.response.data.error);
    });
  store.dispatch("AssetValueList", { page: 1, per_page: 9999 }).catch((err) => {
    toast.error(err.response.data.error);
  });
  store
    .dispatch("AssetDataOwnerList", { page: 1, per_page: 9999 })
    .catch((err) => {
      toast.error(err.response.data.error);
    });

  store.dispatch("AssetLocationList", { page: 1, per_page: 9999 }).catch((err) => {
    toast.error(err.response.data.error);
  });

}
onMounted(() => {
  FetchData();
});


const AssetLocation = computed(() => {
  return store.getters.assetLocationList.rows.map((item) => ({
    id: item.id,
    name: item.name,
  }));
});
const AssetDataOwner = computed(() => {
  return store.getters.assetDataOwnerList.rows.map((item) => ({
    id: item.id,
    name: item.name,
  }));
});

const MainGroup = computed(() => {
  return store.getters.assetMainGroupList.rows.map((item) => ({
    id: item.id,
    name: item.name,
  }));
});

const Group = computed(() => {
  return store.getters.assetGroupList.rows.map((item) => ({
    id: item.id,
    name: item.name,
  }));
});

const StoragePlace = computed(() => {
  return store.getters.assetStoragePlaceList.rows.map((item) => ({
    id: item.id,
    name: item.name,
  }));
});
const Value = computed(() => {
  return store.getters.assetValueList.rows.map((item) => ({
    id: item.id,
    name: item.name,
  }));
});
function updateAsset() {
  store
    .dispatch("updateAsset", form)
    .then((res) => {
      router.push({ name: "assets" });
      toast.success(t("assets.update_success"));
    })
    .catch((err) => {
      console.log(err);
      toast.error(err.response.data.error);
    });
}
const openContent = () => {
  showContent.value = true;
};
const closeTheContent = (e) => {
  showContent.value = false
};

const Score = [
  //1 2
  { id: "1", name: "1" },
  { id: "2", name: "2" },
  { id: "3", name: "3" },
  { id: "4", name: "4" },
  { id: "5", name: "5" },
];
</script>
