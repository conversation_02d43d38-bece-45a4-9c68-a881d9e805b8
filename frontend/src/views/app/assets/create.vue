<template>
  <div class="card mx-auto max-w-4xl">
    <form @submit.prevent="createAsset">
      <div class="grid grid-cols-6 gap-2 " :class="showContent ? 'blur-sm' : ''">
        <div class="col-span-6">
          <h1 class="text-lg font-semibold border-b border-gray-300/50 pb-1">
            {{ $t("assets.asset_information") }}
          </h1>
        </div>

        <div class="col-span-3">
          <label class="block text-sm font-medium">
            {{ $t("assets.asset_number") }}
          </label>
          <input type="text" class="input-text" v-model="form.asset_number" />
        </div>

        <div class="grid col-span-3">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.main_group")
            }}</label>
            <editConstant @openContent="openContent" @click="contentData = MainGroup, editStore = 'EditAssetMainGroup'" />

          </div>
          <div class="">

            <Selectbox v-model="form.main_group" :options="MainGroup" searchable />
          </div>
        </div>

        <div class="grid col-span-3">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.group")
            }}</label>
            <editConstant @openContent="openContent" @click="contentData = Group, editStore = 'EditAssetGroup'" />
          </div>
          <div class="">
            <Selectbox v-model="form.group" :options="Group" searchable />
          </div>
        </div>
        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.name")
          }}</label>
          <input type="text" class="input-text" v-model="form.name" />
        </div>
        <div class="col-span-3">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.asset_owner")
            }}</label>
            <editConstant @openContent="openContent"
              @click="contentData = AssetDataOwner, editStore = 'EditAssetDataOwner'" />
          </div>
          <Selectbox v-model="form.asset_owner" :options="AssetDataOwner" searchable />
        </div>

        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.quantity")
          }}</label>
          <input type="number" class="input-text" v-model="form.quantity" />
        </div>
        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.detail_note")
          }}</label>
          <input type="text" class="input-text" v-model="form.detail_note" />
        </div>
        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.protect_manage_document")
          }}</label>
          <input type="text" class="input-text" v-model="form.protect_manage_document" />
        </div>

        <div class="col-span-6">
          <label class="block text-sm font-medium">{{
            $t("assets.description")
          }}</label>
          <textarea class="input-text" v-model="form.description"></textarea>
        </div>
        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.storage_place")
            }}</label>
            <editConstant @openContent="openContent"
              @click="contentData = StoragePlace, editStore = 'EditAssetStoragePlace'" />
          </div>
          <div class="">
            <Selectbox v-model="form.storage_place" :options="StoragePlace" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{
              $t("assets.asset_location")
            }}</label>
            <editConstant @openContent="openContent"
              @click="contentData = AssetLocation, editStore = 'EditAssetLocation'" />
          </div>
          <div class="">
            <Selectbox v-model="form.asset_location" :options="AssetLocation" searchable />
          </div>
        </div>


        <div class="col-span-6 pt-3">
          <h1 class="text-lg font-semibold border-b border-gray-300/50 pb-1">
            {{ $t("assets.asset_evaluation") }}
          </h1>
        </div>


        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{ $t("assets.privacy_value") }}
            </label>
            <editConstant @openContent="openContent" @click="contentData = Value, editStore = 'EditAssetValue'" />
          </div>
          <div class="">
            <Selectbox v-model="form.privacy_value" :options="Value" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{ $t("assets.integrity_value") }}
            </label>
            <editConstant @openContent="openContent" @click="contentData = Value, editStore = 'EditAssetValue'" />
          </div>

          <div class="">
            <Selectbox v-model="form.integrity_value" :options="Value" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <div class="flex">
            <label class="block text-sm font-medium">{{ $t("assets.accessibility_value") }}
            </label>
            <editConstant @openContent="openContent" @click="contentData = Value, editStore = 'EditAssetValue'" />
          </div>
          <div class="">
            <Selectbox v-model="form.accessibility_value" :options="Value" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <label class="block text-sm font-medium">{{ $t("assets.privacy_score") }}
          </label>
          <div class="">
            <Selectbox v-model="form.privacy_score" :options="Score" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <label class="block text-sm font-medium">{{ $t("assets.integrity_score") }}
          </label>
          <div class="">
            <Selectbox v-model="form.integrity_score" :options="Score" searchable />
          </div>
        </div>
        <div class="grid col-span-3 gap-2">
          <label class="block text-sm font-medium">{{ $t("assets.accessibility_score") }}
          </label>
          <div class="">
            <Selectbox v-model="form.accessibility_score" :options="Score" searchable />
          </div>
        </div>

        <div class="col-span-3">
          <label class="block text-sm font-medium">{{
            $t("assets.status")
          }}</label>
          <input type="checkbox" class="input-checkbox" v-model="form.status" />
        </div>
      </div>
      <div class="flex justify-center mt-5">
        <button type="submit" class="bg-indigo-600 px-4 py-2 rounded-md">
          {{ $t("general.create") }}
        </button>
      </div>
    </form>
    <Contetnt v-if="showContent" @closeContent="closeTheContent" :showContent="showContent" :contant="contentData"
      :editStore="editStore" />
  </div>
</template>

<script setup>
import { computed, inject, onMounted, reactive, ref, watch } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import Selectbox from "@/components/selectbox.vue";

import Contetnt from "@/components/constanst/Constant.vue";
import editConstant from "@/components/constanst/editConstant.vue";
const { t } = useI18n();
const store = useStore();
const router = useRouter();
const toast = inject("toast");
const contentData = ref([]);
const editStore = ref("");
const showContent = ref(false);
const form = reactive({
  asset_number: "",
  name: "",
  description: "",
  status: false,
  main_group: "",
  group: "",
  asset_owner: "",
  quantity: 1,
  storage_place: "",
  asset_location: "",
  privacy_value: "",
  integrity_value: "", //bütünlük
  accessibility_value: "", //erişilebilirlik
  privacy_score: "",
  integrity_score: "",
  accessibility_score: "",
  detail_note: "",
  protect_manage_document: "",
});

const FetchData = () => {
  store
    .dispatch("AssetMainGroupList", { page: 1, per_page: 9999 })
    .catch((err) => {
      toast.error(err.response.data.error);
    });
  store.dispatch("AssetGroupList", { page: 1, per_page: 9999 }).catch((err) => {
    toast.error(err.response.data.error);
  });
  store
    .dispatch("AssetStoragePlaceList", { page: 1, per_page: 9999 })
    .catch((err) => {
      toast.error(err.response.data.error);
    });
  store.dispatch("AssetValueList", { page: 1, per_page: 9999 }).catch((err) => {
    toast.error(err.response.data.error);
  });
  store
    .dispatch("AssetDataOwnerList", { page: 1, per_page: 9999 })
    .catch((err) => {
      toast.error(err.response.data.error);
    });

  store.dispatch("AssetLocationList", { page: 1, per_page: 9999 }).catch((err) => {
    toast.error(err.response.data.error);
  });

}
onMounted(() => {
  FetchData();
});

const AssetLocation = computed(() => {
  return store.getters.assetLocationList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});

const AssetDataOwner = computed(() => {
  return store.getters.assetDataOwnerList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});

const MainGroup = computed(() => {
  return store.getters.assetMainGroupList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});

const Group = computed(() => {
  return store.getters.assetGroupList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});

const StoragePlace = computed(() => {
  return store.getters.assetStoragePlaceList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});
const Value = computed(() => {
  return store.getters.assetValueList.rows.map((item) => ({
    id: item.id,
    name: item.name,
    asset_priority: item.asset_priority,
  }));
});

function createAsset() {
  store
    .dispatch("createAsset", form)
    .then((res) => {
      router.push({ name: "assets" });
      toast.success(t("assets.create_success"));
    })
    .catch((err) => {
      toast.error(err.response.data.error);
    });
}
const openContent = () => {
  showContent.value = true;
};
const closeTheContent = (e) => {
  showContent.value = false;
};


const Score = [
  //1 2
  { id: "1", name: "1" },
  { id: "2", name: "2" },
  { id: "3", name: "3" },
  { id: "4", name: "4" },
  { id: "5", name: "5" },
];
</script>
