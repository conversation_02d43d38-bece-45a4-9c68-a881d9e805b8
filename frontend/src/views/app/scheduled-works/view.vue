<template>
  <div
    class="xl:p-5 flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <div v-if="loading" class="flex items-center justify-center h-full">
      <MonoIcon class="size-20 animate-spin" name="spin" />
    </div>
    <div v-else-if="error" class="flex items-center h-full p-5">
      <div class="flex flex-col md:flex-row gap-2 items-center justify-center w-full h-full">
        <MonoIcon class="size-20 text-rose-600 dark:text-rose-500 md:pt-2" name="exclamation" />
        <div class="text-center md:text-left">
          <h2 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            {{ error.title || $t("general.server_error") }}
          </h2>
          <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
            {{ error.message || $t("general.server_error_description") }}
          </p>
        </div>
      </div>
    </div>
    <div v-else>
      <form @submit.prevent="editName()" class="space-y-4 p-2">
        <div class="w-full px-2 sm:px-0">
          <div class="space-y-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.name_of_work') }}
                </label>
                <input type="text" :value="scheduledWork.name_of_work" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.each_x_time') }}
                </label>
                <input type="text" :value="scheduledWork.each_x_time" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm" />
              </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.start_date') }}
                </label>
                <input type="text" v-model="scheduledWork.start_date" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.end_date') }}
                </label>
                <input type="text" v-model="scheduledWork.end_date" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.how_many_time_to_try') }}
                </label>
                <input type="text" v-model="scheduledWork.how_many_time_to_try" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.total_work') }}
                </label>
                <input type="text" v-model="scheduledWork.total_work" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.total_success') }}
                </label>
                <input type="text" v-model="scheduledWork.total_success" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.total_error') }}
                </label>
                <input type="text" v-model="scheduledWork.total_error" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <!-- json editor -->
            </div>
          </div>
        </div>
        <div class="flex justify-center">
          <button type="submit" class="px-4 py-2 bg-indigo-600 rounded-md">
            {{ $t("general.update") }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, inject } from 'vue';
import { useRoute } from 'vue-router';
import { useScheduledWorksStore } from '@/store/submodules/scheduled-works';
const { t } = useI18n();

const store = useScheduledWorksStore();
const route = useRoute();
const toast = inject("toast");

const loading = ref(true);
const scheduledWork = ref(null);

function deleteItem() {
  loading.value = true;
  store.Delete(deleteRelations.value.id)
    .then(() => {
      toast.success(t("general.delete_success"));
      return store.List(queries.page, queries.per_page, filterlist.value);
    })
    .catch(() => {
      toast.error(t("general.delete_error"));
      errorRes.msg = "Something went wrong";
    })
    .finally(() => {
      deleteModal.value = false;
      loading.value = false;
    });
}

onMounted(async () => {
  if (route.params.id) {
    try {
      const res = await store.Read(route.params.id);
      scheduledWork.value = res;
    } catch (err) {
      console.error('API Error:', err);
    }
  } else {
    console.log('No route.params.id found');
  }

  loading.value = false;
});
</script>