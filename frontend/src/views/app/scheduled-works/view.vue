<template>
  <div
    class="xl:p-5 flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <div v-if="loading" class="flex items-center justify-center h-full">
      <MonoIcon class="size-20 animate-spin" name="spin" />
    </div>
    <div v-else-if="error" class="flex items-center h-full p-5">
      <div class="flex flex-col md:flex-row gap-2 items-center justify-center w-full h-full">
        <MonoIcon class="size-20 text-rose-600 dark:text-rose-500 md:pt-2" name="exclamation" />
        <div class="text-center md:text-left">
          <h2 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            {{ error.title || $t("general.server_error") }}
          </h2>
          <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
            {{ error.message || $t("general.server_error_description") }}
          </p>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="space-y-4 p-2">
        <div class="w-full px-2 sm:px-0">
          <div class="space-y-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.name_of_work') }}
                </label>
                <input type="text" :value="scheduledWork.name_of_work" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.each_x_time') }}
                </label>
                <input type="text" :value="scheduledWork.each_x_time" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm" />
              </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.start_date') }}
                </label>
                <input type="text" v-model="scheduledWork.start_date" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.end_date') }}
                </label>
                <input type="text" v-model="scheduledWork.end_date" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.how_many_time_to_try') }}
                </label>
                <input type="text" v-model="scheduledWork.how_many_time_to_try" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.total_work') }}
                </label>
                <input type="text" v-model="scheduledWork.total_work" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.total_success') }}
                </label>
                <input type="text" v-model="scheduledWork.total_success" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.total_error') }}
                </label>
                <input type="text" v-model="scheduledWork.total_error" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>
            </div>
            <div class="grid grid-cols-1 gap-4">
              <!-- json editor -->
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('scheduled_works.request') }}
                </label>
                <div class="border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden bg-white dark:bg-gray-800">
                  <div class="p-0">
                    <div ref="monacoContainer" class="w-full border-0"></div>
                  </div>
                  <div v-if="scheduledWork?.request && !editorReady" class="p-3">
                    <textarea
                      :value="scheduledWork.request"
                      readonly
                      :style="{ height: calculateTextareaHeight(scheduledWork.request) + 'px' }"
                      class="w-full p-2 text-sm font-mono bg-gray-900 text-green-400 border-0 resize-none"
                      placeholder="Loading JSON editor..."
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { useScheduledWorksStore } from '@/store/submodules/scheduled-works';
import * as monaco from 'monaco-editor';

const store = useScheduledWorksStore();
const route = useRoute();

const loading = ref(true);
const scheduledWork = ref(null);
const error = ref(null);

const monacoContainer = ref(null);
let monacoEditor = null;
const editorReady = ref(false);

function initMonacoEditor() {
  if (!monacoContainer.value || monacoEditor) {
    return;
  }

  const requestData = scheduledWork.value?.request || '{"message": "No request data available"}';

  try {
    const parsedJson = JSON.parse(requestData);
    const formattedJson = JSON.stringify(parsedJson, null, 2);

    monacoEditor = monaco.editor.create(monacoContainer.value, {
      value: formattedJson,
      language: 'json',
      theme: 'vs-dark',
      lineNumbers: 'on',
      automaticLayout: false,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      formatOnPaste: false,
      formatOnType: false,
      fontSize: 15,
      readOnly: true,
      scrollbar: {
        vertical: 'auto',
        horizontal: 'auto'
      },
      contextmenu: false,
      selectOnLineNumbers: false,
      overviewRulerLanes: 0,
      hideCursorInOverviewRuler: true,
      overviewRulerBorder: false
    });

    setTimeout(() => {
      updateEditorHeight();
    }, 50);

    editorReady.value = true;
  } catch (e) {
    monacoEditor = monaco.editor.create(monacoContainer.value, {
      value: requestData,
      language: 'text',
      theme: 'vs-dark',
      lineNumbers: 'on',
      automaticLayout: false,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      fontSize: 14,
      readOnly: true,
      scrollbar: {
        vertical: 'auto',
        horizontal: 'auto'
      },
      contextmenu: false,
      selectOnLineNumbers: false,
      overviewRulerLanes: 0,
      hideCursorInOverviewRuler: true,
      overviewRulerBorder: false
    });

    setTimeout(() => {
      updateEditorHeight();
    }, 50);

    editorReady.value = true;
  }
}

function updateEditorHeight() {
  if (monacoEditor && monacoContainer.value) {
    const model = monacoEditor.getModel();
    if (model) {
      const lineCount = model.getLineCount();
      const lineHeight = monacoEditor.getOption(monaco.editor.EditorOption.lineHeight);
      const contentHeight = lineCount * lineHeight + 40; 
      const height = Math.max(100, Math.min(600, contentHeight));

      monacoContainer.value.style.height = `${height}px`;

      monacoEditor.layout({
        width: monacoContainer.value.offsetWidth,
        height: height
      });
    }
  }
}

function calculateTextareaHeight(text) {
  if (!text) return 100;

  const lines = text.split('\n').length;
  const height = lines * 20 + 40;
  return Math.max(100, Math.min(600, height));
}

onMounted(async () => {
  if (route.params.id) {
    try {
      const res = await store.Read(route.params.id);
      scheduledWork.value = res;

      await nextTick();

      setTimeout(() => {
        initMonacoEditor();
      }, 200);
    } catch (err) {
      console.error('API Error:', err);
      error.value = {
        title: 'Error loading scheduled work',
        message: err.message || 'Something went wrong'
      };
    }
  } else {
    error.value = {
      title: 'Invalid ID',
      message: 'No scheduled work ID provided'
    };
  }

  loading.value = false;
});

onUnmounted(() => {
  if (monacoEditor) {
    monacoEditor.dispose();
    monacoEditor = null;
  }
  editorReady.value = false;
});
</script>