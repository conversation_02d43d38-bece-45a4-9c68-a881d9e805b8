<template>
  <div class="max-w-4xl mx-auto card">
    <form @submit.prevent="createAccessToken()">
      <div class="grid grid-cols-6 gap-2">
        <div class="col-span-3 space-y-2">
          <label class="flex gap-1 text-sm font-medium">{{ $t('access_token.name') }}</label>
          <input type="text" step="any" required class="t-input" v-model="form.name" />
        </div>
        <div class="col-span-3 space-y-2">
          <label class="flex gap-1 text-sm font-medium">{{ $t('access_token.expire_day') }}</label>
          <input type="number" min="1" step="any" required class="t-input" v-model.number="form.expire_day" />
        </div>
      </div>
      <div class="flex justify-center mt-5">
        <button type="submit" class="px-4 py-2 bg-indigo-600 rounded-md">
          {{ $t("general.create") }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { useAccessTokenStore } from "@/store/submodules/access-token";
import { useFormErrorHandler } from '@/composables/error';
const { t } = useI18n();

const store = useAccessTokenStore();
const router = useRouter();
const toast = inject("toast");
const form = reactive({
  name: null,
  expire_day: null,
  errors: {},
});

const errHandler = useFormErrorHandler(form)


function createAccessToken() {
  store.Create(form).then((res) => {
    router.push({ name: "access-token-list" });
    toast.success(t("general.success")); 
  }).catch(errHandler);
}
</script>