<template>
  <div>
    <div>
      <div class="flex items-center justify-between mb-2">
        <h1>{{ flowProperties.title }}</h1>
        <entity-log-button entity="access-token" />
      </div>
    </div>

    <t-table :data="items.list" :columns="tableProperties.columns" entity-locale-name="admins"
      :page="Number(queries.page)" :per_page="Number(queries.per_page)" :total="items.total"
      :total_pages="items.total_pages" @page-change="pageChange" @per-page-change="perPageChange" :loading="loading"
      :create-route="tableProperties.createRoute" :filters="tableProperties.filters" @filter-change="onFilterChange"
      :error="errorRes.msg" inlinesearch>

      <template v-slot:row-actions="itemData">
        <div class="flex items-center gap-2"
          :class="tableProperties.columns[tableProperties.columns.length - 1].align === 'center' ? 'justify-center' : 'justify-start'">
          <div v-for="action in tableProperties.actions">
            <router-link v-if="action.route" :to="{ name: action.route, params: { id: itemData.item.id } }"
              class="flex items-center gap-2 px-2 py-1 text-xs text-white transition-colors rounded-md" :class="[
                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
              ]">
              <t-icon :name="action.icon" :class="[
                action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
              ]" />
              {{ action.label }}
            </router-link>
            <button v-else @click="action.function(itemData.item)"
              class="flex items-center gap-2 px-2 py-1 text-xs text-white transition-colors rounded-md" :class="[
                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
              ]">

              <t-icon :name="action.icon" :class="[
                action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
              ]" />
              {{ action.label }}
            </button>
          </div>
        </div>
      </template>
    </t-table>

    <t-delete-modal v-if="deleteModal" :is-active="deleteModal" @deleteData="deleteItem"
      @closeModal="  deleteModal = false; deleteRelations = {};">
      <template #content>
        <p class="text-md">{{ $t("general.delete_confirm") }}</p>
        <p class="text-xs leading-5">
          <span class="font-semibold text-rose-400">
            {{ $t("general.delete_label", { item: deleteRelations.name }) }}
          </span>
        </p>
      </template>
    </t-delete-modal>
  </div>
</template>

<script setup>
import { useAccessTokenStore } from '@/store/submodules/access-token';

const { t, te } = useI18n();
const route = useRoute();
const toast = inject('toast')
const router = useRouter();
const accessTokenStore = useAccessTokenStore();


const loading = ref(true);

const errorRes = reactive({
  msg: '',
  status: '',
})

const queries = reactive({
  page: route.query.page ? route.query.page : 1,
  per_page: route.query.per_page ? route.query.per_page : 10,
});

const deleteModal = ref(false);
const deleteRelations = ref({});

function setDeleteItem(item) {
  deleteRelations.value = item;
  deleteModal.value = true;
}

function deleteItem(data) {
  localStorage.hash_id = deleteRelations.value.hash_id;
  accessTokenStore.Delete(deleteRelations.value.id).catch((err) => {
    if (te(err.response.data.error)) {
      toast.error(t(err.response.data.error));
    } else {
      toast.error(err.response.data.error);
    }
  }).finally(() => {
    deleteModal.value = false;
    toast.success(t("general.success")); 
  })
}

const filterlist = ref(Object.keys(route.query).forEach((key) => {
  if (key !== 'page' && key !== 'per_page') {
    return {
      [key]: route.query[key]
    };
  }
}));

accessTokenStore.List(queries.page, queries.per_page, filterlist.value).catch((err) => {
  errorRes.msg = "Something went wrong";
}).finally(() => {
  loading.value = false;
});

function pageChange(pageNum) {
  loading.value = true;
  queries.page = Number(pageNum);

  router.push({
    name: route.name,
    query: queries,
  });

  accessTokenStore.List(pageNum, queries.per_page, filterlist.value).then(() => {
    if (errorRes.msg !== '') {
      errorRes.msg = '';
    }
  }).catch((err) => {
    errorRes.msg = "Something went wrong";
  });

  loading.value = false;

}

function perPageChange(per_page) {
  loading.value = true;
  queries.per_page = Number(per_page);
  router.push({
    name: route.name,
    query: queries,
  });

  accessTokenStore.List(queries.page, per_page, filterlist.value).then(() => {
    if (errorRes.msg !== '') {
      errorRes.msg = '';
    }
  }).catch((err) => {
    errorRes.msg = "Something went wrong";
  });

  loading.value = false;
}

const items = computed(() => {
  const list = accessTokenStore.getItems.rows ? accessTokenStore.getItems.rows : [];
  const total_pages = accessTokenStore.getItems.total_pages;
  const per_page = accessTokenStore.getItems.per_page;
  const page = accessTokenStore.getItems.page;
  const total = accessTokenStore.getItems.total;
  return {
    list,
    total_pages,
    per_page,
    page,
    total,
  };
});

function onFilterChange(filters) {
  loading.value = true;
  filterlist.value = filters;

  filterlist.value.forEach((filter) => {
    Object.keys(filter).forEach((key) => {
      if (filter[key] !== null && filter[key] !== "" && filter[key] !== undefined && filter[key] !== "undefined") {
        queries[key] = filter[key];
      } else {
        delete queries[key];
        delete filter[key];
      }

    });
  });

  router.push({
    name: route.name,
    query: queries,
  });


  accessTokenStore.List(queries.page, queries.per_page, filterlist.value).then(() => {
    if (errorRes.msg !== '') {
      errorRes.msg = '';
    }
  }).catch((err) => {
    errorRes.msg = "Something went wrong";
  });

  loading.value = false;
}


const tableProperties = {
  delete: "deleteAccessToken",
  get: "getAccessToken",
  storeGetter: "access-token",
  listRoute: "access-token-list",
  createRoute: "access-token-create",
  paginationOptions: {
    page: route.query.page ? route.query.page : 1,
    per_page: route.query.per_page ? route.query.per_page : 10,
  },
  columns: [
    {
      row: 'name',
      label: 'Name',
      sortable: true,
      align: "center",
      mobile: true,
      visible: true,
    },
    {
      row: 'created_by',
      label: 'created_by',
      sortable: true,
      align: "center",
      mobile: true,
      visible: true,
    },
    {
      row: 'expire_at',
      label: 'expire_at',
      sortable: true,
      align: "center",
      mobile: true,
      visible: true
    },
    {
      row: 'token',
      label: 'token',
      sortable: true,
      align: "center",
      mobile: true,
      visible: true,
      modifier: (value) => {
        return value.slice(0, 8) + '...' + value.slice(-4);
      }
    },
    {
      row: "actions",
      label: t("general.actions"),
      sortable: false,
      align: "center",
      mobile: true,
      visible: true,
    },
  ],
  filters: [
    {
      name: "Organization ID",
      type: "selectbox",
      dataName: "organization_id",
      get: "organizations",
      dataGet: "getOrganizations",
      colSize: 6,
      value: route.query.organization_id ? route.query.organization_id : null,
      show: true
    },
  ],
  actions: [
    {
      label: t("general.copy"),
      icon: "copy",
      color: "indigo",
      action: 'copy',
      function: copyToken,
    },
    {
      label: t("general.delete"),
      icon: "trash",
      color: "rose",
      action: 'delete',
      function: setDeleteItem
    },
  ]
};

function copyToken(item) {
  const token = item.token;
  if (!navigator.clipboard) {
    const textarea = document.createElement("textarea");
    textarea.value = token;
    textarea.setAttribute("readonly", "");
    textarea.style.position = "absolute";
    textarea.style.left = "-9999px";
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand("copy");
    document.body.removeChild(textarea);
  } else {
    navigator.clipboard.writeText(token).then(() => {
      toast.success(t("general.copied")); 
    }).catch(() => {
      toast.error(t("general.copy_failed")); 
    });
  }
}

const flowProperties = {
  title: t('page_titles.access-tokens'),
  entity: 'access-token',
}
</script>