<template>
  <div
    class=" mx-auto max-w-4xl bg-white dark:bg-gray-900 shadow-2xl rounded-2xl border border-gray-200 dark:border-gray-800 overflow-hidden"
    v-if="!loading">
    <div
      class="px-6 flex flex-col sm:flex-row items-start sm:items-center justify-between py-7 border-b border-gray-100 dark:border-gray-800 ">
      <div class="flex items-center gap-3">
        <span :class="log.type === 'error' ? 'bg-rose-100 text-rose-600' : 'bg-emerald-100 text-emerald-600'"
          class="inline-block px-3 py-1 rounded-full font-semibold text-xs tracking-wide shadow-sm border border-gray-200 dark:border-gray-700">
          {{ log.type === 'error' ? 'Failed' : 'Successful' }}
        </span>
      </div>
      <span class="text-sm text-gray-400 mt-3 sm:mt-0">{{ log.created_at }}</span>
    </div>
    <div class="px-6 py-8">
      <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
        <div>
          <dt class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Organization</dt>
          <dd class="mt-1 text-base text-gray-900 dark:text-gray-100 font-medium">{{ log.organization_name }}</dd>
        </div>
        <div>
          <dt class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Admin</dt>
          <dd class="mt-1 text-base text-gray-900 dark:text-gray-100 font-medium">{{ log.admin_name }}</dd>
        </div>
        <div>
          <dt class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Simulate Name</dt>
          <dd class="mt-1 text-base text-gray-900 dark:text-gray-100 font-medium">{{ log.simulate_name }}</dd>
        </div>
        <div>
          <dt class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Proto</dt>
          <span
            class="inline-block mt-2 text-xs px-3 py-1 rounded-full font-semibold shadow-sm border border-gray-200 dark:border-gray-700"
            :class="log.proto === 'http' ? 'bg-indigo-100 text-indigo-700' : 'bg-emerald-100 text-emerald-700'">{{
              log.proto }}</span>
        </div>
        <div>
          <dt class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Type</dt>
          <span
            class="inline-block mt-2 text-xs px-3 py-1 rounded-full font-semibold shadow-sm border border-gray-200 dark:border-gray-700"
            :class="log.type === 'error' ? 'bg-rose-100 text-rose-700' : 'bg-blue-100 text-blue-700'">{{
              log.type }}</span>
        </div>
        <div>
          <dt class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Source</dt>
          <span
            class="inline-block mt-2 text-xs px-3 py-1 rounded-full font-semibold shadow-sm border border-gray-200 dark:border-gray-700"
            :class="log.source === 'error' ? 'bg-rose-300 text-rose-400' : 'bg-blue-300 text-blue-400'">{{
              log.source }}</span>
        </div>
      </dl>
      <div class="mt-8">
        <dt class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Message</dt>
        <div
          class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 text-sm font-mono overflow-x-auto border border-gray-100 dark:border-gray-700">
          <vue-json-pretty :data="message" v-if="isJson(log.message)" />
          <span v-else>{{ log.message }}</span>
        </div>
      </div>
      <div v-if="log.request?.headers" class="mt-8">
        <dt class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Request Headers</dt>
        <div
          class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 text-sm font-mono overflow-x-auto border border-gray-100 dark:border-gray-700">
          <vue-json-pretty :data="log.request.headers" />
        </div>
      </div>
      <div v-if="log.request?.body" class="mt-8">
        <dt class="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Request Body</dt>
        <div
          class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 text-sm font-mono overflow-x-auto border border-gray-100 dark:border-gray-700">
          <vue-json-pretty :data="log.request.body" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import VueJsonPretty from "vue-json-pretty";
import "vue-json-pretty/lib/styles.css";
import { useLogStore } from '@/store/submodules/log';

const logStore = useLogStore();
const route = useRoute()
const log = ref('')
const loading = ref(true)
const message = ref('')

// TODO: localization
onMounted(async () => {
  if (route.params.id) {
    try {
      const res = await logStore.GetSimulateLogSingle(route.params.id)
      log.value = res;
    } catch (err) {
      console.error('API Error:', err);
    }
  } else {
    console.log('No route.params.id found');
  }

  loading.value = false;
});

function isJson(str) {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

</script>
