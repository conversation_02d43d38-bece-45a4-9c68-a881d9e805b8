<template>
  <div class="workspace-container">
    <FlowEditorEdit />
  </div>
</template>

<script setup>
import FlowEditorEdit from '@/components/flow-editor/flow-editor-edit.vue'
</script>

<style scoped>
.workspace-container {
  position: fixed;
  top: 120px;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 10;
  background: white;
}

.workspace-container :deep(.flow-editor-container) {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.workspace-container :deep(.dnd-flow) {
  flex: 1;
  height: calc(100% - 34px);
  overflow: hidden;
  position: relative;
}

.workspace-container :deep(.vue-flow-wrapper) {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden;
}

.workspace-container :deep(.sidebar-container) {
  height: 100%;
  overflow: hidden;
}
</style>
