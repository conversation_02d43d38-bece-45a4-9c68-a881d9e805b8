<template>
  <div>
    <div v-if="loading"></div>
    <View v-else :data="data" title="Data" />
  </div>
</template>
<script setup>
import View from "@/components/view.vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";

const route = useRoute();
const loading = ref(true);
const store = useStore();

store
  .dispatch("getDataToView", route.params.id)
  .then((e) => {
    loading.value = false;
  })
  .catch((err) => {
    console.log(err);
  });
const data = computed(() => {
  return store.getters.dataView;
});
</script>
