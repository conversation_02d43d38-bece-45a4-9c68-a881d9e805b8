<template>
  <div class="flex flex-col gap-3">
    <div class="card mx-auto max-w-4xl gap-3">
      <Form class="flex flex-col" @submit.prevent="createData">
        <template #head>
          <Header :title="$t('data.create_header')" :subtitle="$t('data.subtitle')" />
        </template>

        <div class="flex gap-3 mt-3">
          <div class="col-span-3">
            <Selectbox v-model="form.kri" label="data.kri" :options="dataUniqName" searchable required />
          </div>
          <div class="col-span-3">
            <label class="block text-md font-medium">{{
                          $t("data.reference_id")
                          }}</label>
            <input type="text" class="input-text" v-model="form.reference_id" required />
          </div>
          <div class="col-span-3">
            <label class="block text-md font-medium">{{ $t("data.count") }}</label>
            <input type="number" class="input-text" v-model="form.count" required />
          </div>
          <div class="col-span-3">
            <label class="block text-md font-medium">{{ $t("data.total") }}</label>
            <input type="number" class="input-text" v-model="form.total" required />
          </div>
        </div>

        <div class="flex justify-end p-1">
          <Button htmlType="submit" size="xs" color="primary">Save</Button>
        </div>
      </Form>

    </div>

  </div>
</template>
<script setup>
import { reactive } from "vue";
import Button from "@/components/button.vue";
import Form from "@/components/form/form.vue";
import Header from "@/components/form/header.vue";
import Selectbox from "@/components/selectbox.vue";
const toast = inject('toast')
const store = useStore();
const router = useRouter();
const form = reactive({
  kri: null,
  reference_id: "",
  count: 0,
  total: 0,
});

store.dispatch("listDataUniqName", { page: 1, per_page: 9999 }).catch((err) => {
  console.log(err);
});
const dataUniqName = computed(() => {
  let arr = []
  store.getters.dataUniqName.rows.map((item) => {
    arr.push({ id: item.uuid, name: item.kri })
  })
  return arr
});


const createData = () => {
  if (form.kri == null) {
    toast.error("Kri is required")
    return
  }
  store.dispatch("createData", form).then(() => {
    router.push({ name: "data" });
  });
};
</script>
