<template>
  <div v-if="!errorRes.msg">
    <div class="flex justify-between items-center mb-2">
      <h1>{{ $t("page_titles.data") }}</h1>
      <div class=" flex gap-3">

        <router-link class="bg-green-600 rounded-md px-4 py-2 text-gray-50" :to="{ name: 'list-data-access-token' }">Data
          access token</router-link>
        <router-link class="bg-purple-600 rounded-md px-4 py-2 text-gray-50" :to="{ name: 'create-data' }">Create
          Data</router-link>
      </div>
    </div>

    <div v-if="loading">
      <div v-if="(datas.list == null)" class="col-span-12">
        <Empty title="data.empty.title" description="data.empty.description" />
      </div>
      <Table v-else :columns="columns" :data="datas.list" :page="Number(queries.page)"
        :per_page="Number(queries.per_page)" :total_pages="datas.total_pages" @page-change="pageChange"
        @per-page-change="perPageChange">
        <template v-slot:default="action">
          <td class="table-rows flex gap-2 justify-center h-full">
            <router-link :to="{ name: 'data-view', params: { id: action.item.uuid } }">
              <IconButton size="sm" icon="view" />
            </router-link>
          </td>

        </template>
      </Table>

    </div>
  </div>
  <div class="max-w-max mx-auto" v-else>
    <div class="sm:flex items-center">
      <p class="text-4xl font-semibol text-indigo-600 sm:text-5xl">
        {{ errorRes.status }}
      </p>
      <div class="sm:ml-6">
        <div class="sm:border-l border-gray-300 dark:border-gray-700 sm:pl-6">
          <h1 class="text-xl text-gray-700 dark:text-gray-400 font-semibold">
            {{ errorRes.msg }}
          </h1>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { useStore } from "vuex";
import { computed, onMounted, reactive, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import Table from "@/components/table.vue";
import Empty from "@/components/empty.vue";
import IconButton from "../../../components/icon-buton.vue";
const loading = ref(false);
const store = useStore();
const route = useRoute();
const router = useRouter();
const errorRes = reactive({
  msg: "",
  status: "",
});

const columns = [
  {
    row: "kri",
    label: "Name",
  },
  {
    row: "count",
    label: "count",
  },
  {
    row: "total",
    label: "total",
  },
];
const queries = reactive({
  page: route.query.page ? route.query.page : 1,
  per_page: route.query.per_page ? route.query.per_page : 10,
});


store
  .dispatch("getData", { page: queries.page, per_page: queries.per_page })
  .catch((err) => {
    errorRes.msg = err.response.data.error;
    errorRes.status = err.response.status;
  }).finally(() => {
    loading.value = true;
  })

function pageChange(pageNum) {
  router.push({
    name: "data",
    query: {
      page: pageNum,
      per_page: queries.per_page,
    },
  });
  queries.page = pageNum;
  store
    .dispatch("getData", { page: pageNum, per_page: queries.per_page })
    .catch((err) => {
      errorRes.msg = err.response.data.error;
      errorRes.status = err.response.status;
    });
}

function perPageChange(per_page) {
  router.push({
    name: "data",
    query: {
      page: queries.page,
      per_page: per_page,
    },
  });
  queries.per_page = per_page;
  store
    .dispatch("getData", { page: queries.page, per_page: per_page })
    .catch((err) => {
      errorRes.msg = err.response.data.error;
      errorRes.status = err.response.status;
    });
}


const datas = computed(() => {
  const list = store.getters.data.rows;
  const total_pages = store.getters.data.total_pages;
  const per_page = store.getters.data.per_page;
  const page = store.getters.data.page;

  return {
    list,
    total_pages,
    per_page,
    page,
  };
});


</script>
