<template>
    <div class="flex justify-center">

        <button class="bg-blue-400 w-52 h-9 rounded text-lg " @click="cretaeDummy">
            cretaeDummy
        </button>
    </div>
</template>

<script setup>
//useStore
import { useStore } from 'vuex'
const store = useStore()
const toast = inject('toast')
const cretaeDummy = () => {
    store.dispatch('createDummyData').then(e => {

        toast.success(e.message)
    }

    )
}
</script>