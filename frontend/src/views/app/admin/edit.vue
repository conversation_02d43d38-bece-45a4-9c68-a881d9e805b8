<template>
    <form @submit.prevent="updateAdmin()" class="space-y-2" v-if="!loading">
      <div class="max-w-4xl mx-auto card">
        <div class="grid grid-cols-6 gap-2">
          <div class="col-span-3">
            <InputBox 
              wiki-link="admins.first_name"
              :error-text="form.errors.first_name"
              :label="$t('admins.first_name')"
              is_required
              no-span
              v-model="form.first_name" />
          </div>
          <div class="col-span-3">
            <InputBox 
              wiki-link="admins.last_name"
              :error-text="form.errors.last_name"
              :label="$t('admins.last_name')"
              is_required
              no-span
              v-model="form.last_name" />
          </div>
          <div class="col-span-3">
            <InputBox 
              type="email"
              wiki-link="admins.email"
              :error-text="form.errors.email"
              :label="$t('admins.email')"
              is_required
              no-span
              v-model="form.email" />
          </div>
          <div class="relative col-span-3">
            <div class="flex items-center justify-between">
              <label class="flex gap-1 text-sm font-medium">{{ $t("admins.password") }}
  
              </label>
              <button type="button" class="px-1 text-sm bg-indigo-600 rounded-md py-2t" @click="suggestStrongPassword">
                {{ $t("password.suggest") }}
              </button>
            </div>
            <div>
              <input ref="passwordRef" type="password" class="!pr-8 relative t-input" v-model="form.password" />
            </div>
            <div class="absolute top-8 right-2">
              <Eye v-if="passwordEye == 'on'" class="w-4 h-4 cursor-pointer" @click="eyeOff" />
              <EyeOff v-else class="w-4 h-4 cursor-pointer" @click="eyeOn" />
            </div>
          </div>
        </div>
      </div>
  
      <div class="max-w-4xl mx-auto card">
        <div class="grid grid-cols-6 gap-2">
          <div class="col-span-3">
            <t-select close-after-select long v-model="form.organization_id" :options="organizations" searchable
              :label="$t('admins.organization')" required />
          </div>
          <div class="col-span-3">
            <t-select close-after-select long v-model="form.role_id" :options="roles" searchable :label="$t('users.role')"
              required />
          </div>
        </div>
      </div>

  
      <div class="max-w-4xl mx-auto card">
        <div class="grid grid-cols-6 gap-2">
          <div class="col-span-3 space-y-2">
            <label class="flex gap-1 text-sm font-medium">{{ $t('users.suspended') }}
  
            </label>
            <Switch v-model="form.suspended" />
          </div>
          <div class="col-span-3">
            <label class="flex gap-1 text-sm font-medium">{{ $t('users.suspended_reason') }}
  
            </label>
            <input class="t-input" v-model="form.suspend_reason" />
          </div>
        </div>
      </div>
  
      <div class="flex justify-center">
        <button type="submit" class="px-4 py-2 bg-indigo-600 rounded-md">
          {{ $t("general.update") }}
        </button>
      </div>
    </form>
    <div class="flex items-center justify-center max-w-md mx-auto" v-if="loading">
      <svg class="text-green-500 animate-spin h-28 w-28" xmlns="http://www.w3.org/2000/svg" fill="none"
        viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" />
        <path class="opacity-75" fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
      </svg>
    </div>
  </template>
  
  <script setup>
  import { useAdminStore } from "@/store/submodules/admins";
  import { useOrganizationStore } from "@/store/submodules/organization";
  import { useRoleStore } from "@/store/submodules/role";
  import { passwordGenerate } from "@/utils/password-generate";
  import { passwordStrength } from "@/utils/password-strength";
  import { useFormErrorHandler } from '@/composables/error';
  
  const { t } = useI18n();
  const store = useAdminStore();
  const organizationStore = useOrganizationStore();
  const roleStore = useRoleStore();
  const router = useRouter();
  const route = useRoute();
  const toast = inject("toast");
  const loading = ref(true);
  const passwordRef = ref(null);
  const passwordEye = ref("off");
  
  const form = reactive({
    first_name: "",
    last_name: "",
    email: "",
    organization_id: "",
    role_id: "",
    password: "",
    suspended: false,
    suspend_reason: "",
    errors: {},
  });
  
  const errHandler = useFormErrorHandler(form)
  
  const eyeOn = () => {
    passwordEye.value = "on";
    passwordRef.value.type = "text";
  };
  
  const eyeOff = () => {
    passwordEye.value = "off";
    passwordRef.value.type = "password";
  };
  
  store.Read(route.params.id).then((res) => {
    form.first_name = res.first_name;
    form.last_name = res.last_name;
    form.email = res.email;
    form.organization_id = res.organization_id;
    form.role_id = res.role_id;
    form.id = res.id;
    form.suspended = res.suspended ?? false;
    form.suspend_reason = res.suspend_reason ?? "";
    localStorage.hash_id = res.hash_id;
    loading.value = false;
  }).catch((err) => {
    console.log(err);
    loading.value = false;
  });
  
  
  organizationStore.List(1, 1000)
  
  const organizations = computed(() => {
    return organizationStore.getItems.rows ?? [];
  });
  
  const roles = ref([]);
  
  watch(() => form.organization_id, async (organization_id) => {
    
    await roleStore.List(1, 1000, [{ organization_id: organization_id }]).catch((err) => {
      console.log(err);
    });
  
    roles.value = roleStore.getItems.rows;
  })
  
  function suggestStrongPassword() {
    const suggestedPassword = passwordGenerate(16, "!@#$%?");
    navigator.clipboard.writeText(suggestedPassword);
    form.password = suggestedPassword;
    passwordRef.value.focus();
    toast.info(t("password.copied_to_clipboard"));
  }
  
  function updateAdmin() {
    if (form.password) {
      const strength = passwordStrength(form.password);
      if (strength?.id < 1) {
        toast.error(t("password.strength", { strength_name: t(strength.i18n) }));
        return;
      }
    }
  
    store.Update(form).then((res) => {
      router.push({ name: "admin-list" });
      toast.success(res.message);
      localStorage.removeItem("hash_id");
    }).catch(errHandler);
  }
  </script>
  