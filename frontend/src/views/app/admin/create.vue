<template>
  <div class="max-w-4xl mx-auto card">
    <form @submit.prevent="createAdmin()">
      <div class="grid grid-cols-6 gap-2">
        <div class="col-span-3">
          <InputBox wiki-link="admins.first_name" :error-text="form.errors.first_name" :label="$t('admins.first_name')"
            is_required no-span v-model="form.first_name" />
        </div>
        <div class="col-span-3">
          <InputBox wiki-link="admins.last_name" :error-text="form.errors.last_name" :label="$t('admins.last_name')"
            is_required no-span v-model="form.last_name" />
        </div>
        <div class="col-span-3">
          <InputBox type="email" wiki-link="admins.email" :error-text="form.errors.email" :label="$t('admins.email')"
            is_required no-span v-model="form.email" />
        </div>
        <div class="col-span-3">
          <div class="flex items-center justify-between">
            <label class="flex gap-1 text-sm font-medium">{{ $t("admins.password") }} </label>
            <button type="button" class="px-1 text-sm bg-indigo-600 rounded-md py-2t" @click="suggestStrongPassword">
              {{ $t("password.suggest") }}
            </button>
          </div>

          <div class="relative">
            <input type="password" ref="passwordRef" class="!pr-8 relative t-input" v-model="form.password" required />
            <div class="absolute top-2 right-2">
              <Eye v-if="passwordEye == 'on'" class="w-4 h-4 cursor-pointer" @click="eyeOff" />
              <EyeOff v-else class="w-4 h-4 cursor-pointer" @click="eyeOn" />
            </div>
          </div>

        </div>
        <div class="col-span-3">
          <t-select v-model="form.organization_id" :options="organizations" searchable
            :label="$t('admins.organization')" long close-after-select required />
        </div>
        <div class="col-span-3">
          <t-select v-model="form.role_id" :options="roles" searchable :label="$t('users.role')" long close-after-select
            required />
        </div>
      </div>
      <div class="flex justify-center mt-5">
        <button type="submit" class="px-4 py-2 bg-indigo-600 rounded-md">
          {{ $t("general.create") }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { useAdminStore } from "@/store/submodules/admins";
import { useOrganizationStore } from "@/store/submodules/organization";
import { useRoleStore } from "@/store/submodules/role";
import { passwordGenerate } from "@/utils/password-generate";
import { passwordStrength } from "@/utils/password-strength";
import { useFormErrorHandler } from '@/composables/error';

const { t } = useI18n();
const store = useAdminStore();
const organizationStore = useOrganizationStore();
const roleStore = useRoleStore();
const router = useRouter();
const toast = inject("toast");
const passwordEye = ref("off");
const passwordRef = ref(null);

const form = reactive({
  name: "",
  first_name: "",
  last_name: "",
  email: "",
  password: "",
  role_id: "",
  organization_id: "",
  errors: {},
});

const errHandler = useFormErrorHandler(form)

const eyeOn = () => {
  passwordEye.value = "on";
  passwordRef.value.type = "text";
};

const eyeOff = () => {
  passwordEye.value = "off";
  passwordRef.value.type = "password";
};

organizationStore.List(1, 1000);

const organizations = computed(() => {
  return organizationStore.getItems.rows ?? [];
});

const roles = ref([]);

watch(
  () => form.organization_id,
  async (organization_id) => {

    await roleStore.List(1, 1000, [{ organization_id: organization_id }]).catch((err) => {
      console.log(err);
    });

    roles.value = roleStore.getItems.rows;
  },
);

function suggestStrongPassword() {
  const suggestedPassword = passwordGenerate(16, "!@#$%?");
  navigator.clipboard.writeText(suggestedPassword);
  form.password = suggestedPassword;
  passwordRef.value.focus();
  toast.info(t("password.copied_to_clipboard"));
}

function createAdmin() {
  const strength = passwordStrength(form.password);
  if (strength?.id < 1) {
    toast.error(t("password.strength", { strength_name: t(strength.i18n) }));
    return;
  }

  store
    .Create(form)
    .then((res) => {
      router.push({ name: "admin-list" });
      toast.success(res.message);
    })
    .catch(errHandler);
}
</script>
