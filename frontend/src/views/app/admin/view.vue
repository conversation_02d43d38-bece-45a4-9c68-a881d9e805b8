<template>
  <div class="max-w-7xl mx-auto" v-if="!loading">
    <div class="p-2 sm:p-5 sm:py-0 md:pt-5 space-y-5">

      <div
        class="p-5 pb-0 bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">

        <figure>
          <svg class="w-full rounded-lg" preserveAspectRatio="none" width="1113" height="161" viewBox="0 0 1113 161"
            fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_666_220723)">
              <rect x="1" width="1112" height="348" fill="#818cf8"></rect>
              <path d="M512.694 359.31C547.444 172.086 469.835 34.2204 426.688 -11.3096H1144.27V359.31H512.694Z"
                fill="#6366f1"></path>
              <path
                d="M818.885 185.745C703.515 143.985 709.036 24.7949 726.218 -29.5801H1118.31V331.905C1024.49 260.565 963.098 237.945 818.885 185.745Z"
                fill="#4f46e5"></path>
            </g>
          </svg>
        </figure>
        <div class="-mt-24">
          <div
            class="relative flex w-[120px] h-[120px] mx-auto border-4 border-white rounded-full dark:border-neutral-800">

            <div class="rounded-full flex justify-center bg-indigo-500 items-center size-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                stroke="currentColor" class="size-20 text-white">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
              </svg>
            </div>

          </div>

          <div class="mt-3 text-center">
            <h1 class="text-xl capitalize font-semibold text-gray-800 dark:text-neutral-200">
              {{ admin.name }}
            </h1>
            <p class="text-gray-500 dark:text-neutral-500">
              {{ admin.email }}
            </p>
          </div>
        </div>


        <div
          class="py-2 flex flex-row justify-between items-center gap-x-2 whitespace-nowrap overflow-x-auto overflow-y-hidden [&amp;::-webkit-scrollbar]:h-2 [&amp;::-webkit-scrollbar-thumb]:rounded-full [&amp;::-webkit-scrollbar-track]:bg-gray-100 [&amp;::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&amp;::-webkit-scrollbar-track]:bg-neutral-700 dark:[&amp;::-webkit-scrollbar-thumb]:bg-neutral-500">

          <nav class="flex gap-x-1">
            <button type="button"
              class="px-2.5 py-1.5 transition-colors relative inline-flex items-center gap-x-2 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700  hover:text-gray-800 text-sm rounded-lg focus:outline-none bg-gray-100 text-gray-800 dark:bg-neutral-900 ">
              <svg class="mr-1 size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                </path>
              </svg> Overview
            </button>
          </nav>

          <div class="">
            <router-link :to="{ name: 'admins-edit', params: { id: admin.id } }"
              class="px-2.5 py-1.5 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">

              <MonoIcon class="mr-2 size-5" name="pencil-alt" />

              {{ $t('general.edit') }}
            </router-link>
          </div>
        </div>

      </div>


      <div
        class="xl:p-5 flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
        <div class="xl:flex">

          <div
            class="bg-white p-5 xl:p-0 overflow-y-auto relative z-0 block translate-x-0 end-auto bottom-0 rounded-xl xl:rounded-none  xl:border-e xl:border-gray-200  dark:bg-neutral-800 dark:xl:border-neutral-700">

            <div class="xl:pe-4 mt-3 space-y-5 divide-y divide-gray-200 dark:divide-neutral-700">
              <div class="pt-4 first:pt-0">
                <h2 class="text-sm font-semibold text-gray-800 dark:text-neutral-200">
                  {{ $t('general.details') }}
                </h2>

                <ul class="mt-3 space-y-2">

                  <li>
                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">

                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                        stroke="currentColor" class="shrink-0 size-4 text-gray-600 dark:text-neutral-400">
                        <path stroke-linecap="round" stroke-linejoin="round"
                          d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h1.5m-1.5 3h1.5m-1.5 3h1.5m3-6H15m-1.5 3H15m-1.5 3H15M9 21v-3.375c0-.621.504-1.125 1.125-1.125h3.75c.621 0 1.125.504 1.125 1.125V21" />
                      </svg>

                      {{ admin.organization }}
                    </div>
                  </li>



                  <li>
                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">

                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                        stroke="currentColor" class="shrink-0 size-4 text-gray-600 dark:text-neutral-400">
                        <path stroke-linecap="round" stroke-linejoin="round"
                          d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5" />
                      </svg>

                      {{ admin.created_at }}
                    </div>
                  </li>
                  <li>
                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                        stroke="currentColor" class="shrink-0 size-4 text-gray-600 dark:text-neutral-400">
                        <path stroke-linecap="round" stroke-linejoin="round"
                          d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z" />
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                      </svg>
                      {{ admin.role }}
                    </div>
                  </li>


                  <li>
                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">

                      <svg class="shrink-0 size-4 text-gray-600 dark:text-neutral-400"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect width="20" height="16" x="2" y="4" rx="2"></rect>
                        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path>
                      </svg>
                      {{ admin.email }}
                    </div>
                  </li>


                  <li>
                    <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">

                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="shrink-0 size-4 text-gray-600 dark:text-neutral-400"
                        :class="{ '!text-rose-600': admin.suspended, '!text-green-600': !admin.suspended }">
                        <path stroke-linecap="round" stroke-linejoin="round"
                          d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
                      </svg>

                      {{ admin.suspended ? $t('admins.suspended') :
                        $t('admins.not_suspended') }}
                    </div>
                  </li>
                </ul>

              </div>

              <div class="pt-4 first:pt-0">
                <h2 class="mb-2 text-sm text-gray-500 dark:text-neutral-500">
                  Actions
                </h2>


                <ul class="space-y-2">
                  <li>
                    <button @click="forceLogout()"
                      class="p-2.5 flex w-full justify-between items-center gap-x-3 bg-white border border-gray-200 text-sm font-medium text-gray-800 dark:text-neutral-200 rounded-xl hover:text-rose-600 focus:outline-none focus:bg-gray-100 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:text-rose-500 dark:focus:bg-neutral-700">
                      <div class="flex items-center gap-2">
                        <span
                          class="flex shrink-0 justify-center items-center size-7 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">

                          <MonoIcon class="flex-shrink-0 size-5 text-rose-600 dark:text-rose-500" name="logout" />
                        </span>
                        <p>
                          {{ $t('admins.force_logout') }}
                        </p>
                      </div>
                      <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="m9 18 6-6-6-6"></path>
                      </svg>
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <div class="px-5 py-2 xl:py-0 grow space-y-5">
            <t-table :data="admin.activities" :columns="tableColumns" :page="Number(1)" :per_page="Number(10)"
              :total="10" :total_pages="1" :loading="loading" inlinesearch>
            </t-table>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup>
import { useAdminStore } from "@/store/submodules/admins";

const store = useAdminStore();
const route = useRoute();
const admin = ref(null);
const loading = ref(true);
const { t } = useI18n()
const toast = inject('toast')

store.Details(route.params.id).then((res) => {
  admin.value = res
  loading.value = false
});

function forceLogout() {
  store.ForceLogout(route.params.id).then((res) => {
    toast.success("Force Logout Successful")
  }).catch((err) => {
    toast.error(err.response.data.error)
  })
}

const tableColumns = [
  {
    row: 'action',
    label: 'Action',
    sortable: true,
    align: "center",
    mobile: true,
    visible: true,
  },
  {
    row: 'entity',
    label: 'Entity',
    sortable: true,
    align: "center",
    mobile: true,
    visible: true,
  },
  {
    row: 'ip_address',
    label: 'IP Address',
    sortable: false,
    align: "center",
    mobile: true,
    visible: true,
  },
  {
    row: 'date',
    label: 'Date',
    sortable: false,
    align: "center",
    mobile: true,
    visible: true,
  },
]
</script>