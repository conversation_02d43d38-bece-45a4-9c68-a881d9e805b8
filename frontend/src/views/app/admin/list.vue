<template>
  <div>
    <div>
      <div class="flex items-center justify-between mb-2">
        <h1>{{ flowProperties.title }}</h1>
        <entity-log-button entity="admin" />
      </div>
    </div>

    <t-table :data="items.list" :columns="tableProperties.columns" entity-locale-name="admins"
      :page="Number(queries.page)" :per_page="Number(queries.per_page)" :total="items.total"
      :total_pages="items.total_pages" @page-change="pageChange" @per-page-change="perPageChange" :loading="loading"
      :create-route="tableProperties.createRoute" :filters="tableProperties.filters" @filter-change="onFilterChange"
      :error="errorRes.msg" inlinesearch>

      <template v-slot:row-actions="itemData">
        <div class="flex items-center gap-2"
          :class="tableProperties.columns[tableProperties.columns.length - 1].align === 'center' ? 'justify-center' : 'justify-start'">
          <div v-for="action in tableProperties.actions">
            <router-link v-if="action.route" :to="{ name: action.route, params: { id: itemData.item.id } }"
              class="flex items-center gap-2 px-2 py-1 text-xs text-white transition-colors rounded-md" :class="[
                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
              ]">
              <t-icon :name="action.icon" :class="[
                action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
              ]" />
              {{ action.label }}
            </router-link>
            <button v-else @click="action.function(itemData.item)"
              class="flex items-center gap-2 px-2 py-1 text-xs text-white transition-colors rounded-md" :class="[
                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
              ]">

              <t-icon :name="action.icon" :class="[
                action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
              ]" />
              {{ action.label }}
            </button>
          </div>
        </div>
      </template>
    </t-table>

    <t-delete-modal v-if="deleteModal" :is-active="deleteModal" @deleteData="deleteItem"
      @closeModal="  deleteModal = false; deleteRelations = {};">
      <template #content>
        <p class="text-md">{{ $t("general.delete_confirm") }}</p>
        <p class="text-xs leading-5">
          <span class="font-semibold text-rose-400">
            {{ $t("general.delete_label", { item: deleteRelations.name }) }}
          </span>
        </p>
      </template>
    </t-delete-modal>


    <Modal v-model="exportModal" @closeModal="exportModal = false">
      <template #header>
        <div class="px-4 py-2 border-b border-gray-700">
          <h1 class="text-2xl font-bold">Export</h1>
        </div>
      </template>
      <template #content>
        <div class="px-4 py-2">
          <Selectbox v-model="exportOrganizationID" :options="organizations" label="Organization" />
        </div>

        <div class="flex items-center justify-between px-4 py-2">
          <button @click="exportOrganizationAdmins()"
            class="flex items-center gap-2 px-4 py-2 text-white transition-colors bg-gray-800 rounded-md hover:bg-gray-700">
            <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" x2="12" y1="15" y2="3"></line>
            </svg>
            Export
          </button>

          <button @click="exportModal = false"
            class="flex items-center gap-2 px-4 py-2 text-white transition-colors rounded-md bg-rose-800 hover:bg-rose-700">

            Cancel
          </button>
        </div>



      </template>
    </Modal>

    <Modal v-model="importModal" @closeModal="importModal = false, jsonFileData.admins = []">
      <template #header>
        <div class="px-4 py-2 border-b border-gray-700">
          <h1 class="text-2xl font-bold">
            Import
          </h1>
        </div>
      </template>
      <template #content>

        <div class="px-4 py-2" v-if="jsonFileData.admins.length > 0">
          <Selectbox v-model="importOrganizationID" :options="organizations" label="Organization" />
        </div>

        <div class="p-4 bg-gray-100 border-t border-gray-200 dark:bg-neutral-700 dark:border-neutral-700">
          <div v-if="jsonFileData.admins.length > 0">
            <div
              class="flex flex-col bg-white border border-gray-200 rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
              <div class="relative group">
                <div class="h-36 sm:h-[170px] flex flex-col justify-center items-center">
                  <svg class="flex-shrink-0 w-12 h-12 text-gray-400 dark:text-neutral-600"
                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="21" x2="3" y1="6" y2="6"></line>
                    <line x1="15" x2="3" y1="12" y2="12"></line>
                    <line x1="17" x2="3" y1="18" y2="18"></line>
                  </svg>
                </div>


              </div>

              <!-- Body -->
              <div class="flex items-center p-3 gap-x-3">
                <!-- Icon -->
                <span
                  class="flex flex-shrink-0 justify-center items-center w-7 h-7 sm:w-[38px] sm:h-[38px] bg-white border border-gray-200 rounded-lg sm:rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                  <svg class="flex-shrink-0 w-3.5 h-3.5 sm:w-5 sm:h-5" xmlns="http://www.w3.org/2000/svg" width="16"
                    height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd"
                      d="M14 4.5V11h-1V4.5h-2A1.5 1.5 0 0 1 9.5 3V1H4a1 1 0 0 0-1 1v9H2V2a2 2 0 0 1 2-2h5.5L14 4.5ZM4.151 15.29a1.176 1.176 0 0 1-.111-.449h.764a.578.578 0 0 0 .255.384c.07.049.154.087.25.114.095.028.201.041.319.041.164 0 .301-.023.413-.07a.559.559 0 0 0 .255-.193.507.507 0 0 0 .084-.29.387.387 0 0 0-.152-.326c-.101-.08-.256-.144-.463-.193l-.618-.143a1.72 1.72 0 0 1-.539-.214 1.001 1.001 0 0 1-.352-.367 1.068 1.068 0 0 1-.123-.524c0-.244.064-.457.19-.639.128-.181.304-.322.528-.422.225-.1.484-.149.777-.149.304 0 .564.05.779.152.217.102.384.239.5.41.12.17.186.359.2.566h-.75a.56.56 0 0 0-.12-.258.624.624 0 0 0-.246-.181.923.923 0 0 0-.37-.068c-.216 0-.387.05-.512.152a.472.472 0 0 0-.185.384c0 .121.048.22.144.3a.97.97 0 0 0 .404.175l.621.143c.217.05.406.12.566.211a1 1 0 0 1 .375.358c.09.148.135.335.135.56 0 .247-.063.466-.188.656a1.216 1.216 0 0 1-.539.439c-.234.105-.52.158-.858.158-.254 0-.476-.03-.665-.09a1.404 1.404 0 0 1-.478-.252 1.13 1.13 0 0 1-.29-.375Zm-3.104-.033a1.32 1.32 0 0 1-.082-.466h.764a.576.576 0 0 0 .074.27.499.499 0 0 0 .454.246c.19 0 .33-.055.422-.164.091-.11.137-.265.137-.466v-2.745h.791v2.725c0 .44-.119.774-.357 1.005-.237.23-.565.345-.985.345a1.59 1.59 0 0 1-.568-.094 1.145 1.145 0 0 1-.407-.266 1.14 1.14 0 0 1-.243-.39Zm9.091-1.585v.522c0 .256-.039.47-.117.641a.862.862 0 0 1-.322.387.877.877 0 0 1-.47.126.883.883 0 0 1-.47-.126.87.87 0 0 1-.32-.387 1.55 1.55 0 0 1-.117-.641v-.522c0-.258.039-.471.117-.641a.87.87 0 0 1 .32-.387.868.868 0 0 1 .47-.129c.177 0 .333.043.47.129a.862.862 0 0 1 .322.387c.078.17.117.383.117.641Zm.803.519v-.513c0-.377-.069-.701-.205-.973a1.46 1.46 0 0 0-.59-.63c-.253-.146-.559-.22-.916-.22-.356 0-.662.074-.92.22a1.441 1.441 0 0 0-.589.628c-.137.271-.205.596-.205.975v.513c0 .375.068.699.205.973.137.271.333.48.589.626.258.145.564.217.92.217.357 0 .663-.072.917-.217.256-.146.452-.355.589-.626.136-.274.205-.598.205-.973Zm1.29-.935v2.675h-.746v-3.999h.662l1.752 2.66h.032v-2.66h.75v4h-.656l-1.761-2.676h-.032Z">
                    </path>
                  </svg>
                </span>
                <div class="truncate grow">
                  <p class="block text-sm font-semibold text-gray-800 truncate dark:text-neutral-200">
                    {{ jsonFileData.filename }}
                  </p>
                  <p class="block text-xs text-gray-500 truncate dark:text-neutral-500">

                  </p>
                </div>
                <!-- End Icon -->


              </div>
              <!-- End Body -->
            </div>
          </div>

          <div v-if="jsonFileData.admins.length < 1"
            class="flex justify-center p-12 mt-2 bg-white border border-gray-300 border-dashed rounded-xl dark:bg-neutral-800 dark:border-neutral-600">
            <div class="text-center">
              <svg class="w-16 mx-auto text-gray-400 dark:text-neutral-400" width="70" height="46" viewBox="0 0 70 46"
                fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M6.05172 9.36853L17.2131 7.5083V41.3608L12.3018 42.3947C9.01306 43.0871 5.79705 40.9434 5.17081 37.6414L1.14319 16.4049C0.515988 13.0978 2.73148 9.92191 6.05172 9.36853Z"
                  fill="currentColor" stroke="currentColor" stroke-width="2"
                  class="fill-white stroke-gray-400 dark:fill-neutral-800 dark:stroke-neutral-500" />
                <path
                  d="M63.9483 9.36853L52.7869 7.5083V41.3608L57.6982 42.3947C60.9869 43.0871 64.203 40.9434 64.8292 37.6414L68.8568 16.4049C69.484 13.0978 67.2685 9.92191 63.9483 9.36853Z"
                  fill="currentColor" stroke="currentColor" stroke-width="2"
                  class="fill-white stroke-gray-400 dark:fill-neutral-800 dark:stroke-neutral-500" />
                <rect x="17.0656" y="1.62305" width="35.8689" height="42.7541" rx="5" fill="currentColor"
                  stroke="currentColor" stroke-width="2"
                  class="fill-white stroke-gray-400 dark:fill-neutral-800 dark:stroke-neutral-500" />
                <path
                  d="M47.9344 44.3772H22.0655C19.3041 44.3772 17.0656 42.1386 17.0656 39.3772L17.0656 35.9161L29.4724 22.7682L38.9825 33.7121C39.7832 34.6335 41.2154 34.629 42.0102 33.7025L47.2456 27.5996L52.9344 33.7209V39.3772C52.9344 42.1386 50.6958 44.3772 47.9344 44.3772Z"
                  stroke="currentColor" stroke-width="2" class="stroke-gray-400 dark:stroke-neutral-500" />
                <circle cx="39.5902" cy="14.9672" r="4.16393" stroke="currentColor" stroke-width="2"
                  class="stroke-gray-400 dark:stroke-neutral-500" />
              </svg>

              <div class="flex flex-wrap justify-center mt-4 text-sm leading-6 text-gray-600">
                <span class="font-medium text-gray-800 pe-1 dark:text-neutral-200">
                  Browse
                </span>
                <label for="hs-pro-dauuf"
                  class="relative font-semibold text-blue-600 bg-white rounded-lg cursor-pointer hover:text-blue-700 decoration-2 hover:underline dark:bg-neutral-800 dark:text-blue-500 dark:hover:text-blue-600">
                  <span>file</span>
                  <input id="hs-pro-dauuf" @change="readFile" ref="jsonfile" type="file" class="sr-only"
                    name="hs-pro-dauuf">
                </label>
              </div>

              <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400">
                Json
              </p>
            </div>
          </div>
        </div>

        <div class="flex items-center justify-between px-4 py-2">
          <button @click="importData()"
            class="flex items-center gap-2 px-4 py-2 text-white transition-colors bg-gray-800 rounded-md hover:bg-gray-700">
            Import
          </button>

          <button @click="importModal = false, jsonFileData.admins = []"
            class="flex items-center gap-2 px-4 py-2 text-white transition-colors rounded-md bg-rose-800 hover:bg-rose-700">
            Cancel
          </button>
        </div>


      </template>
    </Modal>

  </div>
</template>

<script setup>
import { useAdminStore } from '@/store/submodules/admins';
import { useOrganizationStore } from '@/store/submodules/organization';
import { securedAxios } from '@/utils/axios';



const { t, te } = useI18n();
const route = useRoute();
const toast = inject('toast')
const store = useAdminStore();
const router = useRouter();
const organizationStore = useOrganizationStore();


const loading = ref(true);


const errorRes = reactive({
  msg: '',
  status: '',
})

const queries = reactive({
  page: route.query.page ? route.query.page : 1,
  per_page: route.query.per_page ? route.query.per_page : 10,
});


const deleteModal = ref(false);
const deleteRelations = ref({});

function setDeleteItem(item) {
  deleteRelations.value = item;
  deleteModal.value = true;
}

function deleteItem(data) {
  localStorage.hash_id = deleteRelations.value.hash_id;
  store.Delete(deleteRelations.value.id).catch((err) => {
    if (te(err.response.data.error)) {
      toast.error(t(err.response.data.error));
    } else {
      toast.error(err.response.data.error);
    }
  }).finally(() => {
    deleteModal.value = false;
  })
}


const filterlist = ref(Object.keys(route.query).forEach((key) => {
  if (key !== 'page' && key !== 'per_page') {
    return {
      [key]: route.query[key]
    };
  }
}));


store.List(queries.page, queries.per_page, filterlist.value).catch((err) => {
  errorRes.msg = "Something went wrong";
}).finally(() => {
  loading.value = false;
});

function pageChange(pageNum) {
  loading.value = true;
  queries.page = Number(pageNum);

  router.push({
    name: route.name,
    query: queries,
  });

  store.List(pageNum, queries.per_page, filterlist.value).then(() => {
    if (errorRes.msg !== '') {
      errorRes.msg = '';
    }
  }).catch((err) => {
    errorRes.msg = "Something went wrong";
  });

  loading.value = false;

}

function perPageChange(per_page) {
  loading.value = true;
  queries.per_page = Number(per_page);
  router.push({
    name: route.name,
    query: queries,
  });

  store.List(queries.page, per_page, filterlist.value).then(() => {
    if (errorRes.msg !== '') {
      errorRes.msg = '';
    }
  }).catch((err) => {
    errorRes.msg = "Something went wrong";
  });

  loading.value = false;
}


const items = computed(() => {
  const list = store.getItems.rows ? store.getItems.rows : [];
  const total_pages = store.getItems.total_pages;
  const per_page = store.getItems.per_page;
  const page = store.getItems.page;
  const total = store.getItems.total;
  return {
    list,
    total_pages,
    per_page,
    page,
    total,
  };
});



organizationStore.List(1, 1000)
const organizations = computed(() => {
  return organizationStore.getItems.rows ? organizationStore.getItems.rows : [];
});

function onFilterChange(filters) {
  loading.value = true;
  filterlist.value = filters;

  filterlist.value.forEach((filter) => {
    Object.keys(filter).forEach((key) => {
      if (filter[key] !== null && filter[key] !== "" && filter[key] !== undefined && filter[key] !== "undefined") {
        queries[key] = filter[key];
      } else {
        delete queries[key];
        delete filter[key];
      }

    });
  });

  router.push({
    name: route.name,
    query: queries,
  });


  store.List(queries.page, queries.per_page, filterlist.value).then(() => {
    if (errorRes.msg !== '') {
      errorRes.msg = '';
    }
  }).catch((err) => {
    errorRes.msg = "Something went wrong";
  });

  loading.value = false;
}


const tableProperties = {
  delete: "deleteAdmin",
  get: "getAdmins",
  storeGetter: "admins",
  listRoute: "admin-list",
  createRoute: "admins-create",
  paginationOptions: {
    page: route.query.page ? route.query.page : 1,
    per_page: route.query.per_page ? route.query.per_page : 10,
  },
  columns: [
    {
      row: 'name',
      label: 'Name',
      sortable: true,
      align: "center",
      mobile: true,
      visible: true,
    },
    {
      row: 'organization',
      label: 'Organization',
      sortable: true,
      align: "center",
      mobile: true,
      visible: true,
    },
    {
      row: 'role',
      label: 'Role',
      sortable: true,
      align: "center",
      mobile: true,
      visible: true,
    },
    {
      row: "actions",
      label: t("general.actions"),
      sortable: false,
      align: "center",
      mobile: true,
      visible: true,
    },
  ],
  filters: [
    {
      name: "Organization ID",
      type: "selectbox",
      dataName: "organization_id",
      get: "organizations",
      dataGet: "getOrganizations",
      colSize: 6,
      value: route.query.organization_id ? route.query.organization_id : null,
      show: true
    },
  ],
  actions: [
    {
      label: t("general.view"),
      icon: "eye",
      color: "indigo",
      action: 'view',
      route: 'admins-view'
    },
    {
      label: t("general.edit"),
      icon: "pencil",
      color: "blue",
      action: 'edit',
      route: 'admins-edit'
    },
    {
      label: t("general.delete"),
      icon: "trash",
      color: "rose",
      action: 'delete',
      function: setDeleteItem
    },
  ]
};

const flowProperties = {
  title: t('page_titles.admins'),
  entity: 'admin',
}

const exportModal = ref(false);
const importModal = ref(false);
const jsonFileData = ref({
  admins: [],
  filename: null,
});

const jsonfile = ref(null);
const exportOrganizationID = ref('');
const importOrganizationID = ref('');

function exportOrganizationAdmins() {
  securedAxios.post('admins/export', { organization_id: exportOrganizationID.value })
    .then((res) => {
      console.log(res);
      // save as json file
      const blob = new Blob([JSON.stringify(res.data)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = exportOrganizationID.value + '-admins.json';
      a.click();
      window.URL.revokeObjectURL(url);

      exportModal.value = false;
      exportOrganizationID.value = '';

      // delete element
      a.remove();



      toast.success('Exporting');
    })
    .catch((err) => {
      console.log(err);
      toast.error('Error exporting');
    })
}

function readFile(e) {
  const file = e.target.files[0];
  const reader = new FileReader();
  reader.onload = (e) => {
    jsonFileData.value = {
      admins: e.target.result,
      filename: file.name,
    }
  }
  reader.readAsText(file);
}

function importData() {
  if (importOrganizationID.value === '') {
    toast.error('Please select organization');
    return;
  }
  if (jsonFileData.value.admins.length < 1) {
    toast.error('Please select file');
    return;
  }
  toast.success('Importing');

  securedAxios.post('admins/import', {
    admins: JSON.parse(jsonFileData.value.admins),
    organization_id: importOrganizationID.value
  }).then((res) => {
    jsonFileData.value.admins = [];
    importOrganizationID.value = '';
    importModal.value = false;
    toast.success('Imported');
  }).catch((err) => {
    console.log(err);
    toast.error('Error importing');
  })
}

</script>