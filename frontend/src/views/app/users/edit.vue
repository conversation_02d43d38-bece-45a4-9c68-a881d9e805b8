<template>
    <div class="card mx-auto max-w-4xl">
        <div v-if="!loading" class="flex justify-center">
            <Spinner color="white" size="20" />
        </div>
        <form v-else @submit.prevent="updateUser()">
            <div class="grid grid-cols-6 gap-2">
                <div class="col-span-3">
                    <label class="block text-sm font-medium">{{ $t('users.name') }}</label>
                    <input type="text" class="input-text" v-model="form.name" />
                </div>
                <div class="col-span-3">
                    <label class="block text-sm font-medium">{{ $t('users.email') }}</label>
                    <input type="email" class="input-text" v-model="form.email" />
                </div>
                <div class="col-span-3">
                    <label class="block text-sm font-medium">{{ $t('users.two-factor') }}</label>
                    <Switch v-model="form.tfa" />
                </div>
                <div class="col-span-3">
                    <label class="block text-sm font-medium">{{ $t('users.two-factor_key') }}</label>
                    <input type="email" class="input-text" v-model="form.tfakey" />
                </div>
                <div class="col-span-3">
                    <label class="block text-sm font-medium">{{ $t('users.account') }}</label>
                    <Selectbox v-model="form.account_uuid" :options="accounts" searchable />
                </div>
                <div class="col-span-3">
                    <label class="block text-sm font-medium">{{ $t('users.role') }}</label>
                    <Selectbox v-model="form.role_uuid" :options="roles" searchable />
                </div>
            </div>
            <div class="flex justify-center mt-5">
                <button
                    type="submit"
                    class="bg-indigo-600 px-4 py-2 rounded-md"
                >{{ $t('general.update') }}</button>
            </div>
        </form>
    </div>
</template>

<script setup>
import { computed, inject, reactive } from 'vue';
import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import { securedAxios } from '@/utils/axios';
import Switch from '@/components/switch.vue';
import Selectbox from "@/components/selectbox.vue";
const loading = ref(false)
const store = useStore()
const router = useRouter()
const route = useRoute()
const toast = inject('toast')
const form = reactive({
    name: '',
    email: '',
    account_uuid: '',
    role_uuid: '',
    tfa: false,
    tfa_key: '',
})

securedAxios.get(`/users/${route.params.id}`).then((res) => {
    form.name = res.data.name
    form.email = res.data.email
    form.account_uuid = res.data.account_uuid
    form.role_uuid = res.data.role_uuid
    form.uuid = res.data.uuid
    form.tfa = res.data.tfa
    form.tfa_key = res.data.tfa_key
}).catch((err) => {
    console.log(err)
}).finally(() => {
    loading.value = true
})
store.dispatch('getAccounts', { page: 1, per_page: 9999 }).catch((err) => {
    console.log(err)
})
const accounts = computed(() => {
    return store.getters.accounts.rows.map((account) => {
        return {
            id: account.uuid,
            name: account.name,
        }
    })
})

store.dispatch('getRoles', { page: 1, per_page: 9999 }).catch((err) => {
    console.log(err)
})
const roles = computed(() => {
    return store.getters.roles.rows.map((role) => {
        return {
            id: role.uuid,
            name: role.name,
        }
    })
})

function updateUser() {
    store.dispatch('updateUser', form).then((res) => {
        router.push({ name: 'users' })
        toast.success(res.data.message)
    }).catch((err) => {
        toast.error(err.response.data.error)
    })
}
</script>