<template>
  <div v-if="!errorRes.msg">
    <Card>
      <div class="flex justify-between items-center mb-2">
        <h1>{{ $t("page_titles.users") }}</h1>
        <router-link class="bg-indigo-500 rounded-md px-4 py-2 text-gray-50" :to="{ name: 'users-create' }">
          <Icon name="plus" />
        </router-link>
      </div>
      <Table actionable :columns="columns" :data="users.list" :page="Number(queries.page)"
        :per_page="Number(queries.per_page)" :total_pages="users.total_pages" @page-change="pageChange"
        @per-page-change="perPageChange">
        <template v-slot:default="action">
          <td class="table-rows flex gap-2 justify-center h-full">
            <router-link
              class="flex items-center justify-center w-8 h-8 font-bold text-white bg-blue-500 rounded hover:bg-blue-700"
              :to="{ name: 'users-edit', params: { id: action.item.uuid } }">
              <Icon name="pencil" class="w-4 h-4" />
            </router-link>


            <button @click="
              deleteModal.id = action.item.uuid
            deleteModal.show = true;
            "
              class="flex items-center justify-center w-8 h-8 font-bold text-white bg-red-500 rounded hover:bg-red-700">
              <Icon name="trash" classes="w-4 h-4 " />
            </button>
            <div v-if="deleteModal.show">
              <DeleteModel :deleteModal="deleteModal" @delete="deleteUser" />
            </div>
          </td>
        </template>
      </Table>
    </Card>
  </div>
  <div class="max-w-max mx-auto" v-else>
    <div class="sm:flex items-center">
      <p class="text-4xl font-semibol text-indigo-600 sm:text-5xl">
        {{ errorRes.status }}
      </p>
      <div class="sm:ml-6">
        <div class="sm:border-l border-gray-300 dark:border-gray-700 sm:pl-6">
          <h1 class="text-xl text-gray-700 dark:text-gray-400 font-semibold">
            {{ errorRes.msg }}
          </h1>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, reactive } from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import Table from "@/components/table.vue";
const store = useStore();
const route = useRoute();
const router = useRouter();

const queries = reactive({
  page: route.query.page ? route.query.page : 1,
  per_page: route.query.per_page ? route.query.per_page : 10,
});

const errorRes = reactive({
  msg: "",
  status: "",
});
const deleteModal = ref({
  show: false,
  id: null,
  processing: false,
});
const columns = [
  {
    row: "name",
    label: "Name",
  },
  {
    row: "email",
    label: "Email",
  },
  {
    row: "organization",
    label: "Organization",
  },
  {
    row: "account",
    label: "Account",
  },
  {
    row: "role",
    label: "Role",
  },
];

function deleteUser(uuid) {
  store.dispatch("deleteUser", uuid);
}

store
  .dispatch("getUsers", { page: queries.page, per_page: queries.per_page })
  .catch((err) => {
    errorRes.msg = err.response.data.error;
    errorRes.status = err.response.status;
  });

function pageChange(pageNum) {
  router.push({
    name: "users",
    query: {
      page: pageNum,
      per_page: queries.per_page,
    },
  });
  queries.page = pageNum;
  store
    .dispatch("getUsers", { page: pageNum, per_page: queries.per_page })
    .catch((err) => {
      errorRes.msg = err.response.data.error;
      errorRes.status = err.response.status;
    });
}

function perPageChange(per_page) {
  router.push({
    name: "users",
    query: {
      page: queries.page,
      per_page: per_page,
    },
  });
  queries.per_page = per_page;
  store
    .dispatch("getUsers", { page: queries.page, per_page: per_page })
    .catch((err) => {
      errorRes.msg = err.response.data.error;
      errorRes.status = err.response.status;
    });
}

const users = computed(() => {
  const list = store.getters.users.rows;
  const total_pages = store.getters.users.total_pages;
  const per_page = store.getters.users.per_page;
  const page = store.getters.users.page;
  return {
    list,
    total_pages,
    per_page,
    page,
  };
});
</script>
