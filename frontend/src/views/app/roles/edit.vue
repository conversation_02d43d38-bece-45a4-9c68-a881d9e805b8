<template>
  <div class="card max-w-3xl mx-auto">
    <form @submit.prevent="updateRole()">
      <div class="grid grid-cols-6">
        <div class="col-span-6">
          <label class="block text-sm font-medium">{{
            $t("roles.role_name")
          }}</label>
          <input type="text" class="input-text" v-model="form.name" />
        </div>
      </div>
      <div class="grid grid-cols-6 gap-2 divide-y divide-gray-900 dark:divide-gray-600 my-2">
        <div class="col-span-6" v-for="permission in form.permissions" :key="permission">
          <label class="block text-sm font-medium capitalize my-2">{{ $t(`roles.entities.${permission.entity}`) }}
            {{ $t("roles.permissions") }}</label>
          <div class="grid grid-cols-8">
            <div class="col-span-2 flex items-center">
              <Switch v-model="permission.create" />
              <label class="ml-2 text-sm">{{
                $t("roles.permission_create")
              }}</label>
            </div>
            <div class="col-span-2 flex items-center">
              <Switch v-model="permission.read" />
              <label class="ml-2 text-sm">{{
                $t("roles.permission_read")
              }}</label>
            </div>
            <div class="col-span-2 flex items-center">
              <Switch v-model="permission.update" />
              <label class="ml-2 text-sm">{{
                $t("roles.permission_update")
              }}</label>
            </div>
            <div class="col-span-2 flex items-center">
              <Switch v-model="permission.delete" />
              <label class="ml-2 text-sm">{{
                $t("roles.permission_delete")
              }}</label>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-center items-center my-5">
        <button type="submit" class="bg-indigo-500 rounded-md px-4 py-2 text-gray-50">
          {{ $t("general.update") }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { securedAxios } from "@/utils/axios";
import { reactive, inject } from "@vue/runtime-core";
import { useRoute, useRouter } from "vue-router";
import Switch from "@/components/switch.vue";

const toast = inject("toast");
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const form = reactive({
  name: "",
  permissions: [],
});

securedAxios.get(`/roles/${route.params.id}`).then((res) => {
  form.name = res.data.name;
  // form.permissions = res.data.permissions
  form.permissions = res.data.permissions;
});

function updateRole() {
  securedAxios
    .patch(`/roles/${route.params.id}`, form)
    .then((res) => {
      toast.success(t("general.sent_to_flow"));
      router.push({ name: "roles" });
    })
    .catch((err) => {
      toast.error(err.response.data.error);
    });
}
</script>
