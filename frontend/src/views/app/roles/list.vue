<template>
  <div v-if="!errorRes.msg">
    <Card>
      <div class="flex justify-between items-center mb-2">
        <h1>{{ $t("page_titles.roles") }}</h1>
        <router-link class="bg-indigo-500 rounded-md px-4 py-2 text-gray-50" :to="{ name: 'roles-create' }">
          <Icon name="plus" />
        </router-link>
      </div>
      <Table actionable :columns="columns" :data="roles.list" :page="Number(queries.page)"
        :per_page="Number(queries.per_page)" :total_pages="roles.total_pages" @page-change="pageChange"
        @per-page-change="perPageChange">
        <template v-slot:default="action">
          <td class="table-rows flex gap-2 justify-center h-full">
            <router-link
              class="flex items-center justify-center w-8 h-8 font-bold text-white bg-blue-500 rounded hover:bg-blue-700"
              :to="{ name: 'roles-edit', params: { id: action.item.uuid } }">
              <Icon name="pencil" class="w-4 h-4" />
            </router-link>


            <button @click="
              deleteModal.id = action.item.uuid
            deleteModal.show = true;
            "
              class="flex items-center justify-center w-8 h-8 font-bold text-white bg-red-500 rounded hover:bg-red-700">
              <Icon name="trash" classes="w-4 h-4 " />
            </button>
            <div v-if="deleteModal.show">
              <DeleteModel :deleteModal="deleteModal" @delete="deleteRole(action.item.uuid)" />
            </div>

          </td>
        </template>
      </Table>
    </Card>
  </div>
  <div class="max-w-max mx-auto" v-else>
    <div class="sm:flex items-center">
      <p class="text-4xl font-semibol text-indigo-600 sm:text-5xl">
        {{ errorRes.status }}
      </p>
      <div class="sm:ml-6">
        <div class="sm:border-l border-gray-300 dark:border-gray-700 sm:pl-6">
          <h1 class="text-xl text-gray-700 dark:text-gray-400 font-semibold">
            {{ errorRes.msg }}
          </h1>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onBeforeMount, reactive, ref, watch } from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import Table from "@/components/table.vue";

const store = useStore();
const route = useRoute();
const router = useRouter();
const errorRes = reactive({
  msg: "",
  status: "",
});
const deleteModal = ref({
  show: false,
  id: null,
  processing: false,
});
const queries = reactive({
  page: route.query.page ? route.query.page : 1,
  per_page: route.query.per_page ? route.query.per_page : 10,
});

const columns = [
  {
    row: "name",
    label: "Name",
  },
];

function deleteRole(uuid) {
  deleteModal.value.show = false;
  store.dispatch("deleteRole", uuid);
}

store
  .dispatch("getRoles", { page: queries.page, per_page: queries.per_page })
  .catch((err) => {
    errorRes.msg = err.response.data.error;
    errorRes.status = err.response.status;
  });

function pageChange(pageNum) {
  router.push({
    name: "roles",
    query: {
      page: pageNum,
      per_page: queries.per_page,
    },
  });
  queries.page = pageNum;
  store
    .dispatch("getRoles", { page: pageNum, per_page: queries.per_page })
    .catch((err) => {
      errorRes.msg = err.response.data.error;
      errorRes.status = err.response.status;
    });
}

function perPageChange(per_page) {
  router.push({
    name: "roles",
    query: {
      page: queries.page,
      per_page: per_page,
    },
  });
  queries.per_page = per_page;
  store
    .dispatch("getRoles", { page: queries.page, per_page: per_page })
    .catch((err) => {
      errorRes.msg = err.response.data.error;
      errorRes.status = err.response.status;
    });
}

const roles = computed(() => {
  // list a-z
  const list = store.getters.roles.rows.sort((a, b) => a - b);
  const total_pages = store.getters.roles.total_pages;
  const per_page = store.getters.roles.per_page;
  const page = store.getters.roles.page;
  return {
    list,
    total_pages,
    per_page,
    page,
  };
});
</script>
