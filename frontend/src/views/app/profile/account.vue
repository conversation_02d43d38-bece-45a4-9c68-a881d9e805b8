<template>
  <div class="lg:grid lg:grid-cols-12 lg:gap-x-5">
    <ProfileSidebar />

    <div class="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
      <div class="card">
        <div class="px-4 py-5 sm:px-6">
          <div class="flex justify-between">
            <h3 class="text-lg leading-6">{{ $t('profile.account.title') }}</h3>
            <LocaleSelect />
          </div>
          <form @submit.prevent="updateProfile()" class="mt-5">
            <div class="grid grid-cols-6 gap-6">
              <div class="col-span-6 md:col-span-3">
                <label class="block text-sm font-medium">{{ $t('profile.account.name') }}</label>
                <input type="text" class="input-text" required v-model="form.name" />
              </div>
              <div class="col-span-6 md:col-span-3">
                <label class="block text-sm font-medium">{{ $t('profile.account.email') }}</label>
                <input type="email" class="input-text" required v-model="form.email" />
              </div>
            </div>
            <div class="flex justify-center mt-5">
              <button
                type="submit"
                class="bg-indigo-600 px-4 py-2 rounded-md"
              >{{ $t('general.update') }}</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ProfileSidebar from '@/components/profile-sidebar.vue';
import { securedAxios } from '@/utils/axios';
import { computed, inject, reactive } from 'vue';
import { useStore } from 'vuex';
import LocaleSelect from '@/components/locale-select.vue';

const toast = inject('toast');
const store = useStore()
const currentUser = computed(() => store.getters.currentUser)

const form = reactive({
  name: currentUser.value.name,
  email: currentUser.value.email
});



function updateProfile() {
  store.dispatch('updateProfile', form).then((res) => {
    toast.success(res.data.message)
  }).catch((err) => {
    toast.error(err.response.data.error)
  })
}

</script>