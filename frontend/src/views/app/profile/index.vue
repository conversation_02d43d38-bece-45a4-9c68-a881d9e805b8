<template>
  <div class="lg:grid lg:grid-cols-12 lg:gap-x-5">
    <ProfileSidebar />

    <div class="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
      <div class="card">
        <div class="px-4 py-5 sm:px-6">
          <div class="flex justify-start">
            <h3 class="text-lg leading-6">{{ $t('profile.profile.title') }}</h3>
          </div>
        </div>
        <div class="divide-y divide-gray-200 dark:divide-gray-700">
          <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <span class="text-sm">{{ $t('profile.profile.email') }}</span>
            <span class="mt-1 text-sm sm:mt-0 sm:col-span-2">{{ CurrentUser.email }}</span>
          </div>
          <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <span class="text-sm">{{ $t('profile.profile.name') }}</span>
            <span class="mt-1 text-sm sm:mt-0 sm:col-span-2">{{ CurrentUser.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';
import ProfileSidebar from '@/components/profile-sidebar.vue';

const store = useStore()

const CurrentUser = computed(() => store.getters.currentUser);
</script>