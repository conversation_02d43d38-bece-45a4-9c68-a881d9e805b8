<template>
  <div class="lg:grid lg:grid-cols-12 lg:gap-x-5">
    <ProfileSidebar />

    <div class="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
      <div class="card">
        <div class="px-4 py-5 sm:px-6">
          <div class="flex justify-start">
            <h3 class="text-lg leading-6">{{ $t('profile.subscription.title') }}</h3>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import ProfileSidebar from '@/components/profile-sidebar.vue';
</script>