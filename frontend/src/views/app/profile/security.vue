<template>
    <div class="lg:grid lg:grid-cols-12 lg:gap-x-5">
        <ProfileSidebar />

        <div class="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
            <div class="card">
                <div class="px-4 py-5 sm:px-6">
                    <div class="flex justify-start">
                        <h3 class="text-lg leading-6">{{ $t('profile.security.title') }}</h3>
                    </div>
                </div>

                <div class="px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <span class="text-sm">{{ $t('profile.security.two-factor') }}</span>
                    <span class="mt-1 text-sm sm:mt-0 sm:col-span-2">
                        <button
                            class="px-2 py-1 rounded-md bg-green-500"
                            @click="enableTFA()"
                            v-if="!currentUser.tfa"
                        >
                            <span class="text-sm">{{ $t('profile.security.enable') }}</span>
                        </button>
                        <button
                            class="px-2 py-1 rounded-md bg-red-500"
                            @click="disableTFA()"
                            v-else
                        >
                            <span class="text-sm">{{ $t('profile.security.disable') }}</span>
                        </button>
                    </span>
                </div>
                <div class="flex flex-col gap-2" v-if="imageUrl">
                    <div class="flex justify-center px-2 py-1">
                        <img :src="imageUrl" />
                    </div>
                    <form @submit.prevent="VerifyTFA()">
                        <div class="grid grid-cols-6 gap-2 my-5">
                            <div class="col-span-6 sm:col-span-6">
                                <label
                                    for="email"
                                    class="block text-sm font-medium leading-5"
                                >2FA Code</label>
                                <div class="mt-1 rounded-md shadow-sm">
                                    <input type="text" class="input-text" v-model="form.code" />
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-center">
                            <button
                                class="px-4 py-2 rounded-md bg-green-500 hover:bg-green-700 transition my-5"
                                type="submit"
                            >Save</button>
                        </div>
                    </form>
                </div>
            </div>
            <div class="card">
                <div class="px-4 py-5 sm:px-6">
                    <div class="flex justify-between">
                        <h3 class="text-lg leading-6">{{ $t('profile.security.activities') }}</h3>
                        <button
                            class="px-2 py-1 rounded-md bg-green-500 hover:bg-green-700 transition"
                            @click="downloadActivityLog()"
                        >
                            <span class="text-sm">{{ $t('profile.security.download_activity_log') }}</span>
                        </button>
                    </div>
                </div>

                <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
                    <li class="py-4" v-for="activity in activities.rows" :key="activity.created_at">
                        <div class="flex-1 space-y-1">
                            <div class="flex items-center justify-between">
                                <h3 class="text-sm font-medium">{{ currentUser.name }}</h3>
                                <div>
                                    <p
                                        class="text-sm dark:text-gray-500"
                                    >{{ timeAgo(activity.created_at) }}</p>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <p
                                    class="text-sm dark:text-gray-500 capitalize"
                                >{{ activity.action }} {{ activity.entity }}</p>
                                <p class="text-sm dark:text-gray-400">{{ activity.ip_address }}</p>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, inject, computed } from 'vue';
import { useStore } from 'vuex';
import { timeAgo } from '@/utils/time';
import ProfileSidebar from '@/components/profile-sidebar.vue';

const store = useStore()
const imageUrl = ref('')
const toast = inject("toast")

store.dispatch('fetchActivities')

const activities = computed(() => store.getters.activities)
const currentUser = computed(() => store.getters.currentUser)

const form = ref({
    code: '',
})

function enableTFA() {
    store.dispatch('enableTwoFactor', currentUser.value.email).then((res) => {
        imageUrl.value = 'data:image/jpg;base64,'.concat(imageUrl.value.concat(res.data))
    })
}

function VerifyTFA() {
    store.dispatch('verifyTwoFactor', form.value).then(() => {
        toast.success('Two factor authentication enabled')
        imageUrl.value = ''
    }).catch((err) => {
        toast.error(err.response.data.error)
    })
}

function disableTFA() {
    store.dispatch('disableTwoFactor').then((res) => {
        toast.success('Two-factor authentication disabled.')
    }).catch((err) => {
        toast.error(err.response.data.error)
    })
}



</script>