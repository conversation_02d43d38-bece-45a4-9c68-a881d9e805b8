<template>
  <form @submit.prevent="updateOrganization()" class="space-y-2 pb-12" v-if="!loading">
    <div class="card max-w-4xl mx-auto">
      <div class="grid grid-cols-12 gap-2">
        <InputBox :span="6" :label="$t('organizations.name')" v-model="form.name" :is_required="true" />
        <div class="col-span-6 space-y-2">
          <t-select :label="$t('organizations.parent')" v-model="form.parent_id" :options="organizations"
            option-label="name" data-label="id" searchable long close-after-select />
        </div>
      </div>
    </div>

    <div class="card max-w-4xl mx-auto">
      <div class="grid grid-cols-2 gap-x-4">
        <t-file-input v-model="form.logo" :label="$t('organizations.logo')" />
        <t-file-input v-model="form.favicon" :label="$t('organizations.favicon')" />
      </div>
    </div>

    <div class="card max-w-4xl mx-auto">
      <div class="col-span-6">
        <input type="text" class="t-input" v-model="form.footer_text[footerTextLanguage]" />
      </div>
    </div>

    <div class="card max-w-4xl mx-auto">
      <t-select :label="$t('organizations.timezone')" v-model="form.timezone" :options="timezoneList" required long
        close-after-select searchable />
    </div>

    <div class="card max-w-4xl mx-auto space-y-2">
      <div class="grid grid-cols-6 gap-2">
        <InputBox :span="6" :label="$t('organizations.domain_address')" v-model="form.domain_address" />
      </div>
    </div>

    <div class="flex justify-center mt-5">
      <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md">
        {{ $t("general.update") }}
      </button>
    </div>


    <div class="fixed bottom-0 start-1/2 -translate-x-1/2 p-6 z-50 w-full max-w-md" v-if="isDirty">
      <div class="py-4 px-6  bg-stone-800 rounded-full shadow-md dark:bg-neutral-950">
        <div class="flex justify-between items-center gap-x-3">
          <button type="button" @click="cancelUpdatedData()"
            class="text-stone-300 decoration-2 font-medium hover:underline focus:outline-none focus:underline dark:text-neutral-400">Cancel</button>
          <div class="w-px h-4 bg-stone-700 dark:bg-neutral-700"></div>
          <button type="submit"
            class="text-green-400 decoration-2 font-medium hover:underline focus:outline-none focus:underline dark:text-green-500"
            href="#">Save changes</button>
        </div>
      </div>
    </div>
  </form>
</template>

<script setup>
import timezoneList from "@/constant/timezonelist";
import { useOrganizationStore } from "@/store/submodules/organization";
import { te } from "@/plugins/i18n";

const store = useOrganizationStore();

const route = useRoute();
const toast = inject("toast");
const loading = ref(true);

const footerTextLanguage = ref(localStorage.getItem("locale"));

const form = reactive({
  name: null,
  parent_id: null,
  logo: null,
  favicon: null,
  footer_text: {},
  timezone: null,
  domain_address: null,
});

const originalForm = ref("");

onMounted(async () => {
  const res = await store.Read(route.params.id);

  form.id = res.id;
  form.name = res.name;
  form.parent_id = res.parent_id === "00000000-0000-0000-0000-000000000000" ? null : res.parent_id;
  form.logo = res.logo;
  form.favicon = res.favicon;
  form.timezone = res.timezone;
  form.domain_address = res.domain_address;
  localStorage.hash_id = res.hash_id;
  loading.value = false;

  originalForm.value = JSON.stringify(form);
});

const isDirty = computed(() => {
  return originalForm.value !== JSON.stringify(form);
});

store.List(1, 1000);

const organizations = computed(() => {
  return store.getItems.rows.filter((org) => org.id !== form.id);
});

function updateOrganization() {
  const data = {
    ...toRaw(form),
  };

  store.Update(data).then((res) => {
    toast.success(res.message);
    localStorage.removeItem("hash_id");
    document.location.reload();
  }).catch((err) => {
    // Checks if translation exist
    const localeKey = "organization." + err.response.data.error;
    if (te(localeKey)) {
      toast.error(t(localeKey));
      return;
    }
    toast.error(err.response.data.error);
  });
}

onBeforeRouteLeave((to, from, next) => {
  if (isDirty.value) {
    if (confirm("You have unsaved changes. Do you really want to leave?")) {
      next();
    } else {
      next(false);
    }
  } else {
    next();
  }
});

const cancelUpdatedData = () => {
  Object.assign(form, JSON.parse(originalForm.value));
};
</script>
