<template>
  <div class="mx-auto max-w-4xl">
    <form @submit.prevent="createOrganization">
      <Wizard v-model:step="step" :steps="['organization', 'admin', 'role']">
        <template #0>
          <div class="card max-w-4xl mx-auto">
            <div class="grid grid-cols-6 gap-2">
              <InputBox :span="3" :label="$t('organizations.name')" v-model="form.organization.name"
                :error-text="form.errors['organization.name']" is_required minlength="3" />

              <div class="col-span-3 space-y-2">
                <Selectbox :label="$t('organizations.parent')" v-model="form.organization.parent_id"
                  :options="organizations" />
              </div>
              <InputBox :span="6" :label="$t('organizations.logo')" v-model="form.organization.logo" />
            </div>
          </div>
        </template>
        <template #1>
          <div class="card mx-auto max-w-4xl">
            <div class="grid grid-cols-6 gap-2">
              <InputBox :span="3" :label="$t('admins.first_name')" v-model="form.admin.first_name"
                :error-text="form.errors['admin.first_name']" is_required minlength="3" />
              <InputBox :span="3" :label="$t('admins.last_name')" v-model="form.admin.last_name"
                :error-text="form.errors['admin.last_name']" is_required minlength="3" />
              <InputBox type="email" :span="3" :label="$t('admins.email')" v-model="form.admin.email"
                :error-text="form.errors['admin.email']" is_required />
              <div class="col-span-3">
                <Selectbox v-model="form.admin.locale" :label="$t('admins.locale')" :options="[
                  {
                    label: 'English',
                    value: 'en',
                  },
                  {
                    label: 'Türkçe',
                    value: 'tr',
                  },
                ]" data-label="value" option-label="label" required />
              </div>

            </div>
          </div>
        </template>
        <template #2>
          <div class="card mx-auto max-w-4xl">
            <div class="grid grid-cols-6 mb-5">
              <div class="col-span-6">
                <label class="flex gap-1 text-sm font-medium">{{ $t('roles.role_name') }}

                </label>
                <input type="text" class="t-input" v-model="form.role.name" />
              </div>
            </div>
            <div class="hidden md:block sticky top-5 start-0 rounded-lg bg-stone-200 dark:bg-neutral-900">
              <div class="grid md:grid-cols-9 lg:gap-x-3 md:gap-x-3 p-1 md:p-3">
                <div class="col-span-5 self-center">
                  <h2 class="font-semibold text-gray-800 dark:text-neutral-200">
                    {{ $t('roles.permissions') }}
                  </h2>
                  <button type="button" @click="enableAllPermissions()"
                    class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500">
                    Toggle all
                  </button>
                </div>
                <div class="col-span-1 text-center">

                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                  </svg>

                  <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">
                    Create
                  </h3>

                  <button type="button"
                    @click="form.role.permissions.forEach(permission => permission.create = !permission.create)"
                    class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500">
                    Toggle all
                  </button>
                </div>
                <div class="col-span-1 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">
                    Read
                  </h3>

                  <button type="button"
                    @click="form.role.permissions.forEach(permission => permission.read = !permission.read)"
                    class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500">
                    Toggle all
                  </button>
                </div>
                <div class="col-span-1 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
                  </svg>

                  <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">
                    Update
                  </h3>

                  <button type="button"
                    @click="form.role.permissions.forEach(permission => permission.update = !permission.update)"
                    class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500">
                    Toggle all
                  </button>
                </div>
                <div class="col-span-1 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                  </svg>

                  <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">
                    Delete
                  </h3>
                  <button type="button"
                    @click="form.role.permissions.forEach(permission => permission.delete = !permission.delete)"
                    class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500">
                    Toggle all
                  </button>
                </div>
              </div>
            </div>

            <ul class="grid md:grid-cols-9 md:items-center gap-1.5 md:gap-6 my-3 px-3 md:px-5"
              v-for="permission in form.role.permissions" :key="permission">

              <li class="md:col-span-5">
                <p class="text-sm font-medium break-words text-gray-800 dark:text-neutral-200">
                  {{ $t(`roles.entities.${permission.entity}`) }}
                </p>
              </li>


              <li class="col-span-1">
                <div class="grid grid-cols-2 items-center md:block">
                  <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200">
                    Create
                  </span>
                  <div class="text-end md:text-center">
                    <input type="checkbox"
                      class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500  dark:bg-neutral-800 dark:border-neutral-600 checked:bg-green-500 checked:border-green-500 dark:checked:bg-green-500 dark:checked:border-green-500 dark:focus:ring-offset-gray-800"
                      v-model="permission.create">
                  </div>
                </div>
              </li>

              <li class="col-span-1">
                <div class="grid grid-cols-2 items-center md:block">
                  <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200">
                    Read
                  </span>
                  <div class="text-end md:text-center">
                    <input type="checkbox"
                      class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500  dark:bg-neutral-800 dark:border-neutral-600 checked:bg-indigo-500 checked:border-indigo-500 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800"
                      v-model="permission.read">
                  </div>
                </div>
              </li>

              <li class="col-span-1">
                <div class="grid grid-cols-2 items-center md:block">
                  <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200">
                    Update
                  </span>
                  <div class="text-end md:text-center">
                    <input type="checkbox" v-model="permission.update"
                      class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500  dark:bg-neutral-800 dark:border-neutral-600 checked:bg-yellow-500 checked:border-yellow-500 dark:checked:bg-yellow-500 dark:checked:border-yellow-500 dark:focus:ring-offset-gray-800">
                  </div>
                </div>
              </li>

              <li class="col-span-1">
                <div class="grid grid-cols-2 items-center md:block">
                  <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200">
                    Delete
                  </span>
                  <div class="text-end md:text-center">
                    <input type="checkbox" v-model="permission.delete"
                      class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500  dark:bg-neutral-800 dark:border-neutral-600 checked:bg-rose-500 checked:border-rose-500 dark:checked:bg-rose-500 dark:checked:border-rose-500 dark:focus:ring-offset-gray-800">
                  </div>
                </div>
              </li>
            </ul>

            <div class="flex justify-center mt-5">
              <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md">
                {{ $t('general.create') }}
              </button>
            </div>
          </div>
        </template>
      </Wizard>
    </form>
  </div>
</template>

<script setup>
import { useFormErrorHandler } from '@/composables/error';
import { useOrganizationStore } from '@/store/submodules/organization';

const store = useOrganizationStore()
const router = useRouter()
const toast = inject('toast')

const form = reactive({
  errors: {},
  organization: {
    name: null,
    parent_id: null,
    logo: null
  },
  admin: {
    name: null,
    first_name: null,
    last_name: null,
    email: null,
    locale: "en",
  },
  role: {
    name: null,
    permissions: [],
  },
})

const step = ref(0)

const onValidationError = (errors) => {
  const steps = {
    organization: 0,
    admin: 1,
    role: 2,
  }
  for (const key in errors) {
    for (const stepKey of Object.keys(steps)) {
      if (key.includes(stepKey)) {
        step.value = steps[stepKey]
        break
      }
    }
  }
}

const errHandler = useFormErrorHandler(form, onValidationError)

store.List(1, 1000)

const organizations = computed(() => {
  return store.getItems.rows ?? [];
});


const permissionsList = [
  "organization", "user", "role", "log", "filter", "event", "admin", "session", "profile",
]

permissionsList.forEach(element => {
  form.role.permissions.push({
    "entity": element,
    "create": true,
    "read": true,
    "update": true,
    "delete": true
  })
});

function enableAllPermissions() {
  form.role.permissions.forEach((permission) => {
    permission.create = true
    permission.read = true
    permission.update = true
    permission.delete = true
  })
}

function createOrganization() {
  store.Create({ ...form, errors: undefined }).then((res) => {
    router.push({ name: 'organization-list' })
    toast.success(res.message)
  }).catch(errHandler)
}
</script>
