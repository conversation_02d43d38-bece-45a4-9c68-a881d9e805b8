<template>
	<div class="flex items-center justify-center gap-x-6 bg-rose-900/50 py-2.5 px-6 sm:px-3.5" v-if="getMaintenance">
		<p class="text-sm leading-6 text-white">
			<svg viewBox="0 0 10 10" class="inline w-1 h-1 fill-white animate-ping" aria-hidden="true">
				<circle cx="5" cy="5" r="5" />
			</svg>
			<strong class="mx-3 font-semibold">
				Maintenance Mode Active
			</strong>
			<svg viewBox="0 0 10 10" class="inline w-1 h-1 fill-white animate-ping" aria-hidden="true">
				<circle cx="5" cy="5" r="5" />
			</svg>
			<span class="ml-3">
				<span class="hidden sm:inline">You can only read your data.</span>
			</span>
		</p>
	</div>
	<div class="flex items-center justify-between gap-x-6 bg-emerald-600 dark:bg-emerald-900/50 py-2.5 px-6 sm:px-3.5"
		v-if="isIPAActive">
		<div>

		</div>
		<div class="flex items-center gap-2 text-sm leading-6 text-white">
			<svg viewBox="0 0 10 10" class="inline w-1 h-1 fill-white animate-ping" aria-hidden="true">
				<circle cx="5" cy="5" r="5" />
			</svg>
			<svg viewBox="0 0 24 24" version="1.1"
				class="flex-shrink-0 size-5 xl:w-6 xl:h-6 text-emerald-100 dark:text-emerald-500"
				xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
				<g stroke="none" stroke-width="2" fill="currentColor" fill-rule="evenodd">
					<g id="ic_fluent_incognito_24_regular" fill-rule="nonzero">
						<path
							d="M17.5,12 C19.9852814,12 22,14.0147186 22,16.5 C22,18.9852814 19.9852814,21 17.5,21 C15.3591076,21 13.5674006,19.5049595 13.1119514,17.5019509 L10.8880486,17.5019509 C10.4325994,19.5049595 8.64089238,21 6.5,21 C4.01471863,21 2,18.9852814 2,16.5 C2,14.0147186 4.01471863,12 6.5,12 C8.81637876,12 10.7239814,13.7501788 10.9725684,16.000297 L13.0274316,16.000297 C13.2760186,13.7501788 15.1836212,12 17.5,12 Z M6.5,13.5 C4.84314575,13.5 3.5,14.8431458 3.5,16.5 C3.5,18.1568542 4.84314575,19.5 6.5,19.5 C8.15685425,19.5 9.5,18.1568542 9.5,16.5 C9.5,14.8431458 8.15685425,13.5 6.5,13.5 Z M17.5,13.5 C15.8431458,13.5 14.5,14.8431458 14.5,16.5 C14.5,18.1568542 15.8431458,19.5 17.5,19.5 C19.1568542,19.5 20.5,18.1568542 20.5,16.5 C20.5,14.8431458 19.1568542,13.5 17.5,13.5 Z M12,9.25 C15.3893368,9.25 18.5301001,9.58954198 21.4217795,10.2699371 C21.8249821,10.3648083 22.0749341,10.7685769 21.9800629,11.1717795 C21.8851917,11.5749821 21.4814231,11.8249341 21.0782205,11.7300629 C18.3032332,11.0771247 15.2773298,10.75 12,10.75 C8.72267018,10.75 5.69676679,11.0771247 2.9217795,11.7300629 C2.51857691,11.8249341 2.11480832,11.5749821 2.01993712,11.1717795 C1.92506593,10.7685769 2.17501791,10.3648083 2.5782205,10.2699371 C5.46989988,9.58954198 8.61066315,9.25 12,9.25 Z M15.7002538,3.25 C16.7230952,3.25 17.6556413,3.81693564 18.1297937,4.71158956 L18.2132356,4.88311922 L19.6853587,8.19539615 C19.8535867,8.57390929 19.683117,9.0171306 19.3046038,9.18535866 C18.9576335,9.33956772 18.5562903,9.20917654 18.3622308,8.89482229 L18.3146413,8.80460385 L16.8425183,5.49232692 C16.6601304,5.08195418 16.2735894,4.80422037 15.8336777,4.75711483 L15.7002538,4.75 L8.29974618,4.75 C7.85066809,4.75 7.43988259,4.99042719 7.21817192,5.37329225 L7.15748174,5.49232692 L5.68535866,8.80460385 C5.5171306,9.18311699 5.07390929,9.35358672 4.69539615,9.18535866 C4.34842577,9.03114961 4.17626965,8.64586983 4.27956492,8.29117594 L4.31464134,8.19539615 L5.78676442,4.88311922 C6.20217965,3.94843495 7.09899484,3.32651789 8.10911143,3.25658537 L8.29974618,3.25 L15.7002538,3.25 Z"
							id="🎨-Color">
						</path>
					</g>
				</g>
			</svg>
			<strong class="mx-3 font-semibold">
				IPA Mode for : {{ ipaData.name }}
			</strong>
			<svg viewBox="0 0 10 10" class="inline w-1 h-1 fill-white animate-ping" aria-hidden="true">
				<circle cx="5" cy="5" r="5" />
			</svg>
		</div>


		<button @click="clearIPA">
			<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
				class="flex-shrink-0 w-5 h-5">
				<path stroke-linecap="round" stroke-linejoin="round"
					d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
			</svg>
		</button>
	</div>



	<Navbar />
	<main class="flex-1">
		<div class="py-8">
			<div class="px-4 mx-auto sm:px-6 lg:px-8">
				<router-view />
			</div>
		</div>
		<Footer />
	</main>
</template>
<script setup>
import { wssUrl } from '@/constant/config';
import { useMaintenanceStore } from '@/store/submodules/maintenance';
import { useAuthStore } from '@/store/submodules/auth';
import Navbar from '@/components/navbar.vue';

const maintenanceStore = useMaintenanceStore();
const authStore = useAuthStore();


maintenanceStore.CheckMaintenance();
const getMaintenance = computed(() => {
	return maintenanceStore.getMaintenance
})

const ipaData = computed(() => {
	return authStore.getIPAData
})

const isIPAActive = computed(() => {
	return authStore.isIPAActive
})

function clearIPA() {
	authStore.ClearIPA()

	window.location.reload();

}

let idleTimer;

const idleTime = 5 * 60 * 1000;

function resetIdleTimer() {
	clearTimeout(idleTimer);
	startIdleTimer();
}

function startIdleTimer() {
	idleTimer = setTimeout(logOutUser, idleTime);
}

function logOutUser() {
	// if env dev or localhost dont logout
	if (process.env.NODE_ENV !== 'development' && window.location.hostname !== 'localhost') {
		authStore.SignOut()
	}
}

window.addEventListener("keydown", resetIdleTimer);
window.addEventListener("mousemove", resetIdleTimer);
window.addEventListener("touchmove", resetIdleTimer);
window.addEventListener("click", resetIdleTimer);
window.addEventListener("scroll", resetIdleTimer);

// İlk zamanlayıcıyı başlat
startIdleTimer();


</script>
