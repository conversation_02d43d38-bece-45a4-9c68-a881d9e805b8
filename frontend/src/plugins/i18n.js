import { createI18n } from 'vue-i18n'
import { defaultLocale, localeOptions } from '@/constant/config';

import en from "@/locales/en.json";
import tr from "@/locales/tr.json";


const messages = { en: en, tr: tr };
const locale =
    localStorage.getItem("locale") &&
        localeOptions.filter(x => x.id === localStorage.getItem("locale"))
            .length > 0
        ? localStorage.getItem("locale")
        : defaultLocale;


export const i18n = createI18n({
    legacy: false,
    locale: locale,
    fallbackLocale: defaultLocale,
    globalInjection: true,
    messages
})

export const t = i18n.global.t;

// Checks if translation exist
export const te = i18n.global.te;
