import { i18n } from "@/plugins/i18n";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import customParseFormat from "dayjs/plugin/customParseFormat";
import duration from "dayjs/plugin/duration";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import localizedFormat from "dayjs/plugin/localizedFormat";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";

import "dayjs/locale/en";
import "dayjs/locale/tr";

dayjs.extend(utc);
dayjs.extend(duration);
dayjs.extend(relativeTime);
dayjs.extend(customParseFormat);
dayjs.extend(localizedFormat);
dayjs.extend(timezone);
dayjs.extend(isSameOrAfter);

const { locale } = i18n.global;
dayjs.locale(locale.value);

dayjs.install = (app) => {
    app.config.globalProperties.$dayjs = dayjs;
};

export default dayjs;
