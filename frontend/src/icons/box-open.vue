<template>
  <svg
    :class="class" 
    fill="none"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 32 32"
    style="enable-background: new 0 0 32 32"
    xml:space="preserve"
  >
    <polyline stroke="currentColor" stroke-width="1" stroke-linejoin="round" stroke-miterlimit="10" points="26,18 26,24 16,28 6,24 6,18 " />
    <polygon stroke="currentColor" stroke-width="1" stroke-linejoin="round" stroke-miterlimit="10" points="6,12 16,8 26,12 16,16 " />
    <polyline stroke="currentColor" stroke-width="1" stroke-linejoin="round" stroke-miterlimit="10" points="16,8 13,5 3,9 6,12 " />
    <polyline stroke="currentColor" stroke-width="1" stroke-linejoin="round" stroke-miterlimit="10" points="16,8 19,5 29,9 26,12 " />
    <polyline stroke="currentColor" stroke-width="1" stroke-linejoin="round" stroke-miterlimit="10" points="6,12 3,16 13,20 16,16 " />
    <polyline stroke="currentColor" stroke-width="1" stroke-linejoin="round" stroke-miterlimit="10" points="26,12 29,16 19,20 16,16 " />
    <rect x="-216" y="-504" width="536" height="680" />
  </svg>
</template>

<script setup>
const props = defineProps({
  class: {
    type: String,
    default: "w-6 h-6",
  },
});
</script>
