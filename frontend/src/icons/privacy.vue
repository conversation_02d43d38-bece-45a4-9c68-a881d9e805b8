


<template>
    <svg :class="class" fill="none" stroke="currentColor" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg">
        <g fill="none" stroke-width="12">

            <path stroke-linecap="round"
                d="M151.8 144.5a74 74 0 0 1-85.59 19.21A74 74 0 0 1 22.42 87.7a74 74 0 0 1 59.55-64.42m28.03.06a74 74 0 0 1 50.06 35.61 74 74 0 0 1 5.915 61.15" />

            <path
                d="M76 92h40c4.432 0 8 3.568 8 8v22c0 4.432-3.568 8-8 8H76c-4.432 0-8-3.568-8-8v-22c0-4.432 3.568-8 8-8zm4 0V77.7C80 69.029 87.163 62 96 62s16 7.029 16 15.7V92" />

        </g>
    </svg>
</template>

<script setup>
const props = defineProps({
    class: {
        type: String,
        default: 'w-6 h-6 text-red-50',
    }
})
</script>
