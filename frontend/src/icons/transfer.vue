<template>
    <svg xmlns="http://www.w3.org/2000/svg" :class="class" width="40" height="40" viewBox="0 0 24 24"
        stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
        <path d="M4 18v3h16v-14l-8 -4l-8 4v3"></path>
        <path d="M4 14h9"></path>
        <path d="M10 11l3 3l-3 3"></path>
    </svg>
</template>

<script setup>
const props = defineProps({
    class: {
        type: String,
        default: 'w-6 h-6',
    }
})
</script>
