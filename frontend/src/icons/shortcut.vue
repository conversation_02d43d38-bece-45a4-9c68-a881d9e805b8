<template>
    <svg xmlns="http://www.w3.org/2000/svg" :class="classes"  viewBox="0 0 50.8 50.8" stroke-width="2"
        stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
        <path fill="none"  stroke-linecap="round" stroke-linejoin="round"
        stroke-width="3.175"
        d="M5.82 41.451s9.994-13.552 23.549-10.495v9.143l15.61-15.463-15.61-15.111v10.319S5.82 23.107 5.82 41.452z"
        style="paint-order:markers stroke fill" />
    </svg>
</template>

<script setup>
const props = defineProps({
    classes: {
        type: String,
        default: 'w-6 h-6',
    }
})
</script>