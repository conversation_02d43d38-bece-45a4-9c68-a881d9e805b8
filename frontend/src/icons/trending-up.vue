


<template>
    <svg :class="class" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
        aria-hidden="true">
        <path
            d="M3 17L8.29289 11.7071C8.68342 11.3166 9.31658 11.3166 9.70711 11.7071L12.2929 14.2929C12.6834 14.6834 13.3166 14.6834 13.7071 14.2929L21 7M21 7H16M21 7V12"
            stroke="#FFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
</template>

<script setup>
const props = defineProps({
    class: {
        type: String,
        default: 'w-6 h-6',
    }
})
</script>
