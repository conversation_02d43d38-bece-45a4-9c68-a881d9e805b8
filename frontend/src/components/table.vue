<template>
    <div>
        <div class="overflow-x-auto">
            <div class="inline-block min-w-full">
                <div class="overflow-hidden sm:rounded-lg flex items-center justify-center">
                    <div class=" text-center px-4 pt-6 pb-4" v-if="loading">
                        <div class="px-4 ">
                            <Spinner size="20" />
                        </div>
                        <div class="px-4 py-3 sm:px-6">
                            <span class="text-2xl text-white">{{
                                $t("general.loading")
                            }}</span>
                        </div>
                    </div>
                    <div class="bg-gray-900 text-center px-4 py-6" v-else-if="error.msg">
                        <div class="px-4">
                            <Icon name="box-open" classes="w-20 h-20 text-gray-400 mx-auto" />
                        </div>
                        <div class="px-4 py-3 sm:px-6">
                            <span class="text-2xl text-white">{{
                                $t("general.error_loading")
                            }}</span>
                        </div>
                    </div>
                    <noData icon="box-open" pageSize v-else-if="dataCount < 1">
                        <template #title>
                            {{ noDataTitle || $t("general.no_records") }}
                        </template>
                        <template v-if="noDataActionDescription" #description>
                            {{ noDataActionDescription }}
                        </template>
                        <template #action v-if="noDataRoute">
                            <router-link :to="{ name: noDataRoute }" class="mt-2">
                                <Button kind="green">
                                    {{ noDataActionText }}
                                </Button>
                            </router-link>
                        </template>
                    </noData>
                    <!--  <div
                        class="dark:bg-gray-800 flex items-center justify-center  min-h-[300px]"
                        v-else-if="dataCount < 1"
                    >
                        <div class="flex flex-col justify-center items-center gap-4 px-4 mb-7">
                            <Icon
                                name="box-open"
                                classes="w-20 h-20 text-gray-900 mx-auto dark:text-white"
                            />
                            <div class="px-4 sm:px-6  ">
                            <span class="text-2xl text-gray-900 dark:text-white">{{
                                $t("general.no_records")
                            }}</span>
                        </div>
                        </div>
                    
                    </div> -->
                    <table class="min-w-full overflow-auto  divide-gray-800" v-else>
                        <thead class="table-head" v-if="!hide_header">
                            <tr>
                                <th scope="col" class="table-col max-w-[18rem]" @click="sortTable(col)"
                                    v-for="(col, index) in columns" :class="{
                                        'hidden md:table-cell': !col.mobile,
                                    }" :key="index">
                                    <span class="flex gap-2 items-center select-none" :class="{
                                        'cursor-pointer hover:bg-gray-700':
                                            col.sortable,
                                    }">
                                        {{ col.label }}
                                        <svg class="w-4 h-4 transition transform" :class="[
                                            sort.field === col.row &&
                                                sort.order === 'desc'
                                                ? 'rotate-180'
                                                : null,
                                        ]" v-if="col.sortable" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" :stroke-width="[
                                                sort.field === col.row
                                                    ? 4
                                                    : 2,
                                            ]" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </span>
                                </th>
                                <th scope="col" class="table-col text-center" v-if="actionable">
                                    {{ $t("components.m_table.actions") }}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="table-body">
                            <slot name="line" :data="filteredData" :columns="columns" v-if="slots.line" />
                            <tr v-else v-for="item in filteredData" :key="item" class="table-tr">
                                <td class="table-rows truncate max-w-[18rem]" v-for="col in columns" :key="col" :class="{
                                    'hidden md:table-cell': !col.mobile,
                                }">
                                    <Icon v-if="col.row == 'direction' &&
                                        item[col.row] == 'cycle'
                                        " name="arrow-cycle" />
                                    <Icon v-else-if="col.row == 'direction' &&
                                        item[col.row] == 'in'
                                        " name="arrow-in" />
                                    <Icon v-else-if="col.row == 'direction' &&
                                        item[col.row] == 'out'
                                        " name="arrow-out" />
                                    <span :title="col.modifier ? col.modifier(item[col.row]) : item[col.row]" v-else>
                                        {{ col.modifier ? col.modifier(item[col.row]) : item[col.row] }}
                                    </span>
                                </td>
                                <slot :item="item" v-if="actionable" />
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-md py-2 flex items-center justify-between" v-if="total_pages > 0">
            <div class="flex-1 flex justify-between sm:hidden">
                <button @click="setPage(Number(page) - 1)" :disabled="page == 1"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50">
                    {{ $t("general.previous") }}
                </button>
                <button @click="setPage(Number(page) + 1)" :disabled="page == total_pages"
                    class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50">
                    {{ $t("general.next") }}
                </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm">
                        {{
                            $t("general.show_page", {
                                page: page,
                                total_pages: total_pages,
                            })
                        }}
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <button @click="setPage(Number(page) - 1)" :disabled="page == 1"
                            :class="{ 'cursor-not-allowed': page == 1 }"
                            class="relative inline-flex items-center px-2 py-2 rounded-l-md bg-gray-200 dark:bg-gray-900 hover:bg-gray-400">
                            <span class="sr-only">{{
                                $t("general.previous")
                            }}</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div v-for="(pagenum, index) in showablePages" :key="index">
                            <button aria-current="page" v-if="pagenum == 1 ||
                                pagenum == total_pages ||
                                Math.abs(pagenum - page) < 3
                                " @click="setPage(pagenum)"
                                class="relative inline-flex items-center px-4 py-2 text-sm font-medium"
                                :disabled="pagenum == page" :class="[
                                    page == pagenum
                                        ? 'bg-slate-700 text-white'
                                        : 'bg-white dark:bg-gray-700  hover:bg-gray-50',
                                ]">
                                {{ pagenum }}
                            </button>
                        </div>
                        <button @click="setPage(Number(page) + 1)" :disabled="page == total_pages" :class="{
                            'cursor-not-allowed': page == total_pages,
                        }"
                            class="relative inline-flex items-center px-2 py-2 rounded-r-md bg-gray-200 dark:bg-gray-900 hover:bg-gray-400">
                            <span class="sr-only">{{
                                $t("general.next")
                            }}</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
                                aria-hidden="true">
                                <path fill-rule="evenodd"
                                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                        </button>
                    </nav>
                </div>
                <div>
                    <select class="select min-w-[123px]" v-model="perpage">
                        <option :value="5">5</option>
                        <option :value="10">10</option>
                        <option :value="50">50</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, watch, ref, reactive, useSlots } from "vue";
import Spinner from "@/components/spinner.vue";
import Icon from "@/components/icon.vue";

const slots = useSlots();

const props = defineProps({
    data: {
        default: () => [],
    },
    columns: {
        type: Array,
        default: () => [],
    },
    emptyText: {
        type: String,
        default: "No data",
    },
    page: {
        type: Number,
    },
    per_page: {
        type: Number,
    },
    total_pages: {
        type: Number,
    },
    hide_header: {
        type: Boolean,
        default: false,
    },
    actionable: {
        type: Boolean,
        default: false,
    },
    loading: {
        type: Boolean,
        default: false,
    },
    error: {
        type: Object,
        default: {
            msg: "",
            status: "",
        },
    },
    noDataRoute: {
        type: String,
    },
    noDataTitle: {
        type: String,
    },
    noDataActionText: {
        type: String,
    },
    noDataActionDescription: {
        type: String,
    }
});

const columnCount = computed(() => {
    return props.columns.length + 1;
});

const dataCount = computed(() => {
    return props.data ? props.data.length : 0;
});

const emits = defineEmits(["page-change", "per-page-change"]);

function setPage(page) {
    emits("page-change", page);
}

const perpage = ref(props.per_page);

watch(
    () => perpage.value,
    (newVal) => {
        emits("per-page-change", newVal);
    }
);

const sort = reactive({
    order: "asc",
    field: null,
});

const showablePages = computed(() => {
    // 2 before current page, 2 after current page, 1 current page
    // if there are less than 5 pages, show all pages
    if (props.total_pages < 5) {
        return props.total_pages;
    }

    let pages = [];
    let start = props.page - 2;
    let end = props.page + 2;
    if (start < 1) {
        start = 1;
        end = 5;
    }
    if (end > props.total_pages) {
        end = props.total_pages;
        start = props.total_pages - 4;
    }
    for (let i = start; i <= end; i++) {
        pages.push(i);
    }
    return pages;
});

const filteredData = computed(() => {
    return props.data.sort((a, b) => {
        if (sort.field) {
            if (sort.order === "asc") {
                return a[sort.field] > b[sort.field] ? 1 : -1;
            } else {
                return a[sort.field] < b[sort.field] ? 1 : -1;
            }
        }
        return props.data;
    });
});
function sortTable(field) {
    if (field.sortable) {
        sort.field = field.row;
        sort.order === "asc" ? (sort.order = "desc") : (sort.order = "asc");
    }
}
</script>
