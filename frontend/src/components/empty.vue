<template>
    <div :class="{ 'border-t border-slate-200': !noBorder }">
        <div class="max-w-2xl m-auto" :class="{
            'mt-16': !noMargin,
        }">
            <div class="text-center px-4">
                <div
                    class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-t from-slate-200 to-slate-100 mb-4">
                    <slot name="icon">
                        <svg class="w-5 h-6 fill-current" viewBox="0 0 20 24">
                            <path class="text-slate-500" d="M10 10.562l9-5-8.514-4.73a1 1 0 00-.972 0L1 5.562l9 5z" />
                            <path class="text-slate-300" d="M9 12.294l-9-5v10.412a1 1 0 00.514.874L9 23.294v-11z" />
                            <path class="text-slate-400" d="M11 12.294v11l8.486-4.714a1 1 0 00.514-.874V7.295l-9 4.999z" />
                        </svg>
                    </slot>
                </div>
                <slot name="title">
                    <h2 class="text-2xl  font-bold mb-2">
                        {{ noTranslate ? title : $t(`${title}`) }}
                    </h2>
                </slot>
                <div class="mb-6">
                    <slot name="description"> {{ noTranslate ? description : $t(`${description}`) }}</slot>
                </div>
                <slot name="actions"></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    title: String,
    description: String,
    noMargin: Boolean,
    noBorder: Boolean,
    noTranslate: Boolean,
});
</script>
