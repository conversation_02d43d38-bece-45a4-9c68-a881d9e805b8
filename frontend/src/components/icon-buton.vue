<template>
  <button :class="`${iconClass[icon]}`">
    <svg :class="`${iconColor[icon]} ${sizes[size]} `" fill="none" stroke="currentColor" viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="`${icons[icon]}`" />
    </svg>
  </button>
</template>
<script setup>
import { defineProps } from "vue";

const icons = {
  view: "M12.406 13.844c1.188 0 2.156 0.969 2.156 2.156s-0.969 2.125-2.156 2.125-2.125-0.938-2.125-2.125 0.938-2.156 2.125-2.156zM12.406 8.531c7.063 0 12.156 6.625 12.156 6.625 0.344 0.438 0.344 1.219 0 1.656 0 0-5.094 6.625-12.156 6.625s-12.156-6.625-12.156-6.625c-0.344-0.438-0.344-1.219 0-1.656 0 0 5.094-6.625 12.156-6.625zM12.406 21.344c2.938 0 5.344-2.406 5.344-5.344s-2.406-5.344-5.344-5.344-5.344 2.406-5.344 5.344 2.406 5.344 5.344 5.344z",
  edit: "M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z",
  accept: "M5 13l4 4L19 7",
  delete:
    "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16",
};

const iconClass = {
  view: "px-1 py-1 rounded-md hover:bg-gray-900  tranition border  border-blue-700",
  edit: "px-1 py-1 rounded-md hover:bg-gray-800 tranition border border-blue-700",
  accept: "px-1 py-1 rounded-md hover:bg-gray-800  border border-green-700",
  delete: "px-1 py-1 rounded-md hover:bg-gray-800  border border-red-700",
};
const iconColor = {
  view: "text-blue-500",
  edit: "text-blue-500",
  accept: "text-green-500",
  delete: "text-red-500",
};

const sizes = {
  xs: "w-4 h-4",
  sm: "w-5 h-5",
  md: "w-6 h-6",
  lg: "w-7 h-7",
};
defineProps({
  size: {
    type: String,
    default: "md",
    validator: (value) => {
      return ["2xs", "xs", "sm", "md", "lg"].includes(value);
    },
  },

  icon: {
    type: String,
    default: "primary",
    validator: (value) => {
      return ["view", "edit", "delete"].includes(value);
    },
  },
  loading: {
    type: Boolean,
  },
  disabaled: {
    type: Boolean,
    default: false,
  },
});
</script>
