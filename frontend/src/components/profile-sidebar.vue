<template>
    <aside class="py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3">
        <nav class="space-y-1 card">
            <router-link
                :to="{ name: 'profile' }"
                exact
                class="rounded-md px-3 py-2 flex items-center text-sm font-medium"
                exact-active-class="bg-gray-300 dark:bg-gray-900"
            >
                <svg
                    class="text-gray-600 dark:text-gray-100 flex-shrink-0 -ml-1 mr-3 h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                </svg>
                <span class="truncate">{{ $t('profile.sidebar.profile') }}</span>
            </router-link>
            <router-link
                :to="{ name: 'profile-account' }"
                exact
                class="rounded-md px-3 py-2 flex items-center text-sm font-medium"
                exact-active-class="bg-gray-100 dark:bg-gray-900"
            >
                <svg
                    class="text-gray-600 dark:text-gray-100 flex-shrink-0 -ml-1 mr-3 h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    />
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                </svg>
                <span class="truncate">{{ $t('profile.sidebar.account') }}</span>
            </router-link>
            <router-link
                :to="{ name: 'profile-security' }"
                exact
                class="rounded-md px-3 py-2 flex items-center text-sm font-medium"
                exact-active-class="bg-gray-100 dark:bg-gray-900"
            >
                <svg
                    class="text-gray-600 dark:text-gray-100 flex-shrink-0 -ml-1 mr-3 h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
                    />
                </svg>
                <span class="truncate">{{ $t('profile.sidebar.security') }}</span>
            </router-link>
            <router-link
                :to="{ name: 'profile-subscription' }"
                exact
                class="rounded-md px-3 py-2 flex items-center text-sm font-medium"
                exact-active-class="bg-gray-100 dark:bg-gray-900"
            >
                <svg
                    class="text-gray-600 dark:text-gray-100 flex-shrink-0 -ml-1 mr-3 h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                    />
                </svg>
                <span class="truncate">{{ $t('profile.sidebar.subscription') }}</span>
            </router-link>
            <router-link
                :to="{ name: 'profile-notification' }"
                exact
                class="rounded-md px-3 py-2 flex items-center text-sm font-medium"
                exact-active-class="bg-gray-100 dark:bg-gray-900"
            >
                <svg
                    class="text-gray-600 dark:text-gray-100 flex-shrink-0 -ml-1 mr-3 h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
                    />
                </svg>
                <span class="truncate">{{ $t('profile.sidebar.notification') }}</span>
            </router-link>
        </nav>
    </aside>
</template>

<script setup>

</script>

