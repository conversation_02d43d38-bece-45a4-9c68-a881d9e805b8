<template>
    <div class="relative space-y-1" ref="dropdown">
        <button @click="route.children ? show = !show : null" v-if="route.children"
            class="dark:text-gray-300 dark:hover:bg-slate-900 transition w-full flex justify-between items-center px-2 py-2 text-sm font-medium rounded-md ">
            {{ route.name }}
            <svg class="h-5 w-5 transform transition" xmlns="http://www.w3.org/2000/svg"
                :class="[show ? 'rotate-180' : 'rotate-0']" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"
                v-if="route.children">
                <path fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
            </svg>
        </button>
        <router-link :to="{ name: route.route }"
            class="dark:text-gray-300 dark:hover:bg-slate-700 transition dark:hover:text-white flex items-center px-2 py-2 text-sm font-medium rounded-md "
            exact-active-class="bg-gray-200 dark:bg-slate-900" exact v-else>{{ $t(`routes.${route.route}`) }}
        </router-link>
        <transition enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95">
            <div class="absolute z-50 min-w-[12rem] rounded-md px-1 bg-gray-200 dark:bg-slate-900 mx-3 my-2 py-2 flex flex-col"
                v-if="route.children && show">
                <router-link
                    class="transition hover:bg-gray-400 dark:hover:bg-slate-700 hover:text-gray-100 text-xs px-4 py-2 rounded-md "
                    v-for="child in route.children" :key="child" :to="{ name: child.route }">
                    {{ $t(`routes.${child.route}`) }}
                </router-link>
            </div>
        </transition>
    </div>
</template>

<script setup>
import { onBeforeUnmount, ref } from 'vue';
const props = defineProps({
    route: {
        type: Object,
    },
})

const show = ref(false);
const dropdown = ref(null);

function clickOutside(e) {
    if (dropdown.value && !dropdown.value.contains(e.target)) {
        show.value = false;
    }
}
document.addEventListener('click', clickOutside);
onBeforeUnmount(() => {
    document.removeEventListener('click', clickOutside);
});
</script>
