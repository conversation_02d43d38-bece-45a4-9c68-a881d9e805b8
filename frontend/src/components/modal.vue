<template>
    <div class="relative z-10" aria-labelledby="modal-title" role="dialog" aria-modal="true">

        <transition enter-active-class="ease-out duration-300" enter-from-class="opacity-0" enter-to-class="opacity-100"
            leave-active-class="ease-in duration-200" leave-from-class="opacity-100" leave-to-class="opacity-0"
            v-show="modelValue">
            <div class="fixed inset-0 bg-black/75" @click="close()"></div>
        </transition>


        <transition enter-active-class="ease-out duration-300"
            enter-from-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to-class="opacity-100 translate-y-0 sm:scale-100" leave-active-class="ease-in duration-200"
            leave-to-class="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <div class="fixed inset-0 z-10 overflow-y-auto" v-show="modelValue">
                <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"
                    @click.self="close">
                    <div class="relative transform overflow-hidden rounded-lg  text-left shadow-xl transition-all sm:my-8 sm:w-full"
                        :class="classes">
                        <div class="bg-white dark:bg-gray-800" :class="{ 'px-4 pt-5 pb-4 sm:p-6 sm:pb-4': !isPaddingless }">
                            <slot name="content" />
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:flex sm:justify-between  sm:px-6"
                            v-if="$slots[`footer`]">
                            <slot name="footer" />
                        </div>
                    </div>

                </div>
            </div>
        </transition>
    </div>
</template>

<script setup>
import { computed } from 'vue'
const props = defineProps({
    modelValue: Boolean,
    size: {
        type: String,
        default: "lg",
    },
    isPaddingless: {
        type: Boolean,
        default: false
    }
});
const classes = computed(() => {
    let classesTemp = []

    switch (props.size) {
        case "sm":
            classesTemp.push("sm:max-w-sm")
            break;
        case "md":
            classesTemp.push("sm:max-w-md")
            break;
        case "lg":
            classesTemp.push("sm:max-w-lg")
            break;
        case "xl":
            classesTemp.push("sm:max-w-xl")
            break;
        case "2xl":
            classesTemp.push("sm:max-w-2xl")
            break;
        case "3xl":
            classesTemp.push("sm:max-w-3xl")
            break;
        case "4xl":
            classesTemp.push("sm:max-w-4xl")
            break;
        case "5xl":
            classesTemp.push("sm:max-w-5xl")
            break;
        case "6xl":
            classesTemp.push("sm:max-w-6xl")
            break;
        case "7xl":
            classesTemp.push("sm:max-w-7xl")
            break;

        default:
            break;
    }

    return classesTemp.join(" ")
})

const emits = defineEmits(['update:modelValue']);
function close() {
    emits("update:modelValue", !props.modelValue)
}

function testoAler() {
    alert("testo")
}

</script>