<template>
	<div class="flex flex-col bg-white divide-y divide-gray-100 rounded-md dark:bg-gray-800 dark:divide-gray-600 shadow-std"
		:class="[bordered ? 'border' : '']">
		<header v-if="title || description || $slots.header" class="px-4 py-4">
			<div class="flex justify-between">
				<h3 v-if="title" class="text-xl whitespace-nowrap">{{ title }}</h3>

				<div class="flex w-full gap-3" v-if="$slots.header">
					<slot name="header" />
				</div>
			</div>

			<p v-if="description" class="text-sm text-gray-500 dark:text-gray-400/80">{{ description }}</p>

		</header>
		<section class="flex-1" :class="padding ? 'p-4' : ''">
			<slot />
		</section>
		<footer v-if="$slots.footer" class="px-4 py-2 mt-auto bg-gray-50 dark:bg-gray-800 dark:text-white">
			<slot name="footer" />
		</footer>
	</div>
</template>

<script>
export default {
	name: 'mpCard',
	props: {
		title: {
			type: String,
			required: false,
			default: null,
		},
		description: {
			type: String,
			required: false,
			default: null,
		},
		bordered: {
			type: Boolean,
			required: false,
			default: false,
		},
		padding: {
			type: Boolean,
			required: false,
			default: true,
		},
	},
}
</script>
