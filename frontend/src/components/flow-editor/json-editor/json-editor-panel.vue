<template>
  <div class="json-editor-panel" :class="{ 'visible': isVisible }" :style="editorStyle" ref="editorPanel"
    v-show="isVisible">
    <div class="json-editor-header" @mousedown="startDrag" @touchstart="startDrag">
      <h3>Simulator</h3>
      <div class="drag-handle">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    <div class="json-editor-content">
      <div class="split-editor">
        <!-- Left Side for Request Editor -->
        <div class="editor-section request-section">
          <div class="section-header">
            <span>Request</span>
            <button class="run-button" @click="executeRequest">
              <span class="run-icon">▶</span> Run
            </button>
          </div>
          <div class="editor-container" :style="{ height: requestEditorHeight }">
            <json-editor v-model="requestJsonData" :options="requestEditorOptions" class="jse-theme-dark"
              @change="updateRequestHeight" />
          </div>
        </div>

        <!-- Right Side for Request Editor -->
        <div class="editor-section output-section">
          <div class="section-header">
            <span>Output</span>
          </div>
          <div class="editor-container" :style="{ height: outputEditorHeight }">
            <json-editor v-model="outputJsonData" :options="outputEditorOptions" class="jse-theme-dark"
              @change="updateOutputHeight" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, nextTick, onBeforeUnmount, reactive } from 'vue';
import JsonEditor from 'json-editor-vue';
import 'vanilla-jsoneditor/themes/jse-theme-dark.css'
import { useSimulateStore } from '@/store/submodules/simulate';

const { t } = useI18n();
const store = useSimulateStore();
const route = useRoute();

import { editNodeAndEdge } from '../utils/graphUtils'

// Props
const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  initialJson: {
    type: Object,
    default: () => ({})
  },
  nodes: {
    type: Array,
    default: () => []
  },
  edges: {
    type: Array,
    default: () => []
  },
  simulateInfo: {
    type: Object,
    default: () => ({ id: "", name: "" })
  }
});

// Emits
const emit = defineEmits(['update:isVisible', 'applyJson']);

const requestJsonData = ref(props.initialJson || {});
const outputJsonData = ref({});
const editorPanel = ref(null);

const requestEditorHeight = ref('300px');
const outputEditorHeight = ref('300px');
const minEditorHeight = 150;
const maxEditorHeight = 600;

function updateRequestHeight() {
  calculateEditorHeight(requestJsonData.value, requestEditorHeight);
}

function updateOutputHeight() {
  calculateEditorHeight(outputJsonData.value, outputEditorHeight);
}

function calculateEditorHeight(jsonData, heightRef) {
  const jsonString = JSON.stringify(jsonData, null, 2);
  const lineCount = (jsonString.match(/\n/g) || []).length + 1;
  const lineHeight = 20;
  const estimatedHeight = Math.max(
    minEditorHeight,
    Math.min(lineCount * lineHeight + 40, maxEditorHeight)
  );

  heightRef.value = `${estimatedHeight}px`;
}

const requestEditorOptions = {
  mode: 'code',
  mainMenuBar: false,
  navigationBar: false,
  statusBar: true,
  colorPicker: true,
  language: 'tr-TR',
  search: true,
  history: true
};

const outputEditorOptions = {
  mode: 'code',
  mainMenuBar: false,
  navigationBar: false,
  statusBar: true,
  colorPicker: false,
  search: true,
  history: false,
  readOnly: true
};

const position = ref({ x: 0, y: 0 });
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

function startDrag(event) {
  const clientX = event.clientX || (event.touches && event.touches[0].clientX);
  const clientY = event.clientY || (event.touches && event.touches[0].clientY);

  if (clientX === undefined || clientY === undefined) return;

  isDragging.value = true;

  const rect = editorPanel.value.getBoundingClientRect();

  dragOffset.value = {
    x: clientX - rect.left,
    y: clientY - rect.top
  };

  document.addEventListener('mousemove', onDrag);
  document.addEventListener('touchmove', onDrag, { passive: false });
  document.addEventListener('mouseup', stopDrag);
  document.addEventListener('touchend', stopDrag);

  event.preventDefault();
}

function onDrag(event) {
  if (!isDragging.value) return;

  const clientX = event.clientX || (event.touches && event.touches[0].clientX);
  const clientY = event.clientY || (event.touches && event.touches[0].clientY);

  if (clientX === undefined || clientY === undefined) return;

  if (event.touches) {
    event.preventDefault();
  }

  position.value = {
    x: clientX - dragOffset.value.x,
    y: clientY - dragOffset.value.y
  };

  const rect = editorPanel.value.getBoundingClientRect();
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;

  if (position.value.x + rect.width > windowWidth) {
    position.value.x = windowWidth - rect.width;
  }

  if (position.value.x < 0) {
    position.value.x = 0;
  }

  if (position.value.y + rect.height > windowHeight) {
    position.value.y = windowHeight - rect.height;
  }

  if (position.value.y < 0) {
    position.value.y = 0;
  }
}

function stopDrag() {
  isDragging.value = false;

  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('touchmove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('touchend', stopDrag);
}

const editorStyle = computed(() => {
  if (position.value.x === 0 && position.value.y === 0) {
    return {
      left: '50%',
      bottom: '5%',
      transform: 'translateX(-50%)',
      position: 'fixed'
    };
  } else {
    return {
      left: `${position.value.x}px`,
      top: `${position.value.y}px`,
      transform: 'none',
      position: 'fixed'
    };
  }
});

watch(() => props.initialJson, (newVal) => {
  requestJsonData.value = newVal || {};
  nextTick(() => {
    updateRequestHeight();
  });
}, { immediate: true });

watch(() => outputJsonData.value, () => {
  nextTick(() => {
    updateOutputHeight();
  });
});

watch(() => props.isVisible, (newVal) => {
  if (newVal) {
    if (position.value.x === 0 && position.value.y === 0) {
    }

    nextTick(() => {
      updateRequestHeight();
      updateOutputHeight();
    });
  }
}, { immediate: true });

const simulate_payload = reactive({
  id: "",
  name: "",
  context: {},
  content: {},  
  errors: {},
});

function executeRequest() {
  try {
    const editedData = editNodeAndEdge(props.nodes, props.edges)
    const requestData = requestJsonData.value;

    let contextData = {};
    if (typeof requestData === 'string') {
      try {
        contextData = JSON.parse(requestData);
      } catch (e) {
        console.error('JSON parse error:', e);
        contextData = { raw: requestData };
      }
    } else if (typeof requestData === 'object' && requestData !== null) {
      contextData = requestData;
    } else {
      contextData = { value: requestData };
    }

    simulate_payload.id = route.params.id
    simulate_payload.context = contextData;
    simulate_payload.content = {
      nodes: editedData.nodes,
      edges: editedData.edges
    };


    // if (props.simulateInfo && props.simulateInfo.id) {
    //   simulate_payload.id = props.simulateInfo.id;
    //   simulate_payload.name = props.simulateInfo.name || "";
    // } else {
    //   simulate_payload.id = "";
    //   simulate_payload.name = "";
    // }

    store
      .Simulate(simulate_payload)
      .then((res) => {
        outputJsonData.value = res.result;
        nextTick(() => {
          updateOutputHeight();
        });
      })
      .catch((err) => {
        console.error('Simulation Error:', err);
        outputJsonData.value = { error: err.message || 'Simulation failed' };
        nextTick(() => {
          updateOutputHeight();
        });
      });



  } catch (error) {
    console.error('error:', error);
    outputJsonData.value = { error: error.message };
    nextTick(() => {
      updateOutputHeight();
    });
  }
}

onMounted(() => {
  nextTick(() => {
    updateRequestHeight();
    updateOutputHeight();
  });
});

function getRequestData() {
  return requestJsonData.value;
}

defineExpose({
  getRequestData,
  executeRequest
});

onBeforeUnmount(() => {
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('touchmove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
  document.removeEventListener('touchend', stopDrag);
});
</script>

<style scoped>
.json-editor-panel {
  width: 1200px;
  background-color: #1e1e1e;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1000;
  min-width: 800px;
}

.json-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: #4a90e2;
  border-bottom: 1px solid #383838;
  cursor: move;
  user-select: none;
  color: white;
}

.json-editor-header h3 {
  margin: 0;
  font-size: 16px;
}

.drag-handle {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.drag-handle span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 2px;
}

.json-editor-content {
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

/* Bölünmüş editör stili */
.split-editor {
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

.editor-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-right: 1px solid #383838;
}

.editor-section:last-child {
  border-right: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #2d2d2d;
  border-bottom: 1px solid #383838;
  color: white;
}

.run-button {
  display: flex;
  align-items: center;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.run-button:hover {
  background-color: #45a049;
}

.run-icon {
  margin-right: 4px;
  font-size: 10px;
}

.editor-container {
  overflow: auto;
  transition: height 0.3s ease;
}

@media (max-width: 992px) {
  .json-editor-panel {
    width: 90%;
    min-width: 300px;
  }

  .split-editor {
    flex-direction: column;
  }

  .editor-section {
    border-right: none;
    border-bottom: 1px solid #383838;
  }

  .editor-section:last-child {
    border-bottom: none;
  }
}
</style>