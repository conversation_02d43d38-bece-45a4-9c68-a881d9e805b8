<template>
  <div class="flow-editor-container">
    <div class="topbar">
      <div class="topbar-buttons">
        <a href="#" class="topbar-link" @click.prevent="handleNew">New</a>
        <a href="#" class="topbar-link" @click.prevent="handleOpen">Open</a>
        <a href="#" class="topbar-link" @click.prevent="handleSave">Save</a>
      </div>

      <div class="topbar-name">
        <input
          v-model="simulateName"
          type="text"
          placeholder="Click to edit name..."
          class="name-input"
          @input="onNameChange"
          @keyup.enter="$event.target.blur()"
          title="Click to edit simulate name"
        />
      </div>
    </div>
    
    <!-- Flow Editor -->
    <div class="dnd-flow" @drop="onDrop">
      <VueFlow
        :min-zoom="0.2"
        :max-zoom="4"
        :nodes="nodes"
        :edges="edges"
        :node-types="nodeTypes"
        @dragover="onDragOver"
        @dragleave="onDragLeave"
        @connect="onConnect"
        @update:node="onNodeUpdate"
      >
        <SaveAndRestore ref="saveAndRestore" />
        <DropzoneBackground
          :style="{
            backgroundColor: isDragOver ? '#e7f3ff' : 'transparent',
            transition: 'background-color 0.2s ease',
          }"
        >
          <p v-if="isDragOver">Drop here</p>
        </DropzoneBackground>
        <Controls  />
        <MiniMap pannable zoomable />

        <Panel position="top-right" class="custom-controls">
          <button @click="toggleSidebar" class="sidebar-toggle-btn" :title="sidebarCollapsed ? 'Show Components' : 'Hide Components'">
            <svg v-if="sidebarCollapsed" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect width="18" height="18" x="3" y="3" rx="2"></rect>
              <path d="M9 3v18"></path>
              <path d="m14 9 3 3-3 3"></path>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect width="18" height="18" x="3" y="3" rx="2"></rect>
              <path d="M9 3v18"></path>
              <path d="m16 15-3-3 3-3"></path>
            </svg>
          </button>
        </Panel>
      </VueFlow>
      <Sidebar />
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRoute } from 'vue-router'
  import { VueFlow, useVueFlow, Panel } from '@vue-flow/core'
  import { Controls } from '@vue-flow/controls'
  import { MiniMap } from '@vue-flow/minimap'

  import DropzoneBackground from '@/components/flow-editor/sidebar/dropzone-background.vue';
  import Sidebar from '@/components/flow-editor/sidebar/sidebar.vue';
  import useDragAndDrop from '@/components/flow-editor/sidebar/useDnD';
  import { useSimulateStore } from '@/store/submodules/simulate';

  // -----> our custom nodes
  import InputNode from './nodes/input-node.vue'
  import OutputNode from './nodes/output-node.vue'
  import SwitchNode from './nodes/switch-node.vue'
  import TimeoutNode from './nodes/timeout-node.vue'
  import FunctionNode from './nodes/function-node.vue'
  import SaveAndRestore from './save/controls.vue'
  import { editNodeAndEdge } from './utils/graphUtils'

  import '@vue-flow/core/dist/style.css'
  import '@vue-flow/core/dist/theme-default.css'
  import '@vue-flow/minimap/dist/style.css'
  import '@vue-flow/node-resizer/dist/style.css'
  import '@vue-flow/controls/dist/style.css'

  import './styles/editor.css'

  const { addEdges, updateNode, getNodes, getEdges } = useVueFlow()
  const { onDragOver, onDrop, onDragLeave, isDragOver } = useDragAndDrop()
  const store = useSimulateStore();
  const toast = inject("toast");
  const route = useRoute();
  const router = useRouter();

  const nodeTypes = {
    inputNode: InputNode, 
    outputNode: OutputNode,
    switchNode: SwitchNode,
    timeoutNode: TimeoutNode,
    functionNode: FunctionNode,
  }

const onConnect = (params) => {
  // find node type
  const sourceNode = getNodes.value.find(node => node.id === params.source)

  const newEdge = {
    id: crypto.randomUUID(),
    source: params.source,
    target: params.target,
    sourceHandle: params.sourceHandle,
    // targetHandle: params.targetHandle, 
    type: 'edge',
    animated: true,
    style: {
      stroke: '#ff0072',
      strokeWidth: 2,
      transition: 'stroke-dasharray 1s ease-in-out',
    },
    markerEnd: {
      type: 'arrow',
      width: 10,
      height: 10,
    },
  }

  addEdges(newEdge)
}

const onNodeDataUpdate = (nodeId, newData) => {
  updateNode(nodeId, () => ({
    data: newData
  }));
}

const onNodeUpdate = (nodeUpdates) => {
  console.log('Node update received:', nodeUpdates);
}

import { onMounted, onBeforeUnmount } from 'vue'

onMounted(() => {
  window.addEventListener('node:update', (event) => {
    const { id, data } = event.detail;
    onNodeDataUpdate(id, data);
  });
});

onBeforeUnmount(() => {
  window.removeEventListener('node:update', (event) => {
    const { id, data } = event.detail;
    onNodeDataUpdate(id, data);
  });
})
  
const nodes = ref([])
const edges = ref([])
const sidebarCollapsed = ref(false)
const saveAndRestore = ref(null)
const simulateName = ref("Untitled Simulate") // Default name for new simulates

function handleNew() {
  // TODO: localization
  if (confirm('Are you sure you want to create new blank decision, your current work might be lost?')) {
    window.location.reload()
  }
}

function handleOpen() {
  const savedFlow = localStorage.getItem('vue-flow--saved-flow')
  if (savedFlow) {
    try {
      const flow = JSON.parse(savedFlow)
      nodes.value = flow.nodes || []
      edges.value = flow.edges || []
      console.log('Flow loaded from localStorage')
    } catch (error) {
      console.error('Error loading flow:', error)
      alert('Failed to load the flow. The saved data might be corrupted.')
    }
  } else {
    alert('No saved flow found.')
  }
}

//-----> we are creating new simulate, cos of it we have used status 1
const simulate_save_payload = reactive({
  name: "",
  context: {},
  content: {},
  status: 1,
  errors: {},
});

function onNameChange() {
  simulate_save_payload.name = simulateName.value;
}

function handleSave() {
  if (confirm('Are you sure you want to save it?')) {
    try {
      const editedData = editNodeAndEdge(getNodes.value, getEdges.value)
      const requestData = saveAndRestore.value?.getRequestData() || {}

      let contextData = {};
      if (typeof requestData === 'string') {
        try {
          contextData = JSON.parse(requestData);
        } catch (e) {
          console.error('JSON parse error:', e);
          contextData = { raw: requestData };
        }
      } else if (typeof requestData === 'object' && requestData !== null) {
        contextData = requestData;
      } else {
        contextData = { value: requestData };
      }

      simulate_save_payload.name = simulateName.value;
      simulate_save_payload.context = contextData;
      simulate_save_payload.content = {
        nodes: editedData.nodes,
        edges: editedData.edges
      };
      simulate_save_payload.status = 1;

      store
        .SimulateUpdateOrCreate(simulate_save_payload)
        .then((res) => {
          // TODO: localization
          toast.success("successfully saved");
          router.push({ name: "workspace-edit", params: { id: res.id } });
        })
        .catch((err) => {
          toast.error(err.response.data.error); 
        });
    } catch (error) {
      toast.error(err.response.data.error); 
    }
  }
}

function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value
  const event = new CustomEvent('toggle-sidebar', {
    detail: { collapsed: sidebarCollapsed.value }
  })
  window.dispatchEvent(event)
}
</script>

<style scoped>
.flow-editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.topbar {
  display: flex;
  align-items: center;
  background-color: #2d3748;
  padding: 0 15px;
  height: 34px;
  border-bottom: 1px solid #4a5568;
}

.topbar-buttons {
  display: flex;
  gap: 12px;
}

.topbar-link {
  color: #e2e8f0;
  text-decoration: none;
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 3px;
  transition: background-color 0.2s, color 0.2s;
}

.topbar-link:hover {
  background-color: #4a5568;
  color: white;
}

.dnd-flow {
  position: relative;
  display: flex;
  flex-direction: row;
  width: 100%;
  flex-grow: 1;
  overflow: hidden;
}

.dnd-flow .vue-flow-wrapper {
  flex-grow: 1;
  height: 100%;
  transition: all 0.3s ease-in-out;
}

/* Custom Controls Panel */
.custom-controls {
  background: rgba(45, 55, 72, 0.9);
  border-radius: 8px;
  padding: 4px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-toggle-btn {
  background: none;
  border: none;
  color: #e2e8f0;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
}

.sidebar-toggle-btn:hover {
  background-color: rgba(74, 85, 104, 0.8);
  color: white;
  transform: scale(1.05);
}
</style>