<template>
  <div class="output-node" :class="{ selected: selected }">
    <div class="output-node-header">
      <div class="output-node-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M19 12H5"></path>
          <path d="m12 19-7-7 7-7"></path>
        </svg>
      </div>
      <div class="output-node-title" v-if="!isEditing" @click="startEditing">
        {{ data.label || 'Response' }}
      </div>
      <input
        v-else
        ref="titleInput"
        v-model="editableLabel"
        class="title-input"
        @blur="stopEditing"
        @keyup.enter="stopEditing"
        @keyup.esc="cancelEditing"
        type="text"
      />

      <div class="node-menu" @click.stop>
        <button class="menu-trigger" @click="toggleMenu" :class="{ active: showMenu }">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="1"></circle>
            <circle cx="12" cy="5" r="1"></circle>
            <circle cx="12" cy="19" r="1"></circle>
          </svg>
        </button>

        <div v-if="showMenu" class="menu-dropdown" @click.stop>
          <button class="menu-item" @click="openDocumentation">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
            </svg>
            Documentation
          </button>
          <button class="menu-item" @click="duplicateNode">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect>
              <path d="M4 16c-1.1 0-2-.9-2 2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path>
            </svg>
            Duplicate
          </button>
          <button class="menu-item delete" @click="deleteNode">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
            </svg>
            Delete
          </button>
        </div>
      </div>
    </div>
    <div class="output-node-content">
      <slot></slot>
    </div>
    <NodeResizer 
      v-if="selected" 
      :min-width="150" 
      :min-height="50" 
      :is-visible="selected" 
    />
    <Handle 
      id="target" 
      type="target" 
      position="left" 
    />
  </div>
</template>

<script setup>
import { Handle, useVueFlow } from '@vue-flow/core'
import { NodeResizer } from '@vue-flow/node-resizer'
import '@vue-flow/node-resizer/dist/style.css'
import { ref, nextTick } from 'vue'

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
})

const emit = defineEmits(['update:data'])

const { removeNodes, addNodes } = useVueFlow()

const isEditing = ref(false)
const editableLabel = ref('')
const titleInput = ref(null)
const showMenu = ref(false)

function startEditing() {
  editableLabel.value = props.data.label || 'Response'
  isEditing.value = true
  
  nextTick(() => {
    titleInput.value.focus()
  })
}

function stopEditing() {
  if (isEditing.value) {
    isEditing.value = false
    if (editableLabel.value.trim()) {
      const updatedData = { ...props.data, label: editableLabel.value.trim() }
      emit('update:data', updatedData)
      const updateEvent = new CustomEvent('node:update', {
        detail: { id: props.id, data: updatedData }
      })
      window.dispatchEvent(updateEvent)
    }
  }
}

function cancelEditing() {
  isEditing.value = false
}

function toggleMenu() {
  showMenu.value = !showMenu.value
}

function openDocumentation() {
  showMenu.value = false
  alert('Documentation for Response Node')
}

function duplicateNode() {
  showMenu.value = false

  const newId = crypto.randomUUID()

  const duplicatedNode = {
    id: newId,
    type: 'outputNode',
    position: {
      x: props.position?.x + 50 || 50,
      y: props.position?.y + 50 || 50
    },
    data: {
      ...props.data,
      label: `${props.data.label || 'Response'} (Copy)`
    }
  }

  addNodes([duplicatedNode])
}

function deleteNode() {
  showMenu.value = false
  removeNodes([props.id])
}
</script>

<style scoped>
.output-node {
  padding: 10px;
  border-radius: 8px;
  background-color: #fff0f5;
  border: 2px solid #e05297;
  width: 180px;
  color: #333;
  font-family: sans-serif;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.output-node.selected {
  box-shadow: 0 0 0 2px #ff6b6b;
  border-color: #ff6b6b;
}

.output-node-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
  margin-bottom: 8px;
}

.output-node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e05297;
  color: white;
  border-radius: 4px;
  padding: 4px;
  margin-right: 8px;
}

.output-node-title {
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
}

.output-node-title:hover {
  color: #e05297;
  text-decoration: underline dotted;
}

.title-input {
  font-weight: bold;
  font-size: 14px;
  border: 1px solid #e05297;
  border-radius: 4px;
  padding: 2px 4px;
  width: 100px;
  outline: none;
}

.output-node-content {
  font-size: 12px;
  padding: 4px 0;
}

/* Menu styles */
.node-menu {
  position: relative;
  margin-left: auto;
}

.menu-trigger {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.menu-trigger:hover,
.menu-trigger.active {
  background-color: #e0e0e0;
  color: #333;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 140px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item.delete {
  color: #dc3545;
}

.menu-item.delete:hover {
  background-color: #ffeaea;
}
</style>