<template>
  <div class="function-node">
    <Handle
      id="input"
      type="target"
      :position="Position.Left"
      :style="{ background: '#555' }"
    />

    <div class="function-node-header">
      <div class="function-node-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M3 3h18v18H3V3zm16.525 13.707c-.131-.821-.666-1.511-2.252-2.155-.552-.259-1.165-.438-1.349-.854-.068-.248-.078-.382-.034-.529.113-.484.687-.629 1.137-.495.293.09.563.315.732.676.775-.507.775-.507 1.316-.844-.203-.314-.304-.451-.439-.586-.473-.528-1.103-.798-2.126-.77l-.528.067c-.507.124-.991.395-1.283.754-.855.968-.608 2.655.427 3.354 1.023.765 2.521.933 2.712 1.653.18.878-.652 1.159-1.475 1.058-.607-.136-.945-.439-1.316-1.002l-1.372.788c.157.359.337.517.607.832 1.305 1.316 4.568 1.249 5.153-.754.021-.067.18-.528.056-1.237l.034.049zm-6.737-5.434h-1.686c0 1.453-.007 2.898-.007 4.354 0 .924.047 1.772-.104 2.033-.247.517-.886.451-1.175.359-.297-.146-.448-.349-.623-.641-.047-.078-.082-.146-.095-.146l-1.368.844c.229.473.563.879.994 1.137.641.383 1.502.507 2.404.305.588-.17 1.095-.519 1.358-1.059.384-.697.302-1.553.299-2.509.008-1.541 0-3.083 0-4.635l.003-.042z"/>
        </svg>
      </div>
      <div class="function-node-title" v-if="!isEditing" @click="startEditing">
        {{ nodeLabel || 'Function' }}
      </div>
      <input v-else ref="titleInput" v-model="editableLabel" class="title-input" @blur="stopEditing"
        @keyup.enter="stopEditing" @keyup.esc="cancelEditing" type="text" />

      <div class="node-menu" @click.stop>
        <button class="menu-trigger" @click="toggleMenu" :class="{ active: showMenu }">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="1"></circle>
            <circle cx="12" cy="5" r="1"></circle>
            <circle cx="12" cy="19" r="1"></circle>
          </svg>
        </button>

        <div v-if="showMenu" class="menu-dropdown" @click.stop>
          <button class="menu-item" @click="openDocumentation">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
            </svg>
            Documentation
          </button>

          <button class="menu-item delete" @click="deleteNode">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
            </svg>
            Delete
          </button>
        </div>
      </div>
    </div>

    <div class="function-node-content" @dblclick="openEditor">
      <div class="edit-hint">Double-click to edit code</div>
    </div>

    <Handle
      id="output"
      type="source"
      :position="Position.Right"
      :style="{ background: '#555' }"
    />

    <div v-if="showEditor" class="editor-overlay" @click="closeEditor" @mousedown.stop @mousemove.stop @mouseup.stop>
      <div class="editor-modal" @click.stop @mousedown.stop @mousemove.stop @mouseup.stop>
        <div class="editor-header">
          <input v-model="nodeLabel" placeholder="Function Name" class="node-name-input" />
          <button @click="closeEditor" class="close-btn">×</button>
        </div>

        <div class="editor-body">
          <div ref="editorContainer" class="codemirror-container"></div>
        </div>

        <div class="editor-footer">
          <button @click="saveFunction" class="save-btn">Save Function</button>
          <button @click="closeEditor" class="cancel-btn">Cancel</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onUnmounted } from 'vue'
import { Handle, Position, useVueFlow } from '@vue-flow/core'
import { EditorView } from '@codemirror/view'
import { EditorState } from '@codemirror/state'
import { javascript } from '@codemirror/lang-javascript'
import { basicSetup } from 'codemirror'

// Dark theme definition
const darkTheme = EditorView.theme({
  '&': {
    color: '#ffffff',
    backgroundColor: '#1e1e1e'
  },
  '.cm-content': {
    padding: '16px',
    caretColor: '#ffffff'
  },
  '.cm-focused .cm-cursor': {
    borderLeftColor: '#ffffff'
  },
  '.cm-focused .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection': {
    backgroundColor: '#264f78'
  },
  '.cm-panels': {
    backgroundColor: '#252526',
    color: '#ffffff'
  },
  '.cm-panels.cm-panels-top': {
    borderBottom: '2px solid #007acc'
  },
  '.cm-panels.cm-panels-bottom': {
    borderTop: '2px solid #007acc'
  },
  '.cm-searchMatch': {
    backgroundColor: '#72a1ff59',
    outline: '1px solid #457dff'
  },
  '.cm-searchMatch.cm-searchMatch-selected': {
    backgroundColor: '#6199ff2f'
  },
  '.cm-activeLine': {
    backgroundColor: '#2a2d2e'
  },
  '.cm-selectionMatch': {
    backgroundColor: '#72a1ff59'
  },
  '.cm-matchingBracket, .cm-nonmatchingBracket': {
    backgroundColor: '#bad0f847',
    outline: '1px solid #515a6b'
  },
  '.cm-gutters': {
    backgroundColor: '#1e1e1e',
    color: '#858585',
    border: 'none'
  },
  '.cm-activeLineGutter': {
    backgroundColor: '#2a2d2e'
  },
  '.cm-foldPlaceholder': {
    backgroundColor: 'transparent',
    border: 'none',
    color: '#ddd'
  },
  '.cm-tooltip': {
    border: 'none',
    backgroundColor: '#353a42'
  },
  '.cm-tooltip .cm-tooltip-arrow:before': {
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent'
  },
  '.cm-tooltip .cm-tooltip-arrow:after': {
    borderTopColor: '#353a42',
    borderBottomColor: '#353a42'
  },
  '.cm-tooltip-autocomplete': {
    '& > ul > li[aria-selected]': {
      backgroundColor: '#2c313a',
      color: '#ffffff'
    }
  }
}, { dark: true })

const props = defineProps({
  id: String,
  data: Object,
})

const emit = defineEmits(['update:data'])

const { removeNodes } = useVueFlow()

// Editor state
const showEditor = ref(false)
const jsCode = ref(props.data?.jsCode || "import zen from 'zen';\n\n/** @type {Handler} **/\nexport const handler = async (input) => {\n  return input;\n};\n")
const nodeLabel = ref(props.data?.label || 'Function')
const editorContainer = ref(null)
let editorView = null

const showMenu = ref(false)
const isEditing = ref(false)
const editableLabel = ref('')
const titleInput = ref(null)

function openEditor() {
  showEditor.value = true
  nextTick(() => {
    initCodeMirror()
  })
}

function closeEditor() {
  if (editorView) {
    editorView.destroy()
    editorView = null
  }
  showEditor.value = false
}

function initCodeMirror() {
  if (!editorContainer.value) return

  const state = EditorState.create({
    doc: jsCode.value,
    extensions: [
      basicSetup,
      javascript(),
      darkTheme,
      EditorView.updateListener.of((update) => {
        if (update.docChanged) {
          jsCode.value = update.state.doc.toString()
        }
      })
    ]
  })

  editorView = new EditorView({
    state,
    parent: editorContainer.value
  })
}

// Menu functions
function toggleMenu() {
  showMenu.value = !showMenu.value
}

function startEditing() {
  isEditing.value = true
  editableLabel.value = nodeLabel.value
  showMenu.value = false
  nextTick(() => {
    if (titleInput.value) {
      titleInput.value.focus()
      titleInput.value.select()
    }
  })
}

function stopEditing() {
  if (editableLabel.value.trim()) {
    nodeLabel.value = editableLabel.value.trim()
    updateNodeData()
  }
  isEditing.value = false
}

function cancelEditing() {
  isEditing.value = false
  editableLabel.value = ''
}

function updateNodeData() {
  const newData = {
    ...props.data,
    label: nodeLabel.value
  }

  emit('update:data', newData)

  // CustomEvent dispatch
  const updateEvent = new CustomEvent('node:update', {
    detail: { id: props.id, data: newData }
  })
  window.dispatchEvent(updateEvent)
}

function openDocumentation() {
  // TODO: Open documentation
  console.log('Opening documentation for function node')
  showMenu.value = false
}

function deleteNode() {
  showMenu.value = false
  removeNodes([props.id])
}

function saveFunction() {
  const newData = {
    ...props.data,
    jsCode: jsCode.value,
    label: nodeLabel.value
  }

  emit('update:data', newData)

  // CustomEvent dispatch
  const updateEvent = new CustomEvent('node:update', {
    detail: { id: props.id, data: newData }
  })
  window.dispatchEvent(updateEvent)

  closeEditor()
}

onUnmounted(() => {
  if (editorView) {
    editorView.destroy()
  }
})
</script>

<style scoped>
.function-node {
  position: relative;
  background: white;
  border: 2px solid #10b981;
  border-radius: 8px;
  min-width: 200px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 8px;
}

.function-node-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px;
  margin-bottom: 8px;
}

.function-node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #10b981;
  color: white;
  border-radius: 4px;
  padding: 6px;
  margin-right: 12px;
}

.function-node-title {
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  flex: 1;
  min-width: 0;
  color: #333;
}

.function-node-title:hover {
  color: #10b981;
  text-decoration: underline dotted;
}

.title-input {
  font-weight: bold;
  font-size: 14px;
  border: 1px solid #10b981;
  border-radius: 4px;
  padding: 2px 4px;
  width: 120px;
  outline: none;
  flex: 1;
  min-width: 0;
  color: #333;
  background: white;
}

.function-node-content {
  font-size: 12px;
  padding: 12px 8px;
  cursor: pointer;
  color: #666;
  text-align: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.function-node-content:hover {
  background-color: #f8f9fa;
  color: #10b981;
}

.edit-hint {
  font-style: italic;
}

/* Menu styles */
.node-menu {
  position: relative;
  margin-left: auto;
}

.menu-trigger {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.menu-trigger:hover,
.menu-trigger.active {
  background-color: #e0e0e0;
  color: #333;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 140px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item.delete {
  color: #dc3545;
}

.menu-item.delete:hover {
  background-color: #ffeaea;
}

/* Editor Modal Styles */
.editor-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(2px);
  padding: 20px;
  box-sizing: border-box;
  pointer-events: auto;
}

.editor-modal {
  background: #2d2d2d;
  border-radius: 8px;
  width: 90vw;
  max-width: 1600px;
  min-width: 1000px;
  height: 55vh;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
  border: 1px solid #404040;
  position: relative;
  transform: none !important;
  pointer-events: auto;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #404040;
  background: #252526;
}

.node-name-input {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  flex: 1;
  margin-right: 16px;
}

.node-name-input:focus {
  outline: none;
  background: #3e3e3e;
}

.node-name-input::placeholder {
  color: #888888;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #cccccc;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
  flex-shrink: 0;
}

.close-btn:hover {
  background: #3e3e3e;
  color: #ffffff;
}

.editor-body {
  padding: 0;
  flex: 1;
  overflow: hidden;
  display: flex;
  background: #2d2d2d;
}

.codemirror-container {
  border: none;
  border-radius: 0;
  overflow: hidden;
  flex: 1;
  background: #1e1e1e;
}

.codemirror-container .cm-editor {
  height: 100%;
}

.codemirror-container .cm-focused {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.editor-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px 20px;
  border-top: 1px solid #404040;
  background: #252526;
}

.save-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: background 0.2s;
}

.save-btn:hover {
  background: #059669;
}

.cancel-btn {
  background: #3e3e3e;
  color: #cccccc;
  border: 1px solid #555555;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.cancel-btn:hover {
  background: #4a4a4a;
  color: #ffffff;
}
</style>
