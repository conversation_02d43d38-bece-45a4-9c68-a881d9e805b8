<template>
  <div class="switch-node" :class="{ selected: selected }">
    <div class="switch-node-header">
      <div class="switch-node-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M12 2 L20 12 L12 22 L4 12 Z" fill="currentColor" stroke="none"/>
          <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" stroke="white" stroke-width="1.5" fill="none"/>
          <circle cx="12" cy="17" r="1" fill="white"/>
        </svg>
      </div>
      <div class="switch-node-title" v-if="!isEditing" @click="startEditing">
        {{ data.label || 'Switch' }}
      </div>
      <input v-else ref="titleInput" v-model="editableLabel" class="title-input" @blur="stopEditing"
        @keyup.enter="stopEditing" @keyup.esc="cancelEditing" type="text" />
      
      <!-- Three dots menu -->
      <div class="node-menu" @click.stop>
        <button class="menu-trigger" @click="toggleMenu" :class="{ active: showMenu }">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="1"></circle>
            <circle cx="12" cy="5" r="1"></circle>
            <circle cx="12" cy="19" r="1"></circle>
          </svg>
        </button>
        
        <div v-if="showMenu" class="menu-dropdown" @click.stop>
          <button class="menu-item" @click="openDocumentation">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
            </svg>
            Documentation
          </button>
          <button class="menu-item" @click="duplicateNode">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect>
              <path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path>
            </svg>
            Duplicate
          </button>
          <button class="menu-item delete" @click="deleteNode">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
            </svg>
            Delete
          </button>
        </div>
      </div>
    </div>

    <!-- Conditions List -->
    <div class="conditions-container">
      <div v-for="(condition, index) in conditions" :key="condition.id" class="condition-wrapper">
        <div class="condition-item">
          <input
            v-model="condition.expression"
            @input="() => updateCondition(index, condition.expression)"
            class="condition-input"
            placeholder=""
          />
          <button @click="removeCondition(index)" class="delete-condition-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
            </svg>
          </button>
        </div>

      </div>
    </div>

    <!-- Output handles for each condition - positioned on node level -->
    <Handle
      v-for="(condition, index) in conditions"
      :key="condition.id"
      :id="condition.id"
      type="source"
      :position="Position.Right"
      :style="{
        top: `${74 + (index * 40)}px`,
        background: '#667eea',
        border: '2px solid white',
        width: '12px',
        height: '12px',
        zIndex: 1000 + index,
        pointerEvents: 'all'
      }"
    />

    <!-- Bottom Controls -->
    <div class="bottom-controls">
      <button @click="addCondition" class="add-condition-btn">
        Add Condition
      </button>
      
      <div class="mode-selector">
        <select v-model="mode" @change="updateMode" class="mode-dropdown">
          <option value="first">First</option>
          <option value="collect">Collect</option>
        </select>
      </div>
    </div>

    <!-- Input handle -->
    <Handle :id="`${id}-input`" type="target" :position="Position.Left" :style="{ top: '20px' }" />
    
    <!-- Node Resizer -->
    <NodeResizer v-if="selected" min-width="280" min-height="150" />
  </div>
</template>

<script setup>
import { Handle, useVueFlow, Position } from '@vue-flow/core'
import { NodeResizer } from '@vue-flow/node-resizer'
import '@vue-flow/node-resizer/dist/style.css'
import { ref, nextTick } from 'vue'

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
})

const emit = defineEmits(['update:data'])

const { removeNodes, addNodes } = useVueFlow()

const isEditing = ref(false)
const editableLabel = ref('')
const titleInput = ref(null)
const showMenu = ref(false)

// Switch node specific data
const conditions = ref(props.data.conditions || [
  { id: crypto.randomUUID(), expression: 'true' }
])
const mode = ref(props.data.mode || 'first')



// Title editing functions
function startEditing() {
  isEditing.value = true
  editableLabel.value = props.data.label || 'Switch'
  nextTick(() => {
    titleInput.value?.focus()
    titleInput.value?.select()
  })
}

function stopEditing() {
  isEditing.value = false
  const newData = { ...props.data, label: editableLabel.value }
  emit('update:data', newData)
}

function cancelEditing() {
  isEditing.value = false
}

// Menu functions
function toggleMenu() {
  showMenu.value = !showMenu.value
}

function openDocumentation() {
  showMenu.value = false
  alert('Documentation for Switch Node')
}

function duplicateNode() {
  showMenu.value = false
  
  const newId = `${props.id}_copy_${Date.now()}`
  
  const duplicatedNode = {
    id: newId,
    type: 'switch',
    position: {
      x: props.position?.x + 50 || 50,
      y: props.position?.y + 50 || 50
    },
    data: {
      ...props.data,
      label: `${props.data.label || 'Switch'} (Copy)`,
      conditions: [...conditions.value],
      mode: mode.value
    }
  }
  
  addNodes([duplicatedNode])
}

function deleteNode() {
  showMenu.value = false
  removeNodes([props.id])
}

// Condition management
function logCurrentHandlers() {
  const handlers = conditions.value.map((condition, index) => ({
    handleId: condition.id,
    index: index,
    expression: condition.expression,
    isDefault: condition.isDefault
  }))
}

function addCondition() {
  const newCondition = {
    id: crypto.randomUUID(),
    expression: '',
    isDefault: false
  }
  conditions.value.push(newCondition)
  logCurrentHandlers()
  updateNodeData()
}

function removeCondition(index) {
  if (conditions.value.length > 1) { // Keep at least one condition
    conditions.value.splice(index, 1)
    logCurrentHandlers()
    updateNodeData()
  }
}

function updateCondition(index, expression) {
  conditions.value[index].expression = expression
  updateNodeData()
}

function updateMode() {
  updateNodeData()
}

function updateNodeData() {
  const newData = {
    ...props.data,
    conditions: conditions.value,
    mode: mode.value
  }
  emit('update:data', newData)
  const updateEvent = new CustomEvent('node:update', {
    detail: { id: props.id, data: newData }
  })
  window.dispatchEvent(updateEvent)
}
</script>

<style scoped>
.switch-node {
  background: #f8f9fa;
  border: 2px solid #667eea;
  border-radius: 8px;
  padding: 12px;
  min-width: 280px;
  min-height: 150px;
  color: #333;
  font-family: sans-serif;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
}

.switch-node.selected {
  box-shadow: 0 0 0 2px #ff6b6b;
  border-color: #ff6b6b;
}

.switch-node-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
  margin-bottom: 12px;
}

.switch-node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #667eea;
  color: white;
  border-radius: 4px;
  padding: 4px;
  margin-right: 8px;
}

.switch-node-title {
  font-weight: bold;
  font-size: 14px;
  cursor: pointer;
  flex: 1;
}

.switch-node-title:hover {
  color: #667eea;
  text-decoration: underline dotted;
}

.title-input {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.title-input:focus {
  outline: none;
  border-color: #667eea;
}

.conditions-container {
  margin-bottom: 12px;
}

.condition-wrapper {
  position: relative;
  margin-bottom: 12px;
}

.condition-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px 6px 8px;
  border: 1px solid #e0e0e0;
  border-radius: 3px;
  background: white;
  min-height: 28px;
}

.condition-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 2px 6px;
  font-size: 11px;
  color: #333;
  line-height: 1.3;
}

.condition-input:focus {
  outline: none;
}

.condition-wrapper:focus-within .condition-item {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.delete-condition-btn {
  background: rgba(229, 62, 62, 0.8);
  border: none;
  border-radius: 2px;
  padding: 2px;
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  width: 16px;
  height: 16px;
}

.delete-condition-btn:hover {
  background: rgba(229, 62, 62, 1);
}

.bottom-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.add-condition-btn {
  background: #667eea;
  border: 1px solid #667eea;
  border-radius: 3px;
  padding: 4px 8px;
  color: white;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.2s ease;
}

.add-condition-btn:hover {
  background: #5a67d8;
  border-color: #5a67d8;
}

.mode-selector {
  position: relative;
}

.mode-dropdown {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
}

.mode-dropdown:focus {
  outline: none;
  border-color: #667eea;
}

/* Menu styles */
.node-menu {
  position: relative;
  margin-left: auto;
}

.menu-trigger {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.menu-trigger:hover,
.menu-trigger.active {
  background-color: #e0e0e0;
  color: #333;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 140px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  color: #333;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item.delete {
  color: #dc3545;
}

.menu-item.delete:hover {
  background-color: #ffeaea;
}
</style>
