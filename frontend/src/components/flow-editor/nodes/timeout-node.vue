<template>
  <div class="timeout-node" :class="{ selected: selected }">
    <div class="timeout-node-header">
      <div class="timeout-node-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"/>
          <polyline points="12,6 12,12 16,14"/>
        </svg>
      </div>
      <div class="timeout-node-title" v-if="!isEditing" @click="startEditing">
        {{ data.label || 'Timeout' }}
      </div>
      <input v-else ref="titleInput" v-model="editableLabel" class="title-input" @blur="stopEditing"
        @keyup.enter="stopEditing" @keyup.esc="cancelEditing" type="text" />

      <div class="node-menu" @click.stop>
        <button class="menu-trigger" @click="toggleMenu" :class="{ active: showMenu }">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="1"></circle>
            <circle cx="12" cy="5" r="1"></circle>
            <circle cx="12" cy="19" r="1"></circle>
          </svg>
        </button>

        <div v-if="showMenu" class="menu-dropdown" @click.stop>
          <button class="menu-item" @click="openDocumentation">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
              <polyline points="14,2 14,8 20,8"></polyline>
            </svg>
            Documentation
          </button>

          <button class="menu-item delete" @click="deleteNode">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
            </svg>
            Delete
          </button>
        </div>
      </div>
    </div>
    <div class="timeout-node-content">
      <div class="timeout-config">
        <div class="timeout-header">
          <label class="timeout-label">Timeout:</label>
          <span class="timeout-value">{{ timeoutValue }}s</span>
        </div>
        <div class="slider-container">
          <input
            v-model.number="timeoutValue"
            @input="updateTimeout"
            type="range"
            min="1"
            max="15"
            step="1"
            class="timeout-slider"
          />
          <div class="slider-track">
            <div class="slider-progress" :style="{ width: progressWidth }"></div>
          </div>
        </div>
        <div class="timeout-info">
          <span class="timeout-min">1s</span>
          <span class="timeout-max">15s</span>
        </div>
      </div>
    </div>

    <Handle :id="`${id}-input`" type="target" :position="Position.Left" :style="{ top: '20px' }" />
    <Handle :id="`${id}-output`" type="source" :position="Position.Right" :style="{ top: '20px' }" />

    <!-- Node Resizer -->
    <NodeResizer v-if="selected" min-width="180" min-height="100" />
  </div>
</template>

<script setup>
import { Handle, useVueFlow, Position } from '@vue-flow/core'
import { NodeResizer } from '@vue-flow/node-resizer'
import '@vue-flow/node-resizer/dist/style.css'
import { ref, nextTick, computed } from 'vue'

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  position: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  },
})

const emit = defineEmits(['update:data'])

const { removeNodes } = useVueFlow()

const isEditing = ref(false)
const editableLabel = ref('')
const titleInput = ref(null)
const showMenu = ref(false)

const timeoutValue = computed({
  get() {
    return props.data.timeout || 5
  },
  set(value) {
    const clampedValue = Math.max(1, Math.min(15, parseInt(value) || 1))
    updateNodeData({ timeout: clampedValue })
  }
})

const progressWidth = computed(() => {
  const value = timeoutValue.value
  const percentage = ((value - 1) / (15 - 1)) * 100
  return `${percentage}%`
})

function startEditing() {
  isEditing.value = true
  editableLabel.value = props.data.label || 'Timeout'
  nextTick(() => {
    titleInput.value?.focus()
    titleInput.value?.select()
  })
}

function stopEditing() {
  isEditing.value = false
  const newLabel = editableLabel.value.trim() || 'Timeout'
  updateNodeData({ label: newLabel })
}

function cancelEditing() {
  isEditing.value = false
}

function updateTimeout(event) {
  let value = parseInt(event.target.value)

  if (isNaN(value) || value < 1) {
    value = 1
  } else if (value > 15) {
    value = 15
  }

  event.target.value = value
  updateNodeData({ timeout: value })
}

function updateNodeData(newData) {
  const updatedData = { ...props.data, ...newData }
  emit('update:data', updatedData)

  const updateEvent = new CustomEvent('node:update', {
    detail: { id: props.id, data: updatedData }
  })
  window.dispatchEvent(updateEvent)
}

function toggleMenu() {
  showMenu.value = !showMenu.value
}

function openDocumentation() {
  showMenu.value = false
  // TODO: Open documentation for timeout node
  alert('Documentation for Timeout Node')
}

function deleteNode() {
  showMenu.value = false
  removeNodes([props.id])
}
</script>

<style scoped>
.timeout-node {
  padding: 10px;
  border-radius: 8px;
  background-color: #f7fafc;
  border: 2px solid #e53e3e;
  width: 180px;
  color: #1a202c;
  font-family: sans-serif;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.timeout-node.selected {
  box-shadow: 0 0 0 2px #ff6b6b;
  border-color: #ff6b6b;
}

.timeout-node-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.timeout-node-icon {
  width: 32px;
  height: 32px;
  background-color: #e53e3e;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  flex-shrink: 0;
}

.timeout-node-icon svg {
  color: white;
}

.timeout-node-title {
  flex: 1;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  color: #000000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title-input {
  flex: 1;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  font-weight: 600;
  background-color: white;
  color: #000000;
  min-width: 0;
}

.title-input:focus {
  outline: none;
  border-color: #e53e3e;
  box-shadow: 0 0 0 1px #e53e3e;
}

.node-menu {
  position: relative;
  margin-left: 8px;
}

.menu-trigger {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #718096;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-trigger:hover,
.menu-trigger.active {
  background-color: #edf2f7;
  color: #2d3748;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  min-width: 150px;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #1a202c;
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  gap: 8px;
}

.menu-item:hover {
  background-color: #f7fafc;
}

.menu-item.delete {
  color: #e53e3e;
}

.menu-item.delete:hover {
  background-color: #fed7d7;
}

.timeout-node-content {
  color: #000000;
}

.timeout-config {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.timeout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timeout-label {
  font-size: 12px;
  font-weight: 600;
  color: #000000;
}

.timeout-value {
  font-size: 14px;
  font-weight: 700;
  color: #e53e3e;
  background: #fed7d7;
  padding: 2px 8px;
  border-radius: 12px;
  min-width: 32px;
  text-align: center;
}

.slider-container {
  position: relative;
  height: 20px;
  display: flex;
  align-items: center;
}

.timeout-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e2e8f0;
  outline: none;
  opacity: 0;
  position: absolute;
  cursor: pointer;
  z-index: 2;
}

.slider-track {
  width: 100%;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.slider-progress {
  height: 100%;
  background: linear-gradient(90deg, #e53e3e 0%, #c53030 100%);
  border-radius: 3px;
  transition: width 0.2s ease;
  position: relative;
}

.slider-progress::after {
  content: '';
  position: absolute;
  right: -2px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background: #e53e3e;
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}



.timeout-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -4px;
}

.timeout-min,
.timeout-max {
  font-size: 10px;
  color: #718096;
  font-weight: 500;
}

.timeout-config {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>