@import 'https://cdn.jsdelivr.net/npm/@vue-flow/core@1.43.1/dist/style.css';
@import 'https://cdn.jsdelivr.net/npm/@vue-flow/core@1.43.1/dist/theme-default.css';
@import 'https://cdn.jsdelivr.net/npm/@vue-flow/controls@latest/dist/style.css';
@import 'https://cdn.jsdelivr.net/npm/@vue-flow/minimap@latest/dist/style.css';
@import 'https://cdn.jsdelivr.net/npm/@vue-flow/node-resizer@latest/dist/style.css';

html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}

#app {
  text-transform: uppercase;
  font-family: 'JetBrains Mono', monospace;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
}

.vue-flow__minimap {
  transform: scale(100%);
  transform-origin: bottom right;
}

.vue-flow__panel {
  background-color: #2d3748;
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 0 10px #00000080
}

.vue-flow__panel .buttons {
  display: flex;
  gap: 8px
}

.vue-flow__panel button {
  border: none;
  cursor: pointer;
  background-color: #4a5568;
  border-radius: 8px;
  color: #fff;
  box-shadow: 0 0 10px #0000004d;
  width: 40px;
  height: 40px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center
}

.vue-flow__panel button:hover {
  background-color: #2563eb;
  transition: background-color .2s
}

.vue-flow__panel button svg {
  width: 100%;
  height: 100%
}

.dnd-flow {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  overflow: hidden;
  max-width: 100vw;
  max-height: 100%;
}

.dnd-flow .vue-flow-wrapper {
  flex-grow: 1;
  height: 100%;
  transition: all 0.3s ease-in-out;
}

@media screen and (min-width: 640px) {
  .dnd-flow {
    flex-direction: row
  }

  .dnd-flow aside {
    min-width: 25%
  }
}

@media screen and (max-width: 639px) {
  .dnd-flow {
    flex-direction: column-reverse;
  }

  .sidebar-container {
    width: 100% !important;
    height: auto;
    max-height: 130px;
    transition: max-height 0.3s ease-in-out !important;
  }

  .sidebar-container.collapsed {
    width: 100% !important;
    max-height: 35px;
  }

  .collapse-toggle {
    top: 5px;
    right: 10px;
    left: auto;
    border-radius: 0 0 4px 4px;
    transform: rotate(90deg) !important;
    width: 25px;
    height: 30px;
  }

  .sidebar-container.collapsed .collapse-toggle {
    transform: rotate(-90deg) !important;
  }
}

.dropzone-background {
  position: relative;
  height: 100%;
  width: 100%
}

.dropzone-background .overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  pointer-events: none
}

/* Topbar Styles */
.topbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2d3748;
  padding: 12px 20px;
  border-bottom: 1px solid #4a5568;
  color: white;
  min-height: 50px;
}

.topbar-buttons {
  display: flex;
  gap: 15px;
  flex-shrink: 0;
}

.topbar-link {
  color: #e2e8f0;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-weight: 500;
  text-transform: none;
  white-space: nowrap;
}

.topbar-link:hover {
  background-color: #4a5568;
  color: white;
}

.topbar-name {
  position: relative;
  min-width: 200px;
  max-width: 300px;
}

.name-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid rgba(74, 85, 104, 0.6);
  border-radius: 6px;
  background-color: rgba(26, 32, 44, 0.8);
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-transform: none;
  outline: none;
  cursor: text;
}

.name-input:hover {
  border-color: #4a5568;
  background-color: rgba(26, 32, 44, 0.9);
  box-shadow: 0 0 0 1px rgba(74, 85, 104, 0.3);
}

.name-input:focus {
  border-color: #3182ce;
  background-color: #1a202c;
  color: white;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.name-input::placeholder {
  color: #a0aec0;
  font-weight: normal;
  font-style: italic;
}

.topbar-name::after {
  content: "✎";
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #a0aec0;
  font-size: 12px;
  pointer-events: none;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.topbar-name:hover::after {
  opacity: 1;
  color: #e2e8f0;
}

.name-input:focus + .topbar-name::after,
.topbar-name:focus-within::after {
  opacity: 0;
}

@media (max-width: 768px) {
  .topbar {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }

  .topbar-name {
    min-width: 100%;
    max-width: none;
  }

  .topbar-buttons {
    gap: 10px;
    align-self: flex-start;
  }

  .topbar-link {
    padding: 6px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .topbar-buttons {
    flex-wrap: wrap;
    gap: 8px;
  }

  .topbar-link {
    padding: 5px 10px;
    font-size: 12px;
  }

  .name-input {
    font-size: 13px;
    padding: 6px 10px;
  }
}