<template>
    <div class="fixed left-5 bottom-5">
        <a
            type="button"
            class="px-4 py-2 rounded-md min-w-[12rem] bg-green-600 text-white flex items-center gap-2 font-bold"
            @click="show = !show"
            target="_blank"
            :href="selectedLink"
        >
            <MonoIcon
                name="support"
                class="w-5 h-5 text-black dark:text-white"
            />
            <span>{{ t("wiki.how_to") }}</span>
        </a>
    </div>
</template>

<script setup>
import { securedAxios } from "@/utils/axios";
import wikiLinks from "@/constant/wikiLinks";
const { t } = useI18n();
const props = defineProps({
    name: String,
});

const selectedLink = wikiLinks[props.name] || wikiLinks["default"];
const show = ref(false);
</script>
