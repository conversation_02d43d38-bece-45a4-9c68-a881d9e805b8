<template>
    <div class="space-y-2" :class="[!noSpan ? `col-span-${span}` : '']">
        <label class="flex gap-1 text-sm font-medium">{{ label }}
            <span v-if="is_required && !disabled" class="text-red-500">
                *
            </span>
            <WikiLink v-if="wikiLink" :Name="wikiLink" />
        </label>
        <input :type="$props.type" class="t-input disabled:bg-slate-100" :min="min" v-model="inputValue" :autocomplete="autocomplete"
            :required="is_required" :minlength="min_length" :maxlength="max_length" :disabled="disabled"
            @input="emits('update:modelValue', $event.target.value)" v-bind="$attrs" />
        <span class="text-rose-500 text-xs leading-3" v-if="errors.visible">
            {{ errors.text }} *
        </span>
    </div>
</template>

<script setup>
const props = defineProps({
    type: {
        type: String,
        default: "text",
    },
    label: {
        type: String,
    },
    modelValue: {
        default: null,
        required: true,
    },
    min: {
        type: Number,
        default: null,
    },
    min_length: {
        type: Number,
        default: null,
    },
    max_length: {
        type: Number,
        default: null,
    },
    is_required: {
        type: Boolean,
        default: false,
    },
    numberic: {
        type: Boolean,
        default: false,
    },
    span: {
        type: Number,
        default: 6,
    },
    noSpan: {
        type: Boolean,
        default: false,
    },
    wikiLink: {
        type: String,
        default: "",
    },
    error: {
        type: Boolean,
        default: false,
    },
    errorText: {
        type: String,
        default: "",
    },
    prefix: {
        type: String,
        default: null,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    autocomplete: {
        type: String,
        default: "off",
    },
});
const emits = defineEmits(["update:modelValue"]);


const inputValue = computed({
    get: () => props.modelValue,
    set: (value) => {
        emits('update:modelValue', value)
    }
})

const { t } = useI18n()

const errors = computed(() => ({
    visible: props.error || !!props.errorText,
    text: props.errorText || t('general.invalid_input')
}))

</script>
