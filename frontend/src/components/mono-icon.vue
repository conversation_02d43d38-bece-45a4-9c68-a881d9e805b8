<template>
  <component
    :is="iconComponent"
    v-if="iconComponent"
    v-bind="$attrs"
  />
  <span v-else class="text-red-500 text-xs">{{ name }}</span>
</template>

<script setup>
import { computed, defineAsyncComponent } from 'vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  }
})

const iconComponent = computed(() => {
  if (!props.name) return null

  try {
    return defineAsyncComponent(() =>
      import(`@/components/icons/${props.name}.vue`)
    )
  } catch (error) {
    console.warn(`Icon not found: ${props.name}`)
    return null
  }
})
</script>
