<script setup>
import { MenuWithGroupList } from "@/composables/menu";

const menuItems = computed(() => MenuWithGroupList());

const navbar = reactive({
	open: false,
	animationInProcess: false,
});

const toggleNavbar = () => {
	if (navbar.animationInProcess) {
		return;
	}

	const navbarElm = document.getElementById("navbar-collapse-with-animation");
	const triggerElms = document.querySelectorAll('[aria-controls="navbar-collapse-with-animation"]');

	if (navbar.open) {
		// hide navbar
		if (!navbarElm.classList.contains('open')) {
			return
		}

		navbar.animationInProcess = true;

		triggerElms.forEach((triggerElm) => {
			triggerElm.classList.remove('open');
		});

		navbarElm.style.height = `${navbarElm.scrollHeight}px`;

		setTimeout(() => {
			navbarElm.style.height = '0';
		});

		navbarElm.classList.remove('open');

		const afterTransition = () => {
			navbarElm.classList.add('hidden');
			navbarElm.style.height = '';

			navbar.animationInProcess = false;
			navbar.open = false;
		}

		const handleEvent = () => {
			afterTransition();

			navbarElm.removeEventListener('transitionend', handleEvent, true);
		};

		navbarElm.addEventListener('transitionend', handleEvent, true)

	} else {
		// show navbar
		if (navbarElm.classList.contains('open')) {
			return
		}

		navbar.animationInProcess = true;

		triggerElms.forEach((triggerElm) => {
			triggerElm.classList.add('open');
		});

		navbarElm.classList.add('open');
		navbarElm.classList.remove('hidden');

		navbarElm.style.height = '0';

		setTimeout(() => {
			navbarElm.style.height = `${navbarElm.scrollHeight}px`;
		});

		const afterTransition = () => {
			navbarElm.style.height = '';

			// open navbar
			navbarElm.classList.remove('hidden');

			navbar.animationInProcess = false;
			navbar.open = true;
		}

		const handleEvent = () => {
			afterTransition();

			navbarElm.removeEventListener('transitionend', handleEvent, true);
		};

		navbarElm.addEventListener('transitionend', handleEvent, true)
	}
};
</script>

<template>
	<header class="z-50 flex flex-col">
		<nav style="background-color: #e4bc72;">
			<div class="flex flex-wrap basis-full items-center w-full mx-auto py-2.5 px-4 sm:px-6 lg:px-8"
				aria-label="Global">
				<div class="flex items-center md:order-1 gap-x-3">
					<div class="md:hidden">
						<button type="button"
							class="hs-collapse-toggle inline-flex justify-center items-center w-7 h-[38px] text-start border border-white/20 text-white rounded-lg shadow-sm align-middle hover:bg-white/10 disabled:opacity-50 focus:outline-none focus:bg-white/10"
							data-hs-collapse="#navbar-collapse-with-animation" aria-controls="navbar-collapse-with-animation"
							aria-label="Toggle navigation" @click="toggleNavbar">

							<svg class="flex-shrink-0 size-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M4 7L7 7M20 7L11 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
								<path d="M20 17H17M4 17L13 17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
								<path d="M4 12H7L20 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
							</svg>
						</button>
					</div>
					<router-link
						class="flex-none inline-block text-xl font-semibold text-white rounded-md focus:outline-none focus:opacity-80"
						to="/">
						<div class="flex justify-center items-center gap-2">
							<img src="../assets/images/pars.png" alt="PARS" class="w-[40px] h-[40px] object-contain">
							<h1
								class="font-display text-balance font-semibold tracking-tight text-gray-950 text-2xl sm:text-3xl md:text-4xl leading-[40px]">
								PARS
							</h1>
						</div>
					</router-link>
				</div>

				<div class="flex items-center justify-end md:order-4 ms-auto md:ms-0 grow gap-x-2">
					<div class="hidden sm:block border-e border-white/20 w-px h-6 mx-1.5"></div>
					<t-profile-dropdown />
				</div>
				<t-navbar-search />
			</div>
		</nav>

		<nav class="relative border-b border-gray-200 dark:border-neutral-700  bg-gray-50 dark:bg-gray-900 ">
			<div class="flex flex-wrap basis-full items-center w-full mx-auto py-2.5 px-4 sm:px-6 lg:px-8"
				aria-label="Global">
				<div class="basis-full grow lg:basis-auto lg:grow-0">
					<div id="navbar-collapse-with-animation" class="hidden overflow-hidden transition-all duration-300 md:block">
						<div class="overflow-hidden overflow-y-auto max-h-[75vh]">
							<div class="gap-1 md:flex md:items-center space-y-2 md:space-y-0">
								<router-link :to="{ name: 'dashboard' }"
									class="flex gap-x-3 lg:gap-x-1.5 py-2 px-3 w-full lg:w-auto items-center text-sm text-start font-semibold text-stone-800 rounded-lg hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
									exact exact-active-class="bg-stone-100 dark:bg-neutral-700">
									<svg class="flex-shrink-0 lg:hidden size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
										viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
										stroke-linejoin="round">
										<path d="M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"></path>
										<path d="M3 6h18"></path>
										<path d="M16 10a4 4 0 0 1-8 0"></path>
									</svg>
									{{ $t('routes.dashboard') }}
								</router-link>

								<slot v-for="menu in menuItems">

									<t-flyout :label="menu.name" v-if="menu.childrens.length > 0" :z-index="5">
										<template #content>
											<div class="grid gap-4 select-none lg:grid-cols-12 lg:gap-0">
												<div class="pt-2 lg:col-span-12 md:p-3">
													<div class="grid gap-2 sm:grid-cols-4 sm:gap-5 lg:gap-5">
														<div class="flex flex-col space-y-3" v-for="item in menu.childrens">
															<div class="space-y-1">
																<span
																	class="block pl-3 mb-2 text-xs font-semibold text-left text-gray-800 dark:text-indigo-500">
																	{{ item.title }}
																</span>

																<router-link :to="{ name: child.route }"
																	class="flex items-center px-3 py-2 text-gray-800 rounded-lg group gap-x-3 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-500/10 dark:focus:bg-neutral-700"
																	v-for="(child, index) in item.childs" :key="index">

																	<div class="grow">
																		<p class="text-sm text-gray-800 dark:text-neutral-300">
																			{{ $t(`routes.${child.route}`) }}
																		</p>
																	</div>
																</router-link>

															</div>
														</div>
													</div>
												</div>
											</div>
										</template>
									</t-flyout>
									<t-navbar-dropdown :label="menu.name" v-else>
										<template #content>
											<slot v-for="item in menu.childrens">

												<span
													class="block pl-3 my-2 text-xs font-semibold text-gray-800 select-none dark:text-indigo-500 ">
													{{ item.title }}
												</span>
												<router-link :to="{ name: child.route }"
													class="group p-3 flex gap-x-3.5 select-none text-gray-800 hover:bg-gray-100 rounded-lg dark:text-neutral-200 dark:hover:bg-neutral-500/10"
													v-for="child in item.childs">

													<div class="grow">
														<p class="text-sm text-gray-800 dark:text-neutral-300">
															{{ $t(`routes.${child.route}`) }}
														</p>
													</div>
												</router-link>
											</slot>

										</template>
									</t-navbar-dropdown>
								</slot>

							</div>
						</div>
					</div>
				</div>
			</div>
		</nav>
	</header>
</template>