<template>
  <button
    :class="`${colors[color]} ${sizes[size]} ${
      disabled || loading ? '' : ''
    } p-1 m-[2px] rounded flex items-center justify-center`"
    :disabled="disabled || loading"
    :type="htmlType"
  >
    <svg
      class="animate-spin w-4 h-4 fill-current shrink-0"
      viewBox="0 0 16 16"
      v-if="loading"
    >
      <path
        d="M8 16a7.928 7.928 0 01-3.428-.77l.857-1.807A6.006 6.006 0 0014 8c0-3.309-2.691-6-6-6a6.006 6.006 0 00-5.422 8.572l-1.806.859A7.929 7.929 0 010 8c0-4.411 3.589-8 8-8s8 3.589 8 8-3.589 8-8 8z"
      />
    </svg>
    <slot />
  </button>
</template>
<script setup>
import { defineProps } from "vue";
const colors = {
  primary:
    "bg-indigo-500 hover:bg-indigo-600 text-white disabled:bg-opacity-50 disabled:text-opacity-50 disabled:cursor-not-allowed",
  primaryText:
    "bg-transparent text-indigo-500 hover:text-indigo-600 disabled:bg-opacity-50 disabled:text-opacity-50 disabled:cursor-not-allowed shadow-none",
  secondary:
    "border-slate-200 hover:border-slate-300 text-indigo-500 disabled:text-opacity-50 disabled:cursor-not-allowed",
  tertiary:
    "border-slate-200 hover:border-slate-300 text-slate-600 disabled:text-opacity-50 disabled:cursor-not-allowed",
  danger:
    "bg-rose-500 hover:bg-rose-600 text-white disabled:bg-opacity-50 disabled:text-opacity-50 disabled:cursor-not-allowed",
  dangerOutline:
    "border-slate-200 hover:border-slate-300 text-rose-500 disabled:text-opacity-50 disabled:cursor-not-allowed",
  success:
    "bg-emerald-500 hover:bg-emerald-600 text-white disabled:bg-opacity-50 disabled:text-opacity-50 disabled:cursor-not-allowed",
  successOutline:
    "border-slate-200 hover:border-slate-300 text-emerald-500 disabled:text-opacity-50 disabled:cursor-not-allowed",
  white:
    "bg-white hover:bg-slate-50 text-slate-600 disabled:text-opacity-50 disabled:cursor-not-allowed",
  transparent:
    "bg-transparent border-none shadow-none hover:bg-slate-50 text-slate-600 disabled:text-opacity-50 disabled:cursor-not-allowed",
  warning:
    "bg-yellow-500 hover:bg-yellow-600 text-white disabled:bg-opacity-50 disabled:text-opacity-50 disabled:cursor-not-allowed",
};
const sizes = {
  xs: "btn-xs",
  sm: "btn-sm",
  md: "btn",
  lg: "btn-lg",
};
defineProps({
  size: {
    type: String,

    validator: (value) => {
      return ["2xs", "xs", "sm", "md", "lg"].includes(value);
    },
  },
  color: {
    type: String,
    default: "primary",
    validator: (value) => {
      return [
        "primary",
        "primaryText",
        "secondary",
        "tertiary",
        "danger",
        "dangerOutline",
        "success",
        "successOutline",
        "white",
        "transparent",
        "warning",
      ].includes(value);
    },
  },
  loading: {
    type: Boolean,
  },
  htmlType: {
    type: String,
    default: "button",
    validator: (value) => ["button", "submit", "reset"].includes(value),
  },
  disabaled: {
    type: Boolean,
    default: false,
  },
});
</script>
