<template>
    <div>
        <Modal v-model="props.showContent">
            <template #content>
                <div v-for="(value, key) in props.contant" :key="key">
                    <div class="grid grid-cols-6 gap-2">
                        <div class=" col-span-3">
                            <div class="flex pt-1">

                                <button
                                    class="flex items-center justify-center w-8 h-8 font-bold text-white bg-red-500 rounded hover:bg-red-700">
                                    <Icon name="trash" @click="editConstant(key)" />
                                </button>
                                <input type="text" v-model="props.contant[key].name"
                                    class="ml-2 rounded-md  text-xs font-medium dark:text-gray-900" />
                            </div>
                        </div>
                    </div>
                </div>
                <div>

                    <!-- <button @click="updateConstant"> -->
                    <div>
                        <button class="bg-indigo-500 rounded-md px-4 py-2 text-gray-50" @click="addConstant">
                            <Icon name="plus" />
                        </button>

                    </div>
                </div>
            </template>
            <template #footer>

                <button @click="updateConstant" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md">Edit</button>
                <button @click="closeModal" class="mt-4 px-4 py-2 bg-red-500 text-white rounded-md">Kapat</button>

            </template>
        </Modal>
    </div>
</template>
<script setup>


import { defineEmits } from 'vue';
import IconButton from "@/components/icon-buton.vue";

import Modal from "@/components/modal.vue";
const emits = defineEmits(["closeContent"]); // emit fonksiyonunu tanımla
//store
import { useStore } from "vuex";
const store = useStore();
const closeModal = () => {
    emits('closeContent',);
}
const props = defineProps({
    showContent: {
        type: Boolean,
        required: true,
    },
    contant: {
        type: [],
        required: true,
    },
    editStore: {
        type: String,
        required: true,
    },
})

const addConstant = () => {
    props.contant.push({
        id: "",
        name: "",
        asset_priority: "",
    });
}
const editConstant = (key) => {
    props.contant.splice(key, 1);
}
const updateConstant = () => {
    store.dispatch(props.editStore, props.contant);
    closeModal();
}

</script>