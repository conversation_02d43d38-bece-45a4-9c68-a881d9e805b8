<template>
    <nav class="flex items-center justify-between mb-5" aria-label="Progress">
        <button type="button"
            class="px-2 py-1 rounded-md bg-indigo-500 hover:bg-indigo-700 active:bg-indigo-900 transition-colors disabled:bg-indigo-300 disabled:cursor-not-allowed"
            @click="selectStep(currentStep - 1)" :disabled="currentStep < 1">{{ $t('general.previous') }}</button>
        <div class="flex items-center justify-center">
            <p class="text-sm font-medium">{{ $t('general.step', { step: currentStep + 1, total_steps: steps.length }) }}
            </p>
            <ol role="list" class="ml-8 flex items-center space-x-5">
                <li v-for="(item, index) in steps" :key="index">
                    <button class="relative flex items-center justify-center h-2.5 w-2.5 rounded-full " type="button"
                        @click="selectStep(index)"
                        :class="currentStep === index || index < currentStep ? 'bg-indigo-600' : 'bg-gray-200'">
                        <span class="absolute flex h-5 w-5 p-px" aria-hidden="true" v-if="currentStep === index">
                            <span class="h-full w-full rounded-full bg-indigo-200" />
                        </span>
                        <span class="relative block h-2.5 w-2.5 rounded-full bg-indigo-600" aria-hidden="true"
                            v-if="currentStep === index" />
                        <span class="sr-only">Step {{ item }}</span>
                    </button>
                </li>
            </ol>
        </div>
        <button type="button"
            class="px-2 py-1 rounded-md bg-indigo-500 hover:bg-indigo-700 active:bg-indigo-900 transition-colors disabled:bg-indigo-300 disabled:cursor-not-allowed"
            @click="selectStep(currentStep + 1)" :disabled="steps.length === currentStep + 1">{{ $t('general.next')
            }}</button>
    </nav>

    <slot :name="currentStep" />

</template>

<script setup>
const props = defineProps({
    steps: {
        type: Array,
        default: () => [],
        required: true
    },
    step: {
        type: Number,
        default: 0
    }
})
const emit = defineEmits(['update:step'])

const currentStep = computed({
    get: () => props.step,
    set: (value) => emit('update:step', value)
})

function selectStep(index) {
    currentStep.value = index
}
</script>
