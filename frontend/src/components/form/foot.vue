<template>
    <div class="p-4 border-t flex justify-end gap-2">
        <slot name="actions" />
        <Button html-type="submit" :disabled="props.submitDisabled">
            {{ props.submitText ?? $t("general.submit") }}
        </Button>
        <Button v-if="props.cancelable" color="secondary" @click="$emit('click:cancel')">
            {{ props.cancelText ?? $t("general.cancel") }}
        </Button>
    </div>
</template>

<script setup>
import Button from "@/components/button.vue";
const props = defineProps({
    cancelable: Boolean,
    cancelText: String,
    submitText: String,
    submitDisabled: <PERSON><PERSON><PERSON>,
});
const emit = defineEmits(["click:cancel"]);
</script>
