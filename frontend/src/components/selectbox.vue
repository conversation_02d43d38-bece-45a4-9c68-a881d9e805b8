<template>
	<div ref="selectbox" class="space-y-2" :class="[span ? `col-span-${span}` : '']">
		<label class="flex gap-1 text-sm font-medium" v-if="label">{{ label }}
			<WikiLink v-if="wikiLink" ref="wikiLinkRef" :Name="wikiLink" />
		</label>
		<div class="relative">
			<button type="button"
				class="w-full h-[38px] min-w-[12rem] relative bg-white dark:bg-gray-800 border dark:hover:bg-gray-700 transition-colors rounded-md flex flex-col items-center px-2 py-2 focus:outline-none sm:text-sm disabled:cursor-not-allowed disabled:bg-slate-100"
				:class="[showError ? 'border-rose-400 dark:border-rose-600' : 'border-gray-300 dark:border-gray-600']"
				:disabled="disabled" @click="show = !show">
				<div class="flex items-center justify-between w-full">
					<span class="block truncate" v-if="multiple">
						{{
							selectedOptions && selectedOptions.length > 0
								? options.filter((option) => selectedOptions.includes(option[dataLabel]))[0]?.[
								optionLabel
								]
								: t("general.select_placeholder")
						}}
						{{ selectedOptions && selectedOptions.length > 1 ? `+${selectedOptions.length - 1} more` : "" }}
					</span>

					<span class="block truncate" v-if="!multiple">
						{{
							options.find((option) => selectedOption === option[dataLabel])
								? options.find((option) => selectedOption === option[dataLabel])[optionLabel]
								: t("general.select_placeholder")
						}}
					</span>

					<span class="flex items-center" v-if="selectedOption > 0 && !disabled" @click="clearSelected()">
						<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"
							xmlns="http://www.w3.org/2000/svg">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					</span>
					<span class="flex items-center" v-if="selectedOption < 1">
						<svg class="w-4 h-4 transition transform" :class="[show ? 'rotate-180' : null]" fill="none"
							stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
						</svg>
					</span>
				</div>
				<select class="w-0 h-0 p-0 opacity-0" :required="required" :disabled="disabled" :name="label"
					:value="multiple ? selectedOptions[0] : selectedOption">
					<option v-if="!multiple" :value="null" disabled selected hidden>
						{{ placeholder }}
					</option>
					<option v-for="(option, index) in options" :key="index" :value="option[dataLabel]">
						{{ option[optionLabel] }}
					</option>
				</select>
			</button>
			<span class="text-xs leading-3 text-rose-500" v-if="showError">
				{{ $t("general.field_required", { field: label }) }} *
			</span>
			<teleport to="body">
				<transition enter-active-class="transition duration-100 ease-out"
					enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100"
					leave-active-class="transition duration-75 ease-in" leave-from-class="transform scale-100 opacity-100"
					leave-to-class="transform scale-95 opacity-0">
					<ul
						class="fixed z-[5000] mt-1 w-full bg-white dark:bg-gray-900 border border-gray-600 dark:border-gray-600 rounded-md py-1 overflow-auto focus:outline-none sm:text-sm max-h-60"
						v-if="show" ref="selectboxList" :style="{
							width: selectbox.offsetWidth + 'px',
							top: selectbox.getBoundingClientRect().bottom + 'px',
							left: selectbox.getBoundingClientRect().left + 'px',
						}">
						<div class="px-2 my-2" v-if="searchable">
							<input type="text" class="t-input" :placeholder="$t('general.search')" v-model="searchText" />
						</div>

						<span class="flex justify-center my-5 text-xs font-medium text-gray-800 select-none dark:text-gray-500"
							v-if="filteredData.length < 1">{{ $t("general.no_records") }}</span>

						<div
							class="flex items-center justify-between px-2 py-2 transition-colors cursor-pointer hover:bg-gray-300 dark:hover:bg-gray-700"
							v-for="(item, index) in filteredData" :key="index"
							:class="{ 'bg-gray-300 dark:bg-gray-700': isSelected(item[dataLabel]) }"
							@click="selectItem(item[dataLabel])">
							<span class="block ml-3 truncate" v-if="multiple"
								:class="[selectedOptions.includes(item.id) ? 'font-semibold' : 'font-normal']">{{ item[optionLabel]
								}}</span>
							<span class="block ml-3 truncate" :class="[selectedOption === item.id ? 'font-semibold' : 'font-normal']"
								v-else>{{ item[optionLabel] }}</span>
							<span class="inset-y-0 right-0 flex items-center text-indigo-600"
								v-if="multiple && selectedOptions.includes(item.id)">
								<svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"
									aria-hidden="true">
									<path fill-rule="evenodd"
										d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
										clip-rule="evenodd" />
								</svg>
							</span>
						</div>
					</ul>
				</transition>
			</teleport>
		</div>
	</div>
</template>

<script setup>
import { ref, onBeforeUnmount, computed } from "vue";
import { turkishToUniversal } from "@/utils/helpers";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
	options: {
		type: Array,
		default: () => [],
	},
	modelValue: {
		default: () => [],
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	span: {
		type: Number,
	},
	label: {
		type: String,
		default: null,
	},
	optionLabel: {
		default: "name",
	},
	dataLabel: {
		default: "id",
	},
	searchable: {
		type: Boolean,
		default: false,
	},
	placeholder: {
		type: String,
		default: "Select an option",
	},
	required: {
		type: Boolean,
		default: false,
	},
	wikiLink: {
		type: String,
		default: "",
	},
	disabled: {
		type: Boolean,
		default: false,
	}
});

const show = ref(false);
const selectbox = ref(null);
const selectboxList = ref(null);
const wikiLinkRef = ref(null);

const selectedOptions = ref([]);
const selectedOption = ref("");
const searchText = ref("");

const emits = defineEmits(["update:modelValue"]);

const showError = computed(() => {
	if (props.required) {
		if (props.multiple) {
			return selectedOptions.value.length < 1;
		} else {
			return selectedOption.value === "" || selectedOption.value === null || selectedOption.value === undefined;
		}
	}
	return false;
});

function selectItem(id) {
	if (props.multiple) {
		const selected = selectedOptions?.value?.find((item) => item === id);
		if (selected) {
			selectedOptions.value = selectedOptions?.value?.filter((item) => item !== id);
		} else {
			selectedOptions.value.push(id);
		}
		emits("update:modelValue", selectedOptions.value);
	} else {
		const selected = selectedOption.value === id;
		if (selected) {
			selectedOption.value = null;
		} else {
			selectedOption.value = id;
		}
		emits("update:modelValue", selectedOption.value);
		show.value = false;
	}
}

const filteredData = computed(() => {
	const data = props.options;
	const search = searchText.value.toLowerCase();
	if (!search) return data;
	return data.filter((item) => turkishToUniversal(item[props.optionLabel]).toLowerCase().includes(search));
});

function isSelected(id) {
	if (props.multiple) {
		return selectedOptions.value.includes(id);
	} else {
		return selectedOption.value === id;
	}
}

if (props.modelValue) {
	if (props.multiple) {
		selectedOptions.value = props.modelValue;
	} else {
		selectedOption.value = props.modelValue;
	}
}
watch(() => props.modelValue, (newVal) => {
	if (props.multiple) {
		selectedOptions.value = newVal;
	} else {
		selectedOption.value = newVal;
	}
}, { immediate: true, deep: true });

function clearSelected() {
	selectedOption.value = null;
	emits("update:modelValue", null);
}

document.addEventListener("click", clickOutside);
onBeforeUnmount(() => {
	document.removeEventListener("click", clickOutside);
});

function clickOutside(e) {
	if (selectbox.value?.contains(e.target) || selectboxList.value?.contains(e.target)) return;
	show.value = false;
}
watch(
	() => props.options,
	(newVal, oldVal) => {
		if (props.multiple) {
			props.modelValue.map((item) => {
				if (!newVal.find((option) => option.id == item)) {
					selectedOptions.value = selectedOptions.value.filter((option) => option != item);
				} else {
					selectedOptions.value.push(item);
				}
			});
		} else {
			if (!newVal.find((option) => option.id == props.modelValue)) {
				selectedOption.value = null;
			} else {
				selectedOption.value = props.modelValue;
			}
		}
	},
);
onMounted(() => {
	window.addEventListener("resize", closeWhenResize);
	window.addEventListener("scroll", setPositionWhenScroll);
});
onBeforeUnmount(() => {
	show.value = false;
});
const closeWhenResize = () => {
	show.value = false;
};

//when page scroll set selectbox top and left position with translate x and y
const setPositionWhenScroll = () => {
	if (show.value) {
		selectboxList.value.style.top = selectbox.value.getBoundingClientRect().bottom + "px";
		selectboxList.value.style.left = selectbox.value.getBoundingClientRect().left + "px";
	}
};
</script>
