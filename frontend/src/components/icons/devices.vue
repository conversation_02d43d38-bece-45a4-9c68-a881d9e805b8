<template>
  <svg
    :class="props.class"
    fill="currentColor"
    viewBox="0 0 36 36"
    version="1.1"
    preserveAspectRatio="xMidYMid meet"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <path
      class="clr-i-outline clr-i-outline-path-1"
      d="M32,13H24a2,2,0,0,0-2,2V30a2,2,0,0,0,2,2h8a2,2,0,0,0,2-2V15A2,2,0,0,0,32,13Zm0,2V26H24V15ZM24,30V27.6h8V30Z"
    ></path>
    <path
      class="clr-i-outline clr-i-outline-path-2"
      d="M20,22H4V6H28v5h2V6a2,2,0,0,0-2-2H4A2,2,0,0,0,2,6V22a2,2,0,0,0,2,2H20Z"
    ></path>
    <path
      class="clr-i-outline clr-i-outline-path-3"
      d="M20,26H9a1,1,0,0,0,0,2H20Z"
    ></path>
    <rect x="0" y="0" width="36" height="36" fill-opacity="0" />
  </svg>
</template>
<script setup>
const props = defineProps({
  class: {
    type: String,
    default: "w-6 h-6 text-gray-400",
  },
});
</script>
