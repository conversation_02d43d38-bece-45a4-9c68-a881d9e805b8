<script setup>
const props = defineProps({
  formValues: {
    type: Object,
    required: true
  },
})
const emit = defineEmits(['update:formValues', 'onFileChange'])
const form = computed({
  get: () => props.formValues,
  set: (value) => emit('update:formValues', value)
})
</script>


<template>
  <div class="card max-w-5xl mx-auto">
    <div class="col-span-6">
      <div class="flex items-center justify-between">
        <label class="block text-sm font-medium">{{ $t("organizations.footer_text") }}</label>
        <div class="w-1/12">
          <select name="language" class="select" v-model="footerTextLanguage">
            <option value="en">en</option>
            <option value="tr">tr</option>
          </select>
        </div>
      </div>
      <input type="text" class="t-input" required v-model="form.footer_text[footerTextLanguage]" />
    </div>
  </div>
</template>