<template>
  <div class="xl:p-5 flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <div v-if="loading" class="flex items-center justify-center h-64">
      <MonoIcon class="size-20 animate-spin" name="spin" />
    </div>

    <div v-else class="space-y-4 p-2">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="bg-gray-50 dark:bg-neutral-700 border border-gray-200 dark:border-neutral-600 rounded-lg p-4">
          <div class="mb-4">
            <h3 class="text-lg font-medium text-gray-800 dark:text-neutral-200">
              {{ $t('simulates.statistics.chart1_title') }}
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-400">
              {{ $t('simulates.statistics.chart1_description') }}
            </p>
          </div>

          <div class="chart-container" style="height: 320px;">
            <t-pie-chart
              :theme="theme"
              :data="simulateStatisticsStatusForPieChart"
              :title="$t('simulates.statistics.chart1_title')"
              v-if="simulateStatisticsStatusForPieChart.length > 0"
            />
            <t-no-data v-else />
          </div>
        </div>
        <div class="bg-gray-50 dark:bg-neutral-700 border border-gray-200 dark:border-neutral-600 rounded-lg p-4">
          <div class="mb-4">
            <h3 class="text-lg font-medium text-gray-800 dark:text-neutral-200">
              {{ $t('simulates.statistics.chart2_title') }}
            </h3>
            <p class="text-sm text-gray-500 dark:text-neutral-400">
              {{ $t('simulates.statistics.chart2_description') }}
            </p>
          </div>

          <div class="chart-container" style="height: 320px;">
            <t-pie-chart
              :theme="theme"
              :data="simulateStatisticsProtocolForPieChart"
              :title="$t('simulates.statistics.chart2_title')"
              v-if="simulateStatisticsProtocolForPieChart.length > 0"
            />
            <t-no-data v-else />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useStatisticsStore } from '@/store/submodules/statistics';
import { useRoute } from 'vue-router';

const statisticStore = useStatisticsStore();
const route = useRoute();
const loading = ref(true);
const simulateStatistics = ref({});

const theme = ref('light');

async function fetchSimulateStatistics() {
  loading.value = true;
  try {
    const response = await statisticStore.fetchSimulateStatistics(route.params.id);
    simulateStatistics.value = response;
  } catch (error) {
    console.error('Simulate statistics fetch failed:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(fetchSimulateStatistics);

const simulateStatisticsProtocolForPieChart = computed(() => {
  const data = simulateStatistics.value;
  if (!data || Object.keys(data).length === 0) return [];

  const protocolData = [
    {
      name: 'HTTP',
      value: Number(data.total_http_count) || 0
    },
    {
      name: 'gRPC',
      value: Number(data.total_grpc_count) || 0
    }
  ];

  return protocolData
    .filter(item => item.value > 0)
    .sort((a, b) => b.value - a.value);
});

const simulateStatisticsStatusForPieChart = computed(() => {
  const data = simulateStatistics.value;
  if (!data || Object.keys(data).length === 0) return [];

  const statusData = [
    {
      name: 'Success',
      value: Number(data.total_success_count) || 0
    },
    {
      name: 'Error',
      value: Number(data.total_error_count) || 0
    }
  ];

  return statusData
    .filter(item => item.value > 0)
    .sort((a, b) => b.value - a.value);
});
</script>
