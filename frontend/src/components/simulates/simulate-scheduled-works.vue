<template>
  <div>
    <t-table 
      :data="items.list" 
      :columns="tableProperties.columns" 
      entity-locale-name="admins"
      :page="Number(queries.page)" 
      :per_page="Number(queries.per_page)" 
      :total="items.total"
      :total_pages="items.total_pages" 
      @page-change="pageChange" 
      @per-page-change="perPageChange" 
      :loading="loading"
      :create-route="tableProperties.createRoute" :filters="tableProperties.filters" 
      @filter-change="onFilterChange"
      :error="errorRes.msg" 
      inlinesearch
    >
      <template v-slot:row-actions="itemData">
        <div class="flex items-center gap-2"
          :class="tableProperties.columns[tableProperties.columns.length - 1].align === 'center' ? 'justify-center' : 'justify-start'">
          <div v-for="action in tableProperties.actions">
            <router-link v-if="action.route" :to="{ name: action.route, params: { id: itemData.item.id } }"
              class="flex items-center gap-2 px-2 py-1 text-xs text-white transition-colors rounded-md" :class="[
                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
              ]">
              <t-icon :name="action.icon" :class="[
                action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
              ]" />
              {{ action.label }}
            </router-link>
            <button v-else @click="action.function(itemData.item)"
              class="flex items-center gap-2 px-2 py-1 text-xs text-white transition-colors rounded-md" :class="[
                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
              ]">

              <t-icon :name="action.icon" :class="[
                action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
              ]" />
              {{ action.label }}
            </button>
          </div>
        </div>
      </template>
    </t-table>

    <t-delete-modal v-if="deleteModal" :is-active="deleteModal" @deleteData="deleteItem"
      @closeModal="  deleteModal = false; deleteRelations = {};">
      <template #content>
        <p class="text-md">{{ $t("general.delete_confirm") }}</p>
        <p class="text-xs leading-5">
          <span class="font-semibold text-rose-400">
            {{ $t("general.delete_label", { item: deleteRelations.name }) }}
          </span>
        </p>
      </template>
    </t-delete-modal>
  </div>

  <div class="mx-auto card mt-3">
    <form @submit.prevent="createScheduledWork()">
      <div class="grid grid-cols-6 gap-2">

        <div class="col-span-3">
          <InputBox wiki-link="scheduled_works.name_of_work" :error-text="form.errors.name_of_work"
            :label="$t('scheduled_works.name_of_work')" is_required no-span v-model="form.name_of_work" />
        </div>

        <div class="col-span-3">
          <t-select v-model="form.each_x_time" :options="hours" :label="$t('scheduled_works.each_x_time')" long
            close-after-select required />
        </div>

        <div class="col-span-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {{ $t('scheduled_works.request') }}
            <span class="text-red-500">*</span>
          </label>

          <div class="border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden bg-white dark:bg-gray-800">

            <div class="p-0">
              <div ref="monacoContainer" class="w-full min-h-[200px] max-h-[500px] border-0"></div>
            </div>
          </div>

          <div v-if="jsonError" class="mt-1 text-sm text-red-600">
            {{ jsonError }}
          </div>
          <div v-if="form.errors.request" class="mt-1 text-sm text-red-600">
            {{ form.errors.request }}
          </div>
        </div>

        <div class="col-span-3">
          <label class="flex gap-1 text-sm font-medium">{{ $t('scheduled_works.start_date') }}
            <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            class="t-input"
            v-model="form.start_date"
            :min="tomorrowDate"
            @change="validateStartDate"
            required
          />
          <div v-if="form.errors.start_date" class="mt-1 text-sm text-red-600">
            {{ form.errors.start_date }}
          </div>
        </div>
        <div class="col-span-3">
          <label class="flex gap-1 text-sm font-medium">{{ $t('scheduled_works.end_date') }}
            <span class="text-red-500">*</span>
          </label>
          <input
            type="date"
            class="t-input"
            v-model="form.end_date"
            :min="minEndDate"
            @change="validateEndDate"
            required
          />
          <div v-if="form.errors.end_date" class="mt-1 text-sm text-red-600">
            {{ form.errors.end_date }}
          </div>
        </div>
      </div>

      <div class="flex justify-center mt-5">
        <button type="submit" class="px-4 py-2 bg-indigo-600 rounded-md">{{ $t("general.create") }}</button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, reactive, nextTick, computed } from 'vue';
import { useScheduledWorksStore } from '@/store/submodules/scheduled-works';
import { useFormErrorHandler } from '@/composables/error';
import * as monaco from 'monaco-editor';

const { t } = useI18n();
const route = useRoute();
const toast = inject('toast');
const router = useRouter();
const store = useScheduledWorksStore();

const loading = ref(true);
const errorRes = reactive({ msg: '', status: '' });

const queries = reactive({
  page: Number(route.query.page) || 1,
  per_page: Number(route.query.per_page) || 10,
});

const deleteModal = ref(false);
const deleteRelations = ref({});

function setDeleteItem(item) {
  deleteRelations.value = item;
  deleteModal.value = true;
}

function deleteItem() {
  loading.value = true;
  store.Delete(deleteRelations.value.id)
    .then(() => {
      toast.success(t("general.delete_success"));
      return store.List(queries.page, queries.per_page, filterlist.value);
    })
    .catch(() => {
      toast.error(t("general.delete_error"));
      errorRes.msg = "Something went wrong";
    })
    .finally(() => {
      deleteModal.value = false;
      loading.value = false;
    });
}

const filterlist = ref([]);

store.List(queries.page, queries.per_page, filterlist.value)
  .catch(() => errorRes.msg = "Something went wrong")
  .finally(() => loading.value = false);

function pageChange(pageNum) {
  loading.value = true;
  queries.page = Number(pageNum);
  router.push({ name: route.name, query: queries });
  store.List(pageNum, queries.per_page, filterlist.value)
    .finally(() => loading.value = false);
}

function perPageChange(per_page) {
  loading.value = true;
  queries.per_page = Number(per_page);
  router.push({ name: route.name, query: queries });
  store.List(queries.page, per_page, filterlist.value)
    .finally(() => loading.value = false);
}

const items = computed(() => {
  const list = store.getItems.rows || [];
  return {
    list,
    total_pages: store.getItems.total_pages,
    per_page: store.getItems.per_page,
    page: store.getItems.page,
    total: store.getItems.total,
  };
});

function onFilterChange(filters) {
  loading.value = true;
  filterlist.value = filters;
  Object.assign(queries, ...filters);

  router.push({ name: route.name, query: queries });
  store.List(queries.page, queries.per_page, filterlist.value)
    .finally(() => loading.value = false);
}

const tableProperties = {
  delete: "deleteSimulate",
  get: "getSimulates",
  storeGetter: "simulates",
  listRoute: "simulate-list",
  paginationOptions: queries,
  columns: [
    { row: 'created_at', label: 'Created At', sortable: true, align: "center", mobile: true, visible: true },
    { row: 'name_of_work', label: 'Name of Work', sortable: true, align: "center", mobile: true, visible: true },
    { row: "actions", label: t("general.actions"), sortable: false, align: "center", mobile: true, visible: true }
  ],
  filters: [{
    name: "Organization ID",
    type: "selectbox",
    dataName: "organization_id",
    get: "organizations",
    dataGet: "getOrganizations",
    colSize: 6,
    value: route.query.organization_id || null,
    show: true
  }],
  actions: [
    { 
      label: t("general.view"), 
      icon: "eye", 
      color: "indigo", 
      route: 'scheduled-works-view' 
    },
    { 
      label: t("general.delete"), 
      icon: "trash", 
      color: "rose", 
      action: 'delete', 
      function: setDeleteItem
    }
  ]
};

const form = reactive({
  name_of_work: "",
  each_x_time: "12:00",
  request: "",
  start_date: "",
  end_date: "",
  how_many_time_to_try: 1,
  simulate_id: "",
  errors: {},
});

const requestJson = ref({
  pars: "pars",
  description: "lorem ipsum dolor sit amet",
  parameters: { type: "object", properties: {} },
  count: 1
});

const jsonError = ref('');
const monacoContainer = ref(null);
let monacoEditor = null;

function initMonacoEditor() {
  if (monacoContainer.value && !monacoEditor) {
    monacoEditor = monaco.editor.create(monacoContainer.value, {
      value: JSON.stringify(requestJson.value, null, 2),
      language: 'json',
      theme: 'vs-dark',
      lineNumbers: 'on',
      automaticLayout: false,
      minimap: { enabled: true },
      scrollBeyondLastLine: true,
      wordWrap: 'on',
      formatOnPaste: true,
      formatOnType: true,
      fontSize: 15,
      scrollbar: {
        vertical: 'auto',
        horizontal: 'auto'
      }
    });

    monacoEditor.onDidChangeModelContent(() => {
      try {
        const value = monacoEditor.getValue();
        const parsed = JSON.parse(value);
        requestJson.value = parsed;
        form.request = value;
        jsonError.value = '';
      } catch {
        jsonError.value = 'Invalid JSON format';
        form.request = monacoEditor.getValue();
      }

      updateEditorHeight();
    });

    updateEditorHeight();
  }
}

function updateEditorHeight() {
  if (monacoEditor && monacoContainer.value) {
    const lineCount = monacoEditor.getModel().getLineCount();
    const lineHeight = monacoEditor.getOption(monaco.editor.EditorOption.lineHeight);
    const contentHeight = lineCount * lineHeight + 20;

    const height = Math.max(200, Math.min(500, contentHeight));

    monacoContainer.value.style.height = `${height}px`;
    monacoEditor.layout();
  }
}

const hours = Array.from({ length: 24 }, (_, i) => {
  const hour = String(i).padStart(2, '0') + ":00";
  return { id: hour, name: hour };
});

const errHandler = useFormErrorHandler(form);

const tomorrowDate = computed(() => {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  return tomorrow.toISOString().split('T')[0];
});

const minEndDate = computed(() => {
  if (!form.start_date) return tomorrowDate.value;

  const startDate = new Date(form.start_date);
  startDate.setDate(startDate.getDate() + 1);
  return startDate.toISOString().split('T')[0];
});

function validateStartDate() {
  const tomorrow = new Date(tomorrowDate.value);
  const selectedDate = new Date(form.start_date);

  if (selectedDate < tomorrow) {
    form.errors.start_date = t('scheduled_works.validation.start_date_past');
    return false;
  }

  delete form.errors.start_date;

  if (form.end_date) {
    validateEndDate();
  }

  return true;
}

function validateEndDate() {
  if (!form.start_date) {
    form.errors.end_date = t('scheduled_works.validation.start_date_required');
    return false;
  }

  const startDate = new Date(form.start_date);
  const endDate = new Date(form.end_date);

  if (endDate <= startDate) {
    form.errors.end_date = t('scheduled_works.validation.end_date_invalid');
    return false;
  }

  delete form.errors.end_date;
  return true;
}

onMounted(() => {
  form.request = JSON.stringify(requestJson.value, null, 2);
  nextTick(initMonacoEditor);
});

onUnmounted(() => {
  if (monacoEditor) {
    monacoEditor.dispose();
    monacoEditor = null;
  }
});

function createScheduledWork() {
  const isStartDateValid = validateStartDate();
  const isEndDateValid = validateEndDate();

  if (!isStartDateValid || !isEndDateValid) {
    toast.error(t('scheduled_works.validation.fix_errors'));
    return;
  }

  form.simulate_id = route.params.id;
  store.Create(form)
    .then((res) => {
      router.push({ name: "simulate-view", params: { id: route.params.id } });
      toast.success(res);
    })
    .catch(errHandler);
}
</script>