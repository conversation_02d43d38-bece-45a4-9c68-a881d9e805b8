<template>
  <div class="p-4 bg-white dark:bg-neutral-800 rounded-lg shadow">
    <div v-if="loading" class="flex items-center justify-center h-64">
      <MonoIcon class="size-20 animate-spin" name="spin" />
    </div>

    <div v-else>
      <div class="border border-gray-200 dark:border-neutral-700 rounded-lg overflow-hidden mb-4">
        <div class="bg-gray-50 dark:bg-neutral-700 px-4 py-3 border-b">
          <h3 class="text-sm font-medium text-gray-800 dark:text-neutral-200">
            {{ $t('simulates.default_request.json_editor') }}
          </h3>
        </div>
        <div class="p-4">
          <json-editor
            v-model="jsonData"
            :options="editorOptions"
            class="jse-theme-dark"
            style="min-height: 100px"
          />
        </div>
      </div>

      <button
        @click="saveDefaultRequest"
        :disabled="saving"
        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
      >
        <MonoIcon class="size-4" name="lock" />
        {{ saving ? $t('general.saving') : $t('general.save') }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue';
import { useRoute } from 'vue-router';
import { useSimulateStore } from '@/store/submodules/simulate';
import JsonEditor from 'json-editor-vue';
import 'vanilla-jsoneditor/themes/jse-theme-dark.css';

const { t } = useI18n();
const route = useRoute();
const store = useSimulateStore();
const toast = inject('toast');

const loading = ref(true);
const saving = ref(false);
const jsonData = ref({});
const defaultRequestId = ref(null);

const editorOptions = {
  mode: 'code',
  mainMenuBar: false,
  navigationBar: false,
  statusBar: true
};

async function loadDefaultRequest() {
  loading.value = true;
  try {
    const response = await store.GetDefaultRequest(route.params.id, '2');
    if (response) {
      defaultRequestId.value = response.id;
      jsonData.value = typeof response.value === 'string'
        ? JSON.parse(response.value)
        : response.value;
    } else {
      jsonData.value = {};
    }
  } catch (err) {
    jsonData.value = {};
  } finally {
    loading.value = false;
  }
}

async function saveDefaultRequest() {
  saving.value = true;
  try {
    const payload = {
      simulate_id: route.params.id,
      type: 2,
      content: JSON.stringify(jsonData.value, null, 2)
    };
    if (defaultRequestId.value) {
      payload.id = defaultRequestId.value;
    }
    const res = await store.AddDefaultRequest(payload);
    defaultRequestId.value = res.id;
    toast.success(t('simulates.default_request.success.saved'));
  } catch {
    toast.error(t('simulates.default_request.error.save'));
  } finally {
    saving.value = false;
  }
}

onMounted(loadDefaultRequest);
</script>
