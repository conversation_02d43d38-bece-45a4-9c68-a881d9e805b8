<template>
  <div
    class="flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <t-table 
      :data="items.list" 
      :columns="tableProperties.columns" 
      entity-locale-name="simulate_logs"
      :page="Number(queries.page)" 
      :per_page="Number(queries.per_page)" 
      :total="items.total"
      :total_pages="items.total_pages" 
      @page-change="pageChange" 
      @per-page-change="perPageChange" 
      :loading="loading"
      :filters="tableProperties.filters" 
      @filter-change="onFilterChange"
      :error="errorRes.msg" 
      inlinesearch
    >
      <template v-slot:row-actions="itemData">
        <div class="flex items-center gap-2" :class="tableProperties.columns[tableProperties.columns.length - 1].align === 'center'
          ? 'justify-center'
          : 'justify-start'
          ">
          <div v-for="action in tableProperties.actions">
            <router-link v-if="action.route" :to="{ name: action.route, params: { id: itemData.item.id } }"
              class="px-2 py-1 text-xs text-white rounded-md flex items-center gap-2 transition-colors" :class="[
                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
              ]">
              <t-icon :name="action.icon" :class="[
                action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
              ]" />
              {{ action.label }}
            </router-link>
            <button v-else @click="action.function(itemData.item)"
              class="px-2 py-1 text-xs text-white rounded-md flex items-center gap-2 transition-colors" :class="[
                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
              ]">
              <t-icon :name="action.icon" :class="[
                action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
              ]" />
              {{ action.label }}
            </button>
          </div>
        </div>
      </template>
    </t-table>
  </div>

  <t-delete-modal v-if="deleteModal" :is-active="deleteModal" @deleteData="deleteItem" @closeModal="
    deleteModal = false;
  deleteRelations = {};
  ">
    <template #content>
      <p class="text-md">{{ $t("general.delete_confirm") }}</p>
      <p class="text-xs leading-5">
        <span class="font-semibold text-rose-400">
          {{ $t("general.delete_label", { item: deleteRelations.name }) }}
        </span>
      </p>
    </template>
  </t-delete-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useLogStore } from '@/store/submodules/log';

const store = useLogStore();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();

const loading = ref(true);

const errorRes = reactive({
  msg: "",
  status: "",
});

const queries = reactive({
  page: route.query.page ? route.query.page : 1,
  per_page: route.query.per_page ? route.query.per_page : 10,
});

const deleteModal = ref(false);
const deleteRelations = ref({});

function setDeleteItem(item) {
  deleteRelations.value = item;
  deleteModal.value = true;
}

function deleteItem(data) {
  localStorage.hash_id = deleteRelations.value.hash_id;
  store.Delete(deleteRelations.value.id).finally(() => {
    deleteModal.value = false;
  });
}

const filterlist = ref(
  Object.keys(route.query)
    .filter((key) => key !== "page" && key !== "per_page" && key !== "tab")
    .map((key) => {
      return { [key]: route.query[key] };
    }),
);

store
  .GetSimulateLogs(route.params.id, queries.page, queries.per_page, filterlist.value)
  .catch((err) => {
    errorRes.msg = "Something went wrong";
  })
  .finally(() => {
    loading.value = false;
  });


function pageChange(pageNum) {
  loading.value = true;
  queries.page = Number(pageNum);

  router.push({
    name: route.name,
    query: {
      ...route.query,
      page: queries.page,
      per_page: queries.per_page,
    },
  });

  store
    .GetSimulateLogs(route.params.id, pageNum, queries.per_page, filterlist.value)
    .then(() => {
      if (errorRes.msg !== "") {
        errorRes.msg = "";
      }
    })
    .catch((err) => {
      errorRes.msg = "Something went wrong";
    });

  loading.value = false;
}

function perPageChange(per_page) {
  loading.value = true;
  queries.per_page = Number(per_page);
  router.push({
    name: route.name,
    query: {
      ...route.query,
      page: queries.page,
      per_page: queries.per_page,
    },
  });

  store
    .GetSimulateLogs(route.params.id, queries.page, per_page, filterlist.value)
    .then(() => {
      if (errorRes.msg !== "") {
        errorRes.msg = "";
      }
    })
    .catch((err) => {
      errorRes.msg = "Something went wrong";
    });

  loading.value = false;
}



const items = computed(() => {
  const list = store.items.rows ? store.items.rows : [];
  const total_pages = store.items.total_pages;
  const per_page = store.items.per_page;
  const page = store.items.page;
  const total = store.items.total;
  return {
    list,
    total_pages,
    per_page,
    page,
    total,
  };
});

function onFilterChange(filters) {
  loading.value = true;
  filterlist.value = filters;

  filterlist.value.forEach((filter) => {
    Object.keys(filter).forEach((key) => {
      if (
        filter[key] !== null &&
        filter[key] !== "" &&
        filter[key] !== undefined &&
        filter[key] !== "undefined"
      ) {
        queries[key] = filter[key];
      } else {
        delete queries[key];
        delete filter[key];
      }
    });
  });

  const logQueries = {
    page: queries.page,
    per_page: queries.per_page,
  };

  filterlist.value.forEach((filter) => {
    Object.keys(filter).forEach((key) => {
      if (filter[key] !== null && filter[key] !== "" && filter[key] !== undefined) {
        logQueries[key] = filter[key];
      }
    });
  });

  router.push({
    name: route.name,
    query: {
      ...route.query,
      ...logQueries,
    },
  });

  store
    .GetSimulateLogs(route.params.id, queries.page, queries.per_page, filterlist.value)
    .then(() => {
      if (errorRes.msg !== "") {
        errorRes.msg = "";
      }
    })
    .catch((err) => {
      console.log("GetSubmerchants", err);
      errorRes.msg = "Something went wrong";
    });

  loading.value = false;
}

const tableProperties = {
  get: "getSimulateLogs",
  storeGetter: "simulate_logs",
  listRoute: "",
  id: route.params.id,
  paginationOptions: {
    page: route.query.page ? route.query.page : 1,
    per_page: route.query.per_page ? route.query.per_page : 10,
  },
  columns: [
    {
      row: "created_at",
      label: "Created At",
      sortable: true,
      align: "center",
      mobile: true,
      visible: true,
    },
    {
      row: "type",
      label: "Type",
      sortable: true,
      align: "center",
      mobile: true,
      visible: true,
    },
    {
      row: "source",
      label: "Source",
      sortable: true,
      align: "center",
      mobile: true,
      visible: true,
    },
    {
      row: "proto",
      label: "Protocol",
      sortable: true,
      align: "center",
      mobile: false,
      visible: true,
    },
    {
      row: "message",
      label: "message",
      sortable: true,
      align: "center",
      mobile: false,
      visible: true,
      modifier: (value) => {
        if (!value) return '';
        return value.length > 25 ? value.substring(0, 25) + '...' : value;
        },
    },
    {
      row: "is_retried",
      label: "Retried",
      sortable: true,
      align: "center",
      mobile: false,
      visible: true,
      // TODO: localization
      modifier: (value) => {
        return Boolean(value) ? 'retried' : 'Not retried yet';
      },
    },
    {
      row: "actions",
      label: t("general.actions"),
      sortable: false,
      align: "center",
      mobile: true,
      visible: true,
    },
  ],
  filters: [],
  actions: [
    {
      label: t("general.view"),
      icon: "eye",
      color: "indigo",
      route: "simulate-log-detail",
    },
  ],
};

</script>