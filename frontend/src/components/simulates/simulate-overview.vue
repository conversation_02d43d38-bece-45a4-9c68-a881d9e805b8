<template>
  <div
    class="xl:p-5 flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <div v-if="loading" class="flex items-center justify-center h-full">
      <MonoIcon class="size-20 animate-spin" name="spin" />
    </div>
    <div v-else-if="error" class="flex items-center h-full p-5">
      <div class="flex flex-col md:flex-row gap-2 items-center justify-center w-full h-full">
        <MonoIcon class="size-20 text-rose-600 dark:text-rose-500 md:pt-2" name="exclamation" />
        <div class="text-center md:text-left">
          <h2 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            {{ error.title || $t("general.server_error") }}
          </h2>
          <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
            {{ error.message || $t("general.server_error_description") }}
          </p>
        </div>
      </div>
    </div>
    <div v-else>
      <form @submit.prevent="editName()" class="space-y-4 p-2">
        <div class="w-full px-2 sm:px-0">
          <div class="space-y-4">
            <!-- First Row: ID and Access Token -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('simulates.id') }}
                </label>
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                  <input type="text" :value="simulate.id" readonly
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm break-all" />
                  <button type="button" @click="copyToClipboard(simulate.id, 'ID')"
                    class="flex-shrink-0 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                      </path>
                    </svg>
                    <span class="ml-2 sm:hidden">Copy</span>
                  </button>
                </div>
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('simulates.access_token') }}
                </label>
                <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
                  <input type="text" :value="showFullToken ? simulate.access_token : maskedToken" readonly
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm break-all" />
                  <div class="flex gap-2 flex-shrink-0">
                    <button type="button" @click="toggleTokenVisibility"
                      class="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center"
                      :title="showFullToken ? 'Hide token' : 'Show token'">
                      <svg v-if="showFullToken" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878l-1.414-1.414M14.12 14.12l1.414 1.414M14.12 14.12L15.536 15.536M14.12 14.12l1.414-1.414">
                        </path>
                      </svg>
                      <svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                        </path>
                      </svg>
                      <span class="ml-2 sm:hidden">{{ showFullToken ? 'Hide' : 'Show' }}</span>
                    </button>
                    <button type="button" @click="copyToClipboard(simulate.access_token, 'Access Token')"
                      class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center"
                      title="Copy token">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                        </path>
                      </svg>
                      <span class="ml-2 sm:hidden">Copy</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                {{ $t('simulates.name') }}
              </label>
              <input type="text" v-model="simulate.name"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('simulates.created_at') }}
                </label>
                <input type="text" :value="simulate.created_at" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('simulates.organization_name') }}
                </label>
                <input type="text" :value="simulate.organization_name" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm" />
              </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('simulates.changed_by') }}
                </label>
                <input type="text" v-model="simulate.changed_by" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('simulates.status') }}
                </label>
                <div class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm flex items-center">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="statusBadgeClass"
                  >
                    {{ versionStatus }}
                  </span>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('simulates.version_name') }}
                </label>
                <input type="text" v-model="simulate.version_name" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>

              <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ $t('simulates.last_working_time') }}
                </label>
                <input type="text" v-model="simulate.last_working_time" readonly
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm" />
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-center">
          <button type="submit" class="px-4 py-2 bg-indigo-600 rounded-md">
            {{ $t("general.update") }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, inject } from 'vue';
import { useRoute } from 'vue-router';
import { useSimulateStore } from '@/store/submodules/simulate';
const { t } = useI18n();

const simulateStore = useSimulateStore();
const route = useRoute();
const toast = inject("toast");

const loading = ref(true);
const simulate = ref(null);
const showFullToken = ref(false);

const maskedToken = computed(() => {
  if (!simulate.value?.access_token) return '';
  const token = simulate.value.access_token;
  if (token.length <= 8) return token;
  return token.substring(0, 8) + '...';
});

const versionStatus = computed(() => {
  if (!simulate.value?.version_number && simulate.value?.version_number !== 0) return 'Unknown';

  const versionNumber = parseInt(simulate.value.version_number);

  if (versionNumber === 0) {
    return 'First Version';
  } else {
    return `Version ${versionNumber}`;
  }
});

const statusBadgeClass = computed(() => {
  if (!simulate.value?.version_number && simulate.value?.version_number !== 0) {
    return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  }

  const versionNumber = parseInt(simulate.value.version_number);

  if (versionNumber === 0) {
    return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
  } else {
    return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
  }
});

async function copyToClipboard(text, label) {
  try {
    await navigator.clipboard.writeText(text);
    if (toast) {
      toast.success(`${label} copied to clipboard!`);
    } else {
      alert(`${label} copied to clipboard!`);
    }
  } catch (err) {
    console.error('Failed to copy: ', err);
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);

    if (toast) {
      toast.success(`${label} copied to clipboard!`);
    } else {
      alert(`${label} copied to clipboard!`);
    }
  }
}

function toggleTokenVisibility() {
  showFullToken.value = !showFullToken.value;
}

onMounted(async () => {
  if (route.params.id) {
    try {
      const res = await simulateStore.GetSimulateDetail(route.params.id);
      simulate.value = res;
    } catch (err) {
      console.error('API Error:', err);
    }
  } else {
    console.log('No route.params.id found');
  }

  loading.value = false;
});

function editName() {
  try {
    simulateStore.UpdateSimulateName(route.params.id, simulate.value.name);
    toast.success(t('general.success'));
  } catch (err) {
    console.error('API Error:', err);
  }
}
</script>