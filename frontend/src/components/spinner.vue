<template>
    <svg class="animate-spin" :class="wrapper" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-40 stroke-current" :class="fill" cx="12" cy="12" r="10" stroke-width="4" />
        <path class="opacity-75 fill-current" :class="fill"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
    </svg>
</template>

<script>
export default {
    name: "Spin",
    props: {
        size: {
            type: String,
            required: false,
            default: '6'
        },
        color: {
            type: String,
            required: false,
            default: 'slate-700'
        },
    },
    computed: {
        wrapper() {
            return [`h-${this.size}`, `w-${this.size}`]
        },
        fill() {
            return [`text-${this.color}`]
        }
    }
}
</script>