<template>
  <div class="card mx-auto max-w-4xl">
    <div class="flex justify-center mt-3 mb-3 text-2xl">{{ title }}</div>
    <div class="grid grid-cols-2 gap-4">
      <div class="gap-4" v-for="index in datas" :key="index">
        <div class="text-sm">{{ index.name }}</div>
        <div class="font-bold text-lg rounded ">{{ index.data }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { defineProps, computed } from "vue";
const props = defineProps({
  title: {
    type: String,
    default: "title",
    required: true,
  },
  data: {
    type: Object,
    default: () => { },
    required: true,
  },
});
const keys = Object.getOwnPropertyNames(props.data);
const datas = computed(() => {
  return keys.map((key) => {
    return props.data[key];
  });
});
</script>
