<template>
    <!-- <button type="button" class="inline-flex items-center gap-2 font-bold text-white rounded-md ">
      <MonoIcon name="question-mark" class="w-5 h-5 text-black dark:text-white" @click="show = !show" />
    </button> -->

    <div class="relative z-10" aria-labelledby="slide-over-title" role="dialog" aria-modal="true" v-if="show">
        <!-- Background backdrop, show/hide based on slide-over state. -->
        <div class="fixed inset-0 bg-black/80 backdrop-blur"></div>

        <div class="fixed inset-0 overflow-hidden">
            <div class="absolute inset-0 overflow-hidden" @click="closeOnClickedOutside">
                <div class="fixed inset-y-0 right-0 flex max-w-full pl-10 pointer-events-none">
                    <div ref="wikiContent" class="w-screen max-w-md pointer-events-auto">
                        <div class="flex flex-col h-full overflow-y-scroll bg-gray-800 shadow-xl">
                            <div class="px-4 py-6 bg-indigo-700 sm:px-6">
                                <div class="flex items-center justify-between">
                                    <h2 class="text-base font-semibold leading-6 text-white" id="slide-over-title">{{
                                        content.name }}</h2>
                                    <div class="flex items-center ml-3 h-7">
                                        <button type="button" @click="show = !show"
                                            class="relative text-indigo-200 bg-indigo-700 rounded-md hover:text-white focus:outline-none focus:ring-2 focus:ring-white">
                                            <span class="absolute -inset-2.5"></span>
                                            <span class="sr-only">Close panel</span>
                                            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                                stroke="currentColor" aria-hidden="true">
                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                            </div>


                            <WikiContent :wiki="item" v-for="item in content.wiki_contents" :key="item.id" />

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { securedAxios } from "@/utils/axios";

const { t } = useI18n();
const processing = ref(false);
const error = ref(null);
const success = ref(null);
const wikiContent = ref(null);

const props = defineProps({
    Name: String,
});

const content = ref({
    name: '',
    labels: [],
    wiki_contents: [],
});
const show = ref(false);

// watch show
watch(show, (val) => {
    if (val) {
        // get /api/v1/wiki/:name with securedAxios
        // if error, set error.value
        // if success, set success.value
        // if processing, set processing.value

        if (props.Name === '') {
            return;
        }
        if (content.value.name !== '') {
            return;
        }

        processing.value = true;
        securedAxios.get(`/wikis/details/${props.Name}`).then(res => {
            content.value = res.data;
            console.log(res)
        }).catch(err => {
            console.log(err);
        }).finally(() => {
            processing.value = false;
        });
    }
});


const closeOnClickedOutside = (e) => {
    if (show.value && !wikiContent.value.contains(e.target)) {
        show.value = false;
    }
};

const closeOnEscape = (e) => {
    if (show.value && e.key === 'Escape') {
        show.value = false;
    }
};

onMounted(() => {
    window.addEventListener('keydown', closeOnEscape);
});

defineExpose({
    show,
});

</script>