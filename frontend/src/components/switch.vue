<template>
    <button
        type="button"
        class="bg-gray-200 dark:bg-gray-700 relative inline-flex items-center flex-shrink-0 h-8 w-16 rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none"
        role="switch"
        @click="toggle"
        :aria-checked="modelValue"
    >
        <span class="sr-only">Use setting</span>
        <span
            class="pointer-events-none relative h-6 w-6 rounded-full shadow transform ring-0 transition ease-in-out duration-200"
            :class="[modelValue ? 'translate-x-9' : 'translate-x-1', modelValue ? 'bg-green-600' : 'bg-red-600']"
        >
            <span
                class="absolute inset-0 h-full w-full flex items-center justify-center"
                aria-hidden="true"
            >
                <svg
                    class="h-3 w-3 text-gray-50"
                    fill="currentColor"
                    viewBox="0 0 12 12"
                    v-if="modelValue"
                >
                    <path
                        d="M3.707 5.293a1 1 0 00-1.414 1.414l1.414-1.414zM5 8l-.707.707a1 1 0 001.414 0L5 8zm4.707-3.293a1 1 0 00-1.414-1.414l1.414 1.414zm-7.414 2l2 2 1.414-1.414-2-2-1.414 1.414zm3.414 2l4-4-1.414-1.414-4 4 1.414 1.414z"
                    />
                </svg>
                <svg
                    class="h-3 w-3 text-gray-50"
                    fill="none"
                    viewBox="0 0 12 12"
                    v-else
                >
                    <path
                        d="M4 8l2-2m0 0l2-2M6 6L4 4m2 2l2 2"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                    />
                </svg>
            </span>
        </span>
    </button>
</template>


<script setup>
const props = defineProps({
    modelValue: Boolean,
});

const emits = defineEmits(['update:modelValue']);

function toggle() {
    emits("update:modelValue", !props.modelValue)
}


</script>