<template>
	<footer class="h-[40px] sm:h-[64px]">
		<div class="p-2 sm:p-5 lg:px-8 mx-auto">
			<div class="flex justify-between items-center">
				<p class="text-xs sm:text-sm text-gray-500 dark:text-neutral-500">
					{{ new Date().getFullYear() }} All rights reserved. &copy; {{ version }}
				</p>
				<ul>
					<li
						class="inline-block relative pe-5 text-xs sm:text-sm text-gray-500 align-middle last:pe-0 last-of-type:before:hidden before:absolute before:top-1/2 before:end-2 before:-translate-y-1/2 before:w-px before:h-3.5 before:bg-gray-400 before:rotate-[18deg] dark:text-neutral-500 dark:before:bg-neutral-600">
						<a class="hover:text-blue-600 focus:outline-none focus:underline dark:hover:text-neutral-200"
							href="https://wiki.tapsilat.dev" target="_blank">
							Documentation
						</a>
					</li>
				</ul>
			</div>
		</div>
	</footer>

</template>

<script setup>
import { version } from '../../package.json';
</script>
