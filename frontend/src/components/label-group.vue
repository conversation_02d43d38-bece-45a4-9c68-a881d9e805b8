<template>
    <div class="card mx-auto max-w-4xl">
        <div class="col-span-6">
            <div class="flex justify-between items-center mt-2 mb-2">
                <label for="ports" class="block text-sm font-medium">Labels</label>
            </div>
        </div>

        <div class="space-y-2">
            <div class="flex flex-row flex-wrap gap-2">
                <span
                    class="inline-flex select-none items-center py-0.5 pl-2 pr-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-700"
                    v-for="(item, index) in labels" :key="index">
                    {{ item }}
                    <button type="button"
                        class="flex-shrink-0 ml-0.5 h-4 w-4 rounded-full inline-flex items-center justify-center text-indigo-400 hover:bg-indigo-200 hover:text-indigo-500 focus:outline-none focus:bg-indigo-500 focus:text-white"
                        @click="deleteLabel(index)">
                        <span class="sr-only">Remove option</span>
                        <svg class="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                            <path stroke-linecap="round" stroke-width="1.5" d="M1 1l6 6m0-6L1 7" />
                        </svg>
                    </button>
                </span>
            </div>

            <div class="flex items-center gap-2">
                <div class="relative w-full">
                    <input type="text" placeholder="label" v-model="label" class="input-text" />
                    <div class="absolute border border-gray-400 dark:border-gray-600 flex flex-col min-w-full top-11 max-h-36 overflow-auto bg-gray-200 dark:bg-gray-700 px-4 py-2 rounded-md"
                        v-if="filteredList.length > 0 && label">
                        <span v-for="item in filteredList" :key="item" @click="addLabelFromList(item)"
                            class="hover:bg-gray-400 dark:hover:bg-gray-800 transition-colors px-2 py-1 rounded-md">
                            {{ item }}
                        </span>
                    </div>
                </div>

                <button type="button" class="px-4 py-2 rounded-md  transition-colors focus:outline-none"
                    @click="addLabel(index)"
                    :class="[!label ? 'cursor-not-allowed bg-red-800/25' : 'cursor-pointer bg-gray-200 dark:bg-gray-700']"
                    :disabled="!label">+</button>
            </div>
        </div>
    </div>

</template>

<script setup>
import { securedAxios } from '@/utils/axios';
import { onMounted, ref, inject, computed } from 'vue';

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => [],
    },
})

const toast = inject('toast')
const labels = ref(props.modelValue)
const label = ref('')
const labelList = ref([])

// onMounted(() => {
//     setTimeout(() => {
//         labels.value = props.modelValue
//     }, 200);
// })

const emits = defineEmits(['update:modelValue']);

function addLabel() {
    label.value = label.value.trim()
    if (labels.value.includes(label.value)) {
        label.value = ''
    }
    if (label.value) {
        labels.value.push(label.value)
        label.value = ''
    }
    emits("update:modelValue", labels.value);
}

function addLabelFromList(data) {
    if (labels.value.includes(data)) {
        label.value = ''
    }
    if (label.value) {
        labels.value.push(data)
        label.value = ''
    }
    emits("update:modelValue", labels.value);
}

function deleteLabel(index) {
    labels.value.splice(index, 1);
    toast.success('Label removed')
    emits("update:modelValue", labels.value);
}

securedAxios.get(`/labels?page=1&per_page=9999`).then(res => {
    labelList.value = res.data.rows
})

const filteredList = computed(() => {
    return labelList.value ? labelList.value.filter(item => item.toLowerCase().includes(label.value.toLowerCase())) : []
})


</script>
