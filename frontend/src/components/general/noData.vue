<script setup>
const props = defineProps({
    title: {
        type: String,
        default: ""
    },
    description: {
        type: String,
        default: ""
    },
    icon: {
        type: String,
    },
    pageSize: {
        type: Boolean,
        default: false
    }
})
</script>


<template>
    <div class="flex flex-col items-center h-full justify-center pb-6">
        <div class="
                    flex
                    flex-col
                    items-center
                    justify-center
                    text-gray-500
                    text-sm
                    dark:text-gray-400
                    py-2
                ">
            <div v-if="icon && !pageSize"
                class="rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-6"
                :class="pageSize ? 'w-20 h-20' : 'w-14 h-14'">
                <Icon class="w-7 h-7 text-gray-500 dark:text-gray-300" :name="icon" />
            </div>
            <div v-else-if="pageSize" class="mb-6">
                <Icon class="w-24 h-24 text-gray-500" :name="icon" />
            </div>
            <h3 v-if="$slots.title" class="text-lg font-semibold text-gray-700 dark:text-gray-200">
                <slot name="title">
                    {{ title }}
                </slot>
            </h3>
            <p v-if="$slots.description" class="text-gray-500 dark:text-gray-300 text-sm text-center">

                <slot name="description">

                </slot>
            </p>

        </div>
        <slot name="action">

        </slot>

    </div>
</template>