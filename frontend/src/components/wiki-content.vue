<template>
    <div class="flex flex-col">
        <div class="flex items-center justify-between transition cursor-pointer text-white px-4 py-4"
            @click="show = !show" :class="[show ? 'bg-indigo-300' : 'bg-gray-600']">
            <div class="flex items-center gap-2">
                <span>{{ props.wiki.name }}</span>
                <span class="text-xs leading-3 border border-indigo-500 p-1 rounded-md">
                    {{localeOptions.find((locale) => locale.id === props.wiki.locale).name}}
                </span>
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                stroke="currentColor" class="w-6 h-6 transition" :class="{ 'rotate-180': show }">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
            </svg>

        </div>
        <div class="p-2" v-if="show">
            <markdown-editor-preview :data="props.wiki.content" />
        </div>
    </div>
</template>

<script setup>
import { localeOptions } from "@/constant/config";

const props = defineProps({
    wiki: Object,
});

const show = ref(false);

</script>