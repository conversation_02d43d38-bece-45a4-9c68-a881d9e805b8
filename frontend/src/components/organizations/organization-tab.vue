<script setup>

const emit = defineEmits(['update:activeTab'])
const props = defineProps({
    activeTab: {
        type: String,
        required: false,
        default: "general"
    }, tabs: {
        type: Array,
        required: false,
    }
})
const setCurrentTab = (tab, withId = false) => {
    if (withId) {
        let selectedTab = props.tabs.find((tabObject) => tabObject.id == tab)
        selectedTab ? currentTab.value = selectedTab : ''
    } else {
        currentTab.value = tab
    }

}
const currentTab = computed({
    get: () => props.activeTab,
    set: (value) => emit('update:activeTab', value)
})
const activeButtonClasses = "text-slate-900 shadow-sm border-l-4 border-slate-900 dark:border-inherit bg-slate-100 dark:bg-inherit dark:text-inherit"
const activeChildButtonClasses = "text-slate-900 shadow-sm border-l-4 border-slate-900 dark:border-inherit bg-slate-100 dark:bg-inherit dark:text-inherit"
</script>


<template>
    <ul class="hidden md:block card max-w-sm">
        <li v-for="tab in tabs">
            <button @click="setCurrentTab(tab)"
                class="flex flex-row items-center justify-start w-full p-4 space-x-4 rounded-md "
                :class="currentTab.id === tab.id ? activeButtonClasses : ''">
                <Icon :name="tab.icon" class="w-6 h-6 hidden sm:inline  font-extrabold" />
                <span>{{ tab.title }}</span>
            </button>
            <ul v-for="child in tab?.childrens" :class="currentTab.id === tab.id ? 'block' : 'hidden'">
                <li>
                    {{ child.title }}
                </li>
            </ul>
        </li>
    </ul>
</template>