<template>
    <div
        class="flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700"
    >
        <t-table
            :data="healthCheckStatus"
            :columns="columns"
            :actions="action"
            :loading="loading"
        >
            <template v-slot:row-name="item">
                <span>{{ $t(`health_check.${item.item.name}`) }}</span>
            </template>
            <template v-slot:row-status="item">
                <span
                    :class="item.item.status ? 'bg-green-500' : 'bg-red-500'"
                    class="px-2 py-1 rounded-md"
                    >{{ item.item.status }}</span
                >
            </template>
            <template v-slot:row-action="item">
                <div class="flex items-center gap-2 justify-end">
                    <router-link
                        v-if="!item.item.status"
                        :to="`/${item.item.name}`"
                        class="px-2 py-1 text-xs text-white rounded-md flex items-center gap-2 transition-colors"
                        :class="[
                            action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                            action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                            action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                            action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                            action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
                            item.item.status === true ? 'cursor-not-allowed' : 'cursor-pointer',
                        ]"
                    >
                        <t-icon
                            :name="action.icon"
                            :class="[
                                action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                                action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                                action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                                action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                                action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
                            ]"
                        />
                        {{ action.label }}
                    </router-link>
                </div>
            </template>
        </t-table>
    </div>
</template>

<script setup>
import { useOrganizationStore } from "@/store/submodules/organization";
import { securedAxios } from "@/utils/axios";

const store = useOrganizationStore();
const loading = ref(true);
const route = useRoute();
const toast = inject("toast");
const { t } = useI18n();

const organization = ref({
    name: null,
});

const healthCheckStatus = ref([]);

store.Detail(route.params.id).then((res) => {
    organization.value = res;
    loading.value = false;
});

function healthCheck() {
    securedAxios.get(`/organizations/health-check/${route.params.id}`).then((res) => {
        loading.value = false;
        healthCheckStatus.value = res.data;
    });
}

const columns = ref([
    { label: "Name", row: "name", sortable: false, align: "left", mobile: true, visible: true },
    { label: "Status", row: "status", sortable: false, align: "center", mobile: true, visible: true },
    { label: "Action", row: "action", sortable: false, align: "right", mobile: true, visible: true },
]);

const action = {
    color: "indigo",
    icon: "pencil",
    label: t("general.edit"),
};

onMounted(() => {
    healthCheck();
});

const flowProperties = {
    title: t("page_titles.organizations_health_check"),
    entity: "organization",
};
</script>
