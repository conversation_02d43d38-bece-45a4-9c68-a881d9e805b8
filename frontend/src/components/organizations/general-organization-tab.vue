<script setup>
const props = defineProps({
    formValues: {
        type: Object,
        required: true
    },
})
const emit = defineEmits(['update:formValues', 'onFileChange'])
const form = computed({
    get: () => props.formValues,
    set: (value) => emit('update:formValues', value)
})
const onFileChange = (e) => {
    emit('onFileChange', e)
}
</script>


<template>
    <div class="flex flex-col gap-4">
        <div class="card">
            <div class="grid grid-cols-12 gap-2">
                <InputBox :span="6" :label="$t('organizations.name')" v-model="form.name" />
                <div class="col-span-6 space-y-2">
                    <Selectbox :label="$t('organizations.parent')" v-model="form.parent_id" :options="organizations" />
                </div>

                <div class="col-span-6 space-y-2">
                    <Selectbox :label="$t('organizations.default_user_limit')" v-model="form.default_user_limit_id"
                        :options="limitList" />
                </div>

                <div class="col-span-6 space-y-2">
                    <Selectbox :label="$t('organizations.default_merchant_limit')" v-model="form.default_merchant_limit_id"
                        :options="limitList" />
                </div>
                <div class="col-span-6 md:col-span-4 space-y-2">
                    <label class="block text-sm font-medium">
                        {{ $t("organizations.ttl") }}
                    </label>
                    <input class="t-input" type="number" :min="0" :step="1" v-model="form.ttl" />
                </div>
                <div class="col-span-6 md:col-span-4 space-y-2">
                    <label class="block text-sm font-medium">
                        {{ $t("organizations.session_ttl") }}
                    </label>
                    <input class="t-input" type="number" :min="0" :step="1" v-model="form.session_ttl" />
                </div>
                <div class="col-span-6 md:col-span-4 space-y-2">
                    <label class="block text-sm font-medium">
                        {{ $t("organizations.retry_count") }}
                    </label>
                    <input class="t-input" type="number" :min="0" :max="99" :step="1" v-model="form.retry_count" />
                </div>
                <div class="col-span-6 md:col-span-3 space-y-2 flex flex-col items-center">
                    <label class="block text-sm font-medium">{{ $t("organizations.allow_payment") }}</label>
                    <Switch v-model="form.allow_payment" />
                </div>
                <div class="col-span-6 md:col-span-3 space-y-2 flex flex-col items-center">
                    <label class="block text-sm font-medium">{{ $t("organizations.allow_limit_check") }}</label>
                    <Switch v-model="form.allow_limit_check" />
                </div>
                <div class="col-span-6 md:col-span-3 space-y-2 flex flex-col items-center">
                    <label class="block text-sm font-medium">{{ $t("organizations.custom_checkout") }}</label>
                    <Switch v-model="form.custom_checkout" />
                </div>
                <div class="col-span-6 md:col-span-3 space-y-2">
                    <label class="block text-sm font-medium">{{ $t("organizations.allow_acquirer_rule") }}</label>
                    <Switch v-model="form.allow_acquirer_rule" />
                </div>
            </div>
        </div>
        <div class="card">
            <div class="grid grid-cols-2 gap-x-4">
                <div>
                    <img :src="form.logo" alt="logo" class="h-64 object-contain" v-if="form.logo" />
                    <p class="text-center" v-else>
                        {{ $t("organizations.no_logo") }}
                    </p>
                </div>
                <div>
                    <input type="file" @change="onFileChange" :span="6" :label="$t('organizations.logo')" />
                </div>
            </div>
        </div>
    </div>
</template>