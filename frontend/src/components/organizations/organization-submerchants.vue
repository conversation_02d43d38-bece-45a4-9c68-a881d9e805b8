<template>
    <div
        class="flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700"
    >
        <t-table
            :data="items.list"
            :columns="tableProperties.columns"
            entity-locale-name="organization_submerchants"
            :page="Number(queries.page)"
            :per_page="Number(queries.per_page)"
            :total="items.total"
            :total_pages="items.total_pages"
            @page-change="pageChange"
            @per-page-change="perPageChange"
            :loading="loading"
            :create-route="tableProperties.createRoute"
            :filters="tableProperties.filters"
            @filter-change="onFilterChange"
            :error="errorRes.msg"
            inlinesearch
        >
            <template v-slot:row-actions="itemData">
                <div
                    class="flex items-center gap-2"
                    :class="
                        tableProperties.columns[tableProperties.columns.length - 1].align === 'center'
                            ? 'justify-center'
                            : 'justify-start'
                    "
                >
                    <div v-for="action in tableProperties.actions">
                        <router-link
                            v-if="action.route"
                            :to="{ name: action.route, params: { id: itemData.item.id } }"
                            class="px-2 py-1 text-xs text-white rounded-md flex items-center gap-2 transition-colors"
                            :class="[
                                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
                            ]"
                        >
                            <t-icon
                                :name="action.icon"
                                :class="[
                                    action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                                    action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                                    action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                                    action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                                    action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
                                ]"
                            />
                            {{ action.label }}
                        </router-link>
                        <button
                            v-else
                            @click="action.function(itemData.item)"
                            class="px-2 py-1 text-xs text-white rounded-md flex items-center gap-2 transition-colors"
                            :class="[
                                action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                                action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                                action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                                action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                                action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
                            ]"
                        >
                            <t-icon
                                :name="action.icon"
                                :class="[
                                    action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                                    action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                                    action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                                    action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                                    action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
                                ]"
                            />
                            {{ action.label }}
                        </button>
                    </div>
                </div>
            </template>
        </t-table>
    </div>

    <t-delete-modal
        v-if="deleteModal"
        :is-active="deleteModal"
        @deleteData="deleteItem"
        @closeModal="
            deleteModal = false;
            deleteRelations = {};
        "
    >
        <template #content>
            <p class="text-md">{{ $t("general.delete_confirm") }}</p>
            <p class="text-xs leading-5">
                <span class="font-semibold text-rose-400">
                    {{ $t("general.delete_label", { item: deleteRelations.name }) }}
                </span>
            </p>
        </template>
    </t-delete-modal>
</template>

<script setup>
import { useOrganizationStore } from "@/store/submodules/organization";

const { t, te } = useI18n();
const route = useRoute();
const toast = inject("toast");
const store = useOrganizationStore();
const router = useRouter();
const organizationStore = useOrganizationStore();

const loading = ref(true);

const errorRes = reactive({
    msg: "",
    status: "",
});

const queries = reactive({
    page: route.query.page ? route.query.page : 1,
    per_page: route.query.per_page ? route.query.per_page : 10,
});

const deleteModal = ref(false);
const deleteRelations = ref({});

function setDeleteItem(item) {
    deleteRelations.value = item;
    deleteModal.value = true;
}

function deleteItem(data) {
    localStorage.hash_id = deleteRelations.value.hash_id;
    store.Delete(deleteRelations.value.id).finally(() => {
        deleteModal.value = false;
    });
}

const filterlist = ref(
    Object.keys(route.query)
        .filter((key) => key !== "page" && key !== "per_page")
        .map((key) => {
            return { [key]: route.query[key] };
        }),
);

store
    .GetSubmerchants(route.params.id, queries.page, queries.per_page, filterlist.value)
    .catch((err) => {
        errorRes.msg = "Something went wrong";
    })
    .finally(() => {
        loading.value = false;
    });

function pageChange(pageNum) {
    loading.value = true;
    queries.page = Number(pageNum);

    router.push({
        name: route.name,
        query: queries,
    });

    store
        .GetSubmerchants(route.params.id, pageNum, queries.per_page, filterlist.value)
        .then(() => {
            if (errorRes.msg !== "") {
                errorRes.msg = "";
            }
        })
        .catch((err) => {
            errorRes.msg = "Something went wrong";
        });

    loading.value = false;
}

function perPageChange(per_page) {
    loading.value = true;
    queries.per_page = Number(per_page);
    router.push({
        name: route.name,
        query: queries,
    });

    store
        .GetSubmerchants(route.params.id, queries.page, per_page, filterlist.value)
        .then(() => {
            if (errorRes.msg !== "") {
                errorRes.msg = "";
            }
        })
        .catch((err) => {
            errorRes.msg = "Something went wrong";
        });

    loading.value = false;
}

const items = computed(() => {
    const list = store.getSubmerchants.rows ? store.getSubmerchants.rows : [];
    const total_pages = store.getSubmerchants.total_pages;
    const per_page = store.getSubmerchants.per_page;
    const page = store.getSubmerchants.page;
    const total = store.getSubmerchants.total;
    return {
        list,
        total_pages,
        per_page,
        page,
        total,
    };
});

function onFilterChange(filters) {
    loading.value = true;
    filterlist.value = filters;

    filterlist.value.forEach((filter) => {
        Object.keys(filter).forEach((key) => {
            if (
                filter[key] !== null &&
                filter[key] !== "" &&
                filter[key] !== undefined &&
                filter[key] !== "undefined"
            ) {
                queries[key] = filter[key];
            } else {
                delete queries[key];
                delete filter[key];
            }
        });
    });

    router.push({
        name: route.name,
        query: queries,
    });

    store
        .GetSubmerchants(route.params.id, queries.page, queries.per_page, filterlist.value)
        .then(() => {
            if (errorRes.msg !== "") {
                errorRes.msg = "";
            }
        })
        .catch((err) => {
            console.log("GetSubmerchants", err);
            errorRes.msg = "Something went wrong";
        });

    loading.value = false;
}

const tableProperties = {
    get: "getOrganizationSubmerchants",
    storeGetter: "organization_submerchants",
    listRoute: "organizations-submerchants",
    id: route.params.id,
    paginationOptions: {
        page: route.query.page ? route.query.page : 1,
        per_page: route.query.per_page ? route.query.per_page : 10,
    },
    columns: [
        {
            row: "name",
            label: "Name",
            sortable: true,
            align: "left",
            mobile: true,
            visible: true,
        },
        {
            row: "email",
            label: "Email",
            sortable: true,
            align: "left",
            mobile: false,
            visible: true,
        },
        {
            row: "submerchant_type",
            label: "Type",
            sortable: true,
            align: "left",
            modifier: (value) => {
                return te("submerchants.type."+value.toLowerCase()) ? t("submerchants.type."+value.toLowerCase()) : value;
            },
            mobile: false,
            visible: true,
        },
        {
            row: "acquirer",
            label: "Acquirer",
            sortable: true,
            align: "left",
            mobile: false,
            visible: true,
        },
        {
            row: "actions",
            label: t("general.actions"),
            sortable: false,
            align: "center",
            mobile: true,
            visible: true,
        },
    ],
    filters: [],
    actions: [
        {
            label: t("general.view"),
            icon: "eye",
            color: "blue",
            route: "submerchants-view",
        },
    ],
};
</script>
