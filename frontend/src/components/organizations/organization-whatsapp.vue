<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { useOrganizationStore } from '@/store/submodules/organization';
import { securedAxios } from "@/utils/axios";
import tr from 'dayjs/locale/tr';

const verificationCode = ref('');
const loading = ref(true);
const showConfirmPopup = ref(false);
const showLoadingPopup = ref(false);
const showFailedPopup = ref(false);
const showSuccessPopup = ref(false);
const showLogoutSuccessPopup = ref(false);
const phoneNumber = ref('');
const selectedCountry = ref('+90');
const regId = ref([]);
const showPhonePopup = ref(false);
const showCodePopup = ref(false);
const countdown = ref(30);
const store = useOrganizationStore();
const route = useRoute();
const router = useRouter();

const countries = [
  { name: 'Afghanistan', code: '+93', flag: '🇦🇫' },
  { name: 'Albania', code: '+355', flag: '🇦🇱' },
  { name: 'Algeria', code: '+213', flag: '🇩🇿' },
  { name: 'Andorra', code: '+376', flag: '🇦🇩' },
  { name: 'Angola', code: '+244', flag: '🇦🇴' },
  { name: 'Argentina', code: '+54', flag: '🇦🇷' },
  { name: 'Armenia', code: '+374', flag: '🇦🇲' },
  { name: 'Australia', code: '+61', flag: '🇦🇺' },
  { name: 'Austria', code: '+43', flag: '🇦🇹' },
  { name: 'Azerbaijan', code: '+994', flag: '🇦🇿' },
  { name: 'Bahrain', code: '+973', flag: '🇧🇭' },
  { name: 'Bangladesh', code: '+880', flag: '🇧🇩' },
  { name: 'Belarus', code: '+375', flag: '🇧🇾' },
  { name: 'Belgium', code: '+32', flag: '🇧🇪' },
  { name: 'Belize', code: '+501', flag: '🇧🇿' },
  { name: 'Benin', code: '+229', flag: '🇧🇯' },
  { name: 'Bhutan', code: '+975', flag: '🇧🇹' },
  { name: 'Bolivia', code: '+591', flag: '🇧🇴' },
  { name: 'Bosnia and Herzegovina', code: '+387', flag: '🇧🇦' },
  { name: 'Botswana', code: '+267', flag: '🇧🇼' },
  { name: 'Brazil', code: '+55', flag: '🇧🇷' },
  { name: 'Brunei', code: '+673', flag: '🇧🇳' },
  { name: 'Bulgaria', code: '+359', flag: '🇧🇬' },
  { name: 'Burkina Faso', code: '+226', flag: '🇧🇫' },
  { name: 'Burundi', code: '+257', flag: '🇧🇮' },
  { name: 'Cambodia', code: '+855', flag: '🇰🇭' },
  { name: 'Cameroon', code: '+237', flag: '🇨🇲' },
  { name: 'Canada', code: '+1', flag: '🇨🇦' },
  { name: 'Chile', code: '+56', flag: '🇨🇱' },
  { name: 'China', code: '+86', flag: '🇨🇳' },
  { name: 'Colombia', code: '+57', flag: '🇨🇴' },
  { name: 'Congo', code: '+242', flag: '🇨🇬' },
  { name: 'Costa Rica', code: '+506', flag: '🇨🇷' },
  { name: 'Croatia', code: '+385', flag: '🇭🇷' },
  { name: 'Cuba', code: '+53', flag: '🇨🇺' },
  { name: 'Cyprus', code: '+357', flag: '🇨🇾' },
  { name: 'Czech Republic', code: '+420', flag: '🇨🇿' },
  { name: 'Denmark', code: '+45', flag: '🇩🇰' },
  { name: 'Dominican Republic', code: '+1', flag: '🇩🇴' },
  { name: 'Ecuador', code: '+593', flag: '🇪🇨' },
  { name: 'Egypt', code: '+20', flag: '🇪🇬' },
  { name: 'El Salvador', code: '+503', flag: '🇸🇻' },
  { name: 'Estonia', code: '+372', flag: '🇪🇪' },
  { name: 'Ethiopia', code: '+251', flag: '🇪🇹' },
  { name: 'Finland', code: '+358', flag: '🇫🇮' },
  { name: 'France', code: '+33', flag: '🇫🇷' },
  { name: 'Germany', code: '+49', flag: '🇩🇪' },
  { name: 'Ghana', code: '+233', flag: '🇬🇭' },
  { name: 'Greece', code: '+30', flag: '🇬🇷' },
  { name: 'Guatemala', code: '+502', flag: '🇬🇹' },
  { name: 'Honduras', code: '+504', flag: '🇭🇳' },
  { name: 'Hong Kong', code: '+852', flag: '🇭🇰' },
  { name: 'Hungary', code: '+36', flag: '🇭🇺' },
  { name: 'Iceland', code: '+354', flag: '🇮🇸' },
  { name: 'India', code: '+91', flag: '🇮🇳' },
  { name: 'Indonesia', code: '+62', flag: '🇮🇩' },
  { name: 'Iran', code: '+98', flag: '🇮🇷' },
  { name: 'Iraq', code: '+964', flag: '🇮🇶' },
  { name: 'Ireland', code: '+353', flag: '🇮🇪' },
  { name: 'Israel', code: '+972', flag: '🇮🇱' },
  { name: 'Italy', code: '+39', flag: '🇮🇹' },
  { name: 'Jamaica', code: '+1', flag: '🇯🇲' },
  { name: 'Japan', code: '+81', flag: '🇯🇵' },
  { name: 'Jordan', code: '+962', flag: '🇯🇴' },
  { name: 'Kazakhstan', code: '+7', flag: '🇰🇿' },
  { name: 'Kenya', code: '+254', flag: '🇰🇪' },
  { name: 'Kuwait', code: '+965', flag: '🇰🇼' },
  { name: 'Lebanon', code: '+961', flag: '🇱🇧' },
  { name: 'Malaysia', code: '+60', flag: '🇲🇾' },
  { name: 'Mexico', code: '+52', flag: '🇲🇽' },
  { name: 'Netherlands', code: '+31', flag: '🇳🇱' },
  { name: 'New Zealand', code: '+64', flag: '🇳🇿' },
  { name: 'Nigeria', code: '+234', flag: '🇳🇬' },
  { name: 'Norway', code: '+47', flag: '🇳🇴' },
  { name: 'Pakistan', code: '+92', flag: '🇵🇰' },
  { name: 'Peru', code: '+51', flag: '🇵🇪' },
  { name: 'Philippines', code: '+63', flag: '🇵🇭' },
  { name: 'Poland', code: '+48', flag: '🇵🇱' },
  { name: 'Portugal', code: '+351', flag: '🇵🇹' },
  { name: 'Russia', code: '+7', flag: '🇷🇺' },
  { name: 'Saudi Arabia', code: '+966', flag: '🇸🇦' },
  { name: 'South Africa', code: '+27', flag: '🇿🇦' },
  { name: 'Spain', code: '+34', flag: '🇪🇸' },
  { name: 'Sweden', code: '+46', flag: '🇸🇪' },
  { name: 'Switzerland', code: '+41', flag: '🇨🇭' },
  { name: 'Turkey', code: '+90', flag: '🇹🇷' },
  { name: 'United Kingdom', code: '+44', flag: '🇬🇧' },
  { name: 'United States', code: '+1', flag: '🇺🇸' },
  { name: 'Vietnam', code: '+84', flag: '🇻🇳' }
];

const requestLoginCode = async () => {
    try {
        if (!phoneNumber.value == "") {
            const fullPhoneNumber = `${selectedCountry.value}${phoneNumber.value}`;
            const id = route.params.id;
            const data = await store.GetWpCode(id, fullPhoneNumber);

            if (data && data.code) {
                verificationCode.value = data.code;
                showCodePopup.value = true;
                let remainingTime = 30;

                const countdownInterval = setInterval(() => {
                    remainingTime--;
                    countdown.value = remainingTime;

                    if (remainingTime <= 0) {
                        clearInterval(countdownInterval);
                    }
                }, 1000);

                checkDeviceStatus(data.reg_id, fullPhoneNumber);

                setTimeout(() => {
                    clearInterval(countdownInterval);
                    if (showCodePopup.value) {
                        showCodePopup.value = false;
                        showFailedPopup.value = true;
                    }
                }, 30000);
            }
        }
    } catch (err) {
        console.error("Error fetching code:", err);
    }
};

const checkDeviceStatus = async (regId, phone) => {
  try {
    const id = route.params.id;
    const checkData = await store.CheckDevice(id, regId, phone);
    if (checkData.is_active) {
      showCodePopup.value = false;
      showSuccessPopup.value = true;
    } else {
      showCodePopup.value = false;
      showFailedPopup.value = true;
    }
  } catch (err) {
    console.error("Error checking device status:", err);
    showCodePopup.value = false;
    showFailedPopup.value = true;
  }
};

function fetchRegId(){
    let id = route.params.id;
    securedAxios.get(`/organizations/wp/active/${id}`).then((res) => {
        if (res.data == true) {
            securedAxios.get(`/organizations/wp/regid/${id}`).then((res) => {
                loading.value = false;
                regId.value = [{ name: res.data }];
            });
        } else {
            showPhonePopup.value = true;
        }
        loading.value = false;
    });
}

const showConfirmation = () => {
  showConfirmPopup.value = true;
};

onMounted(() => {
    fetchRegId();
});

const logout = async () => {
  showConfirmPopup.value = false;
  showLoadingPopup.value = true;

  try {
    let id = route.params.id
    console.log("regid:", regId.value[0].name)
    await store.WpLogout(regId.value[0].name, id);
    showLoadingPopup.value = false;
    showLogoutSuccessPopup.value = true;
  } catch (error) {
    console.error('Error logging out:', error);
    showLoadingPopup.value = false;
  }
};

const logoutReload = () => {
  showLogoutSuccessPopup.value = false;
  router.go(0);
}

const columns = ref([
    { label: "Registration Id", row: "name", sortable: false, align: "left", mobile: true, visible: true },
    { label: "", row: "action", sortable: false, align: "right", mobile: true, visible: true },
]);

const action = {
    color: "indigo",
    icon: "pencil",
    // label: t("general.edit"),
    label: "test",
};

</script>

<template>
    <t-table :data="regId" :columns="columns" :actions="action" :loading="loading">
        <template v-slot:row-action="item">
            <div class="flex items-center gap-2 justify-end">
                <router-link :to="{ name: 'organizations-whatsapp-messages', params: { id: $route.params.id } }"
                    class="flex items-center gap-2 px-2 py-1 text-sm bg-green-600 text-white rounded">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                        stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                    </svg>
                    {{ $t("Continue") }}
                </router-link>
                <button @click="showConfirmation"
                    class="flex items-center gap-2 px-2 py-1 text-sm bg-red-500 text-white rounded">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                        stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Logout
                </button>
            </div>
        </template>
    </t-table>
    <div class="flex justify-center pt-20">
        <div v-if="showConfirmPopup" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-red-500 p-6 rounded-lg">
                <p class="mb-4">Are you sure you want to log out?</p>
                <div class="flex justify-end">
                    <button @click="logout" class="bg-red-700 text-white px-4 py-2 rounded mr-2">
                        Yes
                    </button>
                    <button @click="showConfirmPopup = false" class="bg-blue-700 px-4 py-2 rounded">
                        No
                    </button>
                </div>
            </div>
        </div>
        <div v-if="showLoadingPopup" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-gray-600 p-6 rounded-lg">
                <p class="mb-4">Logging out...</p>
                <div class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-gray-900 mx-auto"></div>
            </div>
        </div>
        <div v-if="showLogoutSuccessPopup"
            class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-gray-400 p-6 rounded-lg">
                <p class="mb-4">Logged Out!</p>
                <div class="flex justify-end">
                    <button @click="logoutReload" class="bg-blue-500 text-white px-4 py-2 rounded">Continue</button>
                </div>
            </div>
        </div>
        <div v-if="showPhonePopup" class="popup bg-gray-900 p-6 rounded shadow-lg text-center">
            <h2 class="text-lg font-semibold mb-4">Enter Phone Number</h2>
            <div class="flex gap-2 mb-4">
                <select v-model="selectedCountry" class="border p-2 text-black">
                    <option v-for="country in countries" :key="country.code" :value="country.code">
                        {{ country.flag }} {{ country.name }} ({{ country.code }})
                    </option>
                </select>
                <input v-model="phoneNumber" type="text" placeholder="Phone Number"
                    class="border p-2 w-full text-black" />
            </div>
            <button @click="requestLoginCode" class="px-4 py-2 bg-blue-500 text-black rounded">Continue</button>
        </div>
        <div v-if="showCodePopup" class="popup fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white p-6 rounded shadow-lg text-center">
                <h2 class="text-lg font-semibold mb-4 text-black">Your Verification Code</h2>
                <p class="text-xl font-bold text-blue-600">{{ verificationCode }}</p>
                <p class="mt-2 text-gray-500">Expires in {{ countdown }} seconds</p>
            </div>
        </div>
        <div v-if="showFailedPopup" class="popup fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-red-600 p-6 rounded shadow-lg text-center text-white">
                <div class="flex justify-center mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <line x1="12" y1="8" x2="12" y2="12" />
                        <line x1="12" y1="16" x2="12" y2="16" />
                    </svg>
                </div>
                <h2 class="text-lg font-semibold mb-4">Connection Failed</h2>
                <p class="mt-2">Device not found. Please try again later.</p>
                <button @click="showFailedPopup = false"
                    class="mt-4 px-4 py-2 bg-white text-red-600 rounded">Close</button>
            </div>
        </div>
        <div v-if="showSuccessPopup"
            class="popup fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-green-600 p-6 rounded shadow-lg text-center text-white">
                <div class="flex justify-center mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10" />
                        <line x1="12" y1="8" x2="12" y2="12" />
                        <line x1="12" y1="16" x2="12" y2="16" />
                    </svg>
                </div>
                <h2 class="text-lg font-semibold mb-4">Connection Success</h2>
                <router-link :to="{ name: 'organizations-whatsapp-messages', params: { id: $route.params.id } }"
                    class="mt-4 px-4 py-2 bg-white text-green-600 rounded">
                    {{ $t("Continue") }}
                </router-link>
            </div>
        </div>
    </div>
</template>