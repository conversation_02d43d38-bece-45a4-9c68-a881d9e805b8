<template>
  <div class="bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700 lg:flex">
    <aside
      class="flex overflow-x-auto border-b border-gray-900/5 py-4 lg:block lg:w-64 lg:flex-none lg:border-0 lg:p-5">
      <OrganizationSettingsNav v-model="currentTabId" :tabs="tabs" @update:modelValue="(id) => setCurrentTab(id)" />
    </aside>

    <main
      class="bg-gray-50 dark:bg-neutral-900 px-4 sm:px-6 lg:flex-auto lg:px-0 py-4 lg:border-l dark:border-neutral-700 rounded-xl">
      <component v-if="currentTab" :is="currentTab.component" :key="currentTab.id" />
    </main>
  </div>
</template>

<script setup>
import { t } from "@/plugins/i18n";

const route = useRoute();
const router = useRouter();

const tabs = [
  {
    id: "general",
    title: t("organizations.settings.general.tab"),
    icon: "cog",
    disabled: false,
    isActive: true,
    component: defineAsyncComponent(
      () => import("@/components/organizations/settings/organization-settings-general.vue"),
    ),
  },
  {
    id: "security",
    title: t("organizations.settings.security.tab"),
    icon: "shield-check",
    disabled: false,
    isActive: true,
    component: defineAsyncComponent(
      () => import("@/components/organizations/settings/organization-settings-security.vue"),
    ),
  },
  {
    id: "authorization",
    title: t("organizations.settings.authorization.tab"),
    icon: "login",
    disabled: false,
    isActive: true,
    component: defineAsyncComponent(
      () => import("@/components/organizations/settings/organization-settings-authorization.vue"),
    ),
  },
  {
    id: "authentication",
    title: t("organizations.settings.authentication.tab"),
    icon: "id-card",
    disabled: false,
    isActive: true,
    component: defineAsyncComponent(
      () => import("@/components/organizations/settings/organization-settings-authentication.vue"),
    ),
  },
  {
    id: "terminate",
    title: t("organizations.settings.terminate.tab"),
    icon: "bank",
    disabled: false,
    isActive: false,
    component: defineAsyncComponent(
      () => import("@/components/organizations/settings/organization-settings-terminate.vue"),
    ),
  },
];
const currentTabId = ref(null);
const currentTab = ref(null);

const setCurrentTab = (tab) => {
  router.push({
    query: {
      tab: "settings",
      sub: tab,
    },
    replace: true,
  });

  let selectedTab = tabs.find((tabObject) => tabObject.id == tab);
  selectedTab ? (currentTab.value = selectedTab) : "";
  currentTabId.value = tab;
};

onMounted(() => {
  setCurrentTab(route.query.sub ? route.query.sub : tabs[0].id);
});

watch(
  () => route.query.sub,
  (newValue) => {
    setCurrentTab(newValue);
  },
);
</script>
