<template>
  <div
    class="xl:p-5 flex flex-col bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <div v-if="loading" class="flex items-center justify-center h-full">
      <MonoIcon class="size-20 animate-spin" name="spin" />
    </div>
    <div v-else-if="error" class="flex items-center h-full p-5">
      <div class="flex flex-col md:flex-row gap-2 items-center justify-center w-full h-full">
        <MonoIcon class="size-20 text-rose-600 dark:text-rose-500 md:pt-2" name="exclamation" />
        <div class="text-center md:text-left">
          <h2 class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
            {{ error.title || $t("general.server_error") }}
          </h2>
          <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
            {{ error.message || $t("general.server_error_description") }}
          </p>
        </div>
      </div>
    </div>
    <div v-else class="xl:flex">
      <div
        class="bg-white p-5 xl:p-0 overflow-y-auto relative z-0 block translate-x-0 end-auto bottom-0 rounded-xl xl:rounded-none xl:border-e xl:border-gray-200 dark:bg-neutral-800 dark:xl:border-neutral-700">
        <div class="xl:pe-4 space-y-5 divide-y divide-gray-200 dark:divide-neutral-700">
          <!-- details -->
          <div class="pt-4 first:pt-0">
            <h2 class="text-sm font-semibold text-gray-800 dark:text-neutral-200">
              {{ $t("general.details") }}
            </h2>

            <ul class="mt-3 space-y-2">
              <li v-if="organization.parent" class="flex flex-col">
                <span class="text-xs opacity-50">
                  {{ $t("organizations.overview.parent_organization") }}
                </span>
                <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200 max-w-[230px]">
                  <MonoIcon name="organization" class="flex-shrink-0 size-4 text-gray-600 dark:text-neutral-400" />
                  <span class="whitespace-break-spaces">
                    <router-link :to="{
                      name: 'organizations-view',
                      params: { id: organization.parent_id },
                    }" class="inline hover:text-teal-600 dark:hover:text-teal-500">
                      {{ organization.parent.name }}
                    </router-link>
                  </span>
                </div>
              </li>
              <li class="flex flex-col">
                <span class="text-xs opacity-50">
                  {{ $t("organizations.overview.timezone") }}
                </span>
                <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                  <MonoIcon name="map-pin-house" class="flex-shrink-0 size-4 text-gray-600 dark:text-neutral-400" />

                  {{ organization.timezone }}
                </div>
              </li>
              <li class="flex flex-col">
                <span class="text-xs opacity-50">
                  {{ $t("organizations.overview.created_at") }}
                </span>
                <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                  <MonoIcon name="calendar" class="flex-shrink-0 size-4 text-gray-600 dark:text-neutral-400" />

                  {{ $dayjs(organization.created_at).format("MMM D, YYYY HH:mm") }}
                </div>
              </li>
              <li v-if="organization.domain_address" class="flex flex-col">
                <span class="text-xs opacity-50">
                  {{ $t("organizations.overview.domain_address") }}
                </span>
                <div class="inline-flex items-center gap-x-3 text-sm text-gray-800 dark:text-neutral-200">
                  <MonoIcon name="world" class="flex-shrink-0 size-4 text-gray-600 dark:text-neutral-400" />
                  <a :href="organization.domain_address" target="_blank" rel="noopener noreferrer"
                    class="hover:text-teal-600 dark:hover:text-teal-500">
                    {{ organization.domain_address }}
                  </a>
                </div>
              </li>
            </ul>
          </div>

          <!-- actions -->
          <div class="pt-4 first:pt-0">
            <h2 class="mb-2 text-sm text-gray-500 dark:text-neutral-500">Actions</h2>

            <ul class="space-y-2">
              <li>
                <button @click="paySubmerchantsModal.show = true"
                  class="p-2.5 flex w-full items-center justify-between gap-x-3 bg-white border border-gray-200 text-sm font-medium text-gray-800 dark:text-neutral-200 rounded-xl hover:text-green-600 focus:outline-none focus:bg-gray-100 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:text-green-500 dark:focus:bg-neutral-700">
                  <div class="flex items-center gap-2">
                    <span
                      class="flex shrink-0 justify-center items-center size-7 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                      <MonoIcon class="flex-shrink-0 size-5 text-green-600 dark:text-green-500" name="transfer" />
                    </span>
                    <div class="grow">
                      <p>
                        {{ $t("organizations.overview.pay_submerchants") }}
                      </p>
                    </div>
                  </div>

                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6"></path>
                  </svg>
                </button>
              </li>
              <li>
                <button @click="seedBinsModal.show = true"
                  class="p-2.5 flex w-full items-center justify-between gap-x-3 bg-white border border-gray-200 text-sm font-medium text-gray-800 dark:text-neutral-200 rounded-xl hover:text-sky-600 focus:outline-none focus:bg-gray-100 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:text-sky-500 dark:focus:bg-neutral-700">
                  <div class="flex items-center gap-2">
                    <span
                      class="flex shrink-0 justify-center items-center size-7 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                      <MonoIcon class="flex-shrink-0 size-5 text-sky-600 dark:text-sky-500" name="document-list" />
                    </span>
                    <div class="grow">
                      <p>
                        {{ $t("organizations.overview.seed_bins") }}
                      </p>
                    </div>
                  </div>

                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6"></path>
                  </svg>
                </button>

              </li>
              <li>
                <button @click="seedAcquirers"
                  class="p-2.5 flex w-full items-center justify-between gap-x-3 bg-white border border-gray-200 text-sm font-medium text-gray-800 dark:text-neutral-200 rounded-xl hover:text-orange-600 focus:outline-none focus:bg-gray-100 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:text-orange-500 dark:focus:bg-neutral-700">
                  <div class="flex items-center gap-2">
                    <span
                      class="flex shrink-0 justify-center items-center size-7 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                      <MonoIcon class="flex-shrink-0 size-5 text-orange-600 dark:text-orange-500" name="database" />
                    </span>
                    <div class="grow">
                      <p>
                        {{ $t("organizations.overview.seed_acquirers") }}
                      </p>
                    </div>
                  </div>

                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6"></path>
                  </svg>



                </button>

              </li>
              <li>
                <button @click="seedEventQueue"
                  class="p-2.5 flex w-full items-center justify-between gap-x-3 bg-white border border-gray-200 text-sm font-medium text-gray-800 dark:text-neutral-200 rounded-xl hover:text-orange-600 focus:outline-none focus:bg-gray-100 dark:bg-neutral-800 dark:border-neutral-700 dark:hover:text-orange-500 dark:focus:bg-neutral-700">
                  <div class="flex items-center gap-2">
                    <span
                      class="flex shrink-0 justify-center items-center size-7 bg-white border border-gray-200 rounded-lg dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                      <MonoIcon class="flex-shrink-0 size-5 text-orange-600 dark:text-orange-500" name="database" />
                    </span>
                    <div class="grow">
                      <p>
                        {{ $t("organizations.overview.seed_eventqueue") }}
                      </p>
                    </div>
                  </div>

                  <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="m9 18 6-6-6-6"></path>
                  </svg>



                </button>

              </li>
            </ul>
          </div>
        </div>
      </div>

      <div class="px-5 py-2 xl:py-0 grow space-y-5">
        <!-- todo: add health check alerts -->
        <!-- stats -->
        <div class="grid grid-cols-2 lg:grid-cols-3 gap-2 md:gap-3 xl:gap-5">
          <div
            class="relative overflow-hidden p-4 sm:p-5 bg-white border border-gray-200 rounded-xl shadow-sm before:absolute before:top-0 before:end-0 before:size-full before:bg-gradient-to-br before:from-yellow-100 before:via-transparent before:blur-xl dark:bg-neutral-800 dark:border-neutral-700 dark:before:from-yellow-800/30 dark:before:via-transparent">
            <div class="relative">
              <div class="flex gap-3 items-center">
                <span
                  class="inline-flex justify-center items-center size-8 md:size-10 rounded-lg bg-white text-gray-700 shadow dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                    stroke="currentColor" class="flex-shrink-0 size-4 md:size-5 text-yellow-500">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="m15 15 6-6m0 0-6-6m6 6H9a6 6 0 0 0 0 12h3" />
                  </svg>
                </span>

                <h2 class="text-sm md:text-base text-gray-800 dark:text-neutral-200">
                  {{ $t("organizations.overview.total_sub_organizations") }}
                </h2>
              </div>

              <div class="mt-4 flex items-center justify-center">
                <h3 class="text-lg md:text-3xl font-semibold text-gray-800 dark:text-neutral-200">
                  {{ organizationDetail.total_sub_organizations ?? 0 }}
                </h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>


</template>

<script setup>
import { useOrganizationStore } from "@/store/submodules/organization";
import { formatMoney, getCurrencySymbol } from "@/utils/helpers";
import { securedAxios } from "@/utils/axios";

const store = useOrganizationStore();
const route = useRoute();
const toast = inject("toast");

const props = defineProps({
  detail: {
    type: Object,
    default: () => ({}),
  },
});

const loading = ref(true);
const error = ref(null);
const organization = ref({});
const organizationDetail = ref({});

const paySubmerchantsModal = reactive({
  show: false,
  loading: false,
});
const paySubmerchants = async () => {
  paySubmerchantsModal.loading = true;
  securedAxios
    .get(`/submerchants/pay/${route.params.id}`)
    .then((res) => {
      toast.success(res.data.message);
    })
    .catch((err) => {
      toast.error(err?.response?.data?.message || t("general.internal_error"));
    })
    .finally(() => {
      paySubmerchantsModal.loading = false;
      paySubmerchantsModal.show = false;
    });
};

const seedBinsModal = reactive({
  show: false,
  loading: false,
});
const seedBins = async () => {
  seedBinsModal.loading = true;
  securedAxios
    .get(`/organizations/${route.params.id}/seed-bins`)
    .then((res) => {
      toast.success(res.data.message ? res.data.message : res.data[0].message);
    })
    .catch((err) => {
      toast.error(err?.response?.data?.message || t("general.internal_error"));
    })
    .finally(() => {
      seedBinsModal.loading = false;
      seedBinsModal.show = false;
    });
};

const createBanksModal = reactive({
  show: false,
  loading: false,
});
const createBanks = async () => {
  createBanksModal.loading = true;
  securedAxios
    .get(`/organizations/banks/${route.params.id}`)
    .then((res) => {
      toast.success(res.data.message);
    })
    .catch((err) => {
      toast.error(err?.response?.data?.message || t("general.internal_error"));
    })
    .finally(() => {
      createBanksModal.loading = false;
      createBanksModal.show = false;
    });
};

const clearBlockedCardsModal = reactive({
  show: false,
  loading: false,
});
const clearBlockedCards = async () => {
  clearBlockedCardsModal.loading = true;
  securedAxios
    .get(`/organizations/${route.params.id}/clear-blocked-cards`)
    .then((res) => {
      toast.success(res.data.message);
    })
    .catch((err) => {
      toast.error(err?.response?.data?.message || t("general.internal_error"));
    })
    .finally(() => {
      clearBlockedCardsModal.loading = false;
      clearBlockedCardsModal.show = false;
    });
};

const downloadPosRevenues = () => {
  securedAxios
    .get("/statistics/pos-revenue/csv")
    .then((res) => {
      const url = window.URL.createObjectURL(new Blob([res.data]));
      const link = document.createElement("a");
      link.href = url;
      let filename = res.headers["content-disposition"].split("filename=")[1];
      filename = filename.replace(/"/g, "");
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
    })
    .catch((err) => {
      toast.error(err?.response?.data?.message || t("general.internal_error"));
    });
};

onMounted(async () => {
  try {
    const res = await store.Read(route.params.id);
    organization.value = res;

    if (res.parent_id !== "00000000-0000-0000-0000-000000000000") {
      const parentRes = await store.Read(res.parent_id);
      organization.value.parent = parentRes;
    }
  } catch (err) {
    console.error(err);
    error.value = {
      code: "500",
      title: "Internal Error",
      message: "An unexpected error occurred. Please try again later.",
    };
  }

  try {
    const res = await store.Detail(route.params.id);
    organizationDetail.value = res;
    console.log(res);
  } catch (err) {
    console.error(err);
    error.value = {
      code: "500",
      title: "Internal Error",
      message: "An unexpected error occurred. Please try again later.",
    };
  }

  loading.value = false;
});


function seedAcquirers() {
  securedAxios
    .get(`/organizations/${route.params.id}/seed-acquirers`)
    .then((res) => {
      toast.success(res.data.message);
    })
    .catch((err) => {
      toast.error(err?.response?.data?.message || t("general.internal_error"));
    });
}

function seedEventQueue() {
  securedAxios
    .get(`/organizations/${route.params.id}/seed-eventqueue`)
    .then((res) => {
      toast.success(res.data.message);
    })
    .catch((err) => {
      toast.error(err?.response?.data?.message || t("general.internal_error"));
    });
}
</script>
