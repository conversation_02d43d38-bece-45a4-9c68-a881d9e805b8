<template>
    <div class="mx-auto max-w-2xl space-y-16 sm:space-y-20 lg:mx-0 lg:max-w-none">
        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ $t("organizations.settings.providers.title") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ $t("organizations.settings.providers.description") }}
                </p>
            </div>
            <div
                v-if="loading"
                class="lg:px-5"
            >
                <t-loader />
            </div>
            <div
                v-else-if="error"
                class="lg:px-5"
            >
                <ErrorCustom
                    size="sm"
                    centered
                    :code="error?.code || 500"
                    :title="error?.title || $t('form.internal_error')"
                    :message="error?.message || $t('form.an_error_occurred')"
                >
                    <template #action>
                        <button
                            type="button"
                            class="mt-10 text-sm/7 font-semibold text-indigo-600 flex items-center gap-2"
                            @click.prevent="$router.go(0)"
                        >
                            <MonoIcon
                                name="refresh"
                                class="size-4"
                            />
                            {{ $t("general.retry") }}
                        </button>
                    </template>
                </ErrorCustom>
            </div>
            <div
                v-else
                class="lg:px-5 space-y-4 divide-y divide-gray-200 dark:divide-gray-600"
            >
                <div class="space-y-4 divide-y divide-gray-200 dark:divide-gray-600">
                    <!-- KYC Provider -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-2">
                        <div>
                            <label class="flex gap-1 text-sm font-medium">
                                {{ $t("organizations.settings.providers.kyc_provider") }}
                            </label>
                            <span class="text-xs text-gray-500">
                                {{ $t("organizations.settings.providers.kyc_provider_desc") }}
                            </span>
                        </div>
                        <t-select
                            v-model="form.kyc_provider_id"
                            long
                            close-after-select
                            :options="kycProviders"
                        />
                    </div>
                    <!-- WaaS Provider -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-2 pt-4">
                        <div>
                            <label class="flex gap-1 text-sm font-medium">
                                {{ $t("organizations.settings.providers.waas_provider") }}
                            </label>
                            <span class="text-xs text-gray-500">
                                {{ $t("organizations.settings.providers.waas_provider_desc") }}
                            </span>
                        </div>
                        <t-select
                            v-model="form.waas_provider_id"
                            long
                            close-after-select
                            :options="waasProviders"
                        />
                    </div>
                    <!-- Identity Verification Provider -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-2 pt-4">
                        <div>
                            <label class="flex gap-1 text-sm font-medium">
                                {{ $t("organizations.settings.providers.verification_provider") }}
                            </label>
                            <span class="text-xs text-gray-500">
                                {{ $t("organizations.settings.providers.verification_provider_desc") }}
                            </span>
                        </div>
                        <t-select
                            v-model="form.verification_provider"
                            long
                            close-after-select
                            :options="verificationProviders"
                        />
                    </div>
                </div>

                <!-- button -->
                <div class="grid grid-cols-2 lg:grid-cols-2 items-center gap-4 pt-4">
                    <div>
                        <p
                            class="flex gap-2 text-sm font-medium opacity-70"
                            v-if="isDirty"
                        >
                            <span class="hidden lg:inline">{{ $t("form.unsaved_changes") }}</span>
                            <button
                                @click="cancelUpdatedData"
                                class="font-semibold hover:underline"
                            >
                                {{ $t("form.discard_changes") }}
                            </button>
                        </p>
                    </div>
                    <div class="flex justify-end">
                        <button
                            type="button"
                            class="flex items-center min-w-24 min-h-6 justify-center bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md"
                            @click="update"
                            :disabled="formProcessing"
                        >
                            <MonoIcon
                                v-if="formProcessing"
                                name="spin"
                                class="size-6 animate-spin"
                            />
                            <span v-else>
                                {{ $t("general.update") }}
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { t, te } from "@/plugins/i18n";
import { useKycProviderStore } from "@/store/submodules/kyc-providers";
import { useOrganizationStore } from "@/store/submodules/organization";
import { useWaasProviderStore } from "@/store/submodules/waas-provider";

const toast = inject("toast");
const route = useRoute();
const store = useOrganizationStore();
const waasProviderStore = useWaasProviderStore();
const kycProviderStore = useKycProviderStore();

const loading = ref(true);
const error = ref(null);

const formProcessing = ref(false);
const form = reactive({
    kyc_provider_id: null,
    waas_provider_id: null,
    verification_provider: null,
});

const originalForm = ref("");

const isDirty = computed(() => {
    return originalForm.value !== JSON.stringify(form);
});

const verificationProviders = ref([
    { id: 1, name: "Kps" },
    { id: 2, name: "Paycell" },
]);

const waasProviders = computed(() => {
    return waasProviderStore.getItems.rows ?? [];
});

const kycProviders = computed(() => {
    return kycProviderStore.getItems.rows ?? [];
});

onMounted(async () => {
    try {
        await Promise.all([
            waasProviderStore.List(1, 1000, [{ organization_id: route.params.id }]),
            kycProviderStore.List(1, 1000, [{ organization_id: route.params.id }]),
        ]);

        const res = await getOrganization();

        fillForm(res);
    } catch (err) {
        error.value = {
            code: "500",
            title: t("form.internal_error"),
            message: t("form.an_error_occurred"),
        };
    } finally {
        loading.value = false;
    }
});

onBeforeRouteLeave((_to, _from, next) => {
    if (isDirty.value) {
        if (confirm(t("form.unsaved_changes"))) {
            next();
        } else {
            next(false);
        }
    } else {
        next();
    }
});

const getOrganization = async () => {
    return store.Read(route.params.id);
};

const fillForm = (res) => {
    form.kyc_provider_id = res.kyc_provider_id === "00000000-0000-0000-0000-000000000000" ? null : res.kyc_provider_id;

    form.waas_provider_id =
        res.waas_provider_id === "00000000-0000-0000-0000-000000000000" ? null : res.waas_provider_id;

    form.verification_provider = res.verification_provider === 0 ? null : res.verification_provider;

    localStorage.hash_id = res.hash_id;
    originalForm.value = JSON.stringify(form);
};

const update = () => {
    formProcessing.value = true;

    store
        .Update({
            id: route.params.id,
            ...toRaw(form),
        })
        .then((res) => {
            toast.success(res.message);

            getOrganization().then((res) => {
                fillForm(res);
            });
        })
        .catch((err) => {
            // Checks if translation exist
            const localeKey = "organization." + err.response.data.error;
            if (te(localeKey)) {
                toast.error(t(localeKey));
                return;
            }
            toast.error(err.response.data.error);
        })
        .finally(() => {
            formProcessing.value = false;
        });
};

const cancelUpdatedData = () => {
    Object.assign(form, JSON.parse(originalForm.value));
};
</script>
