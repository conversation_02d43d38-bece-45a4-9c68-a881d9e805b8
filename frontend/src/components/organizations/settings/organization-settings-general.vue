<template>
  <div class="mx-auto max-w-2xl space-y-16 sm:space-y-20 lg:mx-0 lg:max-w-none">
    <div class="space-y-4">
      <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
        <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
          {{ $t("organizations.settings.general.title") }}
        </h2>
        <p class="mt-1 text-sm/6 text-gray-500">
          {{ $t("organizations.settings.general.description") }}
        </p>
      </div>
      <div v-if="loading" class="lg:px-5">
        <t-loader />
      </div>
      <div v-else-if="error" class="lg:px-5">
        <ErrorCustom size="sm" centered :code="error?.code || 500" :title="error?.title || $t('form.internal_error')"
          :message="error?.message || $t('form.an_error_occurred')">
          <template #action>
            <button type="button" class="mt-10 text-sm/7 font-semibold text-indigo-600 flex items-center gap-2"
              @click.prevent="$router.go(0)">
              <MonoIcon name="refresh" class="size-4" />
              {{ $t("general.retry") }}
            </button>
          </template>
        </ErrorCustom>
      </div>
      <div v-else class="lg:px-5 space-y-4 divide-y divide-gray-200 dark:divide-gray-600">
        <!-- Organization Name -->
        <div class="">
          <InputBox :span="6" :label="$t('organizations.settings.general.organization_name')" v-model="form.name"
            :is_required="true" />
        </div>
        <!-- Parent Organization -->
        <div class="pt-4">
          <t-select :label="$t('organizations.settings.general.parent_organization')" v-model="form.parent_id"
            :options="organizations" option-label="name" data-label="id" searchable long close-after-select />
        </div>
        <!-- Logo and Favicon -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 pt-4">
          <t-file-input v-model="form.logo" :label="$t('organizations.settings.general.logo')" />
          <t-file-input v-model="form.favicon" :label="$t('organizations.settings.general.favicon')" />
        </div>
        <!-- Footer Text -->
        <div class="pt-4">
          <div class="flex items-center justify-between">
            <label class="flex gap-1 text-sm font-medium">
              {{ $t("organizations.settings.general.footer_text") }}
            </label>
          </div>
          <div class="flex items-center gap-2 pt-4">
            <select name="language" class="select !w-20 !h-8 !mt-0 !py-1" v-model="footerTextLanguage">
              <option value="en">en</option>
              <option value="tr">tr</option>
            </select>

            <input type="text" class="t-input w-full" v-model="form.footer_text[footerTextLanguage]" />
          </div>
        </div>
        <!-- Allow Registration -->
        <div class="flex items-center justify-between gap-2 pt-4">
          <div>
            <label class="flex gap-1 text-sm font-medium">
              {{ $t("organizations.settings.general.allow_registration") }}
            </label>
            <span class="text-xs text-gray-500">
              {{ $t("organizations.settings.general.allow_registration_desc") }}
            </span>
          </div>
          <Switch v-model="form.allow_registration" />
        </div>

        <div class="flex items-center justify-between gap-2 pt-4">
          <div>
            <label class="flex gap-1 text-sm font-medium">
              {{ $t("organizations.settings.general.quotation_enabled") }}
            </label>
            <span class="text-xs text-gray-500">
              {{ $t("organizations.settings.general.quotation_enabled_desc") }}
            </span>
          </div>
          <Switch v-model="form.quotation_enabled" />
        </div>

        <!-- buttons -->
        <div class="grid grid-cols-2 lg:grid-cols-2 items-center gap-4 pt-4">
          <div>
            <p class="flex gap-2 text-sm font-medium opacity-70" v-if="isDirty">
              <span class="hidden lg:inline">{{ $t("form.unsaved_changes") }}</span>
              <button @click="cancelUpdatedData" class="font-semibold hover:underline">
                {{ $t("form.discard_changes") }}
              </button>
            </p>
          </div>
          <div class="flex justify-end">
            <button type="button"
              class="flex items-center min-w-24 min-h-6 justify-center bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md"
              @click="update" :disabled="formProcessing">
              <MonoIcon v-if="formProcessing" name="spin" class="size-6 animate-spin" />
              <span v-else>
                {{ $t("general.update") }}
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { t, te } from "@/plugins/i18n";
import { useOrganizationStore } from "@/store/submodules/organization";

const toast = inject("toast");
const route = useRoute();
const store = useOrganizationStore();

const loading = ref(true);
const error = ref(null);

const formProcessing = ref(false);
const form = reactive({
  name: "",
  parent_id: null,
  currency_id: null,
  logo: null,
  favicon: null,
  footer_text: {},
  allow_registration: false,
  allow_limit_check: false,
  order_refund_cancel_flow: false,
  quotation_enabled: false,
  labels: [],
});

const originalForm = ref("");

const footerTextLanguage = ref(localStorage.getItem("locale") || "en");

const organizations = computed(() => {
  return store.getItems.rows.filter((org) => org.id !== form.id);
});

const isDirty = computed(() => {
  return originalForm.value !== JSON.stringify(form);
});

onMounted(async () => {
  try {
    await store.List(1, 1000); // for parent organizations
    const res = await getOrganization();

    fillForm(res);
    // throw new Error("test");
  } catch (err) {
    error.value = {
      code: "500",
      title: t("form.internal_error"),
      message: t("form.an_error_occurred"),
    };
  } finally {
    loading.value = false;
  }
});

onBeforeRouteLeave((_to, _from, next) => {
  if (isDirty.value) {
    if (confirm(t("form.unsaved_changes"))) {
      next();
    } else {
      next(false);
    }
  } else {
    next();
  }
});

const getOrganization = async () => {
  return store.Read(route.params.id);
};

const fillForm = (res) => {
  form.name = res.name;
  form.parent_id = res.parent_id === "00000000-0000-0000-0000-000000000000" ? null : res.parent_id;
  form.logo = res.logo;
  form.favicon = res.favicon;
  form.footer_text = res.footer_text ?? {};
  form.allow_registration = res.allow_registration;
  form.allow_limit_check = res.allow_limit_check;
  form.quotation_enabled = res.quotation_enabled;
  form.labels = res.labels ? res.labels : [];
  localStorage.hash_id = res.hash_id;
  originalForm.value = JSON.stringify(form);
};

const update = () => {
  formProcessing.value = true;

  store
    .Update({ id: route.params.id, ...toRaw(form) })
    .then((res) => {
      toast.success(res.message);

      getOrganization().then((res) => {
        fillForm(res);
      });
    })
    .catch((err) => {
      // Checks if translation exist
      const localeKey = "organization." + err.response.data.error;
      if (te(localeKey)) {
        toast.error(t(localeKey));
        return;
      }
      toast.error(err.response.data.error);
    })
    .finally(() => {
      formProcessing.value = false;
    });
};

const cancelUpdatedData = () => {
  Object.assign(form, JSON.parse(originalForm.value));
};
</script>
