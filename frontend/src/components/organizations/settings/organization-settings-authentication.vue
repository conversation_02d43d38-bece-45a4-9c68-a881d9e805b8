<template>
    <div class="mx-auto max-w-2xl space-y-16 sm:space-y-20 lg:mx-0 lg:max-w-none">
        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ $t("organizations.settings.authentication.title") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ $t("organizations.settings.authentication.description") }}
                </p>
            </div>
            <div
                v-if="loading"
                class="lg:px-5"
            >
                <t-loader />
            </div>
            <div
                v-else-if="error"
                class="lg:px-5"
            >
                <ErrorCustom
                    size="sm"
                    centered
                    :code="error?.code || 500"
                    :title="error?.title || $t('form.internal_error')"
                    :message="error?.message || $t('form.an_error_occurred')"
                >
                    <template #action>
                        <button
                            type="button"
                            class="mt-10 text-sm/7 font-semibold text-indigo-600 flex items-center gap-2"
                            @click.prevent="$router.go(0)"
                        >
                            <MonoIcon
                                name="refresh"
                                class="size-4"
                            />
                            {{ $t("general.retry") }}
                        </button>
                    </template>
                </ErrorCustom>
            </div>
            <div
                v-else
                class="lg:px-5 space-y-4 divide-y divide-gray-200 dark:divide-gray-600"
            >
                <div class="space-y-4 divide-y divide-gray-200 dark:divide-gray-600">
                    <div class="hidden lg:grid grid-cols-12 w-full">
                        <span class="col-span-8 font-semibold">
                            <!-- {{ $t("organizations.authentication_steps.step_name") }} -->
                        </span>
                        <span class="text-xs text-center col-span-2 font-semibold">
                            {{ $t("organizations.authentication_steps.status") }}
                        </span>
                        <span class="text-xs text-right col-span-2 font-semibold">
                            {{ $t("organizations.authentication_steps.optional") }}
                        </span>
                    </div>
                    <!-- email verification -->
                    <div class="w-full grid grid-cols-12 gap-2 pt-4">
                        <div class="col-span-12 lg:col-span-8">
                            <label class="flex items-center gap-1 text-sm font-medium">
                                {{ $t("organizations.authentication_steps.email_verification") }}
                            </label>
                            <p class="text-xs text-gray-500">
                                {{ $t("organizations.authentication_steps.email_verification_desc") }}
                            </p>
                        </div>
                        <div class="col-span-6 lg:col-span-2 lg:text-center">
                            <label class="flex lg:hidden items-center gap-1 text-sm font-medium">
                                {{ $t("organizations.authentication_steps.status") }}
                            </label>
                            <Switch v-model="form.authentication_steps.email_verification.enabled" />
                        </div>
                        <div class="col-span-6 lg:col-span-2 text-right">
                            <label class="flex lg:hidden justify-end items-center gap-1 text-sm font-medium">
                                {{ $t("organizations.authentication_steps.optional") }}
                            </label>
                            <Switch v-model="form.authentication_steps.email_verification.is_optional" />
                        </div>
                    </div>
                    <!-- phone number verification -->
                    <div class="w-full grid grid-cols-12 gap-2 pt-4">
                        <div class="col-span-12 lg:col-span-8">
                            <label class="flex items-center gap-1 text-sm font-medium">
                                {{ $t("organizations.authentication_steps.phone_number_verification") }}
                            </label>
                            <p class="text-xs text-gray-500">
                                {{ $t("organizations.authentication_steps.phone_number_verification_desc") }}
                            </p>
                        </div>
                        <div class="col-span-6 lg:col-span-2 lg:text-center">
                            <label class="flex lg:hidden items-center gap-1 text-sm font-medium">
                                {{ $t("organizations.authentication_steps.status") }}
                            </label>
                            <Switch v-model="form.authentication_steps.phone_number_verification.enabled" />
                        </div>
                        <div class="col-span-6 lg:col-span-2 text-right">
                            <label class="flex lg:hidden justify-end items-center gap-1 text-sm font-medium">
                                {{ $t("organizations.authentication_steps.optional") }}
                            </label>
                            <Switch v-model="form.authentication_steps.phone_number_verification.is_optional" />
                        </div>
                    </div>
                    <!-- kyc verification -->
                    <div class="w-full grid grid-cols-12 gap-2 pt-4">
                        <div class="col-span-12 lg:col-span-8">
                            <label class="flex items-center gap-1 text-sm font-medium">
                                {{ $t("organizations.authentication_steps.kyc_verification") }}
                            </label>
                            <p class="text-xs text-gray-500">
                                {{ $t("organizations.authentication_steps.kyc_verification_desc") }}
                            </p>
                        </div>
                        <div class="col-span-6 lg:col-span-2 lg:text-center">
                            <label class="flex lg:hidden items-center gap-1 text-sm font-medium">
                                {{ $t("organizations.authentication_steps.status") }}
                            </label>
                            <Switch v-model="form.authentication_steps.kyc_verification.enabled" />
                        </div>
                        <div class="col-span-6 lg:col-span-2 text-right">
                            <label class="flex lg:hidden justify-end items-center gap-1 text-sm font-medium">
                                {{ $t("organizations.authentication_steps.optional") }}
                            </label>
                            <Switch v-model="form.authentication_steps.kyc_verification.is_optional" />
                        </div>
                    </div>
                </div>

                <!-- button -->
                <div class="grid grid-cols-2 lg:grid-cols-2 items-center gap-4 pt-4">
                    <div>
                        <p
                            class="flex gap-2 text-sm font-medium opacity-70"
                            v-if="isDirty"
                        >
                            <span class="hidden lg:inline">{{ $t("form.unsaved_changes") }}</span>
                            <button
                                @click="cancelUpdatedData"
                                class="font-semibold hover:underline"
                            >
                                {{ $t("form.discard_changes") }}
                            </button>
                        </p>
                    </div>
                    <div class="flex justify-end">
                        <button
                            type="button"
                            class="flex items-center min-w-24 min-h-6 justify-center bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md"
                            @click="update"
                            :disabled="formProcessing"
                        >
                            <MonoIcon
                                v-if="formProcessing"
                                name="spin"
                                class="size-6 animate-spin"
                            />
                            <span v-else>
                                {{ $t("general.update") }}
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { t, te } from "@/plugins/i18n";
import { useOrganizationStore } from "@/store/submodules/organization";

const toast = inject("toast");
const route = useRoute();
const store = useOrganizationStore();

const loading = ref(true);
const error = ref(null);

const formProcessing = ref(false);
const form = reactive({
    authentication_steps: {
        email_verification: {
            enabled: false,
            is_optional: false,
        },
        phone_number_verification: {
            enabled: false,
            is_optional: false,
        },
        kyc_verification: {
            enabled: false,
            is_optional: false,
        },
    },
});

const originalForm = ref("");

const isDirty = computed(() => {
    return originalForm.value !== JSON.stringify(form);
});

onMounted(async () => {
    try {
        const res = await getOrganization();

        fillForm(res);
    } catch (err) {
        error.value = {
            code: "500",
            title: t("form.internal_error"),
            message: t("form.an_error_occurred"),
        };
    } finally {
        loading.value = false;
    }
});

onBeforeRouteLeave((_to, _from, next) => {
    if (isDirty.value) {
        if (confirm(t("form.unsaved_changes"))) {
            next();
        } else {
            next(false);
        }
    } else {
        next();
    }
});

const getOrganization = async () => {
    return store.Read(route.params.id);
};

const fillForm = (res) => {
    if (res.authentication_steps) {
        res.authentication_steps.forEach((step) => {
            form.authentication_steps[step.type].enabled = step.enabled;
            form.authentication_steps[step.type].is_optional = step.is_optional;
        });
    }

    localStorage.hash_id = res.hash_id;
    originalForm.value = JSON.stringify(form);
};

const update = () => {
    formProcessing.value = true;

    store
        .Update({
            id: route.params.id,
            authentication_steps: [
                {
                    type: "email_verification",
                    enabled: form.authentication_steps.email_verification.enabled,
                    is_optional: form.authentication_steps.email_verification.is_optional,
                },
                {
                    type: "phone_number_verification",
                    enabled: form.authentication_steps.phone_number_verification.enabled,
                    is_optional: form.authentication_steps.phone_number_verification.is_optional,
                },
                {
                    type: "kyc_verification",
                    enabled: form.authentication_steps.kyc_verification.enabled,
                    is_optional: form.authentication_steps.kyc_verification.is_optional,
                },
            ],
        })
        .then((res) => {
            toast.success(res.message);

            getOrganization().then((res) => {
                fillForm(res);
            });
        })
        .catch((err) => {
            // Checks if translation exist
            const localeKey = "organization." + err.response.data.error;
            if (te(localeKey)) {
                toast.error(t(localeKey));
                return;
            }
            toast.error(err.response.data.error);
        })
        .finally(() => {
            formProcessing.value = false;
        });
};

const cancelUpdatedData = () => {
    Object.assign(form, JSON.parse(originalForm.value));
};
</script>
