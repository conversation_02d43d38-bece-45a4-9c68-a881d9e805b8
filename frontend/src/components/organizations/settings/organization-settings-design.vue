<template>
    <div class="mx-auto max-w-2xl space-y-8 sm:space-y-10 lg:mx-0 lg:max-w-none">
        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ t("organizations.settings.design.title") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ t("organizations.settings.design.description") }}
                </p>
            </div>

            <div class="lg:px-5">
                <t-table
                    :data="items.list"
                    :columns="tableProperties.columns"
                    entity-locale-name="organization_designs"
                    :page="Number(queries.page)"
                    :per_page="Number(queries.per_page)"
                    :total="items.total"
                    :total_pages="items.total_pages"
                    @page-change="pageChange"
                    @per-page-change="perPageChange"
                    :loading="loading"
                    :create-route="tableProperties.createRoute"
                    :filters="tableProperties.filters"
                    @filter-change="onFilterChange"
                    :error="errorRes.msg"
                    inlinesearch
                >
                    <template v-slot:row-actions="itemData">
                        <div
                            class="flex items-center gap-2"
                            :class="
                                tableProperties.columns[tableProperties.columns.length - 1].align === 'center'
                                    ? 'justify-center'
                                    : 'justify-start'
                            "
                        >
                            <div v-for="action in tableProperties.actions">
                                <router-link
                                    v-if="action.route"
                                    :to="{ name: action.route, params: { id: itemData.item.id } }"
                                    class="px-2 py-1 text-xs text-white rounded-md flex items-center gap-2 transition-colors"
                                    :class="[
                                        action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                                        action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                                        action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                                        action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                                        action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
                                    ]"
                                >
                                    <t-icon
                                        :name="action.icon"
                                        :class="[
                                            action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                                            action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                                            action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                                            action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                                            action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
                                        ]"
                                    />
                                    {{ action.label }}
                                </router-link>
                                <button
                                    v-else
                                    @click="action.function(itemData.item)"
                                    class="px-2 py-1 text-xs text-white rounded-md flex items-center gap-2 transition-colors"
                                    :class="[
                                        action.color === 'blue' ? ' bg-blue-600 hover:bg-blue-800' : '',
                                        action.color === 'indigo' ? ' bg-indigo-600 hover:bg-indigo-800' : '',
                                        action.color === 'rose' ? ' bg-red-600 hover:bg-red-800' : '',
                                        action.color === 'green' ? ' bg-green-600 hover:bg-green-800' : '',
                                        action.color === 'yellow' ? ' bg-yellow-600 hover:bg-yellow-800' : '',
                                    ]"
                                >
                                    <t-icon
                                        :name="action.icon"
                                        :class="[
                                            action.color === 'blue' ? 'w-4 h-4 text-blue-200' : '',
                                            action.color === 'indigo' ? 'w-4 h-4 text-indigo-200' : '',
                                            action.color === 'rose' ? 'w-4 h-4 text-red-200' : '',
                                            action.color === 'green' ? 'w-4 h-4 text-green-200' : '',
                                            action.color === 'yellow' ? 'w-4 h-4 text-yellow-200' : '',
                                        ]"
                                    />
                                    {{ action.label }}
                                </button>
                            </div>
                        </div>
                    </template>
                </t-table>
            </div>
        </div>
    </div>
    <t-delete-modal
        v-if="deleteModal"
        :is-active="deleteModal"
        @deleteData="deleteItem"
        @closeModal="
            deleteModal = false;
            deleteRelations = {};
        "
    >
        <template #content>
            <p class="text-md">{{ $t("general.delete_confirm") }}</p>
            <p class="text-xs leading-5">
                <span class="font-semibold text-rose-400">
                    {{ $t("general.delete_label", { item: deleteRelations.name }) }}
                </span>
            </p>
        </template>
    </t-delete-modal>
</template>

<script setup>
import { useOrganizationDesignStore } from "@/store/submodules/organization-design";

const { t } = useI18n();
const route = useRoute();
const store = useOrganizationDesignStore();
const router = useRouter();

const loading = ref(true);

const errorRes = reactive({
    msg: "",
    status: "",
});

const queries = reactive({
    page: route.query.page ? route.query.page : 1,
    per_page: route.query.per_page ? route.query.per_page : 10,
    organization_id: route.params.id,
});

const deleteModal = ref(false);
const deleteRelations = ref({});

function setDeleteItem(item) {
    deleteRelations.value = item;
    deleteModal.value = true;
}

function deleteItem(data) {
    localStorage.hash_id = deleteRelations.value.hash_id;
    store.Delete(deleteRelations.value.id).finally(() => {
        deleteModal.value = false;
    });
}

const filterlist = ref(
    Object.keys(route.query)
        .filter((key) => key !== "page" && key !== "per_page")
        .map((key) => {
            return { [key]: route.query[key] };
        }),
);

store
    .List(queries.page, queries.per_page, filterlist.value)
    .catch((err) => {
        console.log("err", err);
        errorRes.msg = "Something went wrong";
    })
    .finally(() => {
        loading.value = false;
    });

function pageChange(pageNum) {
    loading.value = true;
    queries.page = Number(pageNum);

    router.push({
        name: route.name,
        query: queries,
    });

    store
        .List(pageNum, queries.per_page, filterlist.value)
        .then(() => {
            if (errorRes.msg !== "") {
                errorRes.msg = "";
            }
        })
        .catch((err) => {
            console.log("pageChange err", err);
            errorRes.msg = "Something went wrong";
        });

    loading.value = false;
}

function perPageChange(per_page) {
    loading.value = true;
    queries.per_page = Number(per_page);
    router.push({
        name: route.name,
        query: queries,
    });

    store
        .List(queries.page, per_page, filterlist.value)
        .then(() => {
            if (errorRes.msg !== "") {
                errorRes.msg = "";
            }
        })
        .catch((err) => {
            console.log("perPageChange err", err);
            errorRes.msg = "Something went wrong";
        });

    loading.value = false;
}

const items = computed(() => {
    const list = store.getItems.rows ? store.getItems.rows : [];
    const total_pages = store.getItems.total_pages;
    const per_page = store.getItems.per_page;
    const page = store.getItems.page;
    const total = store.getItems.total;
    return {
        list,
        total_pages,
        per_page,
        page,
        total,
    };
});

function onFilterChange(filters) {
    loading.value = true;
    filterlist.value = filters;

    filterlist.value.forEach((filter) => {
        Object.keys(filter).forEach((key) => {
            if (
                filter[key] !== null &&
                filter[key] !== "" &&
                filter[key] !== undefined &&
                filter[key] !== "undefined"
            ) {
                queries[key] = filter[key];
            } else {
                delete queries[key];
                delete filter[key];
            }
        });
    });

    router.push({
        name: route.name,
        query: queries,
    });

    store
        .List(queries.page, queries.per_page, filterlist.value)
        .then(() => {
            if (errorRes.msg !== "") {
                errorRes.msg = "";
            }
        })
        .catch((err) => {
            console.log("onFilterChange err", err);
            errorRes.msg = "Something went wrong";
        });

    loading.value = false;
}

const tableProperties = {
    get: "getOrganizationDesigns",
    storeGetter: "organization_designs",
    listRoute: "organization-designs-list",
    createRoute: "organization-designs-create",
    id: route.params.id,
    paginationOptions: {
        page: route.query.page ? route.query.page : 1,
        per_page: route.query.per_page ? route.query.per_page : 10,
    },
    columns: [
        {
            row: "name",
            label: t("organization_design.general.name"),
            sortable: true,
            align: "center",
            mobile: true,
            visible: true,
        },
        {
            row: "organization_name",
            label: t("organization_design.general.organization"),
            sortable: true,
            align: "center",
            mobile: true,
            visible: true,
        },
        {
            row: "status",
            label: t("organization_design.general.status"),
            sortable: true,
            align: "center",
            mobile: false,
            visible: true,
            modifier: (value) => {
                return Boolean(value) ? t("general.active") : t("general.passive");
            },
        },
        {
            row: "actions",
            label: t("general.actions"),
            sortable: false,
            align: "center",
            mobile: true,
            visible: true,
        },
    ],
    actions: [
        {
            label: t("general.edit"),
            icon: "pencil",
            color: "blue",
            action: "edit",
            route: "organization-designs-edit",
        },
        {
            label: t("general.delete"),
            icon: "trash",
            color: "rose",
            action: "delete",
            function: setDeleteItem,
        },
    ],
    filters: [
        {
            name: "Status",
            type: "selectbox",
            dataName: "status",
            options: [
                { id: 0, name: "Passive" },
                { id: 1, name: "Active" },
            ],
            colSize: 6,
            value: route.query.status ? route.query.status : null,
            show: true,
        },
    ],
};
</script>
