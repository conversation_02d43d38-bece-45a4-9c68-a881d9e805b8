<template>
    <div class="mx-auto max-w-2xl space-y-8 sm:space-y-10 lg:mx-0 lg:max-w-none">
        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ $t("organizations.settings.treasury.treasury_descriptors") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ $t("organizations.settings.treasury.treasury_descriptors_desc") }}
                </p>
            </div>

            <div class="lg:px-5">
                <t-table
                    v-if="organizations.length > 0"
                    v-bind="treasuryDescriptorTableProps"
                    v-on="handlers"
                    :columns="treasuryDescriptorTableProperties.columns"
                    :filters="treasuryDescriptorTableProperties.filters"
                    entity-locale-name="organization_treasury_descriptor"
                    create-route="organization-treasury-descriptor-create"
                >
                    <template #prefix-actions>
                        <router-link
                            :to="{
                                name: 'organization-treasury-descriptor-reorder',
                            }"
                            class="py-2 px-2.5 inline-flex items-center gap-x-1.5 text-xs rounded-lg border border-gray-200 bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                        >
                            {{ $t("treasury_descriptors.reorder") }}
                            <MonoIcon name="sort" />
                        </router-link>
                    </template>
                    <template #row-actions="{ item }">
                        <t-table-actions
                            :item="item"
                            :columns="treasuryDescriptorTableProperties.columns"
                            :actions="treasuryDescriptorTableProperties.actions"
                        ></t-table-actions>
                    </template>

                    <template #row-created_at="{ item }">
                        {{ new Date(item.created_at).toLocaleString() }}
                    </template>
                </t-table>
            </div>
        </div>
        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ $t("organizations.settings.treasury.treasury_viban_preferences") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ $t("organizations.settings.treasury.treasury_viban_preferences_desc") }}
                </p>
            </div>

            <div class="lg:px-5">
                <t-table
                    v-bind="vibanPreferenceTableProps"
                    v-on="vibanPreferenceTableHandlers"
                    :columns="vibanPreferenceTableProperties.columns"
                    :filters="vibanPreferenceTableProperties.filters"
                    entity-locale-name="treasury_viban_preference"
                    create-route="treasury-viban-preference-create"
                >
                    <template #row-actions="{ item }">
                        <t-table-actions
                            :item="item"
                            :columns="vibanPreferenceTableProperties.columns"
                            :actions="vibanPreferenceTableProperties.actions"
                        ></t-table-actions>
                    </template>

                    <template #row-kind="{ item }">
                        {{ $t(`treasury.viban-preference.kinds.${item.kind}`) }}
                    </template>
                    <template #row-is_default="{ item }">
                        <span
                            class="p-1 my-1 text-xs rounded-md bg-opacity-10"
                            :class="{
                                'bg-green-100': item.is_default,
                                'bg-red-100': !item.is_default,
                            }"
                        >
                            {{ item.is_default ? $t("general.yes") : $t("general.no") }}
                        </span>
                    </template>
                </t-table>
            </div>
        </div>
        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ $t("organizations.settings.treasury.treasury_providers") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ $t("organizations.settings.treasury.treasury_providers_desc") }}
                </p>
            </div>

            <div class="lg:px-5">
                <t-table
                    v-bind="treasuryProviderTableProps"
                    v-on="treasuryProviderTableHandlers"
                    :columns="treasuryProviderTableProperties.columns"
                    :filters="treasuryProviderTableProperties.filters"
                    entity-locale-name="treasury_provider"
                    create-route="treasury-provider-create"
                >
                    <template #row-actions="{ item }">
                        <t-table-actions
                            :item="item"
                            :columns="treasuryProviderTableProperties.columns"
                            :actions="treasuryProviderTableProperties.actions"
                        ></t-table-actions>
                    </template>

                    <template #row-channel="{ item }">
                        <span class="text-sm">
                            {{ $t(`treasury.provider.channels.${item.channel}`) }}
                        </span>
                    </template>
                    <template #row-status="{ item }">
                        <span
                            class="p-1 my-1 text-xs rounded-md bg-opacity-10"
                            :class="TreasuryProviderStatusColors[item.status]"
                        >
                            {{ $t(`treasury.provider.status.${item.status}`) }}
                        </span>
                    </template>

                    <template #row-created_at="{ item }">
                        <span class="text-sm">
                            {{ dayjs(item.created_at).format("YYYY MMMM DD HH:mm") }}
                        </span>
                    </template>
                </t-table>
            </div>
        </div>

        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ $t("organizations.settings.treasury.treasury_organization_limits") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ $t("organizations.settings.treasury.treasury_organization_limits_desc") }}
                </p>
            </div>

            <div class="lg:px-5">
                <t-table
                    v-bind="treasuryOrganizationLimitTableProps"
                    v-on="treasuryOrganizationLimitTableHandlers"
                    :columns="treasuryOrganizationLimitTableProperties.columns"
                    :filters="treasuryOrganizationLimitTableProperties.filters"
                    entity-locale-name="treasury_organization_limit"
                    create-route="treasury-organization-limit-create"
                >
                    <template #row-actions="{ item }">
                        <t-table-actions
                            :item="item"
                            :columns="treasuryOrganizationLimitTableProperties.columns"
                            :actions="treasuryOrganizationLimitTableProperties.actions"
                        ></t-table-actions>
                    </template>

                    <template #row-limit_type="{ item }">
                        <span class="text-sm">
                            {{ $t(`treasury.organization_limit.limit_types.${item.limit_type}`) }}
                        </span>
                    </template>

                    <template #row-created_at="{ item }">
                        <span class="text-sm">
                            {{ dayjs(item.created_at).format("YYYY MMMM DD HH:mm") }}
                        </span>
                    </template>
                </t-table>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useListTable } from "@/composables/table";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { useOrganizationStore } from "@/store/submodules/organization";
import { useTreasuryVibanPreferenceStore } from "@/store/submodules/treasury-viban-preference";
import { useOrganizationTreasuryDescriptorStore } from "@/store/submodules/organization-treasury-descriptor";
import { useTreasuryProviderStore } from "@/store/submodules/treasury-provider";
import { useTreasuryOrganizationLimitStore } from "@/store/submodules/treasury-organization-limit";
import { TreasuryProviderStatusColors } from "@/constant/treasury"
import dayjs from "dayjs";

const { t } = useI18n();
const route = useRoute();
const vibanPreferenceStore = useTreasuryVibanPreferenceStore();
const organizationStore = useOrganizationStore();
const treasuryDescriptorStore = useOrganizationTreasuryDescriptorStore();
const treasuryProviderStore = useTreasuryProviderStore();
const treasuryOrganizationLimitStore = useTreasuryOrganizationLimitStore();

const organizations = computed(() => (organizationStore.getItems.rows ? organizationStore.getItems.rows : []));

const {
    props: vibanPreferenceTableProps,
    handlers: vibanPreferenceTableHandlers,
    onMount: vibanPreferenceTableOnMount,
    refresh: vibanPreferenceTableRefresh,
} = useListTable(
    () => vibanPreferenceStore.getItems,
    vibanPreferenceStore.List,
    computed(() => ({
        id: route.params.id,
    })),
);

const deleteVibanPreference = async (item) => {
    await vibanPreferenceStore.Delete(route.params.id, item.id);
    vibanPreferenceTableRefresh();
};

const vibanPreferenceTableProperties = computed(() => ({
    id: route.params.id,
    columns: [
        {
            row: "name",
            label: "Name",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "kind",
            label: "Kind",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "is_default",
            label: "Is Default",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "actions",
            label: t("general.actions"),
            sortable: false,
            align: "center",
            mobile: true,
            visible: true,
        },
    ],
    actions: [
        {
            label: t("general.edit"),
            params: (item) => ({
                id: route.params.id,
                preferenceId: item.id,
            }),
            icon: "pencil",
            color: "blue",
            action: "edit",
            route: "treasury-viban-preference-edit",
        },
        {
            label: t("general.delete"),
            icon: "trash",
            color: "rose",
            action: "delete",
            function: deleteVibanPreference,
        },
    ],
}));

const {
    props: treasuryDescriptorTableProps,
    handlers: _treasuryDescriptorTableHandlers,
    onMount: treasuryDescriptorTableOnMount,
    refresh: treasuryDescriptorTableRefresh,
} = useListTable(() => treasuryDescriptorStore.getItems, treasuryDescriptorStore.List);

const deleteOrganizationTreasuryDescriptor = async (item) => {
    await treasuryDescriptorStore.Delete(item.id);
    treasuryDescriptorTableRefresh();
};

const treasuryDescriptorTableProperties = computed(() => {
    const isOrganizationFiltered = !!route.query.organization_id;

    return {
        columns: [
            {
                row: "title",
                label: "Title",
                sortable: true,
                mobile: true,
                align: "center",
                visible: true,
            },
            {
                row: "organization_name",
                label: t("organization_design.general.organization"),
                sortable: true,
                align: "center",
                mobile: true,
                visible: !isOrganizationFiltered,
            },
            {
                row: "created_at",
                label: "Created At",
                sortable: true,
                mobile: true,
                align: "center",
                visible: true,
            },
            {
                row: "actions",
                label: t("general.actions"),
                sortable: false,
                align: "center",
                mobile: true,
                visible: true,
            },
        ],
        filters: [
            {
                name: "Organization ID",
                type: "selectbox",
                dataName: "organization_id",
                colSize: 6,
                value: route.query.organization_id ? route.query.organization_id : null,
                data: organizations.value,
                show: true,
            },
        ],
        actions: [
            {
                label: t("general.edit"),
                icon: "pencil",
                color: "blue",
                action: "edit",
                route: "organization-treasury-descriptor-edit",
            },
            {
                label: t("general.delete"),
                icon: "trash",
                color: "rose",
                action: "delete",
                function: deleteOrganizationTreasuryDescriptor,
            },
        ],
    };
});

const {
    props: treasuryProviderTableProps,
    handlers: treasuryProviderTableHandlers,
    onMount: treasuryProviderTableOnMount,
    refresh: treasuryProviderTableRefresh,
} = useListTable(
    () => treasuryProviderStore.getItems,
    treasuryProviderStore.List,
    computed(() => ({
        id: route.params.id,
    })),
);

const deleteTreasuryProvider = async (item) => {
    await treasuryProviderStore.Delete(route.params.id, item.id);
    treasuryProviderTableRefresh();
};

const treasuryProviderTableProperties = computed(() => ({
    id: route.params.id,
    columns: [
        {
            row: "channel",
            label: "Channel",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "name",
            label: "Name",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "status",
            label: "Status",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "created_at",
            label: "Created At",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "actions",
            label: t("general.actions"),
            sortable: false,
            align: "center",
            mobile: true,
            visible: true,
        },
    ],
    actions: [
        {
            label: t("treasury.view_actions"),
            params: (item) => ({
                id: route.params.id,
                providerId: item.id,
            }),
            icon: "terminal",
            color: "sky",
            route: "treasury-cron-action-list",
        },
        {
            label: t("general.edit"),
            params: (item) => ({
                id: route.params.id,
                providerId: item.id,
            }),
            icon: "pencil",
            color: "blue",
            action: "edit",
            route: "treasury-provider-edit",
        },
        {
            label: t("general.delete"),
            icon: "trash",
            color: "rose",
            action: "delete",
            function: deleteTreasuryProvider,
        },
    ],
}));

const {
    props: treasuryOrganizationLimitTableProps,
    handlers: treasuryOrganizationLimitTableHandlers,
    onMount: treasuryOrganizationLimitTableOnMount,
    refresh: treasuryOrganizationLimitTableRefresh,
} = useListTable(
    () => treasuryOrganizationLimitStore.getItems,
    treasuryOrganizationLimitStore.List,
    computed(() => ({
        id: route.params.id,
    })),
);

const deleteTreasuryOrganizationLimit = async (item) => {
    await treasuryOrganizationLimitStore.Delete(route.params.id, item.id);
    treasuryOrganizationLimitTableRefresh();
};

const treasuryOrganizationLimitTableProperties = computed(() => ({
    id: route.params.id,
    columns: [
        {
            row: "limit_type",
            label: "Limit Type",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "max_automatic_transfer_amount",
            label: "Max Auto Transfer Amount",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "daily_statement_fetch_limit",
            label: "Daily Statement Fetch Limit",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "daily_transfer_count_limit",
            label: "Daily Transfer Count Limit",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "daily_transfer_amount_limit",
            label: "Daily Transfer Amount Limit",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "created_at",
            label: "Created At",
            sortable: true,
            mobile: true,
            align: "center",
            visible: true,
        },
        {
            row: "actions",
            label: t("general.actions"),
            sortable: false,
            align: "center",
            mobile: true,
            visible: true,
        },
    ],
    actions: [
        {
            label: t("general.edit"),
            params: (item) => ({
                id: route.params.id,
                limitId: item.id,
            }),
            icon: "pencil",
            color: "blue",
            action: "edit",
            route: "treasury-organization-limit-edit",
        },
        {
            label: t("general.delete"),
            icon: "trash",
            color: "rose",
            action: "delete",
            function: deleteTreasuryOrganizationLimit,
        },
    ],
}));

onMounted(() => {
    organizationStore.List(1, 1000);

    vibanPreferenceTableOnMount();

    treasuryDescriptorTableOnMount();

    treasuryProviderTableOnMount();

    treasuryOrganizationLimitTableOnMount();
});
</script>
