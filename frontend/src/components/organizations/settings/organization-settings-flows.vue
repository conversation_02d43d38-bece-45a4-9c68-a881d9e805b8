<template>
    <div class="mx-auto max-w-2xl space-y-16 sm:space-y-20 lg:mx-0 lg:max-w-none">
        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ $t("organizations.settings.flows.title") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ $t("organizations.settings.flows.description") }}
                </p>
            </div>

            <div
                v-if="loading"
                class="lg:px-5"
            >
                <t-loader />
            </div>
            <div
                v-else-if="error"
                class="lg:px-5"
            >
                <ErrorCustom
                    size="sm"
                    centered
                    :code="error?.code || 500"
                    :title="error?.title || $t('form.internal_error')"
                    :message="error?.message || $t('form.an_error_occurred')"
                >
                    <template #action>
                        <button
                            type="button"
                            class="mt-10 text-sm/7 font-semibold text-indigo-600 flex items-center gap-2"
                            @click.prevent="$router.go(0)"
                        >
                            <MonoIcon
                                name="refresh"
                                class="size-4"
                            />
                            {{ $t("general.retry") }}
                        </button>
                    </template>
                </ErrorCustom>
            </div>
            <div
                v-else
                class="space-y-4 divide-y divide-gray-100 dark:divide-gray-600 lg:px-5"
            >
                <div
                    v-for="entity in flows"
                    :key="entity"
                    class="pt-4"
                >
                    <CollapsableCard :title="`${$t(`entities.${entity.entity}`)} Flow Settings`">
                        <form
                            @submit.prevent="updateOrganization(entity)"
                            class="px-4"
                        >
                            <div class="grid grid-cols-6 gap-3">
                                <div class="col-span-3 space-y-2">
                                    <label class="block text-sm font-medium">
                                        {{ $t("organizations.settings.flows.assign_admin_users") }}
                                    </label>
                                    <t-select
                                        v-model="entity.admins"
                                        searchable
                                        multiple
                                        long
                                        :options="users"
                                    />
                                </div>
                                <div class="col-span-3 space-y-2">
                                    <label class="block text-sm font-medium">
                                        {{ $t("organizations.settings.flows.is_enabled") }}
                                    </label>
                                    <Switch v-model="entity.is_enabled" />
                                </div>
                                <div class="col-span-3 space-y-2">
                                    <label class="block text-sm font-medium">
                                        {{ $t("organizations.settings.flows.eligible_value") }}
                                    </label>
                                    <input
                                        type="number"
                                        :min="0"
                                        step="1"
                                        class="t-input"
                                        v-model="entity.eligible_value"
                                    />
                                </div>
                                <div class="col-span-3 space-y-2">
                                    <label class="block text-sm font-medium">
                                        {{ $t("organizations.settings.flows.minimum_approval") }}
                                    </label>
                                    <input
                                        type="number"
                                        class="t-input"
                                        v-model="entity.minimum_approval"
                                    />
                                </div>
                            </div>
                            <div class="flex justify-center mt-5">
                                <button
                                    type="submit"
                                    class="bg-indigo-600 text-white px-4 py-2 rounded-md"
                                >
                                    {{ $t("general.update") }}
                                </button>
                            </div>
                        </form>
                    </CollapsableCard>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useOrganizationStore } from "@/store/submodules/organization";

const store = useOrganizationStore();
const route = useRoute();
const toast = inject("toast");
const flows = ref([]);

const loading = ref(true);
const error = ref(null);

const users = ref([]);

onMounted(async () => {
    try {
        const res = await store.GetFlows(route.params.id);
        flows.value = res.sort((a, b) => a.entity.localeCompare(b.entity));

        users.value = await store.GetUsers();
    } catch (err) {
        error.value = {
            code: "500",
            title: t("form.internal_error"),
            message: t("form.an_error_occurred"),
        };
    } finally {
        loading.value = false;
    }
});

function updateOrganization(data) {
    if (data.admins?.length === 0 || !data.admins) {
        toast.error("Please select at least one admin user");
        return;
    }
    store
        .UpdateFlows(data)
        .then((res) => {
            toast.success(res.message);
            document.location.reload();
        })
        .catch((err) => {
            toast.error(err.response.data.error);
        });
}
</script>
