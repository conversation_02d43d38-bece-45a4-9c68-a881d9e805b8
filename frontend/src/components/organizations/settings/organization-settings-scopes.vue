<template>
    <div class="mx-auto max-w-2xl space-y-16 sm:space-y-20 lg:mx-0 lg:max-w-none">
        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ $t("organizations.settings.scopes.title") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ $t("organizations.settings.scopes.description") }}
                </p>
            </div>
            <div
                v-if="loading"
                class="lg:px-5"
            >
                <t-loader />
            </div>
            <div
                v-else-if="error"
                class="lg:px-5"
            >
                <ErrorCustom
                    size="sm"
                    centered
                    :code="error?.code || 500"
                    :title="error?.title || $t('form.internal_error')"
                    :message="error?.message || $t('form.an_error_occurred')"
                >
                    <template #action>
                        <button
                            type="button"
                            class="mt-10 text-sm/7 font-semibold text-indigo-600 flex items-center gap-2"
                            @click.prevent="$router.go(0)"
                        >
                            <MonoIcon
                                name="refresh"
                                class="size-4"
                            />
                            {{ $t("general.retry") }}
                        </button>
                    </template>
                </ErrorCustom>
            </div>
            <form
                v-else
                @submit.prevent="updateOrganization()"
                class="space-y-4 lg:px-5"
            >
                <div class="hidden md:block sticky top-0 start-0 rounded-lg bg-stone-200 dark:bg-neutral-800">
                    <div class="grid md:grid-cols-9 lg:gap-x-3 md:gap-x-3 p-1 md:p-3">
                        <div class="col-span-5 self-center">
                            <h2 class="font-semibold text-gray-800 dark:text-neutral-200">User Scopes</h2>
                            <button
                                type="button"
                                @click="
                                    form.user_scopes.forEach((item) => {
                                        item.create = !item.create;
                                        item.read = !item.read;
                                        item.update = !item.update;
                                        item.delete = !item.delete;
                                    })
                                "
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Create</h3>

                            <button
                                type="button"
                                @click="form.user_scopes.forEach((item) => (item.create = !item.create))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                                />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Read</h3>

                            <button
                                type="button"
                                @click="form.user_scopes.forEach((item) => (item.read = !item.read))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                                />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Update</h3>

                            <button
                                type="button"
                                @click="form.user_scopes.forEach((item) => (item.update = !item.update))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Delete</h3>
                            <button
                                type="button"
                                @click="form.user_scopes.forEach((item) => (item.delete = !item.delete))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                    </div>
                </div>

                <ul
                    class="grid grid-cols-5 md:grid-cols-9 md:items-center gap-1.5 md:gap-6 my-3 px-3 md:px-5"
                    v-for="(item, index) in form.user_scopes"
                    :key="index"
                >
                    <li class="md:col-span-5">
                        <p class="text-sm font-medium break-words text-gray-800 dark:text-neutral-200">
                            {{ $t(`scopes.entities.${item.entity}`) }}
                        </p>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Create </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-green-500 checked:border-green-500 dark:checked:bg-green-500 dark:checked:border-green-500 dark:focus:ring-offset-gray-800"
                                    v-model="item.create"
                                />
                            </div>
                        </div>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Read </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-indigo-500 checked:border-indigo-500 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800"
                                    v-model="item.read"
                                />
                            </div>
                        </div>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Update </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    v-model="item.update"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-yellow-500 checked:border-yellow-500 dark:checked:bg-yellow-500 dark:checked:border-yellow-500 dark:focus:ring-offset-gray-800"
                                />
                            </div>
                        </div>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Delete </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    v-model="item.delete"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-rose-500 checked:border-rose-500 dark:checked:bg-rose-500 dark:checked:border-rose-500 dark:focus:ring-offset-gray-800"
                                />
                            </div>
                        </div>
                    </li>
                </ul>

                <div
                    v-for="(item, index) in form.user_scopes"
                    v-if="false"
                    class="flex flex-col px-4 py-2 border border-gray-500"
                    :key="index"
                >
                    <div class="grid grid-cols-8 gap-3">
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ item.entity }} Create</label>
                            <Switch v-model="item.create" />
                        </div>
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ item.entity }} Read</label>
                            <Switch v-model="item.read" />
                        </div>
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ item.entity }} Update</label>
                            <Switch v-model="item.update" />
                        </div>
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ item.entity }} Delete</label>
                            <Switch v-model="item.delete" />
                        </div>
                    </div>
                </div>

                <div class="hidden md:block sticky top-0 start-0 rounded-lg bg-stone-200 dark:bg-neutral-800">
                    <div class="grid md:grid-cols-9 lg:gap-x-3 md:gap-x-3 p-1 md:p-3">
                        <div class="col-span-5 self-center">
                            <h2 class="font-semibold text-gray-800 dark:text-neutral-200">Merchant Scopes</h2>
                            <button
                                type="button"
                                @click="
                                    form.merchant_scopes.forEach((item) => {
                                        item.create = !item.create;
                                        item.read = !item.read;
                                        item.update = !item.update;
                                        item.delete = !item.delete;
                                    })
                                "
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Create</h3>

                            <button
                                type="button"
                                @click="form.merchant_scopes.forEach((item) => (item.create = !item.create))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                                />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Read</h3>

                            <button
                                type="button"
                                @click="form.merchant_scopes.forEach((item) => (item.read = !item.read))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                                />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Update</h3>

                            <button
                                type="button"
                                @click="form.merchant_scopes.forEach((item) => (item.update = !item.update))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Delete</h3>
                            <button
                                type="button"
                                @click="form.merchant_scopes.forEach((item) => (item.delete = !item.delete))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                    </div>
                </div>

                <ul
                    class="grid grid-cols-5 md:grid-cols-9 md:items-center gap-1.5 md:gap-6 my-3 px-3 md:px-5"
                    v-for="(item, index) in form.merchant_scopes"
                    :key="index"
                >
                    <li class="md:col-span-5">
                        <p class="text-sm font-medium break-words text-gray-800 dark:text-neutral-200">
                            {{ $t(`scopes.entities.${item.entity}`) }}
                        </p>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Create </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-green-500 checked:border-green-500 dark:checked:bg-green-500 dark:checked:border-green-500 dark:focus:ring-offset-gray-800"
                                    v-model="item.create"
                                />
                            </div>
                        </div>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Read </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-indigo-500 checked:border-indigo-500 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800"
                                    v-model="item.read"
                                />
                            </div>
                        </div>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Update </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    v-model="item.update"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-yellow-500 checked:border-yellow-500 dark:checked:bg-yellow-500 dark:checked:border-yellow-500 dark:focus:ring-offset-gray-800"
                                />
                            </div>
                        </div>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Delete </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    v-model="item.delete"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-rose-500 checked:border-rose-500 dark:checked:bg-rose-500 dark:checked:border-rose-500 dark:focus:ring-offset-gray-800"
                                />
                            </div>
                        </div>
                    </li>
                </ul>

                <div
                    v-for="(mscope, index) in form.merchant_scopes"
                    v-if="false"
                    class="flex flex-col px-4 py-2 border border-gray-500"
                    :key="index"
                >
                    <div class="grid grid-cols-8 gap-3">
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ mscope.entity }} Create</label>
                            <Switch v-model="mscope.create" />
                        </div>
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ mscope.entity }} Read</label>
                            <Switch v-model="mscope.read" />
                        </div>
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ mscope.entity }} Update</label>
                            <Switch v-model="mscope.update" />
                        </div>
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ mscope.entity }} Delete</label>
                            <Switch v-model="mscope.delete" />
                        </div>
                    </div>
                </div>

                <div class="hidden md:block sticky top-0 start-0 rounded-lg bg-stone-200 dark:bg-neutral-800">
                    <div class="grid md:grid-cols-9 lg:gap-x-3 md:gap-x-3 p-1 md:p-3">
                        <div class="col-span-5 self-center">
                            <h2 class="font-semibold text-gray-800 dark:text-neutral-200">Payment Scopes</h2>
                            <button
                                type="button"
                                @click="
                                    form.payment_scopes.forEach((item) => {
                                        item.create = !item.create;
                                        item.read = !item.read;
                                        item.update = !item.update;
                                        item.delete = !item.delete;
                                    })
                                "
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                                />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Create</h3>

                            <button
                                type="button"
                                @click="form.payment_scopes.forEach((item) => (item.create = !item.create))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"
                                />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Read</h3>

                            <button
                                type="button"
                                @click="form.payment_scopes.forEach((item) => (item.read = !item.read))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
                                />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Update</h3>

                            <button
                                type="button"
                                @click="form.payment_scopes.forEach((item) => (item.update = !item.update))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                        <div class="col-span-1 text-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke-width="2"
                                stroke="currentColor"
                                class="flex-shrink-0 size-5 mx-auto text-gray-500 dark:text-neutral-500"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                                />
                            </svg>

                            <h3 class="mt-2 text-sm font-medium text-gray-800 dark:text-neutral-200">Delete</h3>
                            <button
                                type="button"
                                @click="form.payment_scopes.forEach((item) => (item.delete = !item.delete))"
                                class="text-xs text-blue-600 decoration-2 hover:underline font-medium focus:outline-none focus:underline dark:text-blue-400 dark:hover:text-blue-500"
                            >
                                Toggle all
                            </button>
                        </div>
                    </div>
                </div>

                <ul
                    class="grid grid-cols-5 md:grid-cols-9 md:items-center gap-1.5 md:gap-6 my-3 px-3 md:px-5"
                    v-for="(item, index) in form.payment_scopes"
                    :key="index"
                >
                    <li class="md:col-span-5">
                        <p class="text-sm font-medium break-words text-gray-800 dark:text-neutral-200">
                            {{ $t(`scopes.entities.${item.entity}`) }}
                        </p>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Create </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-green-500 checked:border-green-500 dark:checked:bg-green-500 dark:checked:border-green-500 dark:focus:ring-offset-gray-800"
                                    v-model="item.create"
                                />
                            </div>
                        </div>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Read </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-indigo-500 checked:border-indigo-500 dark:checked:bg-indigo-500 dark:checked:border-indigo-500 dark:focus:ring-offset-gray-800"
                                    v-model="item.read"
                                />
                            </div>
                        </div>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Update </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    v-model="item.update"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-yellow-500 checked:border-yellow-500 dark:checked:bg-yellow-500 dark:checked:border-yellow-500 dark:focus:ring-offset-gray-800"
                                />
                            </div>
                        </div>
                    </li>

                    <li class="col-span-1">
                        <div class="grid grid-cols-2 items-center md:block">
                            <span class="md:hidden text-sm text-gray-500 dark:text-neutral-200"> Delete </span>
                            <div class="text-end md:text-center">
                                <input
                                    type="checkbox"
                                    v-model="item.delete"
                                    class="shrink-0 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:bg-neutral-800 dark:border-neutral-600 checked:bg-rose-500 checked:border-rose-500 dark:checked:bg-rose-500 dark:checked:border-rose-500 dark:focus:ring-offset-gray-800"
                                />
                            </div>
                        </div>
                    </li>
                </ul>

                <div
                    v-for="(item, index) in form.payment_scopes"
                    v-if="false"
                    class="flex flex-col px-4 py-2 border border-gray-500"
                    :key="index"
                >
                    <div class="grid grid-cols-8 gap-3">
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ item.entity }} Create</label>
                            <Switch v-model="item.create" />
                        </div>
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ item.entity }} Read</label>
                            <Switch v-model="item.read" />
                        </div>
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ item.entity }} Update</label>
                            <Switch v-model="item.update" />
                        </div>
                        <div class="col-span-2 space-y-2">
                            <label class="block text-sm font-medium">{{ item.entity }} Delete</label>
                            <Switch v-model="item.delete" />
                        </div>
                    </div>
                </div>

                <div class="flex justify-center mt-5">
                    <button
                        type="submit"
                        class="px-4 py-2 text-white bg-indigo-600 rounded-md"
                    >
                        {{ $t("general.update") }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</template>

<script setup>
import { useOrganizationStore } from "@/store/submodules/organization";

const store = useOrganizationStore();
const route = useRoute();
const toast = inject("toast");

const loading = ref(true);
const error = ref(null);

const organization = ref({
    name: "",
});
const user_scopes = ref([]);
const merchant_scopes = ref([]);
const payment_scopes = ref([]);

const form = reactive({
    user_scopes: [],
    merchant_scopes: [],
    payment_scopes: [],
});

onMounted(async () => {
    try {
        const scopeList = await store.GetScopes();
        user_scopes.value = scopeList.user_scopes;
        merchant_scopes.value = scopeList.merchant_scopes;
        payment_scopes.value = scopeList.payment_scopes;

        const res = await store.Read(route.params.id);
        form.id = res.id;
        organization.value.name = res.name;
        localStorage.hash_id = res.hash_id;
        form.user_scopes = res.user_scopes
            ? res.user_scopes.map((item) => {
                  return {
                      ...item,
                      create: item.create ? item.create : false,
                      read: item.read ? item.read : false,
                      update: item.update ? item.update : false,
                      delete: item.delete ? item.delete : false,
                  };
              })
            : user_scopes.value.map((item) => {
                  return {
                      ...item,
                      create: false,
                      read: false,
                      update: false,
                      delete: false,
                  };
              });
        user_scopes.value.forEach((item) => {
            if (!form.user_scopes.find((scope) => scope.entity === item.entity)) {
                form.user_scopes.push({
                    ...item,
                    create: false,
                    read: false,
                    update: false,
                    delete: false,
                });
            }
        });
        form.merchant_scopes = res.merchant_scopes
            ? res.merchant_scopes.map((item) => {
                  return {
                      ...item,
                      create: item.create ? item.create : false,
                      read: item.read ? item.read : false,
                      update: item.update ? item.update : false,
                      delete: item.delete ? item.delete : false,
                  };
              })
            : merchant_scopes.value.map((item) => {
                  return {
                      ...item,
                      create: false,
                      read: false,
                      update: false,
                      delete: false,
                  };
              });
        merchant_scopes.value.forEach((item) => {
            if (!form.merchant_scopes.find((scope) => scope.entity === item.entity)) {
                form.merchant_scopes.push({
                    ...item,
                    create: false,
                    read: false,
                    update: false,
                    delete: false,
                });
            }
        });
        form.payment_scopes = res.payment_scopes
            ? res.payment_scopes.map((item) => {
                  return {
                      ...item,
                      create: item.create ? item.create : false,
                      read: item.read ? item.read : false,
                      update: item.update ? item.update : false,
                      delete: item.delete ? item.delete : false,
                  };
              })
            : payment_scopes.value.map((item) => {
                  return {
                      ...item,
                      create: false,
                      read: false,
                      update: false,
                      delete: false,
                  };
              });
        payment_scopes.value.forEach((item) => {
            if (!form.payment_scopes.find((scope) => scope.entity === item.entity)) {
                form.payment_scopes.push({
                    ...item,
                    create: false,
                    read: false,
                    update: false,
                    delete: false,
                });
            }
        });
    } catch (err) {
        error.value = {
            code: "500",
            title: t("form.internal_error"),
            message: t("form.an_error_occurred"),
        };
    } finally {
        loading.value = false;
    }
});

function updateOrganization() {
    store
        .UpdateScopes(form)
        .then((res) => {
            toast.success(res.message);
            // localStorage.removeItem("hash_id");
            // document.location.reload();
        })
        .catch((err) => {
            toast.error(err.response.data.error);
        });
}
</script>
