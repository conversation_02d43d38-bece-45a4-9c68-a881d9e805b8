<template>
    <nav class="flex-none px-4 sm:px-6 lg:px-0">
        <ul
            role="list"
            class="flex gap-x-3 gap-y-1 whitespace-nowrap lg:flex-col"
        >
            <li v-for="tab in tabs">
                <button
                    :key="tab.id"
                    type="button"
                    :disabled="tab.disabled"
                    class="flex gap-x-3 w-full rounded-md py-2 pl-2 pr-3 text-sm/6 font-semibold text-gray-700 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700"
                    :class="{
                        'bg-gray-50 text-indigo-600 dark:bg-neutral-900 dark:text-white dark:hover:bg-gray-700':
                            tab.isActive,
                        'text-gray-700 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700': !tab.isActive,
                    }"
                    @click="selectTab(tab)"
                >
                    <MonoIcon
                        class="size-6 shrink-0"
                        :name="tab.icon"
                    />
                    {{ tab.title }}
                </button>
            </li>
        </ul>
    </nav>
</template>

<script setup>
const props = defineProps({
    modelValue: {
        type: [String, Number],
        required: true,
    },
    tabs: {
        type: Array,
        required: true,
    },
});

const emits = defineEmits(["update:modelValue"]);

const selectTab = (selectedTab) => {
    if (!selectedTab.disabled) {
        emits("update:modelValue", selectedTab.id);
    }
};

const set = () => {
    if (props.tabs && props.tabs.length > 0) {
        props.tabs.forEach((tab) => {
            tab.isActive = tab.id === props.modelValue && !tab.disabled;
        });
    }
};

// Watchers
watch(
    () => props.modelValue,
    () => {
        set();
    },
    { immediate: true, deep: true },
);

watch(
    () => props.tabs,
    () => {
        set();
    },
    { immediate: true, deep: true },
);

// Lifecycle
onMounted(() => {
    set();
});
</script>
