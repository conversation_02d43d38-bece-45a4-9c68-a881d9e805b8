<template>
    <div class="mx-auto max-w-2xl space-y-16 sm:space-y-20 lg:mx-0 lg:max-w-none">
        <div class="space-y-4">
            <div class="border-b border-gray-200 dark:border-gray-600 pb-4 lg:px-5">
                <h2 class="text-base/7 font-semibold text-gray-900 dark:text-neutral-200">
                    {{ $t("organizations.settings.terminate.title") }}
                </h2>
                <p class="mt-1 text-sm/6 text-gray-500">
                    {{ $t("organizations.settings.terminate.description") }}
                </p>
            </div>
            <div
                v-if="loading"
                class="lg:px-5"
            >
                <t-loader />
            </div>
            <div
                v-else-if="error"
                class="lg:px-5"
            >
                <ErrorCustom
                    size="sm"
                    centered
                    :code="error?.code || 500"
                    :title="error?.title || $t('form.internal_error')"
                    :message="error?.message || $t('form.an_error_occurred')"
                >
                    <template #action>
                        <button
                            type="button"
                            class="mt-10 text-sm/7 font-semibold text-indigo-600 flex items-center gap-2"
                            @click.prevent="$router.go(0)"
                        >
                            <MonoIcon
                                name="refresh"
                                class="size-4"
                            />
                            {{ $t("general.retry") }}
                        </button>
                    </template>
                </ErrorCustom>
            </div>
            <div
                v-else
                class="lg:px-5 space-y-4 divide-y divide-gray-200 dark:divide-gray-600"
            >
                <!-- terminate organization -->

                <div class="space-y-4">
                    <div v-if="enitity_list.length > 0">
                        <p class="text-sm/6 text-gray-500">
                            {{ $t("organizations.settings.terminate.entities") }}
                        </p>
                        <ul class="list-disc pl-5 mt-2 space-y-1">
                            <li
                                v-for="entity in enitity_list"
                                :key="entity.id"
                                class="text-sm/6 text-gray-900 dark:text-neutral-200"
                            >
                                {{ entity.name }}
                            </li>
                        </ul>
                    </div>
                        
                </div>
                

                <!-- button -->
                <div class="grid grid-cols-2 lg:grid-cols-2 items-center gap-4 pt-4">
                    
                    <div class="flex justify-end">
                        <button
                            type="button"
                            class="flex items-center min-w-24 min-h-6 justify-center bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md"
                            @click="terminateOrganization"
                            :disabled="formProcessing"
                        >
                            <MonoIcon
                                v-if="formProcessing"
                                name="spin"
                                class="size-6 animate-spin"
                            />
                            <span v-else>
                                {{ $t("general.delete") }}
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { t, te } from "@/plugins/i18n";
import { useOrganizationStore } from "@/store/submodules/organization";


const toast = inject("toast");
const route = useRoute();
const store = useOrganizationStore();

const loading = ref(true);
const error = ref(null);

const formProcessing = ref(false);

const terminate_plan = ref([]);

const terminateOrganization = () => {
    formProcessing.value = true;

    store
        .Terminate({ id: route.params.id })
        .then((res) => {
            toast.success(res.message);
            router.push({ name: "organizations" });
        })
        .catch((err) => {
            // Checks if translation exist
            const localeKey = "organization." + err.response.data.error;
            if (te(localeKey)) {
                toast.error(t(localeKey));
                return;
            }
            toast.error(err.response.data.error);
        })
        .finally(() => {
            formProcessing.value = false;
        });
};


onMounted(async () => {
    try {
        const res = await getOrganization();

        if (res) {
            enitity_list.value = res.entities || [];
        }

        const terminatePlan = await store.TerminatePlan(route.params.id);
        if (terminatePlan) {
            enitity_list.value = [...enitity_list.value, ...terminatePlan.entities];
        }
        loading.value = false;
        
    } catch (err) {
        error.value = {
            code: "500",
            title: t("form.internal_error"),
            message: t("form.an_error_occurred"),
        };
    } finally {
        loading.value = false;
    }
});



const getOrganization = async () => {
    return store.Read(route.params.id);
};


</script>
