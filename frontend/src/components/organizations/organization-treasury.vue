<template>
    <div class="space-y-6">
        <!-- Sync Header -->
        <div class="bg-white border border-gray-200 shadow-sm rounded-xl p-6 dark:bg-neutral-800 dark:border-neutral-700">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ t('organizations.treasury.dashboard_title') }}</h3>
                    <p class="text-sm text-gray-500 dark:text-neutral-400">
                        {{ t('organizations.treasury.last_synchronized') }}: {{ formatDate(treasuryStore.getLastSyncDate || new Date()) }}
                    </p>
                </div>
                <button
                    @click="synchronize"
                    :disabled="treasuryStore.getIsLoading"
                    class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-medium rounded-lg transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <MonoIcon
                        class="size-4"
                        :name="treasuryStore.getIsLoading ? 'spin' : 'refresh'"
                        :class="{ 'animate-spin': treasuryStore.getIsLoading }"
                    />
                    {{ treasuryStore.getIsLoading ? t('organizations.treasury.synchronizing') : t('organizations.treasury.synchronize') }}
                </button>
            </div>
        </div>


        <!-- Treasury Summary -->
        <div class="space-y-6">
            <!-- Currency Balance Cards -->
            <div 
                class="flex gap-6"
                :class="{
                    'overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent pb-2': currencyBalances.length > 1,
                    'justify-start': currencyBalances.length === 1
                }"
            >
                <div 
                    v-for="currencyBalance in currencyBalances" 
                    :key="currencyBalance.currency"
                    class="bg-white border border-gray-200 shadow-sm rounded-xl p-6 dark:bg-neutral-800 dark:border-neutral-700 flex-shrink-0"
                    :class="{
                        'min-w-64': currencyBalances.length > 1,
                        'flex-1': currencyBalances.length === 1
                    }"
                >
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-neutral-400">
                                {{ t('organizations.treasury.summary.total_balance') }} ({{ currencyBalance.currency }})
                            </p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">
                                {{ formatCurrency(currencyBalance.balance, currencyBalance.currency) }}
                            </p>
                            <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1">
                                {{ currencyBalance.accountCount }} {{ t('organizations.treasury.accounts') }}
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                            <MonoIcon class="size-6 text-green-600 dark:text-green-400" name="wallet" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Other Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white border border-gray-200 shadow-sm rounded-xl p-6 dark:bg-neutral-800 dark:border-neutral-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-neutral-400">{{ t('organizations.treasury.summary.active_accounts') }}</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ treasuryStore.getActiveAccounts.length }}</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <MonoIcon class="size-6 text-blue-600 dark:text-blue-400" name="bank" />
                        </div>
                    </div>
                </div>

                <div class="bg-white border border-gray-200 shadow-sm rounded-xl p-6 dark:bg-neutral-800 dark:border-neutral-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-neutral-400">{{ t('organizations.treasury.summary.sync_status') }}</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ treasuryStore.getSyncStatus.sync_statuses?.length || 0 }}</p>
                        </div>
                        <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                            <MonoIcon class="size-6 text-orange-600 dark:text-orange-400" name="check-circle" />
                        </div>
                    </div>
                </div>

                <div class="bg-white border border-gray-200 shadow-sm rounded-xl p-6 dark:bg-neutral-800 dark:border-neutral-700">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500 dark:text-neutral-400">{{ t('organizations.treasury.summary.total_accounts') }}</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ treasuryStore.getItems.total }}</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                            <MonoIcon class="size-6 text-purple-600 dark:text-purple-400" name="transfer" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Balances -->
        <div class="bg-white border border-gray-200 shadow-sm rounded-xl p-6 dark:bg-neutral-800 dark:border-neutral-700">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">{{ t('organizations.treasury.account_balances') }}</h4>
            
            <div v-if="treasuryStore.getIsLoading && !treasuryStore.getActiveAccounts.length" class="flex justify-center py-8">
                <MonoIcon class="size-8 animate-spin text-indigo-600" name="spin" />
            </div>
            
            <div v-else-if="!treasuryStore.getActiveAccounts.length" class="text-center py-8">
                <p class="text-gray-500 dark:text-neutral-400">{{ t('organizations.treasury.no_accounts') }}</p>
            </div>
            
            <div v-else class="relative">
                <!-- Accounts container with overflow control -->
                <div 
                    class="relative transition-all duration-300"
                    :class="{ 'max-h-80 overflow-hidden': !showAllAccounts && hasMoreAccounts }"
                >
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div
                            v-for="account in treasuryStore.getActiveAccounts"
                            :key="account.id"
                            class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-neutral-700 dark:to-neutral-800 rounded-lg p-5 border border-gray-200 dark:border-neutral-600"
                        >
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center gap-3">
                                    <div class="w-10 h-10 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
                                        <MonoIcon class="size-5 text-indigo-600 dark:text-indigo-400" name="wallet" />
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-gray-900 dark:text-white">{{ account.account_title || account.account_number }}</h5>
                                        <p class="text-xs text-gray-500 dark:text-neutral-400">{{ getAccountTypeLabel(account.account_type) }}</p>
                                        <p class="text-xs text-gray-500 dark:text-neutral-400">{{ account.provider_name }}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-gray-900 dark:text-white">
                                        {{ formatCurrency(account.balance, account.currency_code) }}
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-neutral-400">
                                        {{ account.currency_code }}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="border-t border-gray-200 dark:border-neutral-600 pt-3 mt-3">
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-500 dark:text-neutral-400">{{ t('organizations.treasury.last_activity') }}</span>
                                    <span class="text-gray-700 dark:text-neutral-300">{{ formatDate(account.last_transaction_date || account.updated_at) }}</span>
                                </div>
                                <div class="flex justify-between items-center text-sm mt-1">
                                    <span class="text-gray-500 dark:text-neutral-400">IBAN</span>
                                    <span class="text-gray-700 dark:text-neutral-300">{{ account.iban || '-' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Gradient overlay when collapsed -->
                    <div 
                        v-if="!showAllAccounts && hasMoreAccounts" 
                        class="absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-white via-white/80 to-transparent dark:from-neutral-800 dark:via-neutral-800/80 pointer-events-none"
                    ></div>
                    
                    <!-- Show More/Less Button -->
                    <div v-if="hasMoreAccounts && !showAllAccounts" class="absolute inset-x-0 bottom-4 flex justify-center pointer-events-none">
                        <button
                            @click="showAllAccounts = !showAllAccounts"
                            class="px-6 py-3 bg-white hover:bg-gray-50 dark:bg-neutral-700 dark:hover:bg-neutral-600 border border-gray-200 dark:border-neutral-600 text-gray-700 dark:text-neutral-300 text-sm font-medium rounded-lg transition-colors flex items-center gap-2 shadow-sm pointer-events-auto"
                        >
                            <span>{{ t('organizations.treasury.show_all_accounts') }}</span>
                            <MonoIcon 
                                class="size-4 transition-transform duration-200" 
                                name="chevron-down"
                            />
                        </button>
                    </div>
                </div>

                <!-- Show Less Button (when expanded) -->
                <div v-if="hasMoreAccounts && showAllAccounts" class="flex justify-center mt-6">
                    <button
                        @click="showAllAccounts = !showAllAccounts"
                        class="px-6 py-3 bg-white hover:bg-gray-50 dark:bg-neutral-700 dark:hover:bg-neutral-600 border border-gray-200 dark:border-neutral-600 text-gray-700 dark:text-neutral-300 text-sm font-medium rounded-lg transition-colors flex items-center gap-2 shadow-sm"
                    >
                        <span>{{ t('organizations.treasury.show_less_accounts') }}</span>
                        <MonoIcon 
                            class="size-4 transition-transform duration-200" 
                            name="chevron-up"
                        />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { t } from "@/plugins/i18n"
import { useTreasuryAccountStore } from "@/store/submodules/treasury-account"

const props = defineProps({
    detail: {
        type: Object,
        required: true
    }
})

const toast = inject("toast")
const treasuryStore = useTreasuryAccountStore()
const showAllAccounts = ref(false)

const hasMoreAccounts = computed(() => {
    return treasuryStore.getActiveAccounts.length > 6
})

const availableCurrencies = computed(() => {
    const currencies = new Set()
    treasuryStore.getActiveAccounts.forEach(account => {
        if (account.currency_code) {
            currencies.add(account.currency_code)
        }
    })
    currencies.add('TRY')
    return Array.from(currencies).sort()
})

const currencyBalances = computed(() => {
    return availableCurrencies.value.map(currency => ({
        currency,
        balance: treasuryStore.getTotalBalance(currency),
        accountCount: treasuryStore.getActiveAccounts.filter(account => 
            account.currency_code === currency && account.is_active
        ).length
    }))
})

const synchronize = async () => {
    try {
        await treasuryStore.Sync({
            organization_id: props.detail.id
        })
        await loadAccountData()
        toast.success(t('organizations.treasury.sync_success'))
    } catch (error) {
        console.error('Sync error:', error)
        toast.error(t('organizations.treasury.sync_error'))
    }
}

const loadAccountData = async () => {
    try {
        await Promise.all([
            treasuryStore.List({ page: 1, per_page: 100 }, [
                { organization_id: props.detail.id },
                { is_active: true }
            ]),
            treasuryStore.ViewSyncStatus(props.detail.id)
        ])
    } catch (error) {
        console.error('Load account data error:', error)
        toast.error(t('organizations.treasury.load_error'))
    }
}

const getAccountTypeLabel = (accountType) => {
    const types = {
        1: t('organizations.treasury.account_types.term_deposit_try'),
        2: t('organizations.treasury.account_types.demand_deposit_try'),
        3: t('organizations.treasury.account_types.term_deposit_foreign_currency'),
        4: t('organizations.treasury.account_types.demand_deposit_foreign_currency'),
    }
    return types[accountType] || t('organizations.treasury.account_types.unknown')
}

const formatCurrency = (amount, currency) => {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
    }).format(amount || 0)
}

const formatDate = (dateString) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('tr-TR', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date)
}

onMounted(() => {
    loadAccountData()
})
</script> 