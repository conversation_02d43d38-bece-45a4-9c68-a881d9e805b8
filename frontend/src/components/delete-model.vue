<template>
    <Modal v-model="deleteModal.show">
        <template #content>
            <div class="flex flex-col items-center justify-center space-y-4">
                <div class="p-3 border-4 border-red-400 rounded-full">
                    <Icon name="exclamation" classes="w-16 h-16 text-red-400" />
                </div>
                <h2 class="text-lg text-semibold">
                    {{ $t("general.delete_confirm") }}
                </h2>
                <p class="text-sm opacity-70">
                    {{ $t("general.cannot_undo") }}
                </p>
            </div>
        </template>
        <template #footer>
            <button :disabled="deleteModal.processing" @click="
                deleteModal.show = false;
            deleteModal.id = null;
            " class="px-4 py-2 text-white bg-gray-500 rounded-md" :class="{
    'opacity-70 cursor-not-allowed': deleteModal.processing,
}">
                {{ $t("general.cancel") }}
            </button>
            <button :disabled="deleteModal.processing" @click="deleteEntity(deleteModal.id)"
                class="flex justify-center w-24 px-4 py-2 text-white bg-red-500 rounded-md" :class="{
                    'opacity-70 cursor-not-allowed': deleteModal.processing,
                }">
                <Spinner v-if="deleteModal.processing" color="white" />
                <span v-else>
                    {{ $t("general.delete") }}
                </span>
            </button>
        </template>
    </Modal>
</template>
<script setup>
import { computed, watch, ref, reactive, useSlots } from "vue";
const slot = useSlots();
const props = defineProps({
    deleteModal: Object,

})

const emits = defineEmits(['delete'])

function deleteEntity(id) {
    props.deleteModal.show = false;
    emits("delete", id)

}


</script>