<template>
    <div class="flex flex-col items-center justify-center p-5 text-center">
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            class="w-32 h-32 text-indigo-700 fill-current dark:text-white"
        >
            <circle
                cx="12"
                cy="12"
                r="0"
            >
                <animate
                    id="spinner_kIfO"
                    begin="0;spinner_xBIM.end"
                    attributeName="r"
                    calcMode="spline"
                    dur="1.2s"
                    values="0;11"
                    keySplines=".52,.6,.25,.99"
                    fill="freeze"
                />
                <animate
                    begin="0;spinner_xBIM.end"
                    attributeName="opacity"
                    calcMode="spline"
                    dur="1.2s"
                    values="1;0"
                    keySplines=".52,.6,.25,.99"
                    fill="freeze"
                />
            </circle>
            <circle
                cx="12"
                cy="12"
                r="0"
            >
                <animate
                    id="spinner_Pbsh"
                    begin="spinner_kIfO.begin+0.2s"
                    attributeName="r"
                    calcMode="spline"
                    dur="1.2s"
                    values="0;11"
                    keySplines=".52,.6,.25,.99"
                    fill="freeze"
                />
                <animate
                    begin="spinner_kIfO.begin+0.2s"
                    attributeName="opacity"
                    calcMode="spline"
                    dur="1.2s"
                    values="1;0"
                    keySplines=".52,.6,.25,.99"
                    fill="freeze"
                />
            </circle>
            <circle
                cx="12"
                cy="12"
                r="0"
            >
                <animate
                    id="spinner_xBIM"
                    begin="spinner_kIfO.begin+0.4s"
                    attributeName="r"
                    calcMode="spline"
                    dur="1.2s"
                    values="0;11"
                    keySplines=".52,.6,.25,.99"
                    fill="freeze"
                />
                <animate
                    begin="spinner_kIfO.begin+0.4s"
                    attributeName="opacity"
                    calcMode="spline"
                    dur="1.2s"
                    values="1;0"
                    keySplines=".52,.6,.25,.99"
                    fill="freeze"
                />
            </circle>
        </svg>

        <div
            class="max-w-sm mx-auto"
            v-if="showText"
        >
            <p class="mt-2 font-medium text-stone-800 dark:text-neutral-200">
                {{ loadingText }}
            </p>
        </div>
    </div>
</template>

<script setup>
import { t } from "@/plugins/i18n";

const props = defineProps({
    showText: {
        type: Boolean,
        default: true,
    },
    loadingText: {
        type: String,
        default: t("general.loading"),
    },
});
</script>
