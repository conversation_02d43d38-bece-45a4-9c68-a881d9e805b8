<template>
    <nav class="flex gap-x-1">
        <button v-for="(tab, index) in tabs" :key="index" @click="selectTab(tab)" type="button"
            class="px-2.5 py-1.5 transition-colors relative inline-flex items-center gap-x-2 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700 text-gray-500 hover:text-gray-800 text-sm rounded-lg focus:outline-none"
            :class="[
                tab.disabled ? 'cursor-not-allowed text-gray-400 dark:text-gray-300' : '',
                tab.isActive ? 'bg-gray-100 text-indigo-600 hover:text-indigo-600 dark:bg-neutral-900 dark:text-neutral-300 font-semibold' : '',
            ]">
            <MonoIcon class="mr-1 w-5 h-5 flex-shrink-0" :name="tab.icon" v-if="tab.icon" />
            {{ tab.title }}
        </button>

    </nav>
</template>

<script setup>
import { watch, onMounted } from 'vue'
import MonoIcon from '@/components/mono-icon.vue'

const props = defineProps({
    tabs: {
        type: Array,
        required: true,
    },
    modelValue: {
        type: [String, Number],
        required: true,
    },
});

const emits = defineEmits(["update:modelValue"]);


const selectTab = (selectedTab) => {
    if (!selectedTab.disabled) {
        emits("update:modelValue", selectedTab.id);
    }
};

const set = () => {
    if (props.tabs && props.tabs.length > 0) {
        props.tabs.forEach((tab) => {
            tab.isActive = tab.id === props.modelValue && !tab.disabled;
        });
    }
};

// Watchers
watch(
    () => props.modelValue,
    () => {
        set();
    },
    { immediate: true, deep: true }
);

watch(
    () => props.tabs,
    () => {
        set();
    },
    { immediate: true, deep: true }
);

// Lifecycle
onMounted(() => {
    set();
});
</script>
