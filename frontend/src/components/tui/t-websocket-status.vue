<template>
  <span v-if="isWebsocketConnected"
    class="py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-teal-100 text-teal-800 dark:bg-teal-500/10 dark:text-teal-500">
    <span class="size-1.5 inline-block bg-teal-800 rounded-full dark:bg-teal-500 animate-pulse"></span>
    Websocket Connected
  </span>
  <span v-if="!isWebsocketConnected && !isWebsocketConnecting"
    class="py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-500/10 dark:text-red-500">
    <span class="size-1.5 inline-block bg-red-800 rounded-full dark:bg-red-500 animate-pulse"></span>
    <button @click="refreshWebsocket">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
        class="w-4 h-4">
        <path stroke-linecap="round" stroke-linejoin="round"
          d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
      </svg>
    </button>
    Websocket Disconnected
  </span>
  <span v-if="isWebsocketConnecting"
    class="py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-orange-100 text-orange-800 dark:bg-orange-500/10 dark:text-orange-500">
    <span class="size-1.5 inline-block bg-teal-800 rounded-full dark:bg-orange-500 animate-pulse"></span>
    Websocket Connecting
  </span>
  <span v-if="isWebsocketConnectionError"
    class="py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-red-100 text-rose-800 dark:bg-rose-500/10 dark:text-rose-500">
    <span class="size-1.5 inline-block bg-rose-800 rounded-full dark:bg-rose-500 animate-pulse"></span>

    <button @click="refreshWebsocket">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
        class="w-4 h-4">
        <path stroke-linecap="round" stroke-linejoin="round"
          d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
      </svg>
    </button>

    Websocket Connection Error
  </span>

</template>

<script setup>
import { useSessionStore } from '@/store/submodules/session';

const sessionStore = useSessionStore();


const isWebsocketConnected = computed(() => {
  return sessionStore.getWebsocketConnected
})

const isWebsocketConnecting = computed(() => {
  return sessionStore.getWebsocketConnecting
})

const isWebsocketConnectionError = computed(() => {
  return sessionStore.getWebsocketConnectionError
})

const refreshWebsocket = () => {
  sessionStore.CloseWebsocketConnection();
  sessionStore.SessionWebsocket();
}

// watch for websocket connection status if connection is lost or error reconnect
watch([isWebsocketConnected, isWebsocketConnectionError], ([isConnected, isError]) => {
  if (!isConnected || isError) {
    sessionStore.CloseWebsocketConnection();
    sessionStore.SessionWebsocket();
  }
})

</script>
