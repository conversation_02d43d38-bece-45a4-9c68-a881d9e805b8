<script setup>
const props = defineProps({
    label: {
        type: String,
        default: ''
    },
    value: {
        default: ''
    },
    copyValue: {
        type: String,
        default: ''
    },
    line: {
        type: Number,
        default: 1
    },
    fullLine: {
        type: Boolean,
        default: false
    },
    copiable: {
        type: Boolean,
        default: false
    }
})

const copyValue = computed(() => props.copyValue ? props.copyValue : props.value)

const copyText = ref('Copy')

const Lines = ['line-clamp-1', 'line-clamp-2', 'line-clamp-3', 'line-clamp-4', 'line-clamp-5', 'line-clamp-6']

const copy = () => {
    window.navigator.clipboard.writeText(copyValue.value)
    copyText.value = 'Copied!'
    setTimeout(() => {
        copyText.value = 'Copy'
    }, 2000)
}
</script>

<template>
    <div class="t-input border flex flex-col rounded-md p-2 relative">
        <span class="text-xs font-semibold leading-tight text-gray-600 dark:text-gray-400">{{ label }}</span>
        <div v-if="copiable" class="absolute top-1 right-2">
            <button @click="copy" class="text-xs font-semibold text-blue-500 dark:text-blue-400">{{copyText}}</button>
        </div>
        <span :class="`text-md font-semibold ${fullLine ? 'block' : line < 6 ? Lines[line] : 'line-clamp-6'}`">
            <slot name="value">
                {{ value }}
        </slot>
            </span>
    </div>
</template>