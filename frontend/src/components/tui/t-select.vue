<template>
    <div class="space-y-2">
        <label class="flex gap-1 text-sm font-medium" v-if="label">
            {{ label }}
            <span v-if="required && !disabled" class="text-red-500">
                *
            </span>
        </label>
        <div class="relative flex [--auto-close:inside]" ref="dropdown">
            <button type="button" @click="dropmenu = !dropmenu" :disabled="disabled"
                :class="[(long ? 'min-w-full' : ''), (showError && 'border-rose-400 dark:border-rose-600')]"
                class="py-2 px-2.5 inline-flex justify-between  items-center gap-x-1.5 w-full  text-xs rounded-lg border border-gray-200 bg-white text-gray-800  hover:bg-gray-50 focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:bg-gray-50">


                <span class="truncate">
                    {{
                        multiple
                            ? selectedOptions?.length > 0
                                ? options.filter((option) => selectedOptions?.includes(option[dataLabel]))[0]?.[
                                      optionLabel
                                  ]
                                : placeholder
                            : selected !== null && selected !== undefined && selected !== ""
                              ? selectedDataFind?.[optionLabel] 
                              : placeholder
                    }}
                    {{ selectedOptions && selectedOptions.length > 1 ? `+${selectedOptions.length - 1} more` : "" }}
                </span>


                <span class="flex items-center">
                    <svg class="w-4 h-4 transition transform" :class="[dropmenu ? 'rotate-180' : null]" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </span>
            </button>

            <transition enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95">

                <div v-if="dropmenu"
                    class="absolute top-full start-0 mt-1 z-10 bg-white rounded-xl dark:bg-neutral-900 ring-2 ring-stone-200 dark:ring-neutral-700 max-h-72 overflow-auto"
                    :class="[long ? 'w-full' : 'w-48']">
                    <div class="p-1 space-y-1 ">
                        <div class="px-2 py-2 border-b dark:border-neutral-700" v-if="props.searchable">
                            <label for="hs-pro-daufvd"
                                class="block mb-2 text-sm font-medium text-stone-800 dark:text-neutral-200">
                                Search
                            </label>
                            <input id="hs-pro-daufvd" type="text" v-model="search"
                                class="py-1 px-2 block w-full border-stone-200 rounded-lg text-sm text-stone-800 placeholder:text-stone-500 focus:z-10 focus:border-green-600 focus:ring-green-600 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600">
                        </div>
                        <div v-if="props.multiple && searchedData.length > 0 && search"
                            class="w-full flex gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 cursor-pointer dark:text-neutral-300 focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800 "
                            @click="selectSearchedData()">
                            <div class="flex w-full items-center justify-between">
                                <div class="">
                                    Select all
                                </div>
                            </div>
                        </div>

                        <div v-for="option in searchedData" @click="selectOption(option[dataLabel])"
                            class="w-full flex gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 cursor-pointer dark:text-neutral-300 focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800 "
                            :class="[isSelected(option[dataLabel]) ? 'bg-gray-100 dark:bg-neutral-800' : '']">
                            <div class="flex w-full items-center justify-between">
                                <div class="break-all">
                                    {{ option[optionLabel] }}
                                </div>
                                <span class="ms-auto" v-if="isSelected(option[dataLabel])">
                                    <svg class="flex-shrink-0 w-3.5 h-3.5 " xmlns="http:.w3.org/2000/svg" width="24"
                                        height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="20 6 9 17 4 12"></polyline>
                                    </svg>
                                </span>
                            </div>
                        </div>
                        <div v-if="searchedData.length < 1"
                            class="w-full flex gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800  dark:text-neutral-300 focus:outline-none focus:bg-gray-100  dark:focus:bg-neutral-800 ">
                            <div class="flex w-full items-center justify-between">
                                <div class="">
                                    No data found
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </transition>
        </div>
        <span class="text-xs leading-3 text-rose-500" v-if="showError && label">
            {{ $t("general.field_required", { field: label }) }} *
        </span>
    </div>

</template>

<script setup>
const props = defineProps({
    options: {
        type: Array,
        default: () => [],
    },
    modelValue: {
        default: null,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    label: {
        type: String,
        default: null,
    },
    optionLabel: {
        default: 'name'
    },
    dataLabel: {
        default: 'id'
    },
    searchable: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Select an option',
    },
    required: {
        type: Boolean,
        default: false,
    },
    closeAfterSelect: {
        type: [Boolean, String],
        default: false,
    },
    long: {
        type: Boolean,
        default: false,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});


const selected = ref(null);
const selectedOptions = ref([]);
const search = ref('');

onMounted(() => {
    if (props.modelValue) {
        if (props.multiple) {
            selectedOptions.value = props.modelValue ?? [];
        } else {
            selected.value = props.modelValue ?? null;
        }
    }
})

const dropmenu = ref(false);
const dropdown = ref(null);

document.addEventListener('click', clickOutside);
onBeforeUnmount(() => {
    document.removeEventListener('click', clickOutside);
});

function clickOutside(e) {
    if (dropdown.value && !dropdown.value.contains(e.target)) {
        dropmenu.value = false;
    }
}

const emits = defineEmits(['update:modelValue']);


const selectOption = (option) => {
    if (props.multiple) {
        if (selectedOptions.value.includes(option)) {
            selectedOptions.value = selectedOptions.value.filter((item) => item !== option);
        } else {
            selectedOptions.value.push(option);
        }
        emits('update:modelValue', selectedOptions.value);
    } else {
        // check if the option is already selected
        if (selected.value === option) {
            selected.value = null;
            emits('update:modelValue', null);
        } else {
            selected.value = option;
            emits('update:modelValue', option);
        }
        
    }
    if (props.closeAfterSelect) {
        dropmenu.value = false;
    }
}

function isSelected(option) {
    if (props.multiple) {
        return selectedOptions?.value?.includes(option)
    }
    return selected.value === option;
}

const showError = computed(() => {
    if (props.required) {
        if (props.multiple) {
            return selectedOptions.value.length < 1;
        } else {
            return selected.value === "" || selected.value === null || selected.value === undefined;
        }
    }
    return false;
});


watchEffect(() => {
    // watch for changes in the modelValue
    if (props.multiple) {
        selectedOptions.value = props.modelValue ?? [];
    } else {
        selected.value = props.modelValue ?? null;
    }
});



const searchedData = computed(() => {
    return props.options.filter((option) => {
        return option[props.optionLabel].toLowerCase().includes(search.value.toLowerCase());
    });
});


const selectedDataFind = computed(() => {
    if (props.multiple) {
        return props.options.filter((option) => selectedOptions.value.includes(option[props.dataLabel]));
    }
    if (selected.value !== null && selected.value !== undefined && selected.value !== "") {

        for (let i = 0; i < props.options.length; i++) {
            if (props.options[i][props.dataLabel] === Number(selected.value)) {
                return props.options[i];
            }
        }

        return props.options.find((option) => option[props.dataLabel] === selected.value);
    }
    return null;
});


const selectSearchedData = () => {
    // if all the searched data is selected, then deselect all
    if (searchedData.value.length === selectedOptions.value.length) {
        selectedOptions.value = [];
    } else {
        selectedOptions.value = searchedData.value.map((option) => option[props.dataLabel]);
    }
    emits('update:modelValue', selectedOptions.value);
}

</script>
