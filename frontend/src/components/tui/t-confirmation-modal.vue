<template>
    <div
        v-if="isActive"
        class="relative z-10"
        aria-labelledby="modal-title"
        role="dialog"
        aria-modal="true"
    >
        <transition
            enter-active-class="transition duration-300 ease-out"
            enter-from-class="opacity-0"
            enter-to-class="opacity-100"
            leave-active-class="transition duration-200 ease-in"
            leave-from-class="opacity-100"
            leave-to-class="opacity-0"
        >
            <div class="fixed inset-0 backdrop-blur bg-black/90"></div>
        </transition>
        <div class="fixed inset-0 z-50 overflow-y-auto">
            <div class="flex items-end justify-center min-h-full p-4 text-center sm:items-center sm:p-0">
                <transition
                    enter-active-class="transition duration-300 ease-out"
                    enter-from-class="translate-y-4 opacity-0 sm:translate-y-0 sm:scale-95"
                    enter-to-class="translate-y-0 opacity-100 sm:scale-100"
                    leave-active-class="transition duration-200 ease-in"
                    leave-from-class="translate-y-0 opacity-100 sm:scale-100"
                    leave-to-class="translate-y-4 opacity-0 sm:translate-y-0 sm:scale-95"
                >
                    <div
                        class="relative overflow-hidden text-left transition-all transform rounded-lg shadow-xl sm:my-8 sm:w-full sm:max-w-xl"
                    >
                        <div class="px-4 pt-5 pb-4 bg-white dark:bg-gray-900 sm:p-6 sm:pb-4">
                            <div class="sm:flex sm:items-start">
                                <div
                                    class="flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto bg-red-100 rounded-full sm:mx-0 sm:h-10 sm:w-10"
                                >
                                    <svg
                                        class="w-6 h-6 text-red-600"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                        aria-hidden="true"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M12 10.5v3.75m-9.303 3.376C1.83 19.126 2.914 21 4.645 21h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 4.88c-.866-1.501-3.032-1.501-3.898 0L2.697 17.626zM12 17.25h.007v.008H12v-.008z"
                                        />
                                    </svg>
                                </div>
                                <div class="flex flex-col w-full gap-2 ml-3">
                                    <h3
                                        class="text-lg font-medium leading-6"
                                        id="modal-title"
                                    >
                                        <slot name="content" />
                                    </h3>
                                </div>
                            </div>
                        </div>
                        <div class="px-4 py-3 bg-gray-50 dark:bg-gray-800 sm:flex sm:justify-between sm:px-6">
                            <button
                                type="button"
                                @click="deleteItem"
                                class="inline-flex justify-center w-full px-4 py-2 font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 sm:ml-3 sm:w-auto sm:text-sm"
								:class="loading ? 'opacity-50 cursor-not-allowed' : ''"
								:disabled="loading"
                            >
                                {{ $t("general." + action) }}
                            </button>
                            <button
                                type="button"
                                @click="closeModal()"
                                class="inline-flex justify-center w-full px-4 py-2 mt-3 font-medium rounded-md shadow-sm hover:bg-gray-300 dark:hover:bg-gray-700 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                            >
                                {{ $t("general.cancel") }}
                            </button>
                        </div>
                    </div>
                </transition>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    isActive: {
        type: Boolean,
    },
    action: {
        type: String,
        default: "delete",
    },
    loading: {
        type: Boolean,
        default: false,
    },
});
const emits = defineEmits(["deleteData", "closeModal"]);

const closeModal = () => {
    emits("closeModal");
};
const deleteItem = () => {
    emits("deleteData");
};

const closefrom = () => {
    if (props.isActive) {
        closeModal();
    }
};
</script>
