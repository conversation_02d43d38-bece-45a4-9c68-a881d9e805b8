<template>
    <!-- Drag 'n Drop -->
    <div class="space-y-2">
        <label class="block  mb-2 text-sm font-medium text-gray-800 dark:text-neutral-200">
            {{ label }}
        </label>
        <div
            class="p-12 flex justify-center bg-white border border-dashed border-gray-300 rounded-xl dark:bg-neutral-800 dark:border-neutral-600">
            <div class="text-center">



                <div class="relative" v-if="!fileIsEmpty">

                    <img class="h-36 w-auto" :src="file" alt="Image Description">

                    <div class="absolute top-2 end-2 z-10">
                        <button type="button" @click="clearFile()"
                            class="size-7 inline-flex justify-center items-center gap-x-1.5 font-medium text-sm rounded-full border border-stone-200 bg-white text-stone-600 shadow-sm hover:bg-stone-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                            data-hs-remove-element="#dismiss-img1">
                            <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18" />
                                <path d="m6 6 12 12" />
                            </svg>
                        </button>
                    </div>
                </div>



                <svg class="w-16 text-gray-400 mx-auto dark:text-neutral-400" width="70" height="46" viewBox="0 0 70 46"
                    v-if="fileIsEmpty" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M6.05172 9.36853L17.2131 7.5083V41.3608L12.3018 42.3947C9.01306 43.0871 5.79705 40.9434 5.17081 37.6414L1.14319 16.4049C0.515988 13.0978 2.73148 9.92191 6.05172 9.36853Z"
                        fill="currentColor" stroke="currentColor" stroke-width="2"
                        class="fill-white stroke-gray-400 dark:fill-neutral-800 dark:stroke-neutral-500" />
                    <path
                        d="M63.9483 9.36853L52.7869 7.5083V41.3608L57.6982 42.3947C60.9869 43.0871 64.203 40.9434 64.8292 37.6414L68.8568 16.4049C69.484 13.0978 67.2685 9.92191 63.9483 9.36853Z"
                        fill="currentColor" stroke="currentColor" stroke-width="2"
                        class="fill-white stroke-gray-400 dark:fill-neutral-800 dark:stroke-neutral-500" />
                    <rect x="17.0656" y="1.62305" width="35.8689" height="42.7541" rx="5" fill="currentColor"
                        stroke="currentColor" stroke-width="2"
                        class="fill-white stroke-gray-400 dark:fill-neutral-800 dark:stroke-neutral-500" />
                    <path
                        d="M47.9344 44.3772H22.0655C19.3041 44.3772 17.0656 42.1386 17.0656 39.3772L17.0656 35.9161L29.4724 22.7682L38.9825 33.7121C39.7832 34.6335 41.2154 34.629 42.0102 33.7025L47.2456 27.5996L52.9344 33.7209V39.3772C52.9344 42.1386 50.6958 44.3772 47.9344 44.3772Z"
                        stroke="currentColor" stroke-width="2" class="stroke-gray-400 dark:stroke-neutral-500" />
                    <circle cx="39.5902" cy="14.9672" r="4.16393" stroke="currentColor" stroke-width="2"
                        class="stroke-gray-400 dark:stroke-neutral-500" />
                </svg>

                <div class="mt-4 flex flex-wrap justify-center text-sm leading-6 text-gray-600" v-if="fileIsEmpty">
                    <span class="pe-1 font-medium text-gray-800 dark:text-neutral-200">
                        Select a file
                    </span>
                    <label for="@@browseID"
                        class="relative cursor-pointer bg-white font-semibold text-blue-600 hover:text-blue-700 rounded-lg decoration-2 hover:underline focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2 dark:bg-neutral-800 dark:text-blue-500 dark:hover:text-blue-600">
                        <span>browse</span>
                        <input id="@@browseID" type="file" @change="onFileChange" class="sr-only">
                    </label>
                </div>




                <p class="mt-1 text-xs text-gray-400 dark:text-neutral-400" v-if="fileIsEmpty">
                    {{ accept }}
                </p>
            </div>
        </div>
    </div>
    <!-- End Drag 'n Drop -->
</template>

<script setup>
const props = defineProps({
    modelValue: String,
    label: String,
    accept: {
        type: String,
        default: 'image/*'
    }
})

const file = ref(null)
if (props.modelValue) {
    file.value = props.modelValue
}

const emits = defineEmits(['update:modelValue'])

const fileIsEmpty = computed(() => !file.value)

function onFileChange(e) {
    var files = e.target.files || e.dataTransfer.files;
    if (!files.length) return;

    var reader = new FileReader(e);
    reader.readAsDataURL(e.target.files[0]);
    reader.onload = () => {
        file.value = reader.result;
        emits('update:modelValue', reader.result)
    };
}

watch(() => props.modelValue, (value) => {
    file.value = value
})


function clearFile() {
    file.value = null
    emits('update:modelValue', null)
}

</script>
