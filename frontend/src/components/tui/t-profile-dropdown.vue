<template>
  <div class="relative" ref="dropdownElm">
    <button type="button" @click="menu = !menu"
      class="inline-flex shrink-0 items-center gap-x-3 text-start focus:outline-none hover:bg-white/10 px-2 py-1 rounded-md transition-colors">
      <div class="flex items-center justify-center w-8 h-8 text-sm font-semibold text-white rounded-full bg-white/10">
        {{
          authStore.getCurrentUser &&
            authStore.getCurrentUser.name &&
            authStore.getCurrentUser.last_name
            ? authStore.getCurrentUser.name.split(' ').length > 1
              ? authStore.getCurrentUser.name.split(' ')[0].charAt(0) +
              authStore.getCurrentUser.name.split(' ')[1].charAt(0)
              : authStore.getCurrentUser.name.charAt(0) +
              authStore.getCurrentUser.last_name.charAt(0)
        : '-'
        }}
      </div>

      <span class="hidden md:block text-white">
        {{ authStore.getCurrentUser.name }}
      </span>
    </button>

    <transition enter-active-class="transition ease-out duration-100" enter-from-class="transform opacity-0 scale-95"
      enter-to-class="transform opacity-100 scale-100" leave-active-class="transition ease-in duration-75"
      leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
      <div
        class="w-72 absolute top-10 right-0 z-20 bg-white rounded-xl shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)] dark:shadow-[0_10px_40px_10px_rgba(0,0,0,0.2)] dark:bg-neutral-900 block"
        role="menu" v-if="menu">

        <div class="p-1 border-b border-gray-200 dark:border-neutral-800">

          <router-link
            class="py-2 px-3 flex items-center gap-x-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
            :to="{ name: 'profile' }">

            <div class="grow">
              <span class="text-sm font-semibold text-gray-800 dark:text-neutral-300">
                {{ user }}
              </span>
              <p class="text-xs text-gray-500 dark:text-neutral-500">
                {{ authStore.getCurrentUser.email }}
              </p>
            </div>
          </router-link>

        </div>

        <div class="p-1 space-y-2">

          <router-link
            class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
            :to="{ name: 'profile-account' }">
            <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
              viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
              stroke-linejoin="round">
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            {{ $t("routes.profile-account") }}
          </router-link>


          <router-link
            class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
            :to="{ name: 'profile-security' }">


            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
              stroke="currentColor" class="shrink-0 size-4">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z" />
            </svg>

            {{ $t("routes.profile-security") }}
          </router-link>
        </div>

        <hr class="border-gray-200 dark:border-gray-800">

        <div class="p-1">

          <button type="button"
            class="flex w-full items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
            @click="signout()">

            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
              stroke="currentColor" class="shrink-0 mt-0.5 size-4">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9" />
            </svg>

            {{ $t('routes.signout') }}
          </button>
        </div>

      </div>
    </transition>
  </div>
</template>

<script setup>
import { useAuthStore } from "@/store/submodules/auth";
const store = useAuthStore();

const menu = ref(false);
const dropdownElm = ref(null);

const authStore = useAuthStore()

const user = computed(() => {
  return authStore.getCurrentUser.name + " " + store.getCurrentUser.last_name
});

function clickOutside(e) {
  if (dropdownElm.value && !dropdownElm.value.contains(e.target)) {
    menu.value = false;
  }
}

function signout() {
  store.SignOut()
}

document.addEventListener('click', clickOutside);

onBeforeUnmount(() => {
  document.removeEventListener('click', clickOutside);
});

</script>
