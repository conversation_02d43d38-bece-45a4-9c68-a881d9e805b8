

<script setup>

const props =defineProps({
    item: Object,
    columns: {
        type: Array,
        default: () => []
    },
    actions: {
        type: Array,
        default: () => []
    },
})

const themes = {
    blue: {
        provider: "bg-blue-600 hover:bg-blue-800",
        icon: "w-4 h-4 text-blue-200",
    },
    indigo: {
        provider: "bg-indigo-600 hover:bg-indigo-800",
        icon: "w-4 h-4 text-indigo-200",
    },
    rose: {
        provider: "bg-red-600 hover:bg-red-800",
        icon: "w-4 h-4 text-red-200",
    },
    green: {
        provider: "bg-green-600 hover:bg-green-800",
        icon: "w-4 h-4 text-green-200",
    },
    yellow: {
        provider: "bg-yellow-600 hover:bg-yellow-800",
        icon: "w-4 h-4 text-yellow-200",
    },
    emerald: {
        provider: "bg-emerald-600 hover:bg-emerald-800",
        icon: "w-4 h-4 text-emerald-200",
    },
    teal: {
        provider: "bg-teal-600 hover:bg-teal-800",
        icon: "w-4 h-4 text-teal-200",
    },
    red: {
        provider: "bg-red-600 hover:bg-red-800",
        icon: "w-4 h-4 text-red-200",
    },
    sky: {
        provider: "bg-sky-600 hover:bg-sky-800",
        icon: "w-4 h-4 text-sky-200",
    },
    gray: {
        provider: "bg-gray-600 hover:bg-gray-800",
        icon: "w-4 h-4 text-gray-200",
    },
}
</script>

<template>
     <div class="flex items-center gap-2"
          :class="columns[columns.length - 1].align === 'center' ? 'justify-center' : 'justify-start'">
          <div class="flex-row items-center flex-wrap" v-for="action in actions">
            <router-link v-if="action.route" :to="{ name: action.route, params: { id: item.id, ...(action.params ? action.params(item) : {}) }, query: action.queries ? action.queries(item) : {} }"
              class="px-2 py-1 text-xs text-white rounded-md flex items-center gap-2 transition-colors" :class="themes[action.color].provider">
              <t-icon :name="action.icon" v-if="action.icon" :class="themes[action.color].icon" />
              {{ action.label }}
            </router-link>
            <button v-else @click="action.function(item)"
              class="px-2 py-1 text-xs text-white rounded-md flex items-center gap-2 transition-colors" :class="themes[action.color].provider">

              <t-icon :name="action.icon" v-if="action.icon" :class="themes[action.color].icon" />
              {{ action.label }}
            </button>
          </div>
        </div>
</template>