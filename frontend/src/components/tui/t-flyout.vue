<template>
    <div
        class="border-t border-dashed border-gray-200 first:border-t-0 py-1 first:pt-0 last:pb-0 md:border-0 md:py-0 dark:border-neutral-700">
        <button type="button" @click="dropmenu = !dropmenu" ref="dropdown"
            class=" py-2 px-3 lg:px-2.5  w-full lg:w-auto flex items-center text-sm text-start lg:font-medium text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700/40 dark:focus:bg-neutral-700"
            :class="{ 'bg-gray-100 dark:bg-neutral-700': dropmenu }">
            {{ props.label }}
            <svg class="ms-auto md:ms-2 flex-shrink-0 size-4 text-gray-400 md:text-gray-600 dark:text-neutral-600 md:dark:text-neutral-400"
                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6" />
            </svg>
        </button>

        <div class="absolute w-full top-full start-0 lg:start-auto min-w-20 max-w-[45rem] bg-white dark:bg-gray-800 ring-1 ring-gray-700 rounded-lg"
            :class="[$slots.content ? 'py-2' : 'py-0', dropmenu ? 'block' : 'hidden', zIndexLevels[zIndex]]" v-if="dropmenu">

            <slot name="content" />

        </div>
    </div>
</template>

<script setup>
const zIndexLevels = {
    1: 'z-10',
    2: 'z-20',
    3: 'z-30',
    4: 'z-40',
    5: 'z-50',
}

const props = defineProps({
    label: {
        type: String,
        required: true,
    },
    zIndex: {
        type: Number,
        default: 1,
    },
})
const dropmenu = ref(false);
const dropdown = ref(null);

document.addEventListener('click', clickOutside);
onBeforeUnmount(() => {
    document.removeEventListener('click', clickOutside);
});

function clickOutside(e) {
    if (dropdown.value && !dropdown.value.contains(e.target)) {
        dropmenu.value = false;
    }
}

</script>
