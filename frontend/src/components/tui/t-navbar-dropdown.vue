<template>
    <div class="">
        <button type="button" @click="dropmenu = !dropmenu" ref="dropdown"
            class="py-2 px-3 lg:px-2.5 w-full lg:w-auto flex items-center text-sm text-start lg:font-medium text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700/40 dark:focus:bg-neutral-700">
            {{ props.label }}
            <svg class="ms-auto md:ms-2 flex-shrink-0 size-4 text-gray-400 md:text-gray-600 dark:text-neutral-600 md:dark:text-neutral-400"
                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6" />
            </svg>
        </button>
        <transition enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95">
            <div v-if="dropmenu"
                class="w-full py-2 z-50 top-full md:w-64 bg-white sm:p-2 dark:bg-gray-800 ring-1 ring-gray-700 rounded-lg md:absolute">
                <slot name="content" />
            </div>
        </transition>
    </div>
</template>

<script setup>
const props = defineProps({
    label: {
        type: String,
        required: true,
    },
});
const dropmenu = ref(false);
const dropdown = ref(null);

document.addEventListener("click", clickOutside);
onBeforeUnmount(() => {
    document.removeEventListener("click", clickOutside);
});

function clickOutside(e) {
    if (dropdown.value && !dropdown.value.contains(e.target)) {
        dropmenu.value = false;
    }
}
</script>
