<template>
    <v-chart class="chart" :option="option" autoresize />
</template>

<script setup>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';

import {
    TitleComponent,
    ToolboxComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import VChart, { THEME_KEY } from 'vue-echarts';


const props = defineProps({
    title: {
        type: String,
        default: ''
    },

    data: {
        type: Array,
        default: () => []
    },
    categories: {
        type: Array,
        default: () => []
    },
    theme: {
        type: String,
        default: 'light'
    },
});

use([
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    ToolboxComponent,
    LineChart,
    CanvasRenderer
]);


const option = ref(
    {
        title: {
            left: 20,
            text: props.title,
            textStyle: {
                color: props.theme === 'light' ? '#000' : '#fff'
            }
        },
        backgroundColor: 'transparent',
        textStyle: {
            color: "#B9B8CE",
            fontFamily: "sans-serif",
            fontSize: 12,
            fontStyle: "normal",
            fontWeight: "normal",
            //color: props.theme === 'light' ? '#000' : '#fff'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        animation: "auto",
        animationDuration: 1000,
        animationEasing: "cubicInOut",
        animationDurationUpdate: 1000,
        animationEasingUpdate: "cubicInOut",
        animationThreshold: 2000,
        progressiveThreshold: 3000,
        progressive: 400,
        hoverLayerThreshold: 3000,
        useUTC: true,
        xAxis: {
            type: 'category',
            data: props.categories,
            axisLine: {
                lineStyle: {
                    color: props.theme === 'light' ? '#000' : '#2e3131',
                }
            },
            axisLabel: {
                color: props.theme === 'light' ? '#000' : '#fff'
            },
            splitLine: {
                lineStyle: {
                    color: props.theme === 'light' ? '#000' : '#2e3131',
                }
            }
        },
        yAxis: {
            name: 'Revenue',
            type: 'value',
            axisLabel: {
                formatter: function (value) {
                    switch (true) {
                        case value < 1000:
                            return value;
                        case value < 1000000:
                            return value / 1000 + 'K';
                        case value < 1000000000:
                            return value / 1000000 + 'M';
                    }
                }
            },
            splitLine: {
                lineStyle: {
                    color: props.theme === 'light' ? '#000' : '#2e3131',
                }
            }
        },
        grid: {
            left: '0%',
            right: '0%',
            bottom: '0%',
            containLabel: true,
            backgroundColor: "rgba(0, 0, 0, 0)",
            borderWidth: 1,
            borderColor: "#ccc",
        },
        series: props.data,
        legend: {
            show: true,
            orient: 'horizontal',
            textStyle: {
                color: "#B9B8CE"
            },
            lineStyle: {
                color: "#14b8a6",
                width: 0
            }
        },
    }
);



// watch to change theme
watch(() => props.theme, (theme) => {
    option.value.title.textStyle.color = theme === 'light' ? '#000' : '#fff';
    option.value.textStyle.color = theme === 'light' ? '#000' : '#fff';
    option.value.xAxis.axisLine.lineStyle.color = theme === 'light' ? '#000' : '#2e3131';
    option.value.xAxis.axisLabel.color = theme === 'light' ? '#000' : '#fff';
    option.value.xAxis.splitLine.lineStyle.color = theme === 'light' ? '#000' : '#2e3131';
    option.value.yAxis.axisLabel.color = theme === 'light' ? '#000' : '#fff';
    option.value.yAxis.splitLine.lineStyle.color = theme === 'light' ? '#000' : '#2e3131';
    option.value.legend.textStyle.color = theme === 'light' ? '#000' : '#fff';
});
</script>


<style>
.chart {
    height: 400px;
}
</style>