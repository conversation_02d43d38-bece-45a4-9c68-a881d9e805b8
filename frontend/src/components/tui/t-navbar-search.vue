<template>
    <div class="relative lg:order-2 lg:min-w-[300px] hidden lg:block lg:ms-5">
        <div class="relative">
            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-20 ps-3.5">
                <svg class="flex-shrink-0 size-4 text-white/60" xmlns="http://www.w3.org/2000/svg" width="24"
                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="11" cy="11" r="8" />
                    <path d="m21 21-4.3-4.3" />
                </svg>
            </div>
            <input type="text" v-model="search"
                class="block w-full px-3 py-2 text-sm text-white border-transparent rounded-lg ps-10 pe-16 bg-white/10 focus:z-10 focus:border-white/10 focus:ring-white/10 placeholder:text-white/60 disabled:opacity-50 focus:outline-none focus:ring-1 dark:focus:ring-white/20"
                :placeholder="$t('general.search')" />


            <div class="absolute inset-y-0 z-20 flex items-center text-gray-400 pointer-events-none end-0 pe-3"
                v-if="false">
                <svg class="flex-shrink-0 size-3 text-white/60" xmlns="http://www.w3.org/2000/svg" width="24"
                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round">
                    <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
                </svg>
                <span class="mx-1">
                    <svg class="flex-shrink-0 size-3 text-white/60" xmlns="http://www.w3.org/2000/svg" width="24"
                        height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round">
                        <path d="M5 12h14" />
                        <path d="M12 5v14" />
                    </svg>
                </span>
                <span class="text-xs">/</span>
            </div>
        </div>
        <div class="absolute w-full z-50 top-full mt-2 bg-white sm:p-2 max-h-96 overflow-auto dark:bg-gray-800 ring-1 ring-gray-700 rounded-lg shadow-lg shadow-neutral-600 dark:shadow-black"
            v-if="searchedResults.length > 0">
            <slot v-for="item in searchedResults">

               <router-link :to="{ name: item.route }"
                    class="group p-3 flex gap-x-3.5 select-none text-gray-800 hover:bg-gray-100 rounded-lg dark:text-neutral-200 dark:hover:bg-neutral-500/10">
                    <div class="grow">
                        <p class="text-sm text-gray-800 dark:text-neutral-300">
                            {{ $t(`routes.${item.route}`) }}
                        </p>
                    </div>
                </router-link>

            </slot>
        </div>
    </div>
</template>

<script setup>
import { MenuWithGroupList } from "@/composables/menu";

const search = ref('')

function similarity(str1, str2) {
    str1 = str1.toLowerCase();
    str2 = str2.toLowerCase();
    const len = Math.max(str1.length, str2.length);
    let matches = 0;

    for (let i = 0; i < Math.min(str1.length, str2.length); i++) {
        if (str1[i] === str2[i]) matches++;
    }

    return matches / len; // Benzerlik oranı
}

const searchedResults = computed(() => {

    let data = MenuWithGroupList()
    let results = [];
    const threshold = 0.2;

    for (let item of data) {
        if (item.childrens) {
            for (let children of item.childrens) {
                if (children.childs) {
                    for (let child of children.childs) {
                        const similarityScore = similarity(child.name, search.value);
                        if (similarityScore >= threshold) {
                            results.push({ name: child.name, route: child.route, similarity: similarityScore });
                        }
                    }
                }
            }
        }
    }
    results.sort((a, b) => b.similarity - a.similarity);

    return results;
})
</script>
