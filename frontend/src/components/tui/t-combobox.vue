<template>
    <div class="space-y-2">

        <label for="combobox" class="flex gap-1 text-sm font-medium" v-if="props.label">
            <span>{{ props.label }}</span>
            <span class="text-red-500" v-if="props.required">*</span>
        </label>
        <div class="relative" ref="combobox">
            <input id="combobox" type="text" class="t-input" role="combobox" aria-controls="options" v-model="search"
                :required="props.required" @input="searchWithDebounce" autocomplete="off"
                :placeholder="props.placeholder" aria-expanded="false">


            <button type="button" @click="comboboxmenu = !comboboxmenu"
                class="absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    :class="{ 'transform rotate-180': comboboxmenu }" stroke="currentColor"
                    class="size-5 text-gray-400 transition">
                    <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
                </svg>
            </button>

            <transition enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95">

                <div v-if="comboboxmenu"
                    class="absolute top-full w-full start-0 mt-1 z-10 bg-white rounded-xl dark:bg-neutral-900 ring-2 ring-stone-200 dark:ring-neutral-700 max-h-72 overflow-auto">
                    <div class="p-1 space-y-1">

                        <div v-for="item in data" @click="selectOption(item[dataLabel])"
                            class="w-full flex gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 cursor-pointer dark:text-neutral-300 focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800 "
                            :class="[isSelected(item[dataLabel]) ? 'bg-gray-100 dark:bg-neutral-800' : '']">
                            <div class="flex w-full items-center justify-between">
                                <div class="">
                                    {{ item[optionLabel] }}
                                </div>
                                <span class="ms-auto" v-if="isSelected(item[dataLabel])">
                                    <svg class="flex-shrink-0 w-3.5 h-3.5 " xmlns="http:.w3.org/2000/svg" width="24"
                                        height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <polyline points="20 6 9 17 4 12"></polyline>
                                    </svg>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </transition>


        </div>
    </div>

</template>

<script setup>

const props = defineProps({
    label: {
        type: String,
        default: '',
    },
    placeholder: {
        type: String,
        default: '',
    },
    modelValue: {
        type: String,
        default: '',
    },
    searchFunction: {
        type: Function,
        default: () => { },
    },
    required: {
        type: Boolean,
        default: false,
    },
    data: {
        type: Array,
        default: () => [],
    },
    dataLabel: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'name',
    },
    searchValue: {
        type: String,
        default: '',
    },
});



const search = ref(props.searchValue);
const selected = ref(props.modelValue);
const emits = defineEmits(['update:modelValue']);
const comboboxmenu = ref(false);
const combobox = ref(null);




// after 400ms search will be called
setTimeout(() => {
    props.searchFunction([{ id: props.modelValue }]).then(() => {
        if (props.searchValue !== '') {
            let item = props.data.find((item) => item[props.optionLabel] === search.value);
            if (item) {
                search.value = item[props.optionLabel];
                comboboxmenu.value = false;
            }
        } else {

            let item = props.data.find((item) => item[props.dataLabel] === props.modelValue);
            if (item) {
                search.value = item[props.optionLabel];
                comboboxmenu.value = false;
            }
        }
    });
}, 400);



const selectOption = (data) => {
    emits('update:modelValue', data);
    // set search value to data option label
    let item = props.data.find((item) => item[props.dataLabel] === data);
    search.value = item[props.optionLabel];
    selected.value = data;
    comboboxmenu.value = false;
};

watch(() => props.data, () => {
    if (props.data.length > 0 && search.value !== '') {
        comboboxmenu.value = true;
    }
});


const isSelected = computed(() => {
    return (option) => selected.value === option;
});

function searchNow() {
    return props.searchFunction([{ name: search.value }]);
}


// i want to search when user break the typing 
// after 400ms search will be called 
// if user type again before 400ms then clear the timeout and set new timeoutü
const timer = ref(null);
function searchWithDebounce() {
    clearTimeout(timer.value);
    timer.value = setTimeout(() => {
        searchNow();
    }, 400);
}

</script>
