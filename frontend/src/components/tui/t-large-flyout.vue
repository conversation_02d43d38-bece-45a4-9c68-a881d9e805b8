<template>
    <div ref="dropdown" 
        class="[--strategy:static] md:[--strategy:absolute] [--adaptive:none] md:[--trigger:hover] border-t border-dashed border-gray-200 first:border-t-0 py-1 first:pt-0 last:pb-0 md:border-0 md:py-0 dark:border-neutral-700">
        <!-- Link Button -->
        <button type="button" @click="dropmenu = !dropmenu"
            class="hs-dropdown-toggle py-2 px-3 lg:px-2.5 w-full lg:w-auto flex items-center text-sm text-start lg:font-medium text-gray-500 lg:text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-500 lg:dark:text-neutral-200 dark:hover:bg-neutral-700/40 dark:focus:bg-neutral-700">
            Preline
            <svg class="ms-auto md:ms-2 flex-shrink-0 size-4 text-gray-400 md:text-gray-600 dark:text-neutral-600 md:dark:text-neutral-400"
                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m6 9 6 6 6-6" />
            </svg>
        </button>
        <!-- End Link Button -->

        <!-- Dropdown Menu -->
        <div v-if="dropmenu" 
            class="absolute w-full z-10 top-full start-0 min-w-60 bg-white md:py-4 lg:px-4 dark:bg-gray-800 ">
            <div class=" w-full mx-auto sm:px-4 2lg:px-8">
                <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-4">
                    <div class="flex flex-col">
                        <!-- Link -->
                        <a class="group p-3 flex gap-x-3.5 text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-500/10 dark:focus:bg-neutral-700"
                            href="#">
                            <svg class="flex-shrink-0 size-5 mt-1" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
                                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
                            </svg>
                            <div class="grow">
                                <p class="text-[15px] font-medium text-gray-800 dark:text-neutral-300">
                                    Support Docs
                                </p>
                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
                                    Explore advice and explanations for all of Preline's
                                    features.
                                </p>
                            </div>
                        </a>
                        <!-- End Link -->

                        <!-- Link -->
                        <a class="group p-3 flex gap-x-3.5 text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-500/10 dark:focus:bg-neutral-700"
                            href="#">
                            <svg class="flex-shrink-0 size-5 mt-1" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <rect width="7" height="7" x="14" y="3" rx="1" />
                                <path
                                    d="M10 21V8a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1H3" />
                            </svg>
                            <div class="grow">
                                <p class="text-[15px] font-medium text-gray-800 dark:text-neutral-300">
                                    Integrations
                                </p>
                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
                                    Discover the huge range of tools that Preline integrates
                                    with.
                                </p>
                            </div>
                        </a>
                        <!-- End Link -->

                        <!-- Link -->
                        <a class="group p-3 flex gap-x-3.5 text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-500/10 dark:focus:bg-neutral-700"
                            href="#">
                            <svg class="flex-shrink-0 size-5 mt-1" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="m7 11 2-2-2-2" />
                                <path d="M11 13h4" />
                                <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                            </svg>
                            <div class="grow">
                                <p class="text-[15px] font-medium text-gray-800 dark:text-neutral-300">
                                    API Reference
                                </p>
                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
                                    Build custom integrations with our first-class API.
                                </p>
                            </div>
                        </a>
                        <!-- End Link -->
                    </div>
                    <!-- End Col -->

                    <div class="flex flex-col">
                        <!-- Link -->
                        <a class="group p-3 flex gap-x-3.5 text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-500/10 dark:focus:bg-neutral-700"
                            href="#">
                            <svg class="flex-shrink-0 size-5 mt-1" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10" />
                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
                                <path d="M12 17h.01" />
                            </svg>
                            <div class="grow">
                                <p class="text-[15px] font-medium text-gray-800 dark:text-neutral-300">
                                    Help Center
                                </p>
                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
                                    Learn how to install, set up, and use Preline.
                                </p>
                            </div>
                        </a>
                        <!-- End Link -->

                        <!-- Link -->
                        <a class="group p-3 flex gap-x-3.5 text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-500/10 dark:focus:bg-neutral-700"
                            href="#">
                            <svg class="flex-shrink-0 size-5 mt-1" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="4" />
                                <path d="M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8" />
                            </svg>
                            <div class="grow">
                                <p class="text-[15px] font-medium text-gray-800 dark:text-neutral-300">
                                    Developer Hub
                                </p>
                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
                                    Learn how to integrate or build on top of Preline.
                                </p>
                            </div>
                        </a>
                        <!-- End Link -->

                        <!-- Link -->
                        <a class="group p-3 flex gap-x-3.5 text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-500/10 dark:focus:bg-neutral-700"
                            href="#">
                            <svg class="flex-shrink-0 size-5 mt-1" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                                <circle cx="9" cy="7" r="4" />
                                <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                                <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                            </svg>
                            <div class="grow">
                                <p class="text-[15px] font-medium text-gray-800 dark:text-neutral-300">
                                    Community Forum
                                </p>
                                <p class="mt-1 text-sm text-gray-500 dark:text-neutral-400">
                                    Learn, share, and connect with other Preline users.
                                </p>
                            </div>
                        </a>
                        <!-- End Link -->
                    </div>
                    <!-- End Col -->

                    <div class="sm:col-span-2 lg:col-span-1 flex flex-col mt-3 lg:mt-0">
                        <span class="px-3 text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-white">
                            Customer stories
                        </span>

                        <!-- Link -->
                        <a class="group mt-2 p-3 flex gap-x-5 items-center rounded-xl hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-500/10 dark:focus:bg-neutral-700"
                            href="#">
                            <img class="size-32 rounded-lg"
                                src="https://images.unsplash.com/photo-1648737967328-690548aec14f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=320&h=320&q=80"
                                alt="Image Description">
                            <div class="grow">
                                <p class="text-sm text-gray-800 dark:text-neutral-400">
                                    Preline Projects has proved to be most efficient cloud
                                    based project tracking and bug tracking tool.
                                </p>
                                <p
                                    class="mt-3 inline-flex items-center gap-x-1 text-sm text-blue-600 decoration-2 hover:underline font-medium dark:text-blue-400 dark:hover:text-blue-500">
                                    Learn more
                                    <svg class="flex-shrink-0 size-4 transition ease-in-out group-hover:translate-x-1"
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round">
                                        <path d="m9 18 6-6-6-6" />
                                    </svg>
                                </p>
                            </div>
                        </a>
                        <!-- End Link -->
                    </div>
                    <!-- End Col -->
                </div>
            </div>
        </div>
        <!-- End Dropdown Menu -->
    </div>
</template>

<script setup>

const dropmenu = ref(false);
const dropdown = ref(null);

document.addEventListener('click', clickOutside);
onBeforeUnmount(() => {
    document.removeEventListener('click', clickOutside);
});

function clickOutside(e) {
    if (dropdown.value && !dropdown.value.contains(e.target)) {
        dropmenu.value = false;
    }
}

</script>
