<template>
    <component :is="dynamicComponent" :class="props.class" />
</template>
<script setup>
import { computed, defineAsyncComponent } from 'vue';
const props = defineProps({
    name: {
        type: String,
        required: true,
    },
    class: {
        type: String,
        default: '',
    },
});
const dynamicComponent = computed(() => {
    const name = props.name;
    return defineAsyncComponent(() => import(`../icons/${name}.vue`));
});
</script>