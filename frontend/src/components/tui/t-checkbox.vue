<template>
    <div class="flex items-center justify-center w-full">
        <input :id="id || label" type="checkbox" :checked="inputValue" @change="inputValue = $event.target.checked"
            class="cursor-pointer border bg-kervan-lightDark focus:outline-none focus:ring-0 focus:ring-transparent focus:ring-offset-0 rounded"
            :class="[variants[variant], sizes[size]]">
        <label v-if="label" :for="id || label" class="ml-2 block cursor-pointer w-full text-sm select-none">
            {{ label }}
        </label>
    </div>
</template>

<script setup>

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    id: {
        type: String,
        default: ''
    },
    label: {
        type: String,
    },
    variant: {
        type: String,
        default: 'rose'
    },
    size: {
        type: String,
        default: 'sm'
    }
})

const emits = defineEmits(['update:modelValue']);

const inputValue = computed({
    get: () => props.modelValue,
    set: (value) => emits("update:modelValue", value)
})

const variants = {
    rose: 'text-rose-600',
    blue: 'text-blue-600',
    green: 'text-green-600',
    red: 'text-red-600',
    yellow: 'text-yellow-600',
    purple: 'text-purple-600',
    indigo: 'text-indigo-600',
    pink: 'text-pink-600',
    cyan: 'text-cyan-600',
    teal: 'text-teal-600',
    gray: 'text-gray-600',
    orange: 'text-orange-600',
    amber: 'text-amber-600',
    lime: 'text-lime-600',
    emerald: 'text-emerald-600',
    lightBlue: 'text-lightBlue-600',
    violet: 'text-violet-600',
    fuchsia: 'text-fuchsia-600',
    cyan: 'text-cyan-600',
}

const sizes = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6",
    xl: "h-7 w-7",
}
</script>