<template>
  <div :class="themes[props.type]">
    <slot>
      {{ props.value }}
    </slot>
  </div>
</template>

<script setup>
const props = defineProps({
  value: {
    default: '',
  },
  type: {
    type: String,
    default: 'default',
  },
});


const themes = {
  default: "py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-gray-100 text-gray-800 dark:bg-gray-500/10 dark:text-gray-500",
  teal: "py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-teal-100 text-teal-800 dark:bg-teal-500/10 dark:text-teal-500",
  green: "py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-500/10 dark:text-green-500",
  red: "py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-500/10 dark:text-red-500",
  yellow: "py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-500/10 dark:text-yellow-500",
  rose: "py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-rose-100 text-rose-800 dark:bg-rose-500/10 dark:text-rose-500",
  sky: "py-[5px] px-1.5 inline-flex items-center gap-x-1 text-xs rounded-full bg-sky-100 text-sky-800 dark:bg-sky-500/10 dark:text-sky-500",
}

</script>

