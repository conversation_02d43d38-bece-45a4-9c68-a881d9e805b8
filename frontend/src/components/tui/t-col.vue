<template>
    <th scope="col" v-show="column.visible">
        <div class="relative inline-flex w-full cursor-pointer">
            <button type="button" ref="dropdown" @click="dropmenu = !dropmenu"
            :class="{
                'justify-center': column.align === 'center',
                'justify-end': column.align === 'right',
                'justify-start': column.align === 'left',
                
            }"
                class="px-5 py-2.5 text-start w-full flex items-center gap-x-1 text-sm font-normal text-stone-500 focus:outline-none focus:bg-stone-100 dark:text-neutral-500 dark:focus:bg-neutral-700">
                {{ te("table.table_items."+column.label.toLowerCase().replace(/\s+/g, "_")) ? t("table.table_items."+column.label.toLowerCase().replace(/\s+/g, "_")) : column.label }}
                <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="m7 15 5 5 5-5" />
                    <path d="m7 9 5-5 5 5" />
                </svg>
            </button>


            <div class="origin-top-right right-0 mt-9 absolute w-40 z-10 bg-white rounded-xl dark:bg-neutral-900" v-if="dropmenu">
                <div class="p-1">
                    <button type="button" @click="AscByColumn(column.row)" v-if="column.sortable"
                        class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-lg text-[13px] font-normal text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-300 focus:outline-none focus:bg-stone-100 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                        <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="m5 12 7-7 7 7" />
                            <path d="M12 19V5" />
                        </svg>
                        {{ t('table.sort_ascending') }}
                    </button>
                    <button type="button" @click="DescByColumn(column.row)"  v-if="column.sortable"
                        class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-lg text-[13px] font-normal text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-300 focus:outline-none focus:bg-stone-100 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                        <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12 5v14" />
                            <path d="m19 12-7 7-7-7" />
                        </svg>
                        {{ t('table.sort_descending') }}
                    </button>
                    <button type="button" @click="MoveColumnLeft(column.row)"
                        class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-lg text-[13px] font-normal text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-300 focus:outline-none focus:bg-stone-100 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                        <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="m12 19-7-7 7-7" />
                            <path d="M19 12H5" />
                        </svg>
                        {{ t('table.move_left') }}
                    </button>
                    <button type="button" @click="MoveColumnRight(column.row)"
                        class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-lg text-[13px] font-normal text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-300 focus:outline-none focus:bg-stone-100 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                        <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="M5 12h14" />
                            <path d="m12 5 7 7-7 7" />
                        </svg>
                        {{ t('table.move_right') }}
                    </button>

                    <div class="my-1 border-t border-stone-200 dark:border-neutral-800">
                    </div>

                    <button type="button" @click="HideColumn(column.row)"
                        class="w-full flex items-center gap-x-3 py-1.5 px-2 rounded-lg text-[13px] font-normal text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-300 focus:outline-none focus:bg-stone-100 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                        <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="M9.88 9.88a3 3 0 1 0 4.24 4.24" />
                            <path d="M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68" />
                            <path d="M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61" />
                            <line x1="2" x2="22" y1="2" y2="22" />
                        </svg>
                        {{ t('table.hide_in_view') }}
                    </button>
                </div>
            </div>
        </div>
    </th>
</template>

<script setup>
import { te, t } from "@/plugins/i18n";
const props = defineProps({
    column: Object,
});

const dropmenu = ref(false);
const dropdown = ref(null);

document.addEventListener('click', clickOutside);
onBeforeUnmount(() => {
    document.removeEventListener('click', clickOutside);
});

function clickOutside(e) {
    if (dropdown.value && !dropdown.value.contains(e.target)) {
        dropmenu.value = false;
    }
}

const emits = defineEmits(['asc-by-column', 'desc-by-column', 'move-column-left', 'move-column-right', 'hide-column']);


function AscByColumn(column) {
    emits('asc-by-column', column);
}

function DescByColumn(column) {
    emits('desc-by-column', column);
}

function MoveColumnLeft(column) {
    emits('move-column-left', column);
}

function MoveColumnRight(column) {
    emits('move-column-right', column);
}

function HideColumn(column) {
    emits('hide-column', column);
}

</script>
