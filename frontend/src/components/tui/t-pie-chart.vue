<template>

    <v-chart class="pie-chart" :option="option" autoresize />

</template>

<script setup>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import {
    TitleComponent,
    ToolboxComponent,
    TooltipComponent,
    GridComponent,
    LegendComponent
} from 'echarts/components';

import { PieChart } from 'echarts/charts';
import VChart from 'vue-echarts';

use([
    TitleComponent,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    ToolboxComponent,
    PieC<PERSON>,
    CanvasRenderer
]);

const props = defineProps({
    data: {
        type: Array,
        default: () => []
    },
    title: {
        type: String,
        default: ''
    },
    theme: {
        type: String,
        default: 'light'
    },
});

const option = computed(() => ({
    tooltip: {
        trigger: 'item',
    },
    legend: {
        top: '5%',
        left: 'center',
        textStyle: {
            color: props.theme === 'light' ? '#000' : '#fff'
        }
    },
    series: [
        {
            name: props.title,
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '70%'],
            label: {
                fontWeight: 'bold',
                color: props.theme === 'light' ? '#000' : '#fff',
                position: 'inside',
                overflow: 'none',
                formatter: (params) => {
                    const value = params.value;
                    if (value >= 1000000000) {
                        return (value / 1000000000).toFixed(1) + 'b';
                    } else if (value >= 1000000) {
                        return (value / 1000000).toFixed(1) + 'm';
                    } else if (value >= 10000) {
                        return (value / 1000).toFixed(1) + 'k';
                    } else {
                        return value;
                    }
                }
            },
            emphasis: {
                label: {
                    show: true,
                }
            },
            labelLine: {
                show: true
            },
            startAngle: 180,
            endAngle: 360,
            data: props.data ?? []
        }
    ]
}));

watch(() => props.theme, (theme) => {
    option.value.series[0].label.color = theme === 'light' ? '#000' : '#fff';
    option.value.legend.textStyle.color = theme === 'light' ? '#000' : '#fff';
});



</script>

<style>
.pie-chart {
    height: 400px;
}
</style>
