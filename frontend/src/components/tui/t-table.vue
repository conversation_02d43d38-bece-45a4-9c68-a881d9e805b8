<template>

    <div class="flex flex-col space-y-4 bg-white rounded-xl dark:bg-neutral-800" :class="{ 'p-5': !noPadding }"
        v-if="!loading">
        <div class="flex items-center justify-between mb-2" v-if="props.title">
            <h1>{{ props.title }}</h1>
        </div>
        <div class="w-full flex items-center">
            <div v-if="props.inlinesearch" class="relative">
                <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none z-10 ps-3.5">
                    <svg class="flex-shrink-0 size-4 text-stone-500 dark:text-neutral-400"
                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8" />
                        <path d="m21 21-4.3-4.3" />
                    </svg>
                </div>
                <input type="text" v-model="search" class="t-table-search-input !w-10 sm:!w-auto focus:!w-full"
                    :placeholder="t('table.search')">
            </div>
            <!-- applied badges -->
            <div class="hidden md:flex ml-4 grow-0" v-if="props.showAppliedFilters && filters.length > 0">
                <span v-if="filters.filter((f) => f.value !== null && f.value !== '').length > 0"
                    class="font-semibold text-sm pr-4">{{ t('table.applied_filters') }}:</span>
                <template v-for="filter in filters.filter((f) => f.value !== null && f.value !== '')">
                    <span
                        class="flex gap-2 items-center cursor-pointer border border-stone-200 dark:border-neutral-700 rounded-full px-2 py-1 text-xs text-stone-500 dark:text-neutral-300 hover:bg-stone-100 dark:hover:bg-neutral-700"
                        @click="removeFilter(filter.dataName)">
                        <span>
                            {{ filter.name }}:
                        </span>
                        <span class="font-medium">{{ te('table.filters.'+filter.dataName+'.'+filter.value) ? t('table.filters.'+filter.dataName+'.'+filter.value) : filter.value }}</span>
                        <svg class="flex-shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round">
                            <path d="M18 6 6 18" />
                            <path d="m6 6 12 12" />
                        </svg>
                    </span>
                </template>
            </div>
            <slot name="head" />
            <div class="flex shrink-0 items-center md:justify-end gap-x-2 ml-auto">
                <slot name="prefix-actions" />

                <router-link :to="{ name: props.createRoute, query: props.createQuery }" v-if="props.createRoute"
                    class="py-2 px-2.5 inline-flex items-center gap-x-1.5 text-xs rounded-lg border border-gray-200 bg-white text-gray-800  hover:bg-gray-50 focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">

                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="flex-shrink-0 w-4 h-4 transition ms-auto lg:ms-0">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                    </svg>

                    {{ $t('general.create') }}
                </router-link>

                <t-dropdown placement="top-right" :label="t('table.table_cols')">
                    <template #icon>
                        <svg class="size-3.5 text-gray-800 dark:text-white" aria-hidden="true"
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 5v14M9 5v14M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z" />
                        </svg>
                    </template>
                    <template #menu>
                        <span class="block mt-3 mb-1 text-xs text-gray-500 ps-1 dark:text-neutral-500">
                            {{ t('table.table_cols') }}
                        </span>

                        <div v-for="col in selectedColumns" :key="col.row" @click="col.visible = !col.visible"
                            class="flex items-center px-3 py-2 rounded-lg cursor-pointer gap-x-3 hover:bg-gray-100 dark:hover:bg-neutral-800">
                            <input type="checkbox" v-model="col.visible"
                                class="shrink-0 border-gray-200 size-3.5 rounded text-blue-600 focus:ring-offset-0 dark:bg-neutral-800 dark:border-neutral-700">
                            <label
                                class="flex items-center flex-1 text-sm text-gray-800 cursor-pointer select-none gap-x-3 dark:text-neutral-300">
                                {{ te("table.table_items." + col.label.toLowerCase().replace(/\s+/g, "_")) ?
                                    t("table.table_items." + col.label.toLowerCase().replace(/\s+/g, "_")) : col.label }}
                            </label>
                        </div>
                    </template>
                </t-dropdown>

                <t-table-filter @filter="applyFilters" :filters="filters" v-if="filters.length > 0"
                    :filter-type="props.filterType" />
            </div>
        </div>
        <div v-if="!error">
            <div v-if="dataCount > 0">

                <div class="overflow-y-auto">
                    <div class="inline-block min-w-full align-middle">
                        <table class="min-w-full divide-y divide-stone-200 dark:divide-neutral-700">
                            <thead>
                                <tr class="border-t border-stone-200 dark:border-neutral-700">
                                    <t-col v-for="(column, index) in selectedColumns" :key="index"
                                        v-show="column.visible" :column="column" @asc-by-column="AscByColumn"
                                        @desc-by-column="DescByColumn" @move-column-left="MoveColumnLeft"
                                        @move-column-right="MoveColumnRight" @hide-column="HideColumn">
                                    </t-col>
                                </tr>
                            </thead>

                            <tbody class="divide-y divide-stone-200 dark:divide-neutral-700">
                                <tr v-for="item in searchedData" :key="item"
                                    class="hover:bg-stone-100 dark:hover:bg-neutral-700">
                                    <td class="w-px h-px px-4 py-1 break-words whitespace-pre-line max-w-56" :class="[
                                        col.align === 'left' ? 'text-left' : '',
                                        col.align === 'center' ? 'text-center' : '',
                                        col.align === 'right' ? 'text-right' : '',
                                    ]" v-for="(col, index) in selectedColumns" :key="index" v-show="col.visible">
                                        <slot class="text-sm dark:text-neutral-400" :item="item"
                                            :name="`row-${col.row}`" v-if="$slots[`row-${col.row}`]" />
                                        <span class="text-xs dark:text-neutral-400" :title="item[col.row]" v-else>
                                            {{ col.modifier ? (col.alldata ? col.modifier(item) :
                                                col.modifier(item[col.row])) : item[col.row] ?
                                                te(props.entityLocaleName + "." + col.label.toLowerCase().replace(/\s+/g,
                                                    "_") + "." + item[col.row].toString().toLowerCase()) ?
                                                    t(props.entityLocaleName + "." + col.label.toLowerCase().replace(/\s+/g,
                                                        "_") + "." + item[col.row].toString().toLowerCase()) : item[col.row] :
                                                item[col.row] }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </div>
                </div>

                <div class="flex flex-wrap items-center justify-between gap-2 mt-5 select-none">
                    <div class="inline-flex items-center gap-2">
                        <t-select class="max-w-20" :options="perPageOptions" v-model="perpage" option-label="label" close-after-select="true" />
                        <p class="space-x-2 text-sm text-stone-800 dark:text-neutral-200">
                            <span class="font-medium">
                                {{ totalCount ?? props.total }}
                            </span>
                            <span class="text-stone-500 dark:text-neutral-500">{{ t('table.results') }}</span>
                        </p>
                    </div>


                    <nav class="flex items-center justify-end gap-x-1">
                        <button type="button" @click="setPage(Number(1))" :disabled="page === 1"
                            :class="{ 'cursor-not-allowed': page === 1 }"
                            class="min-h-[38px] min-w-[38px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700">

                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                                stroke="currentColor" class="flex-shrink-0 size-3.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="m18.75 4.5-7.5 7.5 7.5 7.5m-6-15L5.25 12l7.5 7.5" />
                            </svg>

                            <span aria-hidden="true" class="sr-only">first</span>
                        </button>
                        <button type="button" @click="setPage(Number(page) - 1)" :disabled="page === 1"
                            :class="{ 'cursor-not-allowed': page === 1 }"
                            class="min-h-[38px] min-w-[38px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700">
                            <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="m15 18-6-6 6-6" />
                            </svg>
                            <span aria-hidden="true" class="sr-only">Previous</span>
                        </button>
                        <div class="flex items-center gap-x-1">
                            <span
                                class="min-h-[38px] min-w-[38px] flex justify-center items-center bg-stone-100 text-stone-800 py-2 px-3 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:text-white">
                                {{ props.page }}
                            </span>
                            <span
                                class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">of</span>
                            <span
                                class="min-h-[38px] flex justify-center items-center text-stone-500 py-2 px-1.5 text-sm dark:text-neutral-500">
                                {{ props.total_pages }}
                            </span>
                        </div>
                        <button type="button" @click="setPage(Number(page) + 1)" :disabled="page === total_pages"
                            :class="{ 'cursor-not-allowed': page === total_pages }"
                            class="min-h-[38px] min-w-[38px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700">
                            <span aria-hidden="true" class="sr-only">Next</span>
                            <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24"
                                height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="m9 18 6-6-6-6" />
                            </svg>
                        </button>
                        <button type="button" @click="setPage(Number(total_pages))" :disabled="page === total_pages"
                            :class="{ 'cursor-not-allowed': page === total_pages }"
                            class="min-h-[38px] min-w-[38px] py-2 px-2.5 inline-flex justify-center items-center gap-x-2 text-sm rounded-lg text-stone-800 hover:bg-stone-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-100 dark:text-white dark:hover:bg-white/10 dark:focus:bg-neutral-700">
                            <span aria-hidden="true" class="sr-only">Next</span>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                                stroke="currentColor" class="flex-shrink-0 size-3.5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="m5.25 4.5 7.5 7.5-7.5 7.5m6-15 7.5 7.5-7.5 7.5" />
                            </svg>
                        </button>
                    </nav>

                </div>

            </div>

            <div v-else>
                <div class="flex flex-col items-center justify-center p-5 text-center">
                    <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor"
                            class="fill-white dark:fill-neutral-800" />
                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor"
                            class="stroke-stone-50 dark:stroke-neutral-700/10" />
                        <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor"
                            class="fill-stone-50 dark:fill-neutral-700/30" />
                        <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor"
                            class="fill-stone-50 dark:fill-neutral-700/30" />
                        <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor"
                            class="fill-stone-50 dark:fill-neutral-700/30" />
                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor"
                            class="fill-white dark:fill-neutral-800" />
                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor"
                            class="stroke-stone-100 dark:stroke-neutral-700/30" />
                        <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor"
                            class="fill-stone-100 dark:fill-neutral-700/70" />
                        <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor"
                            class="fill-stone-100 dark:fill-neutral-700/70" />
                        <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor"
                            class="fill-stone-100 dark:fill-neutral-700/70" />
                        <g filter="url(#filter7)">
                            <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor"
                                class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                            <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor"
                                class="stroke-stone-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                            <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor"
                                class="fill-stone-200 dark:fill-neutral-700 " />
                            <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor"
                                class="fill-stone-200 dark:fill-neutral-700" />
                            <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor"
                                class="fill-stone-200 dark:fill-neutral-700" />
                        </g>
                        <defs>
                            <filter id="filter7" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse"
                                color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                <feColorMatrix in="SourceAlpha" type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                <feOffset dy="6" />
                                <feGaussianBlur stdDeviation="6" />
                                <feComposite in2="hardAlpha" operator="out" />
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                                <feBlend mode="normal" in2="BackgroundImageFix"
                                    result="effect1_dropShadow_1187_14810" />
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810"
                                    result="shape" />
                            </filter>
                        </defs>
                    </svg>

                    <div class="max-w-sm mx-auto">
                        <p class="mt-2 font-medium text-stone-800 dark:text-neutral-200">
                            {{ $t('general.no_data_description') }}
                        </p>
                        <p class="mb-5 text-sm text-stone-500 dark:text-neutral-500">
                            {{ $t("general.you_have_not_created_any") }}
                        </p>
                    </div>

                </div>
            </div>
        </div>

        <div class="flex flex-col items-center justify-center p-5 text-center" v-if="error">
            <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor"
                    class="fill-white dark:fill-neutral-800"></rect>
                <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor"
                    class="stroke-stone-50 dark:stroke-neutral-700/10"></rect>
                <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor"
                    class="fill-stone-50 dark:fill-neutral-700/30"></rect>
                <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor"
                    class="fill-stone-50 dark:fill-neutral-700/30"></rect>
                <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor"
                    class="fill-stone-50 dark:fill-neutral-700/30"></rect>
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor"
                    class="fill-white dark:fill-neutral-800"></rect>
                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor"
                    class="stroke-stone-100 dark:stroke-neutral-700/30"></rect>
                <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor"
                    class="fill-stone-100 dark:fill-neutral-700/70"></rect>
                <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor"
                    class="fill-stone-100 dark:fill-neutral-700/70"></rect>
                <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor"
                    class="fill-stone-100 dark:fill-neutral-700/70"></rect>
                <g filter="url(#filter1)">
                    <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor"
                        class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"></rect>
                    <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor"
                        class="stroke-stone-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"></rect>
                    <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor"
                        class="fill-stone-200 dark:fill-neutral-700 "></rect>
                    <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor"
                        class="fill-stone-200 dark:fill-neutral-700"></rect>
                    <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor"
                        class="fill-stone-200 dark:fill-neutral-700"></rect>
                </g>
                <defs>
                    <filter id="filter1" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse"
                        color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"></feColorMatrix>
                        <feOffset dy="6"></feOffset>
                        <feGaussianBlur stdDeviation="6"></feGaussianBlur>
                        <feComposite in2="hardAlpha" operator="out"></feComposite>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0">
                        </feColorMatrix>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810">
                        </feBlend>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape">
                        </feBlend>
                    </filter>
                </defs>
            </svg>

            <div class="max-w-sm mx-auto">
                <p class="mt-2 font-medium text-stone-800 dark:text-neutral-200">
                    {{ $t('general.something_went_wrong') }}
                </p>
                <p class="mb-5 text-sm text-stone-500 dark:text-neutral-500">
                    {{ $t('general.try_again') }}
                </p>
            </div>

        </div>
    </div>

    <div class="flex flex-col p-5 space-y-4 bg-white rounded-xl dark:bg-neutral-800" v-if="loading && !error">
        <div class="flex flex-col items-center justify-center p-5 text-center">
            <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"
                class="w-32 h-32 text-indigo-700 fill-current dark:text-white">
                <circle cx="12" cy="12" r="0">
                    <animate id="spinner_kIfO" begin="0;spinner_xBIM.end" attributeName="r" calcMode="spline" dur="1.2s"
                        values="0;11" keySplines=".52,.6,.25,.99" fill="freeze" />
                    <animate begin="0;spinner_xBIM.end" attributeName="opacity" calcMode="spline" dur="1.2s"
                        values="1;0" keySplines=".52,.6,.25,.99" fill="freeze" />
                </circle>
                <circle cx="12" cy="12" r="0">
                    <animate id="spinner_Pbsh" begin="spinner_kIfO.begin+0.2s" attributeName="r" calcMode="spline"
                        dur="1.2s" values="0;11" keySplines=".52,.6,.25,.99" fill="freeze" />
                    <animate begin="spinner_kIfO.begin+0.2s" attributeName="opacity" calcMode="spline" dur="1.2s"
                        values="1;0" keySplines=".52,.6,.25,.99" fill="freeze" />
                </circle>
                <circle cx="12" cy="12" r="0">
                    <animate id="spinner_xBIM" begin="spinner_kIfO.begin+0.4s" attributeName="r" calcMode="spline"
                        dur="1.2s" values="0;11" keySplines=".52,.6,.25,.99" fill="freeze" />
                    <animate begin="spinner_kIfO.begin+0.4s" attributeName="opacity" calcMode="spline" dur="1.2s"
                        values="1;0" keySplines=".52,.6,.25,.99" fill="freeze" />
                </circle>
            </svg>

            <div class="max-w-sm mx-auto">
                <p class="mt-2 font-medium text-stone-800 dark:text-neutral-200">
                    {{ $t('general.loading') }}
                </p>
                <p class="mb-5 text-sm text-stone-500 dark:text-neutral-500">
                    {{ $t('general.please_wait_while_load') }}
                </p>
            </div>

        </div>
    </div>
</template>

<script setup>
import { te, t } from "@/plugins/i18n";
const props = defineProps({
    data: {
        type: Array,
        default: () => [],
        required: true,
    },
    columns: {
        type: Array,
        default: () => [],
        required: true,
    },
    loading: {
        type: Boolean,
        default: false,
        required: false,
    },
    error: {
        type: String,
        default: "",
        required: false,
    },
    entityLocaleName: {
        type: String,
        default: "",
        required: false,
    },
    page: {
        type: Number,
        default: 1,
    },
    per_page: {
        type: Number,
        default: 10,
    },
    total_pages: {
        type: Number,
    },
    total: {
        type: Number,
    },
    inlinesearch: {
        type: Boolean,
        default: false,
    },
    filters: {
        type: Array,
        default: () => [],
    },
    createRoute: {
        type: String,
        default: "",
    },
    createQuery: {
        type: Object,
        default: () => ({}),
    },
    filterType: {
        type: String,
        default: "drawer",
    },
    noPadding: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: "",
    },
    showAppliedFilters: {
        type: Boolean,
        default: false,
    },
})

const search = ref("");
const sort = reactive({
    order: "asc",
    column: "",
});

const emits = defineEmits(["page-change", "per-page-change", "filter-change"]);

const dataCount = computed(() => {
    return props.data ? props.data.length : 0;
});

function setPage(page) {
    emits("page-change", page);
}

function setPerPage(perPage) {
    emits("per-page-change", perPage);
}

const totalCount = ref(0);

function totalCountChanged(e) {
    totalCount.value = e
}

const perpage = ref(props.per_page);

watch(() => perpage.value, (newVal) => {
    emits("per-page-change", newVal);
});

const selectedColumns = ref(props.columns);

const perPageOptions = [
    { id: 10, label: "10" },
    { id: 25, label: "25" },
    { id: 50, label: "50" },
    { id: 100, label: "100" },
];

function AscByColumn(e) {
    sort.column = e;
    sort.order = "asc";
}

function DescByColumn(e) {
    sort.column = e;
    sort.order = "desc";
}

function MoveColumnLeft(e) {
    // find the column and change the index
    const column = selectedColumns.value.find((col) => col.row === e);
    if (column) {
        const index = selectedColumns.value.indexOf(column);
        if (index > 0) {
            selectedColumns.value.splice(index, 1);
            selectedColumns.value.splice(index - 1, 0, column);
        }
    }
}

function MoveColumnRight(e) {
    // find the column and change the index
    const column = selectedColumns.value.find((col) => col.row === e);
    if (column) {
        const index = selectedColumns.value.indexOf(column);
        if (index < selectedColumns.value.length - 1) {
            selectedColumns.value.splice(index, 1);
            selectedColumns.value.splice(index + 1, 0, column);
        }
    }
}

function HideColumn(e) {
    // find the column and set visible to false
    const column = selectedColumns.value.find((col) => col.row === e);
    if (column) {
        column.visible = false;
    }
}

const filteredData = computed(() => {
    if (!sort.column) return [...props.data];

    return [...props.data].sort((a, b) => {
        if (sort.order === "asc") {
            return a[sort.column] > b[sort.column] ? 1 : -1;
        } else {
            return a[sort.column] < b[sort.column] ? 1 : -1;
        }
    });
});

const oldSearch = ref("");

const searchedData = computed(() => {
    if (search.value && search.value !== oldSearch.value) {
        setPage(1);
        setPerPage(100);
        oldSearch.value = search.value;
    }

    const searchData = filteredData.value.filter((item) => {
        return Object.keys(item).some((key) => {
            return String(item[key]).toLowerCase().includes(search.value.toLowerCase());
        });
    });

    totalCountChanged(searchData.length);

    return searchData;
});


const filters = ref(props.filters);


const filterValues = computed(() => {
    return filters.value.map((filter) => {
        // filter has  dataName & value
        // combine the dataName and value to get the data
        // return object like { dataName: value }
        return {
            [filter.dataName]: filter.value,
        };
    });
});

watch(() => filterValues.value, (newVal) => {
    emits("filter-change", newVal);
});

watch(() => props.columns, (newVal) => {
    selectedColumns.value = newVal;
});


function removeFilter(targetDataName) {
    filters.value = filters.value.map((filter) => {
        if (filter.dataName === targetDataName) {
            filter.value = "";
        }
        return filter;
    });
}

function applyFilters(data) {
    filters.value = JSON.parse(JSON.stringify(data));
}

</script>