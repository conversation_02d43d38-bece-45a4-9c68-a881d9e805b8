<template>
    <nav class="flex gap-x-1">
        <router-link :to="{ name: 'profile' }" exact exact-active-class="bg-gray-100 dark:bg-gray-900"
            class="px-2.5 py-1.5 transition-colors relative inline-flex items-center gap-x-2 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700  hover:text-gray-800 text-sm rounded-lg focus:outline-none  text-gray-800 ">
            <svg class="mr-1 size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                </path>
            </svg>

            <span class="truncate">{{ $t('profile.sidebar.profile') }}</span>
        </router-link>

        <router-link :to="{ name: 'profile-account' }" exact exact-active-class="bg-gray-100 dark:bg-gray-900"
            class="px-2.5 py-1.5 transition-colors relative inline-flex items-center gap-x-2 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700  hover:text-gray-800 text-sm rounded-lg focus:outline-none  text-gray-800 ">

            <svg class="mr-1 size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span class="truncate">{{ $t('profile.sidebar.account') }}</span>
        </router-link>

        <router-link :to="{ name: 'profile-security' }" exact exact-active-class="bg-gray-100 dark:bg-gray-900"
            class="px-2.5 py-1.5 transition-colors relative inline-flex items-center gap-x-2 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700  hover:text-gray-800 text-sm rounded-lg focus:outline-none  text-gray-800 ">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                stroke="currentColor" class="mr-1 size-5">
                <path stroke-linecap="round" stroke-linejoin="round"
                    d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
            </svg>

            <span class="truncate">{{ $t('profile.sidebar.security') }}</span>
        </router-link>

        <router-link :to="{ name: 'profile-activity' }" exact exact-active-class="bg-gray-100 dark:bg-gray-900"
            class="px-2.5 py-1.5 transition-colors relative inline-flex items-center gap-x-2 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700  hover:text-gray-800 text-sm rounded-lg focus:outline-none  text-gray-800 ">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                stroke="currentColor" class="mr-1 size-5">
                <path stroke-linecap="round" stroke-linejoin="round"
                    d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z" />
            </svg>
            <span class="truncate">{{ $t('profile.sidebar.activity') }}</span>
        </router-link>

    </nav>
</template>

<script setup>

</script>

