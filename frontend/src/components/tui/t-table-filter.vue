<template>
    <div v-if="props.filterType === 'drawer'">
        <button type="button" @click="openMenu()"
            class="py-2 px-2.5 inline-flex items-center gap-x-1.5 text-xs rounded-lg border border-stone-200 bg-white text-stone-800 shadow-sm hover:bg-stone-50  dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 ">
            <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round">
                <line x1="21" x2="14" y1="4" y2="4" />
                <line x1="10" x2="3" y1="4" y2="4" />
                <line x1="21" x2="12" y1="12" y2="12" />
                <line x1="8" x2="3" y1="12" y2="12" />
                <line x1="21" x2="16" y1="20" y2="20" />
                <line x1="12" x2="3" y1="20" y2="20" />
                <line x1="14" x2="14" y1="2" y2="6" />
                <line x1="8" x2="8" y1="10" y2="14" />
                <line x1="16" x2="16" y1="18" y2="22" />
            </svg>
            <span class="hidden sm:inline">
                {{ $t('table.filters_title') }}
            </span>


            <span v-if="filters.filter(filter => filter.value).length > 0"
                class="font-medium text-[10px] py-0.5 px-[5px] bg-stone-800 text-white leading-3 rounded-full dark:bg-neutral-500">
                {{ filters.filter(filter => filter.value).length }}
            </span>
        </button>
        <div class="fixed right-0 top-0 size-full sm:w-[400px] z-[80] flex flex-col bg-white border-s dark:bg-neutral-800 dark:border-neutral-700"
            v-if="showMenu">
            <div class="flex items-center justify-between px-5 py-3 border-b dark:border-neutral-700">
                <h3 class="font-semibold text-stone-800 dark:text-neutral-200">
                    {{ $t('table.filters_title') }}
                </h3>


                <button type="button" @click="showMenu = false"
                    class="inline-flex items-center justify-center border border-transparent rounded-full size-8 gap-x-2 bg-stone-100 text-stone-800 hover:bg-stone-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-200 dark:bg-neutral-700 dark:hover:bg-neutral-600 dark:text-neutral-400 dark:focus:bg-neutral-600"
                    data-hs-overlay="#hs-pro-epfo">
                    <span class="sr-only">Close</span>
                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                        stroke-linejoin="round">
                        <path d="M18 6 6 18" />
                        <path d="m6 6 12 12" />
                    </svg>
                </button>
            </div>


            <div class="h-full p-5 overflow-hidden overflow-y-auto divide-y">
                <div class="grid grid-cols-6 gap-3">
                    <div v-for="(filter, index) in filters" :key="index" class="flex flex-col gap-1"
                        :class="[filter.size === 'full' ? 'col-span-6' : 'col-span-3']">
                        
                        <label class="text-xs font-semibold text-stone-800 dark:text-neutral-200">
                            {{ $t('table.table_items.' + filter.dataName) }}
                        </label>

                        <t-select v-if="filter.type === 'select' || filter.type === 'selectbox'" searchable long
                            close-after-select v-model="filter.value" :options="filter.data" data-label="id"
                            option-label="name" />

                        <input v-if="filter.type === 'date'" type="date" v-model="filter.value" class="t-input">

                        <input v-if="filter.type === 'text'" type="text" v-model="filter.value" class="t-input">

                        <input type="checkbox" v-if="filter.type === 'switch'" v-model="filter.value" class="t-switch">

                        <input v-if="filter.type === 'number'" type="number" v-model="filter.value" class="t-input">

                        <div v-if="filter.value">
                            <button type="button" @click="clearFilter(filter.dataName)"
                                class="py-1.5 px-2 inline-flex items-center gap-x-1 text-xs font-medium rounded-full border border-dashed border-stone-200 bg-white text-rose-600 hover:bg-stone-50  focus:bg-stone-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-rose-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                <svg class="flex-shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M18 6 6 18" />
                                    <path d="m6 6 12 12" />
                                </svg>
                                {{ $t('table.clear_filter') }}
                            </button>
                        </div>
                    </div>
                </div>

            </div>


            <div class="p-4 border-t dark:border-neutral-700 gap-x-2">
                <div class="flex items-center justify-between w-full">

                    <div class="flex items-center gap-2">
                        <button type="button" @click="applyFilter()"
                            class="py-2 px-4 inline-flex items-center gap-x-2 text-xs font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-none focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                            {{ $t('table.do_filter') }}
                        </button>

                        <button type="button" @click="showMenu = false"
                            class="py-2 px-4 inline-flex items-center gap-x-2 text-xs font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-none focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                            {{ $t('table.close_filter') }}
                        </button>
                    </div>

                    <button v-if="filters.filter(filter => filter.value).length > 0" type="button"
                        @click="clearAllFilters()"
                        class="py-1.5 px-2 inline-flex items-center gap-x-1 text-xs font-medium rounded-full border border-dashed border-stone-200 bg-white text-rose-600 hover:bg-stone-50  focus:bg-stone-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-rose-500 dark:hover:bg-neutral-700/50 dark:focus:bg-neutral-700">
                        <svg class="flex-shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18" />
                            <path d="m6 6 12 12" />
                        </svg>
                        {{ $t('table.clear_all_filters') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div v-if="props.filterType === 'dropdown'" ref="dropdown">
        <t-dropdown v-model="showMenu" placement="top-right" :auto-close="false" width="w-80" close-button>
            <template #button>
                <svg class="flex-shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <line x1="21" x2="14" y1="4" y2="4" />
                    <line x1="10" x2="3" y1="4" y2="4" />
                    <line x1="21" x2="12" y1="12" y2="12" />
                    <line x1="8" x2="3" y1="12" y2="12" />
                    <line x1="21" x2="16" y1="20" y2="20" />
                    <line x1="12" x2="3" y1="20" y2="20" />
                    <line x1="14" x2="14" y1="2" y2="6" />
                    <line x1="8" x2="8" y1="10" y2="14" />
                    <line x1="16" x2="16" y1="18" y2="22" />
                </svg>
                <span class="hidden sm:inline">
                    {{ $t('table.filters_title') }}
                </span>
                <span v-if="filters.filter(filter => filter.value).length > 0"
                    class="font-medium text-[10px] py-0.5 px-[5px] bg-stone-800 text-white leading-3 rounded-full dark:bg-neutral-500">
                    {{ filters.filter(filter => filter.value).length }}
                </span>
                <svg class="ms-auto lg:ms-0 flex-shrink-0 w-4 h-4 transition"
                    :class="{ 'transform rotate-180': showMenu }" xmlns="http://www.w3.org/2000/svg" width="24"
                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                    stroke-linecap="round" stroke-linejoin="round">
                    <path d="m6 9 6 6 6-6" />
                </svg>
            </template>
            <template #menu>
                <div class="h-[calc(100%-58px)] flex flex-col">
                    <div class="h-full p-5 divide-y">
                        <div v-for="(filter, index) in filters" :key="index"
                            class="flex flex-col gap-2 py-2 border-stone-200 dark:border-neutral-700">
                            <label class="text-xs font-semibold text-stone-800 dark:text-neutral-200">
                                {{ filter.name }}
                            </label>


                            <t-select v-if="filter.type === 'select' || filter.type === 'selectbox'" searchable long
                                close-after-select v-model="filter.value" :options="filter.data" data-label="id"
                                option-label="name" />

                            <input v-if="filter.type === 'text'" type="text" v-model="filter.value" class="t-input">

                            <input v-if="filter.type === 'date'" type="date" v-model="filter.value" class="t-input">

                            <input v-if="filter.type === 'number'" type="number" v-model="filter.value" class="t-input">

                            <input type="checkbox" v-if="filter.type === 'switch'" v-model="filter.value"
                                class="t-switch">


                            <div v-if="filter.value">
                                <button type="button" @click="clearFilter(filter.dataName)"
                                    class="py-1.5 px-2 inline-flex items-center gap-x-1 text-xs font-medium rounded-full border border-dashed border-stone-200 bg-white text-rose-600 hover:bg-stone-50  focus:bg-stone-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-rose-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                    <svg class="flex-shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24"
                                        height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M18 6 6 18" />
                                        <path d="m6 6 12 12" />
                                    </svg>
                                    {{ $t('table.clear_filter') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-4 border-t dark:border-neutral-700">
                    <div class="flex items-center justify-between w-full">

                        <button type="button" @click="applyFilter()"
                            class="py-1.5 px-2 inline-flex items-center text-xs font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-none focus:bg-gray-100 dark:border-neutral-700 dark:text-green-500 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                            {{ $t('table.do_filter') }}
                        </button>

                        <button type="button" @click="clearAllFilters()"
                            v-if="filters.filter(filter => filter.value).length > 0"
                            class="inline-flex items-center px-2 py-1 text-xs font-medium bg-white border border-dashed rounded-full gap-x-1 border-stone-200 text-rose-600 hover:bg-stone-50 focus:bg-stone-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-rose-500 dark:hover:bg-neutral-700/50 dark:focus:bg-neutral-700">
                            <svg class="flex-shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18" />
                                <path d="m6 6 12 12" />
                            </svg>
                            {{ $t('table.clear_all_filters') }}
                        </button>
                    </div>
                </div>
            </template>
        </t-dropdown>
    </div>
</template>

<script setup>
const props = defineProps({
    filters: {
        type: Array,
        default: () => []
    },
    filterType: {
        type: String,
        default: 'drawer' // drawer, dropdown
    },
    autoClose: {
        type: Boolean,
        default: true,
    },
});

const filters = ref(JSON.parse(JSON.stringify(props.filters)));

const emits = defineEmits(['filter']);

const showMenu = ref(false);

const dropdown = ref(null);

// for updating filters when filters prop changes outside of the component
watch(() => props.filters, (value) => {
    if (!value) return;
    filters.value = JSON.parse(JSON.stringify(value));
});

function openMenu() {
    showMenu.value = !showMenu.value;
}

function clearAllFilters() {
    filters.value.forEach(filter => {
        filter.value = '';
    });
    emits('filter', filters.value);
}

function clearFilter(filterDataName) {
    filters.value.forEach(f => {
        if (f.dataName === filterDataName) {
            f.value = '';
        }
    });
    emits('filter', filters.value);
}

function applyFilter() {
    emits('filter', filters.value);
    showMenu.value = false;
}

if (props.autoClose) {
    document.addEventListener('click', clickOutside);
    onBeforeUnmount(() => {
        document.removeEventListener('click', clickOutside);
    });
}


function clickOutside(e) {
    if (dropdown.value && !dropdown.value.contains(e.target)) {
        showMenu.value = false;
    }
}

</script>
