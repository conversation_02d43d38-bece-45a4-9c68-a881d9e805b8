<template>
    <div class="relative" ref="dropdown">
        <button @click="dropmenu = !dropmenu"
            class="py-2 px-2.5 text-xs inline-flex items-center gap-x-1.5 rounded-lg border border-gray-200 bg-white text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
            <span>
                {{
                    selectedDate
                        ? selectedDate?.toLocaleString("tr-TR", {
                            month: "long",
                            day: "numeric",
                            year: "numeric",
                            weekday: "long",
                        })
                        : props.placeholder
                }}
            </span>

            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                v-if="!selectedDate" stroke="currentColor" class="w-5 h-5">
                <path stroke-linecap="round" stroke-linejoin="round"
                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" v-if="selectedDate"
                stroke="currentColor" class="w-4 h-4 transition" :class="[dropmenu ? 'transform rotate-180' : '']">
                <path stroke-linecap="round" stroke-linejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
            </svg>
        </button>

        <div id="ranges-tab-preview-datepicker" v-if="dropmenu" ref="dropdownmenu"
            class="absolute z-50 flex flex-col mt-3 overflow-hidden bg-white border shadow-lg top-full rounded-xl dark:bg-neutral-900 dark:border-neutral-700"
            :class="[isElementOverflow ? 'right-0' : 'left-0']">
            <div class="grid gap-8 p-3 md:flex">
                <div class="space-y-0.5">
                    <div class="grid grid-cols-5 items-center gap-x-3 mx-1.5 pb-3">
                        <div class="col-span-1">
                            <button type="button" @click="prevMonth"
                                class="flex items-center justify-center text-gray-800 rounded-full size-8 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-400 dark:hover:bg-neutral-800">
                                <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24"
                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m15 18-6-6 6-6"></path>
                                </svg>
                            </button>
                        </div>

                        <div class="col-span-3">
                            <div class="flex items-center justify-center gap-x-1">
                                <span class="text-sm font-semibold text-gray-800 dark:text-neutral-200">
                                    {{
                                        new Date(currentYear, currentMonth - 1, 1).toLocaleString("tr-TR", {
                                            month: "long",
                                        })
                                    }}
                                </span>
                                <span class="text-sm font-semibold text-gray-800 dark:text-neutral-200"> / </span>
                                <span class="text-sm font-semibold text-gray-800 dark:text-neutral-200">
                                    {{ currentYear }}
                                </span>
                            </div>
                        </div>

                        <div class="flex justify-end col-span-1">
                            <button type="button" @click="nextMonth"
                                class="flex items-center justify-center text-gray-800 rounded-full size-8 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-400 dark:hover:bg-neutral-800">
                                <svg class="flex-shrink-0 size-4" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round">
                                    <path d="m9 18 6-6-6-6"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flex pb-1.5">
                        <span class="block w-10 m-px text-sm text-center text-gray-500 dark:text-neutral-500">
                            Pzt
                        </span>
                        <span class="block w-10 m-px text-sm text-center text-gray-500 dark:text-neutral-500">
                            Sal
                        </span>
                        <span class="block w-10 m-px text-sm text-center text-gray-500 dark:text-neutral-500">
                            Çar
                        </span>
                        <span class="block w-10 m-px text-sm text-center text-gray-500 dark:text-neutral-500">
                            Per
                        </span>
                        <span class="block w-10 m-px text-sm text-center text-gray-500 dark:text-neutral-500">
                            Cum
                        </span>
                        <span class="block w-10 m-px text-sm text-center text-gray-500 dark:text-neutral-500">
                            Cmt
                        </span>
                        <span class="block w-10 m-px text-sm text-center text-gray-500 dark:text-neutral-500">
                            Paz
                        </span>
                    </div>
                    <div class="flex" v-for="week in dynamicCalendar">
                        <div v-for="day in week">
                            <button type="button" @click="() => day.is_disabled || selectDate(day)"
                                class="flex items-center justify-center m-px text-sm transition border border-transparent rounded-lg size-10 hover:bg-gray-100 dark:hover:bg-neutral-800"
                                :class="[
                                    day.is_today
                                        ? ' border-green-600 text-green-600 bg-green-100 dark:bg-green-600 dark:text-white dark:hover:bg-green-600 dark:hover:text-white'
                                        : '',
                                    day.is_current_month ? '' : 'text-gray-300 dark:text-neutral-500',
                                    day.is_selected
                                        ? 'border border-blue-600 text-blue-600 bg-blue-100 dark:bg-blue-600 dark:text-white dark:hover:bg-blue-600 dark:hover:text-white'
                                        : '',
                                    day.is_disabled ? 'text-gray-300 dark:text-neutral-500' : '',
                                ]">
                                {{ day.day }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-end p-3 border-t gap-x-2 dark:border-neutral-700" v-if="false">
                <button type="button"
                    class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-800 bg-white border border-gray-200 rounded-lg shadow-sm gap-x-2 hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-800">
                    Cancel
                </button>
                <button type="button"
                    class="inline-flex items-center px-3 py-2 text-sm font-semibold text-white bg-blue-600 border border-transparent rounded-lg gap-x-2 hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    Apply
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
const dropmenu = ref(false);
const dropdown = ref(null);
const dropdownmenu = ref(null);

const props = defineProps({
    autoClose: {
        type: Boolean,
        default: true,
    },
    modelValue: {
        type: [String, Date],
        default: null,
    },
    placeholder: {
        type: String,
        default: "Tarih Seçiniz",
    },
    range: {
        type: Boolean,
        default: false,
    },
    minSelectableDate: {
        type: Date,
        required: false,
    },
    maxSelectableDate: {
        type: Date,
        required: false,
    },
});

if (props.autoClose) {
    document.addEventListener("click", clickOutside);
    onBeforeUnmount(() => {
        document.removeEventListener("click", clickOutside);
    });
}

function clickOutside(e) {
    if (dropdown.value && !dropdown.value.contains(e.target)) {
        dropmenu.value = false;
    }
}

const emits = defineEmits(["update:modelValue"]);

const today = ref(new Date());
const currentMonth = ref(today.value.getMonth() + 1);
const currentYear = ref(today.value.getFullYear());
const rangenextMonth = ref(currentMonth.value + 1);

const selectedDate = ref(props.modelValue ? new Date(props.modelValue) : null);


const getDynamicCalendar = computed(() => {
    let year = currentYear.value;
    let month = currentMonth.value;
    const calendar = [];
    const weeks = [];

    // Get the day number of the first day of the specified month (0: Sunday, 1: Monday, ..., 6: Saturday)
    const firstDayNumber = new Date(year, month - 1, 1).getDay();

    // Add days from the previous month to start the calendar
    const daysFromPrevMonth = firstDayNumber === 1 ? 0 : firstDayNumber - 1;
    const prevMonthLastDay = new Date(year, month - 1, 0).getDate();
    for (let i = prevMonthLastDay - daysFromPrevMonth + 1; i <= prevMonthLastDay; i++) {
        const date = new Date(year, month - 2, i);
        const isDisabled = props.minSelectableDate ? date < props.minSelectableDate : false;
        calendar.push({
            day: i,
            month: month - 1,
            is_today: false,
            is_current_month: false,
            is_selected: false,
            is_disabled: isDisabled,
        });
    }

    // Add days of the specified month
    const monthLastDay = new Date(year, month, 0).getDate();
    for (let i = 1; i <= monthLastDay; i++) {
        const date = new Date(year, month - 1, i);
        const currentDate = new Date();
        const isToday =
            year === currentDate.getFullYear() && month === currentDate.getMonth() + 1 && i === currentDate.getDate();
        const isDisabled = props.minSelectableDate
            ? date < props.minSelectableDate
            : false || props.maxSelectableDate
                ? date > props.maxSelectableDate
                : false;
        calendar.push({
            day: i,
            month: month,
            is_today: isToday,
            is_current_month: true,
            is_selected: false,
            is_disabled: isDisabled,
        });
    }

    // Add days from the next month to complete the calendar
    let daysFromNextMonth = 42 - calendar.length; // Total of 42 days in the calendar
    for (let i = 1; i <= daysFromNextMonth; i++) {
        const date = new Date(year, month, i);
        const isDisabled = props.maxSelectableDate ? date > props.maxSelectableDate : false;
        calendar.push({
            day: i,
            month: month + 1,
            is_today: false,
            is_current_month: false,
            is_selected: false,
            is_disabled: isDisabled,
        });
    }

    // Group days into weeks
    for (let i = 0; i < calendar.length; i += 7) {
        weeks.push(calendar.slice(i, i + 7));
    }
    // console.log(weeks);
    return weeks;
});

const dynamicCalendar = ref(getDynamicCalendar.value);

if (selectedDate.value) {
    if (dynamicCalendar.value) {
        dynamicCalendar.value.forEach((week) => {
            week.forEach((day) => {
                day.is_selected = false;
                if (
                    day.day === selectedDate.value.getDate() &&
                    day.month === selectedDate.value.getMonth() + 1
                ) {
                    day.is_selected = true;
                }
            });
        });
    }
}

function prevMonth() {
    currentMonth.value--;
    if (currentMonth.value < 1) {
        currentMonth.value = 12;
        currentYear.value--;
    }
    dynamicCalendar.value = getDynamicCalendar.value;
}

function nextMonth() {
    currentMonth.value++;
    if (currentMonth.value > 12) {
        currentMonth.value = 1;
        currentYear.value++;
    }
    dynamicCalendar.value = getDynamicCalendar.value;
}

function selectDate(day) {

    selectedDate.value = new Date(currentYear.value, day.month - 1, day.day);

    dynamicCalendar.value.forEach((week) => {
        week.forEach((d) => {
            d.is_selected = false;
            if (d.day === day.day && d.month === day.month) {
                d.is_selected = true;
            }
        });
    });

    emits("update:modelValue", selectedDate);
}

const isElementOverflow = computed(() => {
    if (dropdownmenu?.value) {
        return dropdownmenu?.value?.getBoundingClientRect().right > window.innerWidth;
    }
});
</script>
