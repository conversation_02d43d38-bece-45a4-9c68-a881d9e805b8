<template>
    <div class="relative inline-flex [--auto-close:inside]" ref="dropdown">
        <div class="inline-block [--placement:bottom]">
            <button id="hs-pro-dnnd" type="button" @click="menu = !menu"
                class="hs-tooltip-toggle relative inline-flex flex-shrink-0 justify-center items-center gap-x-2 size-[38px] rounded-full text-white hover:bg-white/10 disabled:opacity-50 focus:outline-none focus:bg-white/10">
                <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                    viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round">
                    <path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9" />
                    <path d="M10.3 21a1.94 1.94 0 0 0 3.4 0" />
                </svg>
                <span class="flex absolute top-0 end-0 z-10 -mt-1.5 -me-1.5" v-if="unreadedCount > 0">
                    <span
                        class="animate-ping absolute inline-flex size-full rounded-full bg-red-400 opacity-75 dark:bg-red-600"></span>
                    <span
                        class="relative min-w-[18px] min-h-[18px] inline-flex justify-center items-center text-[10px] bg-red-500 text-white rounded-full px-1">
                        {{ unreadedCount }}
                    </span>
                </span>
            </button>
            <span class="sr-only">
                Notifications
            </span>
        </div>


        <transition enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95">
            <div class=" w-72  sm:w-96 absolute top-full origin-top-right right-0 z-10 bg-white border-t border-gray-200 sm:border-t-0 sm:rounded-lg dark:bg-neutral-900 dark:border-neutral-700 shadow-xl shadow-gray-500 dark:shadow-black/50"
                v-if="menu">
                <div class="px-5 py-3 border-b border-gray-200 dark:border-neutral-800">
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-semibold text-gray-800 dark:text-neutral-200">Notifications</span>
                        <span class="text-sm text-gray-500 dark:text-neutral-500">
                            {{ unreadedCount > 0 ? `${unreadedCount} unreaded` : '' }}
                        </span>
                    </div>
                </div>

                <div v-if="notification.length > 0">
                    <div class="h-[480px] overflow-y-auto overflow-hidden">
                        <ul class="divide-y divide-gray-200 dark:divide-neutral-800">

                            <li v-for="item in notification" :key="item.reference_id"
                                class="relative group w-full flex gap-x-5 text-start p-5 bg-white dark:bg-neutral-900">
                                <div class="grow">
                                    <div
                                        class="flex items-center justify-between text-xs text-gray-500 dark:text-neutral-500">
                                        <span>
                                            {{ FormatCreatedDate(item.created_at) }}
                                        </span>
                                        <span v-if="!item.read"
                                            class="relative h-2 w-2 inline-flex justify-center items-center text-[10px] bg-blue-500 text-white rounded-full px-1">
                                        </span>
                                    </div>

                                    <span class="block text-sm font-medium text-gray-800 dark:text-neutral-300">
                                        {{ item.title }}
                                    </span>
                                    <p class="text-sm text-gray-500 dark:text-neutral-500">
                                        {{ item.content }}
                                    </p>
                                </div>

                                <div>
                                    <div class="sm:group-hover:opacity-100 sm:opacity-0 sm:absolute sm:top-5 sm:end-5">
                                        <div
                                            class="inline-block p-0.5 bg-white border border-gray-200 rounded-lg shadow-sm transition ease-out dark:bg-neutral-800 dark:border-neutral-700">
                                            <div class="flex items-center">
                                                <div class="relative inline-block">
                                                    <button type="button" @click="markAsRead(item.id)" v-if="!item.read"
                                                        class="size-7 flex flex-shrink-0 justify-center items-center text-gray-500 hover:bg-gray-100 hover:text-gray-800 rounded disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-400 dark:focus:bg-neutral-700">
                                                        <svg class="flex-shrink-0 size-4"
                                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round">
                                                            <polyline points="9 11 12 14 22 4" />
                                                            <path
                                                                d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11" />
                                                        </svg>
                                                    </button>
                                                    <button type="button" @click="archiveNotification(item.id)"
                                                        class="size-7 flex flex-shrink-0 justify-center items-center text-gray-500 hover:bg-gray-100 hover:text-gray-800 rounded disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-400 dark:focus:bg-neutral-700">
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                            viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
                                                            class="flex-shrink-0 size-4">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                d="m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m8.25 3v6.75m0 0-3-3m3 3 3-3M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                        </ul>

                    </div>

                    <div class="text-center border-t border-gray-200 dark:border-neutral-800">
                        <button @click="markAsReadAll()"
                            class="p-4 flex justify-center items-center w-full gap-x-2 text-sm text-gray-500 font-medium sm:rounded-b-lg hover:text-blue-600 focus:outline-none focus:text-blue-600 dark:text-neutral-400 dark:hover:text-neutral-300 dark:focus:text-neutral-300">
                            Mark all as read
                        </button>
                    </div>
                </div>

                <div class="pt-3" role="tabpanel" v-else>
                    <div class="p-5 min-h-[533px] flex flex-col justify-center items-center text-center">
                        <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor"
                                class="fill-white dark:fill-neutral-800" />
                            <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor"
                                class="stroke-gray-50 dark:stroke-neutral-700/10" />
                            <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor"
                                class="fill-gray-50 dark:fill-neutral-700/30" />
                            <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor"
                                class="fill-gray-50 dark:fill-neutral-700/30" />
                            <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor"
                                class="fill-gray-50 dark:fill-neutral-700/30" />
                            <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor"
                                class="fill-white dark:fill-neutral-800" />
                            <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor"
                                class="stroke-gray-100 dark:stroke-neutral-700/30" />
                            <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor"
                                class="fill-gray-100 dark:fill-neutral-700/70" />
                            <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor"
                                class="fill-gray-100 dark:fill-neutral-700/70" />
                            <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor"
                                class="fill-gray-100 dark:fill-neutral-700/70" />
                            <g filter="url(#filter15)">
                                <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor"
                                    class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges" />
                                <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor"
                                    class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges" />
                                <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor"
                                    class="fill-gray-200 dark:fill-neutral-700 " />
                                <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor"
                                    class="fill-gray-200 dark:fill-neutral-700" />
                                <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor"
                                    class="fill-gray-200 dark:fill-neutral-700" />
                            </g>
                            <defs>
                                <filter id="filter15" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse"
                                    color-interpolation-filters="sRGB">
                                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                    <feColorMatrix in="SourceAlpha" type="matrix"
                                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                    <feOffset dy="6" />
                                    <feGaussianBlur stdDeviation="6" />
                                    <feComposite in2="hardAlpha" operator="out" />
                                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0" />
                                    <feBlend mode="normal" in2="BackgroundImageFix"
                                        result="effect1_dropShadow_1187_14810" />
                                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810"
                                        result="shape" />
                                </filter>
                            </defs>
                        </svg>

                        <div class="max-w-sm mx-auto">
                            <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                No notifications
                            </p>
                            <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                We'll notify you about important updates.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script setup>
import { useNotificationStore } from '@/store/submodules/notification';
import dayjs from 'dayjs';
import relative from 'dayjs/plugin/relativeTime'

dayjs.extend(relative)

const notificationStore = useNotificationStore();
const menu = ref(false);
const dropdown = ref(null);

function clickOutside(e) {
    if (dropdown.value && !dropdown.value.contains(e.target)) {
        menu.value = false;
    }
}

notificationStore.List(1, 25, [])

const notification = computed(() => {
    return notificationStore?.getNotifications?.rows ?? []
});

const unreadedCount = computed(() => {
    return notificationStore?.getUnreadNotificationsCount ?? 0
});


function markAsRead(id) {
    notificationStore.MarkAsRead(id)
}

function archiveNotification(id) {
    notificationStore.Archive(id)
}

function markAsReadAll() {
    notificationStore.MarkAllAsRead()
}

function FormatCreatedDate(date) {
    return dayjs(date).fromNow()
}

document.addEventListener('click', clickOutside);
onBeforeUnmount(() => {
    document.removeEventListener('click', clickOutside);
});

</script>
