{"dashboard": {"welcome": "Welcome"}, "form": {"validation_err": "Doğrulama Hatası. Lütfen bilgilerinizi kontrol edip tekrar deneyin.", "an_error_occurred": "Bir hata olu<PERSON>. Lütfen daha sonra tekrar deneyin.", "internal_error": "<PERSON><PERSON><PERSON>", "unsaved_changes": "Henüz kaydedilmemiş değişiklikleriniz var.", "discard_changes": "Vazgeç", "unsaved_changes_confirm": "Henüz kaydedilmemiş değişiklikleriniz var. Bu sayfadan ayrılmak istediğinize emin misiniz?"}, "auth": {"email": "E-posta adresi", "password": "Şifre", "password_confirmation": "Şifre Tekrarı", "magic_login": "Sihirli Link", "pin_login": "PIN ile Giriş", "challenge_title": "Kimlik Doğrulama", "challenge_description": "Kimlik doğrulama yöntemini seçin", "two_factor_login": "İki Faktörlü Giriş", "info": "Lütfen e-posta adresinizi girin ve giriş yöntemini seçin", "processing": "İşlem Gerçekleştiriliyor...", "magic": "E-postanıza, hesabınıza erişmenizi sağlayacak sihirli bir bağlantı gönderdik.", "check_mail": "Lütfen e-postanızı kontrol edin ve devam etmek için bağlantıya tıklayın.", "two_factor_authentication": "İki faktörlü kimlik uygulamasını kullanarak giriş yapın.", "please_enter_password": "Lütfen şifrenizi girin", "please_enter_pin_code": "Lütfen PIN kodunuzu girin", "please_enter_email": "Lütfen e-posta adresinizi girin", "please_enter_two_factor_code": "Lütfen iki faktörlü kimlik doğrulama kodunuzu girin", "reset_password": "Şifremi Sıfırla", "reset_password_description": "Size bir e-posta gönderdik. Lütfen e-postanıza gelen kodu ve yeni şifrenizi girin.", "forgot_password_success": "Size bir e-posta gönderdik. Lütfen e-postanıza gelen kodu ve yeni şifrenizi girin.", "private_browsing": "Size ait olmayan bir bil<PERSON>, telefon veya tablette geçici olarak oturum açmak için gizli göz atma penceresini kullanın.", "session_drop": "Oturumunuzun süresi doldu veya başka bir cihazdan hesabınıza giriş yapıldı. Lütfen tekrar giriş yapın.", "2fa": {"title": "İki Faktörlü Kimlik Doğrulama", "description": "İki faktörlü kimlik uygulamasını kullanarak giriş yapın.", "verify_code_placeholder": "<PERSON><PERSON> girin", "verify_code_button": "Ko<PERSON> Doğrula", "login": "<PERSON><PERSON><PERSON>", "verify_code_error": "Kod doğrulanamadı"}, "check_mail_pin": "Lütfen e-postanızı kontrol edin ve devam etmek için bağlantıya tıklayın.", "forgot_password": "Şif<PERSON><PERSON>", "forgot_password_description": "Lütfen e-posta adresinizi girin. Şifrenizi sıfırlamak için size bir e-posta göndereceğiz.", "pin": {"title": "PIN ile Giriş", "description": "E-postanıza gönderilen PIN kodunu girin.", "code": "<PERSON><PERSON>", "code_placeholder": "Pin kodunuzu girin"}, "errors": {"check_data": "Girdiğiniz bilgiler hatalı. Lütfen kontrol edin."}, "identifier": {"title": "Kimlik Doğrulama", "description": "Lütfen kimlik doğrulaması için e-posta adresinizi girin."}, "password_page": {"title": "<PERSON><PERSON><PERSON>", "description": "Lütfen şifrenizi girin.", "use_ldap": "Kimlik Doğrulama için LDAP kullan"}, "password_reset_code": "Şifre Sıfırlama Kodu", "tfa_add_forced_title": "Zorunlu İki Faktörlü Kimlik Doğrulama", "tfa_add_forced_description": "Lütfen QR kodunu (authenticator,authy vb.) tarayıcı uygulamanıza okutun ve aşağıdaki kodu girin."}, "profile": {"profile": {"title": "Profil", "name": "Ad Soyad", "first_name": "Ad", "last_name": "Soyad", "email": "E-posta"}}, "routes": {"dashboard": "Ko<PERSON>rol <PERSON>i", "organization-list": "Organizasyonlar", "access-token-list": "<PERSON><PERSON><PERSON><PERSON>", "simulate-list": "Si<PERSON><PERSON><PERSON><PERSON><PERSON>", "workspace": "Çalışma Alanı", "default-request-list": "Tanımlı İstekler", "admin-list": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON>", "log-list": "Loglar", "pars-gpt": "ParsGPT", "useful-information": "Yararlı Bilgiler", "error-log-list": "Hata <PERSON>gları", "profile": "Profil", "signout": "Çıkış Yap", "data-view": "Sorg<PERSON>"}, "organizations": {"id": "ID", "name": "Organizasyon Adı", "parent": "Üst Organizasyon", "logo": "Logo", "favicon": "Favicon", "no_logo": "Logo bulunamadı", "timezone": "<PERSON><PERSON>", "domain_address": "Domain Adresi", "name_min_length": "Organizasyon adı en az 3 karakter olmalıdır.", "min_authorization_methods": "En az bir kimlik doğrulama yöntemi etkin olmalıdır.", "organization_is_sub_org": "Seçtiğiniz Üst Organizasyon, bu organizasyonun alt organizasyonu olduğu için üst organizasyon olarak kaydedilemez.", "health_checks": {"title": "Sağlık Kontrolleri"}, "settings": {"title": "<PERSON><PERSON><PERSON>", "general": {"tab": "<PERSON><PERSON>", "title": "<PERSON><PERSON>", "description": "Organizasyon için genel <PERSON>.", "organization_name": "Organizasyon Adı", "parent_organization": "Üst Organizasyon", "logo": "Logo", "favicon": "Favicon", "footer_text": "Altbilgi Yazısı", "allow_registration": "Yeni Kullanıcı Kaydına İzin Ver", "allow_registration_desc": "Bu organizasyona yeni kullanıcıların kaydolup kaydolamayacağını belirler.", "allow_limit_check": "Limit Ko<PERSON>rolü", "allow_limit_check_desc": "Bu organizasyonun limit kontrolü yapıp yapmayacağını belirler."}, "authentication": {"tab": "Kimlik Doğrulama", "title": "Kimlik Doğrulama Ayarları", "description": "Kullanıcıların organizasyonunuza nasıl kimlik doğruladığını yönetmek için kimlik doğrulama ayarları."}, "authorization": {"tab": "Yetkilendirme", "title": "Yetkilendirme Ayarları", "description": "Kullanıcıların kaynaklara erişim yetkilerini yönetmek için yetkilendirme ayarları."}, "design": {"tab": "Tasarım", "title": "Ta<PERSON>ım <PERSON>ı", "description": "Organizasyonunuzun ve uygulamalarınızın nasıl görüneceğini yönetir. Birden fazla stil ve yapılandırma tanımlayabilirsiniz."}, "terminate": {"tab": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "description": "Organizasyonun silinmesi için gere<PERSON> adı<PERSON>.", "terminate_organization": "Organizasyonu geri dö<PERSON><PERSON><PERSON><PERSON> olmayan şekilde sil", "terminate_organization_desc": "Bu organizasyonu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."}}}, "admins": {"name": "Kullanıcı Adı", "email": "Kullanıcı E-posta", "locale": "Kullanıcı Dili", "account": "Kullanıcı Hesabı", "first_name": "Ad", "last_name": "Soyad", "role": "Kullanıcı Rolü", "organization": "Kullanıcı Organizasyonu", "department": "Kullanıcı Departmanı", "two-factor": "İki Aşamalı Doğrulama", "two-factor_key": "İki Aşamalı Doğrulama Anahtarı", "password": "Şifre", "cannot_deleted": "Bu kullanıcı ile ilişkili veriler olduğu için si<PERSON>.", "suspended_reason": "Askıya Alınma Nedeni", "force_logout": "Zorla Çıkış", "suspend": "Askıya Al", "suspended": "<PERSON><PERSON><PERSON>", "not_suspended": "Hesap Aktif", "reset_password": "Şifre Sıfırlama maili gönder", "reset_password_mail_sent": "Şifre sıfırlama maili gönderildi."}, "page_titles": {"organizations": "Organizations", "organizations_health_check": "Health Check", "users": "Users", "access-tokens": "<PERSON><PERSON><PERSON><PERSON>", "users_create": "Create User", "users_edit": "Edit User", "users_view": "View User", "roles": "Roles", "roles_create": "Create Role", "roles_edit": "Edit Role", "admins": "Admins", "logs": "Logs", "error-logs": "<PERSON><PERSON><PERSON>", "simulates": "Si<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "general": {"select": "Lütfen Seçiniz", "copy": "Kopyala", "copied": "Kopyalandı", "copy_failed": "Kopyalama başarısız oldu", "details": "Detaylar", "processing": "İşleniyor", "unknown": "Bilinmeyen", "actions": "İşlemler", "status": "Status", "view_details": "Detayları Görüntüle", "updated_at": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "Başarısız", "organization": "Organizasyon", "go_last_version": "<PERSON><PERSON><PERSON><PERSON>", "show_page": "{ total_pages } sayfadan { page }.sayfa gösteriliyor", "step": "Adım { step } / { total_steps }", "no_records": "<PERSON><PERSON>t bulunamadı", "no_records_description": "Henüz veri bulunamadı veya hiç veri oluşturulmadı.", "update": "<PERSON><PERSON><PERSON><PERSON>", "create": "Oluştur", "delete": "Sil", "success": "Başarılı", "retry": "<PERSON><PERSON><PERSON>", "fail": "Başarısız", "cancel": "İptal", "save": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON>", "back": "<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "next": "İleri", "in_progress": "<PERSON><PERSON> ediyor", "previous": "<PERSON><PERSON><PERSON>", "search": "Aramak istediğiniz kelimeyi giriniz", "search_placeholder": "Ara...", "date_range": "<PERSON><PERSON><PERSON>", "field_required": "{ field } al<PERSON><PERSON> gere<PERSON>", "invalid_input": "<PERSON><PERSON> alan geç<PERSON><PERSON>ğer içeriyor", "field_invalid": "{ field } alanı geçersiz değer içeriyor", "fill_required_fields": "Gerekli alanlar doldurulmalıdır", "welcome": "<PERSON><PERSON> geldin {name}", "pay": "Ö<PERSON>", "incomes": "<PERSON><PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filter": "Filtrele", "clear_filters": "<PERSON><PERSON><PERSON><PERSON>", "issue": "<PERSON><PERSON><PERSON><PERSON>", "reindex": "Yeniden İndeksle", "loading": "Yükleniyor...", "active": "Aktif", "passive": "<PERSON><PERSON><PERSON>", "change": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "choose_file": "<PERSON><PERSON><PERSON>", "file_not_chosen": "<PERSON><PERSON><PERSON>", "accepted_extensions": "Kabul edilen u<PERSON>: {extensions}", "file_input_disabled": "<PERSON><PERSON><PERSON> se<PERSON><PERSON> de<PERSON> dışı", "please_choose_file": "Lütfen dosya seçiniz", "invalid_file_extension": "Geçersiz dosya uzatısı", "file_upload_error": "<PERSON><PERSON><PERSON> hatası", "please-select": "Lütfen seçiniz", "select_placeholder": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "delete_confirm": "<PERSON><PERSON><PERSON> istediğinize emin mi<PERSON>?", "delete_label": "Sil", "delete_success": "Öğe başarı<PERSON> silind<PERSON>!", "delete_error": "Öğe si<PERSON>en hata oluştu. Lütfen tekrar deneyin.", "reject_confirm": "Reddetmek istediğinize emin misin<PERSON>?", "reject_label": "<PERSON><PERSON>", "approve_confirm": "Onaylamak istediğinize emin misin<PERSON>?", "approve_label": "<PERSON><PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "created_at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON>", "internal_error": "Bir hata olu<PERSON>. Lütfen daha sonra tekrar deneyin.", "name_already_exist": "<PERSON><PERSON> <PERSON><PERSON>", "no_data": "<PERSON><PERSON> bulunamadı", "no_data_description": "Gösterilecek veri bulunamadı", "you_have_not_created_any": "Henüz veri oluşturmadınız. Başlamak için ilk verinizi oluşturun.", "continue": "<PERSON><PERSON> et", "server_error": "<PERSON><PERSON><PERSON>", "server_error_description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da<PERSON>i bir sunucu hatası ile karşılaştık. Lütfen daha sonra tekrar deneyin.", "refresh_page": "Sayfayı Yenile", "page_not_found": "Sayfa Bulunamadı", "page_not_found_description": "Üzgünüz, aradığın<PERSON>z sayfayı bulamadık.", "back_to_home": "<PERSON><PERSON>", "labels": "<PERSON><PERSON><PERSON><PERSON>", "something_went_wrong": "Bir şeyler yanlış gitti", "try_again": "Lütfen tekrar deneyin.", "select_organization": "Organizasyon <PERSON>", "import": "İçe Aktar", "export": "Dışa Aktar"}, "password": {"strength": "<PERSON><PERSON><PERSON> {strength_name}", "suggest": "<PERSON><PERSON><PERSON>", "copied_to_clipboard": "Şifre panoya kopyalandı", "too_weak": "Çok zayıf", "weak": "<PERSON><PERSON><PERSON><PERSON>", "medium": "Orta", "strong": "Güçlü"}, "search_menu": {"placeholder": "<PERSON><PERSON> yap", "parent_menu": "Üst Menü", "no_result": "<PERSON><PERSON><PERSON> bulu<PERSON>adı"}, "table": {"no_related_record": "<PERSON><PERSON>t bulunamadı", "add_record_link": "<PERSON><PERSON><PERSON> <PERSON>", "wait_for_record": "Lütfen kayıt eklenmesini be<PERSON>in", "table_settings": "<PERSON><PERSON><PERSON>", "table_cols": "<PERSON><PERSON><PERSON>", "record_per_page": "Sayfa Başına <PERSON>", "select_all": "<PERSON><PERSON><PERSON>", "select": "Seç", "all": "<PERSON><PERSON><PERSON>", "more": "daha fazlası", "filters_title": "<PERSON><PERSON><PERSON>", "do_filter": "Filtrele", "close_filter": "Ka<PERSON><PERSON>", "clear_filter": "<PERSON><PERSON><PERSON>", "clear_all_filters": "<PERSON><PERSON><PERSON>", "applied_filters": "Uygulanan Filtreler", "filters": {"payment_channel": {"0": "", "1": "Vpos", "2": "Para Transferi", "3": "Cüzdan", "4": "Kredi/Borç", "5": "<PERSON>n", "6": "Havale"}, "status": {"1": "Sipariş Oluşturuldu", "2": "Ödenmedi", "3": "Ödendi", "4": "İşleniyor", "5": "<PERSON><PERSON><PERSON>", "6": "Beklemede", "7": "Ödeme <PERSON>", "8": "İptal Edildi", "9": "Tamamlandı", "10": "<PERSON>ade Edildi", "11": "Şüpheli İşlem", "12": "Reddedildi", "13": "Hatalı işlem", "14": "<PERSON><PERSON><PERSON>", "15": "Parçalı İade", "16": "Alt Üye İşyeri Ödemesi Onaylandı", "17": "Alt Üye İşyeri Ödemesi Onaylanmadı", "18": "Alt Üye İşyeri Ödemesi Hatası", "19": "Ödenmemiş Taksit Bulunuyor", "20": "Ödenmemiş Vade Bulunuyor", "21": "Zaman Aşımına <PERSON>", "22": "Ödenmemiş Alt Üye İşyeri Ödemesi Bulunuyor", "23": "<PERSON><PERSON><PERSON><PERSON>", "24": "Ödeme İşlemi İptal Edildi", "25": "<PERSON><PERSON>", "26": "Ödeme Ön onayı alındı"}}, "sort_ascending": "<PERSON><PERSON>", "sort_descending": "<PERSON><PERSON><PERSON>", "move_left": "Sola taşı", "move_right": "<PERSON>ğa ta<PERSON>ı", "hide_in_view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gizle", "search": "<PERSON><PERSON> yap", "results": "<PERSON><PERSON><PERSON>", "table_items": {"card_hash": "<PERSON><PERSON>", "phone": "Telefon", "first_name": "Ad", "last_name": "Soyad", "admin_id": "Admin ID", "external_reference_id": "Harici Referans ID", "organization_id": "Organizasyon", "user_id": "Kullanıcı ID", "name": "İsim", "identity_number": "<PERSON><PERSON>", "last_login_time": "<PERSON> <PERSON><PERSON><PERSON>", "last_login_ip": "<PERSON> G<PERSON>", "is_user_object": "Kullanıcı Nesnesi mi?", "is_success": "Başarılı mı?", "descriptor": "Cüzdan Numarası", "category": "<PERSON><PERSON><PERSON>", "interval": "Çalışma Zaman Aralığı", "labels": "<PERSON><PERSON><PERSON><PERSON>", "tab": "Tab", "start_date": "Başlangıç <PERSON>", "end_date": "Bitiş Tarihi", "status": "Durum", "payment_channel": "Ö<PERSON>me <PERSON>", "min_amount": "Minimum Tutar", "max_amount": "<PERSON><PERSON><PERSON><PERSON>", "reference_id": "Referans ID", "conversation_id": "İşlem Referans Numarası", "entity": "Entity", "entity_id": "Entity ID", "action": "Aksiyon", "maker": "Oluşturan", "checker": "<PERSON><PERSON><PERSON>", "report_type": "<PERSON><PERSON>", "organization_name": "Organizasyon Adı", "account": "<PERSON><PERSON><PERSON>", "role": "Rol", "amount": "<PERSON><PERSON>", "created": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updated_at": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "Üst Öğe", "title": "Başlık", "currency": "Para Birimi", "percentage": "<PERSON><PERSON>z<PERSON>", "sender": "<PERSON><PERSON><PERSON><PERSON>", "sender_wallet_id": "Gönderen Cüzdan ID", "receiver": "Alıcı", "receiver_wallet_id": "Alıcı Cüzdan ID", "balance": "Bakiye", "locked": "<PERSON><PERSON><PERSON>", "bank_name": "Banka Adı", "user": "Kullanıcı", "default": "Varsayılan", "date": "<PERSON><PERSON><PERSON>", "email": "E-Posta", "type": "Tip", "acquirer": "Acquirer", "organization": "Organizasyon", "active": "Aktif", "buyer_email": "Alıcı E-Posta Adresi", "paid_amount": "Ödenen <PERSON>", "bank": "Banka", "card_type": "<PERSON>rt <PERSON>", "holder_name": "<PERSON><PERSON> <PERSON>", "brand": "<PERSON><PERSON>", "provider": "Sağlayıcı", "provider_type": "Sağlayıcı Tipi", "subscriber_name": "<PERSON><PERSON> Adı", "subscriber_surname": "Abone Soyadı", "invoice_number": "<PERSON><PERSON>", "is_active": "Filtre Aktif Mi?", "target_field": "<PERSON><PERSON><PERSON>", "process_type": "İşlem Tipi", "trn_type": "İşlem Türü", "target_field_value": "<PERSON>", "enabled": "Etkinleştirilmiş mi?", "vpos_id": "VPOS"}, "end_date": "Bitiş Tarihi"}, "data": {"name": "İsim", "organization": "Organizasyon", "enabled": "<PERSON><PERSON><PERSON>", "sql_query": "SQL Sorgusu", "execute_query": "<PERSON><PERSON><PERSON>u <PERSON>ıştır", "write_on_your_sql_query_here": "SQL Sorgunuzu Buraya Yazın", "export_import": {"export": "Export", "import": "Import", "export_success": "veriler başarılı şekilde dışarı aktarıldı.", "import_success": "veriler başarılı şekilde içeriye aktarıldı.", "import_error": "veriler içeri aktarılırken bir hata oluştu.", "export_error": "veriler dışarı aktarılırken bir hata oluştu.", "file_not_found": "İçeri aktarım için dosya bulunamadı.", "data_not_found": "<PERSON><PERSON> bulunamadı", "invalid_file_format": "Geçersiz dosya formatı. Lütfen geçerli bir JSON dosyası yükleyin.", "errors": {"invalid_json_format": "Geçersiz JSON Formatı. metadata ve data alanları gerekli. "}, "import_preview": "İçe Aktarma Önizleme", "data_to_import": "İçeri aktarılacak veriler", "file": "<PERSON><PERSON><PERSON>", "total_records": "Toplam Kayıt", "version": "Versiyon", "export_info_1": "Tüm verileriniz JSON formatında dışa aktarılacaktır.", "export_info_2": "Export dosyası metadata bilgilerini ve SQL sorgularınızı içerir.", "export_info_3": "Dosya başka ortamlarda import edilebilir ve yedekleme amacıyla kullanılabilir.", "data_ready_for_upload": "kayıt yüklenmeye hazır", "select_data_id": "Data ID Seçin", "execute_query": "<PERSON><PERSON><PERSON>u <PERSON>ıştır", "download_as_excel": "Excel Olarak <PERSON>"}}, "users": {"limit": "Kullanıcı Limiti", "name": "Kullanıcı Adı", "email": "Kullanıcı E-posta", "first_name": "Ad", "last_name": "Soyad", "account": "Kullanıcı Hesabı", "organization": "Kullanıcı Organizasyonu", "role": "Kullanıcı Rolü", "department": "Kullanıcı Departmanı", "two-factor": "Two-Factor", "two-factor_key": "Two-Factor Key", "suspended": "Askıya Alındı", "suspended_reason": "Askıya Alınma Nedeni", "is_organization_user": "Organizasyon Kullanıcısı", "user_info": "Kullanıcı Bilgileri", "identity_number": "<PERSON><PERSON>", "last_login": "<PERSON>", "last_login_ip": "<PERSON> G<PERSON>", "last_login_time": "<PERSON> <PERSON><PERSON><PERSON>"}, "roles": {"role_name": "Rol Adı", "organization": "Rol Organizasyonu", "organization_required": "Rol Organizasyonu seçiniz", "permissions": "İzinleri", "role_permissions_description": "<PERSON><PERSON> r<PERSON><PERSON><PERSON> i<PERSON> se<PERSON>", "permission_create": "Oluşturma İzni", "permission_read": "Okuma İzni", "permission_update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permission_delete": "<PERSON><PERSON><PERSON>", "entities": {"organization": "Organizasyon", "user": "Kullanıcı", "role": "Rol", "admin": "Yönetici", "log": "Log", "profile": "Profil", "filter": "Filtre", "session": "O<PERSON><PERSON>", "event": "<PERSON><PERSON>"}, "cannot_deleted": "Bu rol ile ilişkili veriler olduğu i<PERSON>."}, "access_token": {"name": "İsim", "expire_day": "<PERSON>"}, "scheduled_works": {"name_of_work": "G<PERSON>rev Adı", "each_x_time": "Her X Saat", "request": "İstek", "start_date": "Başlangıç <PERSON>", "end_date": "Bitiş Tarihi", "how_many_time_to_try": "<PERSON><PERSON>", "total_work": "Toplam İş", "total_success": "Toplam Başarı", "total_error": "<PERSON><PERSON> Hata", "validation": {"start_date_past": "Başlangıç tarihi geçmişte olamaz", "start_date_required": "Lütfen önce başlangıç tarihini seçin", "end_date_invalid": "Bitiş tarihi ba<PERSON><PERSON><PERSON><PERSON> tarihinden sonra olmalıdır", "fix_errors": "Lütfen göndermeden önce doğrulama hatalarını düzeltin"}}, "simulates": {"id": "ID", "name": "İsim", "created_at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version_name": "Versiyon Adı", "access_token": "<PERSON><PERSON><PERSON><PERSON>", "organization_name": "Organizasyon Adı", "changed_by": "Değiştiren", "status": "Durum", "last_working_time": "Son Çalışma Zamanı", "go_to_simulate": "Simülas<PERSON><PERSON>", "tabs": {"overview": "Genel Bakış", "logs": "Loglar", "versions": "Versiyonlar", "default-request": "Varsayılan İstek", "statistics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retry-management": "<PERSON><PERSON><PERSON>", "scheduled-works": "Zamanlanmış Görevler"}, "default_request": {"title": "Varsayılan İstek", "description": "Bu simülasyon için test amaçlı kullanılacak varsayılan istek JSON'unu yapılandırın.", "create": "Varsayılan İstek Oluştur", "json_editor": "İstek JSON", "empty": {"title": "Varsayılan İstek Yok", "description": "Simülasyonunuzu önceden tanımlanmış verilerle hızlıca test etmek için varsayılan bir istek JSON'u oluşturun."}, "success": {"saved": "Varsayılan istek başarıyla kaydedildi!", "deleted": "Varsayılan istek başarıyla silindi!"}, "error": {"load_title": "Yükleme Başarısız", "load_message": "Varsayılan istek verileri yüklenemedi.", "save": "Varsayılan istek kaydedilemedi. Lütfen tekrar deneyin.", "delete": "Varsayılan istek silinemedi. Lütfen tekrar deneyin."}, "confirm": {"delete": "Varsayılan isteği silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."}}, "statistics": {"chart1_title": "Başarı ve Hata Oranları", "chart1_description": "<PERSON><PERSON> gra<PERSON><PERSON>, zaman içindeki başarılı ve hatalı işlem sayılarını göstermektedir.", "chart2_title": "İstek Protokol Oranları", "chart2_description": "<PERSON><PERSON> gra<PERSON><PERSON>, HTTP ve gRPC protokolleri üzerinden gelen isteklerin oranlarını göstermektedir."}}}