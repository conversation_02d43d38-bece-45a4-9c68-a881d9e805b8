{"dashboard": {"welcome": "Welcome"}, "form": {"validation_err": "Validation Error. Please check your informations and try again.", "an_error_occurred": "An error occurred. Please try again later.", "internal_error": "Internal Error", "unsaved_changes": "You have unsaved changes.", "discard_changes": "Discard changes", "unsaved_changes_confirm": "You have unsaved changes. Do you want to leave this page?"}, "auth": {"email": "Email address", "password": "Password", "password_confirmation": "Password Confirmation", "magic_login": "Magic Login", "info": "Please enter your email address and select login method", "pin_login": "<PERSON><PERSON>", "challenge_title": "Authentication Challenge", "challenge_description": "Please select your authentication method", "two_factor_login": "Two-Factor Login", "two_factor_login_description": "Select for login with Two-Factor Authentication", "pin_login_description": "Select for login with PIN", "processing": "Processing...", "forgot_password_success": "We sent a code to your email address. Please enter the code and your new password.", "magic": "We sent a magic link to your email that will let you access your account.", "check_mail": "Please check your email and click the link to continue.", "check_mail_pin": "Please check your email and enter the PIN code to continue.", "two_factor_authentication": "Please enter the code generated by your authenticator app.", "please_enter_password": "Please enter your password", "please_enter_pin_code": "Please enter your PIN code", "please_enter_email": "Please enter your email address", "please_enter_two_factor_code": "Please enter the code generated by your authenticator app.", "forgot_password": "Forgot Password ?", "forgot_password_description": "Enter your email address and we'll send you a link to reset your password.", "reset_password": "Reset Password", "reset_password_description": "We send a code to your email address. Please enter the code and your new password.", "private_browsing": "If you sign in temporarily on a computer, phone, or tablet that doesn't belong to you, use a private browsing window.", "session_drop": "Your session has expired or you have logged in from another device.", "2fa": {"title": "Two-Factor Authentication", "description": "Please enter the code generated by your authenticator app.", "verify_code_placeholder": "Verification Code", "verify_code_button": "Verify", "login": "Log In", "verify_code_error": "Verification code is invalid."}, "pin": {"title": "<PERSON><PERSON>", "description": "We sent a pin code to your email address. Please enter the code.", "code": "Pin Code", "code_placeholder": "Enter your pin code"}, "errors": {"check_data": "Entered information is incorrect. Please check your information and try again."}, "identifier": {"title": "Identifier", "description": "Please enter your email address"}, "password_page": {"title": "Password", "description": "Please enter your password", "use_ldap": "Use LDAP Authentication"}, "password_reset_code": "Password Reset Code", "tfa_add_forced_title": "Two-Factor Authentication", "tfa_add_forced_description": "Scan the QR code with your authenticator app and enter the code to enable two-factor authentication."}, "profile": {"profile": {"title": "Profile", "name": "Name", "first_name": "First Name", "last_name": "Last Name", "email": "Email"}}, "routes": {"dashboard": "Dashboard", "organization-list": "Organizations", "access-token-list": "Access Tokens", "simulate-list": "Simulates", "workspace": "Workspace", "default-request-list": "Default Requests", "admin-list": "Admins", "settings": "Settings", "log-list": "Logs", "pars-gpt": "ParsGPT", "useful-information": "Useful Information", "error-log-list": "<PERSON><PERSON><PERSON>", "profile": "Profile", "signout": "Sign Out", "data-view": "Data"}, "organizations": {"id": "ID", "name": "Organization Name", "parent": "Parent Organization", "logo": "Logo", "favicon": "Favicon", "no_logo": "Logo not found", "timezone": "Timezone", "domain_address": "Domain Address", "name_min_length": "Organization Name must be at least 2 characters long", "min_authorization_methods": "At least one authorization method must be enabled", "organization_is_sub_org": "The Parent Organization you selected cannot be saved as a parent organization because it is a sub-organization of this organization.", "health_checks": {"title": "Health Checks"}, "settings": {"title": "Settings", "general": {"tab": "General", "title": "General Settings", "description": "General settings for the organization.", "organization_name": "Organization Name", "parent_organization": "Parent Organization", "logo": "Logo", "favicon": "Favicon", "footer_text": "Footer Text", "allow_registration": "Allow Registration", "allow_registration_desc": "Determines if users can register in this organization.", "allow_limit_check": "Limit Checks", "allow_limit_check_desc": "Determines if this organization performs limit checks."}, "authentication": {"tab": "Authentication", "title": "Authentication Settings", "description": "Authentication settings to manage how users authenticate with your organization."}, "authorization": {"tab": "Authorization", "title": "Authorization Settings", "description": "Authorization settings to manage how users are authorized to access resources."}, "design": {"tab": "Design", "title": "Design Settings", "description": "Manage how your organization and applications looks. You can define multiple styles and configurations."}, "terminate": {"tab": "Terminate", "title": "Terminate Organization", "description": "This section allows you to permanently delete the organization and all its data.", "terminate_organization": "Terminate Organization", "terminate_organization_desc": "Are you sure you want to delete this organization? This action cannot be undone."}}}, "admins": {"name": "Admin User Name", "first_name": "Admin User First Name", "last_name": "Admin User Last Name", "email": "Admin User Email", "locale": "Admin User Language", "account": "Admin User Account", "organization": "Admin User Organization", "role": "Admin User Role", "department": "Admin User Department", "two-factor": "Two-Factor Authentication", "two-factor_key": "Two-Factor Key", "password": "Password", "cannot_deleted": "This admin cannot be deleted as it is correlated data.", "suspended_reason": "Suspended Reason", "force_logout": "Force Logout", "suspend": "Suspend", "suspended": "Suspended", "not_suspended": "Not Suspended", "reset_password": "Sent Reset Password Email", "reset_password_mail_sent": "Reset password email sent"}, "page_titles": {"organizations": "Organizations", "organizations_health_check": "Health Check", "users": "Users", "access-tokens": "Access Tokens", "users_create": "Create User", "users_edit": "Edit User", "users_view": "View User", "roles": "Roles", "roles_create": "Create Role", "roles_edit": "Edit Role", "admins": "Admins", "logs": "Logs", "error-logs": "<PERSON><PERSON><PERSON>", "simulates": "Simulates"}, "general": {"select": "Please Select", "copy": "Copy", "copied": "<PERSON>pied", "copy_failed": "Co<PERSON> failed", "view_details": "View Details", "updated_at": "Updated At", "failed": "Failed", "rows_affected": "{ count } rows affected", "details": "Details", "unknown": "Unknown", "status": "Status", "processing": "Processing", "go_last_version": "Go Last Version", "delete_confirm": "Are you sure you want to delete this item?", "delete_label": "You are about to delete { item } . This action cannot be undone. Are you sure you want to continue ?", "delete_success": "Item deleted successfully!", "delete_error": "Failed to delete item. Please try again.", "reject_label": "You are about to reject { item } . Are you sure you want to continue ?", "reject_confirm": "Are you sure you want to reject this item?", "approve_label": "You are about to approve { item } . This action cannot be undone. Are you sure you want to continue ?", "approve_confirm": "Are you sure you want to approve this item?", "actions": "Actions", "show_page": "Showing page { page } of the { total_pages }", "step": "Step { step } of { total_steps }", "no_records": "No records found", "no_records_description": "There are no records to display in this section.", "update": "Update", "create": "Create", "delete": "Delete", "cancel": "Cancel", "yes": "Yes", "no": "No", "add": "Add", "save": "Save", "retry": "Retry", "edit": "Edit", "in_progress": "In Progress", "success": "Success", "fail": "Fail", "view": "View", "back": "Back", "anErrorOccurred": "An error occurred", "close": "Close", "next": "Next", "previous": "Previous", "search": "Search", "search_placeholder": "Search...", "date_range": "Date Range", "field_required": "{ field } is required", "field_invalid": "{ field } is invalid", "invalid_input": "Invalid input", "fill_required_fields": "Required fields must be filled", "welcome": "Welcome { name }", "pay": "Pay", "incomes": "Incomes", "filter": "Filter", "clear_filters": "Clear Filters", "issue": "Issue", "reindex": "Reindex", "loading": "Loading...", "please_wait_while_load": "Please wait while we load the data.", "active": "Active", "passive": "Passive", "change": "Change", "choose_file": "Choose <PERSON>", "file_not_chosen": "No file chosen", "accepted_extensions": "Accepted extensions: { extensions }", "file_input_disabled": "File input disabled", "please_choose_file": "Please choose a file", "invalid_file_extension": "Invalid file extension", "file_upload_error": "File upload error", "please-select": "Please select", "select_placeholder": "Select one", "download": "Download", "created_at": "Created At", "approve": "Approve", "name": "Name", "surname": "Surname", "reject": "Reject", "internal_error": "An error occurred. Please try again later.", "name_already_exist": "Name already exists.", "organization": "Organization", "no_data": "No data", "no_data_description": "There is no data to display", "you_have_not_created_any": "You haven't created any data yet. Create your first data to get started.", "continue": "Continue", "server_error": "Server Error", "server_error_description": "Sorry, we encountered an internal server error. Please try again later.", "refresh_page": "Refresh Page", "page_not_found": "Page Not Found", "page_not_found_description": "Sorry, we couldn't find the page you're looking for.", "back_to_home": "Back to Home", "labels": "Labels", "something_went_wrong": "Something went wrong", "try_again": "Please try again later.", "select_organization": "Select Organization", "import": "Import", "export": "Export"}, "password": {"strength": "The password is {strength_name}", "suggest": "Suggest password", "copied_to_clipboard": "The password has copied to clipboard", "too_weak": "Too weak", "weak": "Weak", "medium": "Medium", "strong": "Strong"}, "search_menu": {"placeholder": "Search", "parent_menu": "<PERSON><PERSON>", "no_result": "No Result"}, "table": {"no_related_record": "No related records found", "add_record_link": "Add a record", "wait_for_record": "Please wait for a record to be added", "table_settings": "Table Settings", "table_cols": "Table Columns", "record_per_page": "Record Per Page", "select_all": "Select All", "select": "Select", "all": "All", "more": "more", "filters_title": "Table Filters", "do_filter": "Filter", "close_filter": "Close", "clear_filter": "Clear", "clear_all_filters": "Clear All Filters", "applied_filters": "Applied Filters", "filters": {"payment_channel": {"0": "", "1": "Vpos", "2": "Money Transfer", "3": "Wallet", "4": "Loan", "5": "<PERSON>n", "6": "Remittance"}, "status": {"1": "Received", "2": "Unpaid", "3": "Paid", "4": "Processing", "5": "Shipped", "6": "On Hold", "7": "Waiting for Payment", "8": "Cancelled", "9": "Completed", "10": "Refunded", "11": "<PERSON><PERSON>", "12": "Rejected", "13": "Failure", "14": "Retrying", "15": "Partial Refunded", "16": "Sub merchant payment approved", "17": "Sub merchant payment disapproved", "18": "Sub merchant payment errored", "19": "Still has unpaid installments", "20": "Still has unpaid terms", "21": "Expired", "22": "Still has unpaid sub merchant payments", "23": "Partially Paid", "24": "Terminated", "25": "Card Tokenization", "26": "Pre Authorized", "27": "Disputed", "28": "Partially Disputed"}}, "sort_ascending": "Sort ascending", "sort_descending": "Sort descending", "move_left": "Move left", "move_right": "Move right", "hide_in_view": "Hide in view", "search": "Search", "results": "results", "table_items": {"card_hash": "<PERSON>", "phone": "Phone", "first_name": "First Name", "last_name": "Last Name", "admin_id": "Admin ID", "external_reference_id": "External Reference ID", "payment_transaction_id": "Payment Transaction ID", "organization_id": "Organization", "user_id": "User ID", "name": "Name", "identity_number": "Identity Number", "last_login_time": "Last Login Time", "last_login_ip": "Last Login IP", "is_user_object": "Is User Object", "descriptor": "Descriptor", "is_success": "Is Success", "labels": "Labels", "account_number": "Account Number", "iban": "IBAN", "category": "Category", "interval": "Work Interval", "tab": "Tab", "start_date": "Start Date", "end_date": "End Date", "status": "Status", "payment_channel": "Payment Channel", "min_amount": "<PERSON>", "max_amount": "<PERSON>", "reference_id": "Reference ID", "conversation_id": "Conversation ID", "entity": "Entity", "entity_id": "Entity ID", "action": "Action", "maker": "Maked By", "checker": "Checked By", "report_type": "Report Type", "organization_name": "Organization Name", "account": "Account", "role": "Role", "amount": "Amount", "created": "Created Date", "created_at": "Created Date", "updated_at": "Updated Date", "parent": "Parent", "title": "Title", "currency": "<PERSON><PERSON><PERSON><PERSON>", "percentage": "Percentage", "sender": "Sender", "sender_wallet_id": "Sender Wallet ID", "receiver": "Receiver", "receiver_wallet_id": "Receiver Wallet ID", "balance": "Balance", "locked": "Locked", "bank_name": "Bank Name", "user": "User", "default": "<PERSON><PERSON><PERSON>", "date": "Date", "email": "E-Mail", "type": "Type", "acquirer": "Acquirer", "organization": "Organization", "active": "Active", "buyer_email": "Buyer E-Mail", "paid_amount": "<PERSON><PERSON>", "bank": "Bank", "card_type": "Card Type", "holder_name": "Holder Name", "brand": "Brand", "provider": "Provider", "provider_type": "Provider Type", "subscriber_name": "Subscriber Name", "subscriber_surname": "Subscriber Surname", "invoice_number": "Invoice Number", "is_active": "Activated?", "target_field": "Target Field", "process_type": "Process Type", "trn_type": "Transaction Type", "target_field_value": "Target Field Value", "enabled": "Enabled?", "vpos_id": "VPOS"}, "end_date": "End Date"}, "data": {"name": "Name", "organization": "Organization", "enabled": "Enabled", "sql_query": "SQL Query", "execute_query": "Execute Query", "write_on_your_sql_query_here": "Write your SQL query here", "export_import": {"export": "Export", "import": "Import", "export_success": "data export successful.", "import_success": "data import successful.", "import_error": "Error occurred during data import.", "export_error": "Error occurred during data export.", "file_not_found": "File not found for import.", "data_not_found": "Data not found", "invalid_file_format": "Invalid file format. Please upload a valid JSON file.", "errors": {"invalid_json_format": "Invalid JSON Format. Metadata and data fields required. "}, "import_preview": "Import Preview", "data_to_import": "Data to import", "file": "File", "total_records": "Total Records", "version": "Version", "export_info_1": "All your data will be exported in JSON format.", "export_info_2": "Export file contains metadata information and your SQL queries.", "export_info_3": "The file can be imported on other environments and used for backup purposes.", "data_ready_for_upload": "data ready for upload", "select_data_id": "Select Data ID", "execute_query": "Execute Query", "download_as_excel": "Download as Excel"}}, "users": {"limit": "User Limit", "name": "User Name", "email": "User Email", "first_name": "User First Name", "last_name": "User Last Name", "account": "User Account", "organization": "User Organization", "role": "User Role", "department": "User Department", "two-factor": "Two-Factor Authentication", "two-factor_key": "Two-Factor Key", "suspended": "Is Suspended", "suspended_reason": "Suspended Reason", "is_organization_user": "Is Organization User", "user_info": "User Info", "identity_number": "Identity Number", "last_login": "Last Login", "last_login_ip": "Last Login IP", "last_login_time": "Last Login Time"}, "roles": {"role_name": "Role Name", "organization": "Role Organization", "organization_required": "Please select an organization", "permissions": "Permissions", "role_permissions_description": "Select permissions for this role", "permission_create": "Create", "permission_read": "Read", "permission_update": "Update", "permission_delete": "Delete", "entities": {"organization": "Organization", "user": "User", "role": "Role", "admin": "Admin", "log": "Log", "profile": "Profile", "filter": "Filter", "session": "Session", "event": "Event"}, "cannot_deleted": "This role cannot be deleted as it is correlated data."}, "access_token": {"name": "name", "expire_day": "Expire Day"}, "scheduled_works": {"name_of_work": "Name of Work", "each_x_time": "Each X Time", "request": "Request", "start_date": "Start Date", "end_date": "End Date", "how_many_time_to_try": "How Many Time to Try", "total_work": "Total Work", "total_success": "Total Success", "total_error": "Total Error", "validation": {"start_date_past": "Start date cannot be in the past", "start_date_required": "Please select a start date first", "end_date_invalid": "End date must be after start date", "fix_errors": "Please fix the validation errors before submitting"}}, "simulates": {"id": "ID", "name": "Name", "created_at": "Created At", "version_name": "Version Name", "access_token": "Access Token", "organization_name": "Organization Name", "changed_by": "Changed By", "status": "Status", "last_working_time": "Last Working Time", "go_to_simulate": "Go to Simulate", "tabs": {"overview": "Overview", "logs": "Logs", "versions": "Versions", "default-request": "De<PERSON>ult Request", "statistics": "Statistics", "retry-management": "Retry Management", "scheduled-works": "Scheduled Works"}, "default_request": {"title": "De<PERSON>ult Request", "description": "Configure the default request JSON that will be used for testing this simulate.", "create": "Create De<PERSON>ult Request", "json_editor": "Request JSON", "empty": {"title": "No Default Request", "description": "Create a default request JSON to quickly test your simulate with predefined data."}, "success": {"saved": "De<PERSON>ult request saved successfully!", "deleted": "De<PERSON>ult request deleted successfully!"}, "error": {"load_title": "Failed to Load", "load_message": "Could not load the default request data.", "save": "Failed to save default request. Please try again.", "delete": "Failed to delete default request. Please try again."}, "confirm": {"delete": "Are you sure you want to delete the default request? This action cannot be undone."}}, "statistics": {"chart1_title": "Success vs Error Rates", "chart1_description": "This chart shows the number of successful and failed operations over time.", "chart2_title": "Request Protocol Rates", "chart2_description": "This graph shows the rate of requests coming in via HTTP and gRPC protocols."}}}