let newLocal = null
let newLocalV2 = null
let wslocal = null

if (window.location.host == "localhost:7000") {
    newLocal = `${window.location.protocol}//${window.location.hostname}:4000/api/v1`;
    newLocalV2 = `${window.location.protocol}//${window.location.hostname}:4000/api/v1`;
} else {
    if (window.location.hostname == "localhost") {
        newLocal = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/api/v1`;
        newLocalV2 = `${window.location.protocol}//${window.location.hostname}:${window.location.port}/api/v1`;
    } else {
        newLocal = `${window.location.protocol}//${window.location.hostname}/api/v1`;
        newLocalV2 = `${window.location.protocol}//${window.location.hostname}/api/v1`;
    }
}


if (window.location.protocol === "http:") {
    if (window.location.port === "80" || window.location.port === "") {
        wslocal = `ws://${window.location.hostname}/api/v1`;
    } else if (window.location.port === "7000") {
        wslocal = `ws://${window.location.hostname}:4000/api/v1`;
    } else {
        wslocal = `ws://${window.location.hostname}:${window.location.port}/api/v1`;
    }

} else {
    // if window location port is 80
    if (window.location.port === "80" || window.location.port === "") {
        wslocal = `wss://${window.location.hostname}/api/v1`;
    } else {
        wslocal = `wss://${window.location.hostname}:${window.location.port}/api/v1`;
    }
}

export const wssUrl = wslocal;
export const apiUrl = newLocal;
export const apiUrlV2 = newLocalV2;
export const defaultLocale = "en";
export const localeOptions = [{ id: "en", name: "English" }, { id: "tr", name: "Türkçe" }];