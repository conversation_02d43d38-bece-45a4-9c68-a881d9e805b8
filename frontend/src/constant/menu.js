import mainRoutes from "../router/main";
import customRoutes from "../router/custom";

const mainMenuItems = [{ name: "dashboard" }];
const dropdownMenuItems = [];

export { mainMenuItems, dropdownMenuItems };

const user = JSON.parse(localStorage.getItem("user"));
for (let i = 0; i < user.role.permissions.length; i++) {
  if (user.role.permissions[i].read) {
    const entity = user.role.permissions[i].entity;
    const mainRoute = mainRoutes.find((route) => route.meta.entity === entity);
    const customRoute = customRoutes.find(
      (route) => route.meta.entity === entity
    );

    if (mainRoute) {
      mainMenuItems.push({ ...mainRoute, name: mainRoute.path });
    } else if (customRoute) {
      dropdownMenuItems.push({ ...customRoute, name: customRoute.path });
    }
  }
}
