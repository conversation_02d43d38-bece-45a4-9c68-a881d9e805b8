.noring {
  @apply focus:outline-none focus:ring-0 focus:ring-offset-transparent focus:ring-transparent;
}

.t-input {
  @apply block w-full py-2 px-2.5 text-xs rounded-lg border-stone-200 text-stone-800 placeholder:text-stone-500 focus:z-10 focus:border-green-600 focus:ring-green-600 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600;
}

.t-table-search-input {
  @apply py-[7px] px-3 ps-10 block w-full text-sm rounded-lg border-stone-200 text-stone-800 placeholder:text-stone-500 focus:z-10 focus:border-green-600 focus:ring-green-600 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600;
}

.input-text {
  @apply transition focus:outline-none focus:shadow-md border-gray-300  focus:border-blue-600 focus:shadow-blue-600 focus:ring-blue-600
    block w-full shadow-sm sm:text-sm  dark:border-gray-600  dark:bg-gray-800  rounded-md;
}

.fake-input {
  @apply flex items-center justify-between w-full px-2 py-1 text-sm rounded-lg border-stone-200 text-stone-800 placeholder:text-stone-500 focus:z-10 focus:border-green-600 focus:ring-green-600 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-200 dark:placeholder:text-neutral-500 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600;
}

.mono-text {
  @apply focus:outline-none block w-full text-sm  focus:bg-gray-200 dark:bg-gray-800 dark:focus:bg-gray-900 rounded-md disabled:cursor-not-allowed
disabled:bg-gray-600 dark:disabled:bg-gray-400 disabled:select-none;
}

.changed-input {
  @apply transition focus:outline-none focus:shadow-md border-yellow-500  focus:border-yellow-600 focus:shadow-yellow-600 focus:ring-yellow-600
    block w-full shadow-sm sm:text-sm  dark:border-yellow-400 dark:bg-gray-800  rounded-md;
}

.input-error {
  @apply border-red-500 dark:border-red-600;
}

.input-text-sm {
  @apply mt-1 transition 
    shadow-sm sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 rounded-md;
}

.table {
  @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
}

.table-head {
  @apply bg-gray-800 dark:bg-gray-800 text-gray-50 border-b border-gray-600 text-left;
}

.table-body {
  @apply bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700 text-left;
}

.table-col {
  @apply px-6 py-3 text-xs  tracking-wider  transition;
}

.table-rows {
  @apply px-6 py-4 whitespace-nowrap;
}

.table-tr {
  @apply hover:bg-gray-300 dark:hover:bg-gray-600 cursor-pointer select-none transition;
}

.text-area {
  @apply shadow-sm focus:ring-0 focus:ring-offset-transparent focus:ring-transparent mt-1 block w-full min-h-[128px] sm:text-sm border border-gray-300 dark:border-gray-600 dark:bg-gray-800 rounded-md;
}

.text-area-required {
  @apply border-red-500 dark:border-red-600;
}

.select {
  @apply mt-1 block w-full py-2 px-3 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500  sm:text-sm;
}

.changed-select {
  @apply mt-1 block w-full py-2 px-3 border border-yellow-500 dark:border-yellow-400 bg-white dark:bg-gray-800 rounded-md shadow-sm focus:outline-none focus:ring-yellow-500 focus:border-yellow-500  sm:text-sm;
}

.check {
  @apply h-4 w-4 text-indigo-600  cursor-pointer border-gray-300 rounded dark:border-gray-600 dark:bg-gray-500;
}

.card {
  @apply bg-white dark:bg-gray-800 shadow rounded-md p-3;
}

.info-card {
  @apply bg-white dark:bg-gray-800 rounded-md shadow p-3 flex flex-col items-center;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.input-code {
  @apply transition text-sm  w-14 h-12 text-center focus:outline-none focus:shadow-md border-gray-300  focus:border-blue-600 focus:shadow-blue-600 focus:ring-blue-600
  rounded-md  dark:border-gray-600  dark:bg-gray-800;
}

.apexcharts-svg {
  background: transparent !important;
}

.col-span-3 {
  @apply space-y-2;
}
.col-span-6 {
  @apply space-y-2;
}
.col-span-9 {
  @apply space-y-2;
}
.col-span-12 {
  @apply space-y-2;
}

.responsive-text {
  font-size: calc(16px + (24 - 16) * ((100vw - 320px) / (1920 - 320)));
}

.proser {
  @apply prose prose-headings:p-0 prose-pre:p-0 prose-headings:text-gray-50 prose-p:text-gray-300 prose-a:text-indigo-400
  prose-blockquote:text-gray-500 prose-code:text-indigo-500 prose-ol:text-gray-50 prose-li:text-gray-200 prose-strong:text-gray-300;
}

.github-theme img {
  @apply !bg-transparent;
}

#filters-wrapper {
  @apply sm:!block;
}

.btn-sml {
  @apply py-1.5 px-3 inline-flex items-center gap-x-2 text-xs font-medium rounded-lg border
  border-transparent text-white disabled:opacity-50 disabled:pointer-events-none
  focus:outline-none focus:ring-2 transition-colors;
}

.btn-sml.red {
  @apply bg-red-600 hover:bg-red-700 focus:ring-red-500;
}

.btn-sml.purple {
  @apply bg-purple-600 hover:bg-purple-700 focus:ring-purple-500;
}

.btn-sml.pink {
  @apply bg-pink-600 hover:bg-pink-700 focus:ring-pink-500;
}

.btn-sml.orange {
  @apply bg-orange-600 hover:bg-orange-700 focus:ring-orange-500;
}

.btn-sml.rose {
  @apply bg-rose-600 hover:bg-rose-700 focus:ring-rose-500;
}

.btn-sml.green {
  @apply bg-green-600 hover:bg-green-700 focus:ring-green-500;
}

.btn-sml.yellow {
  @apply bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-sml.blue {
  @apply bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-sml.gray {
  @apply bg-gray-600 hover:bg-gray-700 focus:ring-gray-500;
}

.btn-sml.indigo {
  @apply bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500;
}

.btn-sml.emerald {
  @apply bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500;
}

.tfa-code-input {
  @apply block w-[38px] text-center border-gray-200 rounded-md sm:text-sm focus:border-indigo-500 focus:ring-indigo-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600;
}

.t-switch {
  @apply relative mt-1 shrink-0 w-[44px] h-6 p-px bg-gray-100 border-transparent text-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:ring-blue-600 
  disabled:opacity-50 disabled:pointer-events-none checked:bg-none checked:text-blue-600 checked:border-blue-600 focus:checked:border-blue-600 dark:bg-neutral-700 dark:border-neutral-700 dark:checked:bg-blue-500 dark:checked:border-blue-500 dark:focus:ring-offset-neutral-600
  before:inline-block before:size-5 before:bg-white checked:before:bg-white before:translate-x-0 checked:before:translate-x-full before:rounded-full before:shadow before:transform before:ring-0 
  before:transition before:ease-in-out before:duration-200 dark:before:bg-neutral-400 dark:checked:before:bg-white;
}

.vjs-tree-node.is-highlight, .vjs-tree-node:hover{
  @apply hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300;
}