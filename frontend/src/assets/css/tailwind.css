@tailwind base;
@tailwind components;
@tailwind utilities;

* {
    @apply font-sans outline-none;
    scrollbar-width: thin;
}

body {
    @apply min-h-screen bg-gray-50 dark:bg-gray-950
  dark:text-gray-50 text-gray-900 subpixel-antialiased;
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgb(38 38 38);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgb(64 64 64);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgb(82 82 82);
}


.is-loading {
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.5) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    background-size: 75% 100%;
    animation-duration: 1500ms;
    animation-name: headerShine;
    animation-iteration-count: infinite;
    background-repeat: no-repeat;
    animation-timing-function: ease;
    background-position: 0 0;
    background-blend-mode: overlay;
    @apply bg-green-700;
}

.progress__bar {
    background-color: #0dbf1b;
    height: 10px;
    width: 50%;
    border-radius: 100px;
}

@keyframes headerShine {
    0% {
        background-position: -300% 0;
    }
    100% {
        background-position: 500% 0;
    }
}
