//isEmpty null undefined "" [] {} are empty
export function isEmpty(value) {
    return (
        value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "") ||
        (Array.isArray(value) && value.length === 0) ||
        (typeof value === "object" && Object.keys(value).length === 0)
    );
}

//if is empty return -
export function emptyDash(value) {
    return isEmpty(value) ? "-" : value;
}
