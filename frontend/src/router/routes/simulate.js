const simulate = [
    {
        path: "/simulate",
        name: "simulate-list",
        component: () => import("@/views/app/simulate/list.vue"),
    },
    {
        path: "/simulate/:id",
        name: "simulate-view",
        component: () => import("@/views/app/simulate/view.vue"),
        props: true
    },
    {
        path: "/simulate/log/:id",
        name: "simulate-log-detail",
        component: () => import("@/views/app/simulate/log-detail.vue"),
        props: true
    },
    {
        path: "/workspace",
        name: "workspace",
        component: () => import("@/views/app/simulate/workspace.vue"),
    },
    {
        path: "/workspace/:id",
        name: "workspace-edit",
        component: () => import("@/views/app/simulate/workspace-edit.vue"),
        props: true
    }
];

export default simulate;
