const colors = require('tailwindcss/colors')
const defaultTheme = require("tailwindcss/defaultTheme");
module.exports = {
  mode: 'jit',
  future: {
    removeDeprecatedGapUtilities: true,
    purgeLayersByDefault: true,
  },
  content: [
    './index.html',
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    './node_modules/vue-tailwind-datepicker/**/*.js',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        'vtd-primary': colors.indigo,
        'vtd-secondary': colors.neutral,
        white: colors.white,
        gray: colors.zinc,
        green: colors.green,
        rose: colors.rose,
        yellow: colors.yellow,
        dark: '#1F222A',
        monoblue: '#002249'
      },
      fontFamily: {
        sans: ["Inter", ...defaultTheme.fontFamily.sans],
      },
      maxWidth: {
        '8xl': '90rem',
        '9xl': '100rem',
        '10xl': '110rem',
      },
    },
  },
  variants: {
    extend: {
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ],
}
