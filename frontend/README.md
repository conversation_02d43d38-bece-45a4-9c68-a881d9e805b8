# Mono-Vue-flow-ex Frontend

This repo is the frontend template. It is use Vue.js 3 (Composition API) & Vite.js & TailwindCSS 2.


## How to Develop

First, you need to install dependencies.

```
yarn install
```

Then, you can run the project.

```
yarn dev
```

## How to build

First, you need to install dependencies. (If already installed, skip)

```
yarn install
```

Then, you can build the project.

```
yarn build
```


## How to Add a new language
First of all, you need to add a new language to the `src/locales` directory as a json file.
After that , you need import locale file to the `src/utils/i18n.js` file.
