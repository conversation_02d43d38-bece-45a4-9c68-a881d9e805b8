/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Annotation: typeof import('./src/components/icons/annotation.vue')['default']
    ArrowRight: typeof import('./src/components/icons/arrow-right.vue')['default']
    ArrowUpDown: typeof import('./src/components/icons/arrow-up-down.vue')['default']
    Bank: typeof import('./src/components/icons/bank.vue')['default']
    Bell: typeof import('./src/components/icons/bell.vue')['default']
    Button: typeof import('./src/components/button.vue')['default']
    Calendar: typeof import('./src/components/icons/calendar.vue')['default']
    Callback: typeof import('./src/components/icons/callback.vue')['default']
    Card: typeof import('./src/components/card.vue')['default']
    CCard: typeof import('./src/components/icons/c-card.vue')['default']
    CheckCircle: typeof import('./src/components/icons/check-circle.vue')['default']
    ChevronDown: typeof import('./src/components/icons/chevron-down.vue')['default']
    ChevronLeft: typeof import('./src/components/icons/chevron-left.vue')['default']
    ChevronRight: typeof import('./src/components/icons/chevron-right.vue')['default']
    ChevronUp: typeof import('./src/components/icons/chevron-up.vue')['default']
    Close: typeof import('./src/components/icons/close.vue')['default']
    Code: typeof import('./src/components/icons/code.vue')['default']
    Cog: typeof import('./src/components/icons/cog.vue')['default']
    Collection: typeof import('./src/components/icons/collection.vue')['default']
    Constant: typeof import('./src/components/constanst/Constant.vue')['default']
    Controls: typeof import('./src/components/flow-editor/save/controls.vue')['default']
    Copy: typeof import('./src/components/icons/copy.vue')['default']
    CopyCheck: typeof import('./src/components/icons/copy-check.vue')['default']
    CornerDownRight: typeof import('./src/components/icons/corner-down-right.vue')['default']
    Create: typeof import('./src/components/icons/create.vue')['default']
    CustomizationOrganizationTab: typeof import('./src/components/organizations/customization-organization-tab.vue')['default']
    CustomizationSimulateTab: typeof import('./src/components/simulates/customization-simulate-tab.vue')['default']
    Database: typeof import('./src/components/icons/database.vue')['default']
    DeleteModel: typeof import('./src/components/delete-model.vue')['default']
    Devices: typeof import('./src/components/icons/devices.vue')['default']
    DocumentList: typeof import('./src/components/icons/document-list.vue')['default']
    Download: typeof import('./src/components/icons/download.vue')['default']
    Drag: typeof import('./src/components/icons/drag.vue')['default']
    DropzoneBackground: typeof import('./src/components/flow-editor/sidebar/dropzone-background.vue')['default']
    EditConstant: typeof import('./src/components/constanst/editConstant.vue')['default']
    Empty: typeof import('./src/components/empty.vue')['default']
    Exclamation: typeof import('./src/components/icons/exclamation.vue')['default']
    Eye: typeof import('./src/components/icons/eye.vue')['default']
    EyeOff: typeof import('./src/components/icons/eye-off.vue')['default']
    FileUser: typeof import('./src/components/icons/file-user.vue')['default']
    Filter: typeof import('./src/components/icons/filter.vue')['default']
    Fingerprint: typeof import('./src/components/icons/fingerprint.vue')['default']
    FlowEditor: typeof import('./src/components/flow-editor/flow-editor.vue')['default']
    FlowEditorEdit: typeof import('./src/components/flow-editor/flow-editor-edit.vue')['default']
    Foot: typeof import('./src/components/form/foot.vue')['default']
    Footer: typeof import('./src/components/footer.vue')['default']
    Form: typeof import('./src/components/form/form.vue')['default']
    FunctionNode: typeof import('./src/components/flow-editor/nodes/function-node.vue')['default']
    GeneralOrganizationTab: typeof import('./src/components/organizations/general-organization-tab.vue')['default']
    Github: typeof import('./src/components/icons/github.vue')['default']
    Google: typeof import('./src/components/icons/google.vue')['default']
    Header: typeof import('./src/components/form/header.vue')['default']
    Home: typeof import('./src/components/icons/home.vue')['default']
    Icon: typeof import('./src/components/flow-editor/save/icon.vue')['default']
    IconButon: typeof import('./src/components/icon-buton.vue')['default']
    IdCard: typeof import('./src/components/icons/id-card.vue')['default']
    Inbox: typeof import('./src/components/icons/inbox.vue')['default']
    InputBox: typeof import('./src/components/input-box.vue')['default']
    InputNode: typeof import('./src/components/flow-editor/nodes/input-node.vue')['default']
    JsonEditorPanel: typeof import('./src/components/flow-editor/json-editor/json-editor-panel.vue')['default']
    LabelGroup: typeof import('./src/components/label-group.vue')['default']
    Library: typeof import('./src/components/icons/library.vue')['default']
    LinkToEntity: typeof import('./src/components/link-to-entity.vue')['default']
    ListTodo: typeof import('./src/components/icons/list-todo.vue')['default']
    Lock: typeof import('./src/components/icons/lock.vue')['default']
    Login: typeof import('./src/components/icons/login.vue')['default']
    Logout: typeof import('./src/components/icons/logout.vue')['default']
    Logs: typeof import('./src/components/icons/logs.vue')['default']
    Mail: typeof import('./src/components/icons/mail.vue')['default']
    MapPinHouse: typeof import('./src/components/icons/map-pin-house.vue')['default']
    Menu: typeof import('./src/components/icons/menu.vue')['default']
    Modal: typeof import('./src/components/modal.vue')['default']
    MonitorCheck: typeof import('./src/components/icons/monitor-check.vue')['default']
    MonoIcon: typeof import('./src/components/mono-icon.vue')['default']
    Navbar: typeof import('./src/components/navbar.vue')['default']
    NightToggle: typeof import('./src/components/night-toggle.vue')['default']
    NoData: typeof import('./src/components/general/noData.vue')['default']
    Organization: typeof import('./src/components/icons/organization.vue')['default']
    OrganizationHealthChecks: typeof import('./src/components/organizations/organization-health-checks.vue')['default']
    OrganizationOverview: typeof import('./src/components/organizations/organization-overview.vue')['default']
    OrganizationSettings: typeof import('./src/components/organizations/organization-settings.vue')['default']
    OrganizationSettingsAuthentication: typeof import('./src/components/organizations/settings/organization-settings-authentication.vue')['default']
    OrganizationSettingsAuthorization: typeof import('./src/components/organizations/settings/organization-settings-authorization.vue')['default']
    OrganizationSettingsCallback: typeof import('./src/components/organizations/settings/organization-settings-callback.vue')['default']
    OrganizationSettingsDesign: typeof import('./src/components/organizations/settings/organization-settings-design.vue')['default']
    OrganizationSettingsFlows: typeof import('./src/components/organizations/settings/organization-settings-flows.vue')['default']
    OrganizationSettingsGeneral: typeof import('./src/components/organizations/settings/organization-settings-general.vue')['default']
    OrganizationSettingsNav: typeof import('./src/components/organizations/settings/organization-settings-nav.vue')['default']
    OrganizationSettingsProviders: typeof import('./src/components/organizations/settings/organization-settings-providers.vue')['default']
    OrganizationSettingsScopes: typeof import('./src/components/organizations/settings/organization-settings-scopes.vue')['default']
    OrganizationSettingsSecurity: typeof import('./src/components/organizations/settings/organization-settings-security.vue')['default']
    OrganizationSettingsSubmerchant: typeof import('./src/components/organizations/settings/organization-settings-submerchant.vue')['default']
    OrganizationSettingsTerminate: typeof import('./src/components/organizations/settings/organization-settings-terminate.vue')['default']
    OrganizationSettingsTreasury: typeof import('./src/components/organizations/settings/organization-settings-treasury.vue')['default']
    OrganizationSubmerchants: typeof import('./src/components/organizations/organization-submerchants.vue')['default']
    OrganizationTab: typeof import('./src/components/organizations/organization-tab.vue')['default']
    OrganizationTreasury: typeof import('./src/components/organizations/organization-treasury.vue')['default']
    OrganizationWhatsapp: typeof import('./src/components/organizations/organization-whatsapp.vue')['default']
    OutputNode: typeof import('./src/components/flow-editor/nodes/output-node.vue')['default']
    Paintbrush: typeof import('./src/components/icons/paintbrush.vue')['default']
    Pencil: typeof import('./src/components/icons/pencil.vue')['default']
    PencilAlt: typeof import('./src/components/icons/pencil-alt.vue')['default']
    PhoneVibrate: typeof import('./src/components/icons/phone-vibrate.vue')['default']
    Plus: typeof import('./src/components/icons/plus.vue')['default']
    ProfileSidebar: typeof import('./src/components/profile-sidebar.vue')['default']
    Qrcode: typeof import('./src/components/icons/qrcode.vue')['default']
    QuestionMark: typeof import('./src/components/icons/question-mark.vue')['default']
    Refresh: typeof import('./src/components/icons/refresh.vue')['default']
    Reload: typeof import('./src/components/icons/reload.vue')['default']
    Reply: typeof import('./src/components/icons/reply.vue')['default']
    RouteItem: typeof import('./src/components/route-item.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Search: typeof import('./src/components/icons/search.vue')['default']
    Selectbox: typeof import('./src/components/selectbox.vue')['default']
    Server: typeof import('./src/components/icons/server.vue')['default']
    ShieldCheck: typeof import('./src/components/icons/shield-check.vue')['default']
    ShoppingCart: typeof import('./src/components/icons/shopping-cart.vue')['default']
    Sidebar: typeof import('./src/components/flow-editor/sidebar/sidebar.vue')['default']
    SimulateDefaultRequest: typeof import('./src/components/simulates/simulate-default-request.vue')['default']
    SimulateLogs: typeof import('./src/components/simulates/simulate-logs.vue')['default']
    SimulateOverview: typeof import('./src/components/simulates/simulate-overview.vue')['default']
    SimulateRetryManagement: typeof import('./src/components/simulates/simulate-retry-management.vue')['default']
    SimulateScheduledWorks: typeof import('./src/components/simulates/simulate-scheduled-works.vue')['default']
    SimulateStatistics: typeof import('./src/components/simulates/simulate-statistics.vue')['default']
    SimulateVersions: typeof import('./src/components/simulates/simulate-versions.vue')['default']
    Sort: typeof import('./src/components/icons/sort.vue')['default']
    Spin: typeof import('./src/components/icons/spin.vue')['default']
    Spinner: typeof import('./src/components/spinner.vue')['default']
    SquareActivity: typeof import('./src/components/icons/square-activity.vue')['default']
    SquareUser: typeof import('./src/components/icons/square-user.vue')['default']
    Support: typeof import('./src/components/icons/support.vue')['default']
    Switch: typeof import('./src/components/switch.vue')['default']
    SwitchNode: typeof import('./src/components/flow-editor/nodes/switch-node.vue')['default']
    Table: typeof import('./src/components/table.vue')['default']
    Tag: typeof import('./src/components/icons/tag.vue')['default']
    TBadge: typeof import('./src/components/tui/t-badge.vue')['default']
    TChart: typeof import('./src/components/tui/t-chart.vue')['default']
    TCheckbox: typeof import('./src/components/tui/t-checkbox.vue')['default']
    TCol: typeof import('./src/components/tui/t-col.vue')['default']
    TCombobox: typeof import('./src/components/tui/t-combobox.vue')['default']
    TConfirmationModal: typeof import('./src/components/tui/t-confirmation-modal.vue')['default']
    TCreateConfirmationModal: typeof import('./src/components/tui/t-create-confirmation-modal.vue')['default']
    TDatepicker: typeof import('./src/components/tui/t-datepicker.vue')['default']
    TDeleteModal: typeof import('./src/components/tui/t-delete-modal.vue')['default']
    TDropdown: typeof import('./src/components/tui/t-dropdown.vue')['default']
    Terminal: typeof import('./src/components/icons/terminal.vue')['default']
    TFileInput: typeof import('./src/components/tui/t-file-input.vue')['default']
    TFlyout: typeof import('./src/components/tui/t-flyout.vue')['default']
    TIcon: typeof import('./src/components/tui/t-icon.vue')['default']
    TimeoutNode: typeof import('./src/components/flow-editor/nodes/timeout-node.vue')['default']
    TKeyValue: typeof import('./src/components/tui/t-key-value.vue')['default']
    TLargeFlyout: typeof import('./src/components/tui/t-large-flyout.vue')['default']
    TLoader: typeof import('./src/components/tui/t-loader.vue')['default']
    TNavbarDropdown: typeof import('./src/components/tui/t-navbar-dropdown.vue')['default']
    TNavbarNotificationDropdown: typeof import('./src/components/tui/t-navbar-notification-dropdown.vue')['default']
    TNavbarSearch: typeof import('./src/components/tui/t-navbar-search.vue')['default']
    TNoData: typeof import('./src/components/tui/t-no-data.vue')['default']
    Toggle: typeof import('./src/components/toggle.vue')['default']
    TPieChart: typeof import('./src/components/tui/t-pie-chart.vue')['default']
    TProfileDropdown: typeof import('./src/components/tui/t-profile-dropdown.vue')['default']
    TProfileNav: typeof import('./src/components/tui/t-profile-nav.vue')['default']
    TProfileTabs: typeof import('./src/components/tui/t-profile-tabs.vue')['default']
    TransactionDescription: typeof import('./src/components/general/transactionDescription.vue')['default']
    Transfer: typeof import('./src/components/icons/transfer.vue')['default']
    Trash: typeof import('./src/components/icons/trash.vue')['default']
    TSelect: typeof import('./src/components/tui/t-select.vue')['default']
    TTable: typeof import('./src/components/tui/t-table.vue')['default']
    TTableActions: typeof import('./src/components/tui/t-table-actions.vue')['default']
    TTableFilter: typeof import('./src/components/tui/t-table-filter.vue')['default']
    TWebsocketStatus: typeof import('./src/components/tui/t-websocket-status.vue')['default']
    Upload: typeof import('./src/components/icons/upload.vue')['default']
    User: typeof import('./src/components/icons/user.vue')['default']
    UserAdd: typeof import('./src/components/icons/user-add.vue')['default']
    UserCheck: typeof import('./src/components/icons/user-check.vue')['default']
    Users: typeof import('./src/components/icons/users.vue')['default']
    View: typeof import('./src/components/view.vue')['default']
    Waas: typeof import('./src/components/icons/waas.vue')['default']
    Wallet: typeof import('./src/components/icons/wallet.vue')['default']
    Whatsapp: typeof import('./src/components/icons/whatsapp.vue')['default']
    Wiki: typeof import('./src/components/wiki.vue')['default']
    WikiContent: typeof import('./src/components/wiki-content.vue')['default']
    WikiLink: typeof import('./src/components/wiki-link.vue')['default']
    Wizard: typeof import('./src/components/wizard.vue')['default']
    Workflow: typeof import('./src/components/icons/workflow.vue')['default']
    World: typeof import('./src/components/icons/world.vue')['default']
    XCircle: typeof import('./src/components/icons/x-circle.vue')['default']
  }
}
