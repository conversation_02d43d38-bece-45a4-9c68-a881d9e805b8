import { defineConfig } from 'vite'
import { resolve } from 'path'

import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import AutoImport from 'unplugin-auto-import/vite'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'

export default defineConfig({
    plugins: [
        vue(),
        Components({
            extensions: ['vue', 'md', 'svg'],
            directoryAsNamespace: false,
            dts: true,
        }),
        AutoImport({
            imports: [
                'vue',
                'vue-router',
                'vue-i18n',
                'pinia',
            ],
            dts: './auto-imports.d.ts',
        }),
        VueI18nPlugin({
            include: resolve(__dirname, './src/locales/**')
        })
    ],
    build: {
        rollupOptions: {
            output: {
                manualChunks(id, { getModuleInfo }) {
                    const match = /.*\.strings\.(\w+)\.js/.exec(id);
                    if (match) {
                        const language = match[1]; // e.g. "en"
                        const dependentEntryPoints = [];

                        // Use a Set to handle each module at most once, preventing infinite loops
                        const idsToHandle = new Set(getModuleInfo(id).dynamicImporters);

                        for (const moduleId of idsToHandle) {
                            const { isEntry, dynamicImporters, importers } = getModuleInfo(moduleId);

                            if (isEntry || dynamicImporters.length > 0) {
                                dependentEntryPoints.push(moduleId);
                            }

                            // Add importers to the set to handle them in future iterations
                            for (const importerId of importers) {
                                idsToHandle.add(importerId);
                            }
                        }

                        // If there is a unique entry, create a chunk based on the entry name
                        if (dependentEntryPoints.length === 1) {
                            return `${dependentEntryPoints[0].split('/').slice(-1)[0].split('.')[0]}.strings.${language}`;
                        }

                        // For multiple entries, create a "shared" chunk
                        if (dependentEntryPoints.length > 1) {
                            return `shared.strings.${language}`;
                        }
                    }
                }
            }
        },
        reportCompressedSize: false,
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src'),
        },
    },
    server: {
        open: false,
        port: 7000,
    },
    esbuild: {
        drop: ['console', 'debugger'],
    },
})